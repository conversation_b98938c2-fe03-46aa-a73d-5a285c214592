# API Migration Quảng Cáo V1 sang V2

## Endpoint: POST /restaurant-ads/migrate-v1-to-v2

### <PERSON><PERSON> tả
Endpoint này cho phép migrate quảng cáo từ hệ thống v1 sang v2 với các tùy chọn filter chi tiết.

### Headers
- `province-id`: ID của tỉnh/thành phố (required)
- `Authorization`: Bearer token (required)

### Request Body
```typescript
{
  "adCategoryCodes"?: string[],     // Danh sách loại quảng cáo cần migrate
  "statuses"?: string[],            // Danh sách trạng thái quảng cáo cần migrate  
  "restaurantIds"?: number[],       // Danh sách restaurant_id cần migrate
  "activeOnly"?: boolean            // Chỉ migrate quảng cáo đang active
}
```

### Các giá trị enum

#### adCategoryCodes (EAdCategoryCode)
- `"category_ad_page"`: Quảng cáo trang danh mục
- `"news_feed_ad_page"`: Qu<PERSON>ng cáo trang tin tức
- `"search_ad_page"`: Qu<PERSON>ng cáo trang tìm kiếm
- `"search_page"`: Trang tìm kiếm

#### statuses (ERestaurantAdStatus)
- `"pending"`: Đang chờ
- `"in_progress"`: Đang xử lý
- `"done"`: Hoàn thành
- `"canceled"`: Đã hủy
- `"renew_pending"`: Đang chờ gia hạn
- `"expired"`: Đã hết hạn

### Ví dụ sử dụng

#### 1. Migrate tất cả quảng cáo (không có filter)
```bash
curl -X POST "http://localhost:3000/restaurant-ads/migrate-v1-to-v2" \
  -H "Content-Type: application/json" \
  -H "province-id: 01" \
  -H "Authorization: Bearer your-token" \
  -d "{}"
```

#### 2. Migrate chỉ quảng cáo loại search_page và news_feed_ad_page
```bash
curl -X POST "http://localhost:3000/restaurant-ads/migrate-v1-to-v2" \
  -H "Content-Type: application/json" \
  -H "province-id: 01" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "adCategoryCodes": ["search_page", "news_feed_ad_page"]
  }'
```

#### 3. Migrate chỉ quảng cáo của một số restaurant cụ thể
```bash
curl -X POST "http://localhost:3000/restaurant-ads/migrate-v1-to-v2" \
  -H "Content-Type: application/json" \
  -H "province-id: 01" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "restaurantIds": [123, 456, 789]
  }'
```

#### 4. Migrate chỉ quảng cáo đang active với trạng thái done
```bash
curl -X POST "http://localhost:3000/restaurant-ads/migrate-v1-to-v2" \
  -H "Content-Type: application/json" \
  -H "province-id: 01" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "statuses": ["done"],
    "activeOnly": true
  }'
```

#### 5. Kết hợp nhiều filter
```bash
curl -X POST "http://localhost:3000/restaurant-ads/migrate-v1-to-v2" \
  -H "Content-Type: application/json" \
  -H "province-id: 01" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "adCategoryCodes": ["search_page"],
    "restaurantIds": [123, 456],
    "statuses": ["done", "in_progress"],
    "activeOnly": true
  }'
```

### Response
```typescript
{
  "success": number,        // Số lượng migrate thành công
  "error": number,          // Số lượng migrate lỗi
  "total": number,          // Tổng số restaurant cần migrate
  "skipped": number,        // Số lượng restaurant đã migrate trước đó
  "campaigns": [            // Danh sách campaign đã tạo
    {
      "id": number,
      "restaurant_id": string,
      "items_count": number,
      "status": string
    }
  ],
  "errors": [               // Danh sách lỗi (nếu có)
    {
      "restaurant_id": string,
      "error": string
    }
  ]
}
```

### Lưu ý
- Nếu không cung cấp `statuses`, mặc định sẽ migrate các trạng thái: `done`, `in_progress`, `pending`, `renew_pending`
- Nếu không cung cấp `adCategoryCodes`, sẽ migrate tất cả loại quảng cáo
- Nếu không cung cấp `restaurantIds`, sẽ migrate tất cả restaurant
- `activeOnly` mặc định là `false`
- Hệ thống sẽ tự động bỏ qua các restaurant đã được migrate thành công trước đó và chưa rollback
