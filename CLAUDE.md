# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Vill Admin Backend** - a comprehensive NestJS-based administration system for Vill Vietnam, a Vietnamese food delivery platform. The system provides APIs for managing restaurants, drivers, orders, payments, and complex business operations across multiple provinces.

## Development Commands

### Core Development Commands

```bash
# Install dependencies
npm install

# Development server (UTC timezone)
npm run start:dev

# Production build and start
npm run build
npm run start:prod

# Testing
npm run test
npm run test:e2e
npm run test:cov

# Code quality
npm run lint
npm run format

# Database migrations
npm run migration:generate -- MigrationName
npm run migration:run
```

### API Documentation

The application runs on **port 3001** with Swagger documentation available at:
```
http://localhost:3001/api/docs
```

## Architecture Overview

### Core Technologies

- **Framework**: NestJS (Node.js with TypeScript)
- **Database**: MySQL with TypeORM
- **Cache**: Redis
- **Message Queue**: RabbitMQ, Bull Queue
- **Search**: Elasticsearch
- **Authentication**: JWT with role-based permissions
- **File Storage**: AWS S3 (DigitalOcean Spaces)
- **Logging**: Winston with Loki integration
- **Monitoring**: Grafana integration

### Multi-Tenant Architecture

The system implements **province-based multi-tenancy** where each province operates with:
- Separate database connections
- Independent configuration settings
- Isolated business operations
- Province-specific user permissions

### Module Structure

The application follows a **feature-based module architecture** with these key domains:

#### **Restaurant Management**
- Restaurant registration and approval workflows
- Menu management with categories, items, and options
- Business hours and delivery zone management
- Revenue tracking and analytics
- Review and rating systems

#### **Driver Management & Scoring System**
- Complex driver scoring engine with configurable rules
- Multi-tiered leaderboard system (daily, weekly, monthly)
- Driver order plan registration and bonus calculations
- Disciplinary action tracking
- Tax reporting and compliance

#### **Order Processing**
- Real-time order status tracking
- Assignment and rejection logging
- Cancellation type management
- Fee calculation and management
- Statistics and analytics

#### **Payment Integration**
- Multi-provider support (MoMo, ZaloPay, 9Pay/nPay)
- Webhook handling with automatic retry mechanisms
- Transaction management and audit trails
- Wallet integration with balance tracking
- Refund processing workflows

#### **Authentication & Authorization**
- Role-based access control (RBAC)
- Province-based permission filtering
- JWT authentication with refresh tokens
- Employee management with activity logging

## Key Business Systems

### Driver Scoring Engine

The driver scoring system is a **sophisticated rule-based engine** that processes multiple scoring sources:

#### **Scoring Rule Architecture**
```typescript
// Three-tier rule system
ScoringRule -> ScoringRuleCondition -> ScoringRuleAction
```

**Key Features:**
- **Flexible Conditions**: Support for complex logical operations (AND/OR)
- **Multiple Calculation Types**: Fixed points or tiered calculations
- **Time-Based Rules**: Timezone-aware scoring (UTC+7 for Vietnam)
- **Multi-Source Support**: ORDER, DRIVER_ORDER_PLAN_REGISTRATION, SHIPPER_REVIEW, MANUAL
- **Comprehensive Audit**: Complete history tracking with before/after states

#### **Leaderboard System**
- **Three Simultaneous Leaderboards**: Daily, Weekly, Monthly
- **Province-Based Isolation**: Separate rankings per province
- **Real-Time Caching**: Redis-based caching with 1-minute TTL
- **Period Key Generation**: Time-based segmentation for rankings

### Payment Processing Architecture

The payment system handles **multiple Vietnamese payment providers**:

#### **Provider Integration**
- **MoMo Wallet**: Signature-based authentication, IPN verification
- **ZaloPay**: Multi-app support, various payment types (Wallet, ATM, Credit, VietQR)
- **9Pay (nPay)**: RSA-SHA256 signatures, business wallet transfers

#### **Webhook Management**
- **Bank Transaction Webhooks**: Automatic retry with exponential backoff
- **Payment Confirmation**: IPN handling for all providers
- **Status Tracking**: PENDING, SUCCESS, FAILED, RETRY states
- **Custom Code Extraction**: Transaction code parsing from bank data

### Restaurant Advertisement System

Complex advertising platform with:
- **Campaign Management**: Budget tracking, schedule management
- **Seller Management**: Advertiser onboarding and management
- **Contract Integration**: Automated contract generation and tracking
- **Payment Integration**: Bank webhook integration for ad payments

## Development Patterns

### Entity Relationship Patterns

The system uses **comprehensive entity relationships**:
- **Province-Scoped Entities**: Most entities include `province_id` for isolation
- **Audit Trail Entities**: History tracking for critical operations
- **Status Management**: Enum-based status tracking with transitions
- **Soft Deletion**: Logical deletion with `deleted_at` timestamps

### Service Layer Architecture

Services are organized by **business domain** with clear separation:
- **Core Services**: Business logic implementation
- **Integration Services**: External API communication
- **Job Services**: Background processing and cron jobs
- **Validation Services**: Input validation and business rules

### Configuration Management

The system uses **environment-based configuration** with:
- **Feature Flags**: Runtime feature toggling
- **Provider Configuration**: Payment provider settings per province
- **Database Configuration**: Multi-database connection management
- **External Service Configuration**: API endpoints and credentials

## Job Processing & Background Tasks

### Bull Queue Integration

The system implements **comprehensive background job processing**:

#### **Job Categories**
- **Driver Jobs**: Scoring calculations, ranking updates, contract processing
- **Order Jobs**: Status synchronization, assignment processing
- **Restaurant Jobs**: Revenue calculations, ad campaign processing
- **Tax Jobs**: Reporting generation, compliance calculations
- **Notification Jobs**: Push notifications, email delivery

#### **Cron Job Management**
- **Timezone-Aware Scheduling**: All jobs consider Vietnam timezone (UTC+7)
- **Province-Based Execution**: Jobs can be scoped to specific provinces
- **Error Handling**: Comprehensive error tracking and retry mechanisms
- **Performance Monitoring**: Job execution time tracking

### Event-Driven Architecture

The system implements **event-driven patterns** for:
- **Order Updates**: Real-time order status propagation
- **Restaurant Changes**: Menu updates, status changes
- **Driver Activities**: Scoring events, status changes
- **Payment Events**: Transaction confirmations, refund processing

## Database Design Patterns

### TypeORM Configuration

The system uses **advanced TypeORM features**:
- **Multiple Database Connections**: Province-based database routing
- **Custom Transformers**: Decimal precision handling, array transformations
- **Migration Management**: Schema version control
- **Connection Pooling**: Optimized database connection management

### Query Optimization

**Performance optimization patterns**:
- **Eager Loading**: Strategic relationship loading
- **Indexing Strategy**: Composite indexes for complex queries
- **Caching Layer**: Redis integration for frequently accessed data
- **Query Builder**: Complex query construction for analytics

## Security Implementation

### Authentication & Authorization

**Multi-layer security**:
- **JWT Authentication**: Access and refresh token management
- **Role-Based Access Control**: Granular permission system
- **Province-Based Isolation**: Data access restricted by province
- **API Key Management**: External service authentication

### Data Protection

**Security best practices**:
- **Input Validation**: class-validator integration
- **SQL Injection Prevention**: TypeORM query building
- **Rate Limiting**: Request throttling implementation
- **Audit Logging**: Complete action tracking

## Testing & Quality Assurance

### Testing Strategy

```bash
# Unit tests
npm run test

# End-to-end tests
npm run test:e2e

# Coverage reports
npm run test:cov
```

### Code Quality

- **ESLint**: Configured with strict rules
- **Prettier**: Code formatting consistency
- **TypeScript**: Strict type checking enabled
- **Husky**: Git hooks for quality gates

## Monitoring & Observability

### Logging Architecture

The system implements **comprehensive logging**:
- **Winston Logger**: Structured logging with multiple transports
- **Loki Integration**: Centralized log aggregation
- **Request Tracking**: HTTP request logging with correlation IDs
- **Error Tracking**: Exception logging with stack traces

### Performance Monitoring

**Observability features**:
- **Bull Board**: Queue monitoring dashboard
- **Swagger Documentation**: API documentation and testing
- **Health Checks**: System health monitoring endpoints
- **Metrics Collection**: Performance metrics gathering

## External Service Integration

### Microservices Communication

The system integrates with **multiple external services**:
- **Auto Order Assignment**: Microservice for order routing
- **Delivery Service**: Driver tracking and routing
- **Promotion Service**: Campaign management
- **Loyalty Service**: Customer loyalty programs
- **Fraud Detection**: Transaction monitoring

### Third-Party APIs

**External API integrations**:
- **OneSignal**: Push notification delivery
- **Elasticsearch**: Search and analytics
- **Firebase**: Mobile app integration
- **Payment Gateways**: MoMo, ZaloPay, 9Pay integrations

## Common Development Tasks

### Adding New Features

1. **Create Module**: Follow NestJS module structure
2. **Define Entities**: TypeORM entities with proper relationships
3. **Implement Services**: Business logic in service layer
4. **Create Controllers**: API endpoint definitions
5. **Add DTOs**: Input/output validation objects
6. **Update Documentation**: Swagger API documentation
7. **Add Tests**: Unit and integration tests
8. **Migration**: Database schema updates

### Database Operations

```bash
# Generate migration
npm run migration:generate -- CreateNewTable

# Run migrations
npm run migration:run
```

### Working with Jobs

The system uses **Bull Queue** for background processing:
- Job definitions in `/jobs/` directory
- Processor implementations for job execution
- Cron job scheduling for periodic tasks
- Queue monitoring via Bull Board

## Environment Configuration

### Required Environment Variables

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=vill_admin
DATABASE_USERNAME=root
DATABASE_PASSWORD=password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# External Services
ELASTICSEARCH_NODE=http://localhost:9200
RABBITMQ_URL=amqp://localhost:5672
```

### Configuration Files

Configuration is managed through:
- **Environment Variables**: Runtime configuration
- **Config Files**: Feature-specific settings in `/config/`
- **Provider Configuration**: Service-specific settings
- **Database Configuration**: Connection management

## Performance Considerations

### Optimization Strategies

- **Database Indexing**: Strategic index placement for query performance
- **Caching Layer**: Redis integration for frequently accessed data
- **Connection Pooling**: Optimized database connection management
- **Query Optimization**: Efficient query patterns and lazy loading
- **Background Processing**: Job queues for heavy operations

### Scaling Considerations

The architecture supports **horizontal scaling** through:
- **Province-Based Sharding**: Geographic data distribution
- **Stateless Design**: Session-independent request handling
- **Queue-Based Processing**: Distributed job processing
- **Service Isolation**: Microservice communication patterns