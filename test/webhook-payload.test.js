// Test script for Webhook Payload with extracted_transaction_code
// Run with: node test/webhook-payload.test.js

function buildWebhookPayload(transaction, setting) {
    const payload = {
        event: 'transaction.created',
        timestamp: new Date().toISOString(),
        data: {
            transaction_id: transaction.id,
            bank_code: transaction.bank_code,
            transaction_code: transaction.transaction_code,
            transaction_time: transaction.transaction_time,
            transaction_type: transaction.transaction_type,
            amount: transaction.amount,
            current_balance: transaction.current_balance,
            raw_data: transaction.raw_data,
            tracking_bank_transaction_type: transaction.tracking_bank_transaction_type,
            is_verified: transaction.is_verified,
            tracking_bank_account_id: transaction.tracking_bank_account_id,
            created_at: transaction.created_at,
        },
    };

    // Nếu là CUSTOM_CODE và có suffix_length, thêm extracted_transaction_code
    if (setting?.trigger_type === 'CUSTOM_CODE' &&
        setting.custom_code &&
        setting.custom_code_suffix_length) {
        const extractedCode = extractTransactionCodeWithSuffix(
            transaction,
            setting.custom_code,
            setting.custom_code_suffix_length
        );
        if (extractedCode) {
            payload.data['extracted_transaction_code'] = extractedCode;
        }
    }

    return payload;
}

function extractTransactionCodeWithSuffix(transaction, customCode, suffixLength) {
    if (!customCode || !suffixLength) return null;

    try {
        const rawData = transaction.raw_data || '';
        const codes = customCode.split(',').map(code => code.trim());

        for (const code of codes) {
            // Tìm tất cả các vị trí xuất hiện của code
            const matches = findAllMatches(rawData, code);

            if (matches.length > 0) {
                // Ưu tiên tìm trong phần nội dung (ND:) trước
                const ndMatch = findInContent(rawData, code, suffixLength);
                if (ndMatch) {
                    return ndMatch;
                }

                // Nếu không tìm thấy trong ND, lấy match đầu tiên
                const firstMatch = matches[0];
                const endIndex = Math.min(firstMatch.index + suffixLength, rawData.length);
                return rawData.substring(firstMatch.index, endIndex);
            }
        }

        return null;
    } catch (error) {
        console.error('Error extracting transaction code with suffix:', error);
        return null;
    }
}

function findAllMatches(text, pattern) {
    const matches = [];
    const lowerText = text.toLowerCase();
    const lowerPattern = pattern.toLowerCase();
    let index = 0;

    while ((index = lowerText.indexOf(lowerPattern, index)) !== -1) {
        matches.push({
            index: index,
            match: text.substring(index, index + pattern.length)
        });
        index += pattern.length;
    }

    return matches;
}

function findInContent(rawData, code, suffixLength) {
    // Tìm trong phần nội dung (ND:) của giao dịch
    const ndPattern = /ND:([^;]+)/i;
    const ndMatch = rawData.match(ndPattern);

    if (ndMatch && ndMatch[1]) {
        const content = ndMatch[1];
        const codeIndex = content.toLowerCase().indexOf(code.toLowerCase());

        if (codeIndex !== -1) {
            // Tìm vị trí thực tế trong raw data
            const actualIndex = rawData.toLowerCase().indexOf(content.toLowerCase()) + codeIndex;
            const endIndex = Math.min(actualIndex + suffixLength, rawData.length);
            return rawData.substring(actualIndex, endIndex);
        }
    }

    return null;
}

// Test cases
const testCases = [
    {
        name: "Test 1: CUSTOM_CODE with suffix_length",
        transaction: {
            id: 123,
            bank_code: "VCB",
            transaction_code: "FT24001123456",
            raw_data: "VILL123456789 Transfer to account",
            amount: 1000000,
            tracking_bank_transaction_type: "DEPOSIT"
        },
        setting: {
            trigger_type: "CUSTOM_CODE",
            custom_code: "VILL",
            custom_code_suffix_length: 10
        },
        expectedExtractedCode: "VILL123456"
    },
    {
        name: "Test 2: CUSTOM_CODE without suffix_length",
        transaction: {
            id: 124,
            raw_data: "VILL123456789 Transfer to account",
        },
        setting: {
            trigger_type: "CUSTOM_CODE",
            custom_code: "VILL"
            // No custom_code_suffix_length
        },
        expectedExtractedCode: null
    },
    {
        name: "Test 3: AMOUNT_THRESHOLD (should not have extracted_code)",
        transaction: {
            id: 125,
            raw_data: "VILL123456789 Transfer to account",
        },
        setting: {
            trigger_type: "AMOUNT_THRESHOLD",
            amount_threshold: 1000000
        },
        expectedExtractedCode: null
    },
    {
        name: "Test 4: CUSTOM_CODE with multiple codes",
        transaction: {
            id: 126,
            raw_data: "Payment TRANSFER987654321 completed",
        },
        setting: {
            trigger_type: "CUSTOM_CODE",
            custom_code: "PAYMENT,TRANSFER",
            custom_code_suffix_length: 8
        },
        expectedExtractedCode: "Payment "
    },
    {
        name: "Test 5: Real Vietinbank data - should extract from ND content",
        transaction: {
            id: 223,
            bank_code: "Vietinbank",
            transaction_code: "VILL-********",
            raw_data: "14/03/2025 13:38|TK:************|GD:+1,000,000VND|SDC:2,244,029,765VND|ND:CT DEN:************ PHAM THAI A2N 2chuyen tien VILL-12312 *****************; tai N123a",
            amount: 1000000,
            tracking_bank_transaction_type: "INCOMING"
        },
        setting: {
            trigger_type: "CUSTOM_CODE",
            custom_code: "VILL",
            custom_code_suffix_length: 10
        },
        expectedExtractedCode: "VILL-12312"
    }
];

console.log("🧪 Testing Webhook Payload with extracted_transaction_code\n");

testCases.forEach((testCase, index) => {
    const payload = buildWebhookPayload(testCase.transaction, testCase.setting);
    const actualExtractedCode = payload.data.extracted_transaction_code || null;
    
    const passed = actualExtractedCode === testCase.expectedExtractedCode;
    const status = passed ? "✅ PASS" : "❌ FAIL";
    
    console.log(`${status} ${testCase.name}`);
    console.log(`   Setting: ${testCase.setting.trigger_type}`);
    if (testCase.setting.custom_code) {
        console.log(`   Custom Code: "${testCase.setting.custom_code}"`);
        console.log(`   Suffix Length: ${testCase.setting.custom_code_suffix_length || 'undefined'}`);
    }
    console.log(`   Raw Data: "${testCase.transaction.raw_data}"`);
    console.log(`   Expected: ${testCase.expectedExtractedCode}`);
    console.log(`   Got: ${actualExtractedCode}`);
    console.log(`   Has extracted_transaction_code field: ${payload.data.hasOwnProperty('extracted_transaction_code')}`);
    console.log("");
});

console.log("🎯 Test completed!");
