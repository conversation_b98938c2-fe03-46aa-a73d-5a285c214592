// Test script for Custom Code with Suffix Length functionality
// Run with: node test/webhook-custom-code.test.js

function extractTransactionCodeWithSuffix(rawData, customCode, suffixLength) {
    if (!customCode || !suffixLength) return null;

    try {
        const codes = customCode.split(',').map(code => code.trim());

        for (const code of codes) {
            // Tìm tất cả các vị trí xuất hiện của code
            const matches = findAllMatches(rawData, code);

            if (matches.length > 0) {
                // Ưu tiên tìm trong phần nội dung (ND:) trước
                const ndMatch = findInContent(rawData, code, suffixLength);
                if (ndMatch) {
                    return ndMatch;
                }

                // Nếu không tìm thấy trong ND, lấy match đầu tiên
                const firstMatch = matches[0];
                const endIndex = Math.min(firstMatch.index + code.length + suffixLength, rawData.length);
                return rawData.substring(firstMatch.index, endIndex);
            }
        }

        return null;
    } catch (error) {
        console.error('Error extracting transaction code with suffix:', error);
        return null;
    }
}

function findAllMatches(text, pattern) {
    const matches = [];
    const lowerText = text.toLowerCase();
    const lowerPattern = pattern.toLowerCase();
    let index = 0;

    while ((index = lowerText.indexOf(lowerPattern, index)) !== -1) {
        matches.push({
            index: index,
            match: text.substring(index, index + pattern.length)
        });
        index += pattern.length;
    }

    return matches;
}

function findInContent(rawData, code, suffixLength) {
    // Tìm trong phần nội dung (ND:) của giao dịch
    const ndPattern = /ND:([^;]+)/i;
    const ndMatch = rawData.match(ndPattern);

    if (ndMatch && ndMatch[1]) {
        const content = ndMatch[1];
        const codeIndex = content.toLowerCase().indexOf(code.toLowerCase());

        if (codeIndex !== -1) {
            // Tìm vị trí thực tế trong raw data
            const actualIndex = rawData.toLowerCase().indexOf(content.toLowerCase()) + codeIndex;
            const endIndex = Math.min(actualIndex + code.length + suffixLength, rawData.length);
            return rawData.substring(actualIndex, endIndex);
        }
    }

    return null;
}

// Test cases
const testCases = [
    {
        name: "Test 1: VILL code + 6 suffix characters",
        rawData: "VILL123456789ABCDEF Transfer to account",
        customCode: "VILL",
        suffixLength: 6,
        expected: "VILL123456"
    },
    {
        name: "Test 2: Multiple codes, first match (Payment)",
        rawData: "Payment TRANSFER987654321 completed",
        customCode: "PAYMENT,TRANSFER",
        suffixLength: 1,
        expected: "Payment "
    },
    {
        name: "Test 3: Case insensitive matching",
        rawData: "vill555666777 transaction",
        customCode: "VILL",
        suffixLength: 2,
        expected: "vill55"
    },
    {
        name: "Test 4: Suffix longer than remaining text",
        rawData: "VILL123",
        customCode: "VILL",
        suffixLength: 10,
        expected: "VILL123"
    },
    {
        name: "Test 5: No match found",
        rawData: "ABC123456789",
        customCode: "VILL",
        suffixLength: 5,
        expected: null
    },
    {
        name: "Test 6: TRANSFER + 6 suffix characters",
        rawData: "TRANSFER123456",
        customCode: "TRANSFER",
        suffixLength: 6,
        expected: "TRANSFER123456"
    },
    {
        name: "Test 7: Real Vietinbank data - should prioritize ND content",
        rawData: "14/03/2025 13:38|TK:************|GD:+1,000,000VND|SDC:2,244,029,765VND|ND:CT DEN:************ PHAM THAI A2N 2chuyen tien VILL-12312 FT250738021115492; tai N123a",
        customCode: "VILL",
        suffixLength: 6,
        expected: "VILL-12312"
    },
    {
        name: "Test 8: Multiple VILL codes - should pick from ND",
        rawData: "VILL-99999 other data|ND:payment for VILL-12345 service",
        customCode: "VILL",
        suffixLength: 6,
        expected: "VILL-12345"
    },
    {
        name: "Test 9: No ND section - should use first match",
        rawData: "VILL-11111 transfer data VILL-22222 more data",
        customCode: "VILL",
        suffixLength: 6,
        expected: "VILL-11111"
    }
];

console.log("🧪 Testing Custom Code with Suffix Length functionality\n");

testCases.forEach((testCase, index) => {
    const result = extractTransactionCodeWithSuffix(
        testCase.rawData, 
        testCase.customCode, 
        testCase.suffixLength
    );
    
    const passed = result === testCase.expected;
    const status = passed ? "✅ PASS" : "❌ FAIL";
    
    console.log(`${status} ${testCase.name}`);
    console.log(`   Raw Data: "${testCase.rawData}"`);
    console.log(`   Custom Code: "${testCase.customCode}"`);
    console.log(`   Suffix Length: ${testCase.suffixLength}`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Got: ${result}`);
    console.log("");
});

console.log("🎯 Test completed!");
