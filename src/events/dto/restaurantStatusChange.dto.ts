import { ERestaurantOperatingStatus } from 'src/entities/restaurant.entity';

export class RestaurantStatusChangeDto {
    constructor(restaurantId: number, status: ERestaurantOperatingStatus, provinceId: number) {
        this.restaurantId = restaurantId;
        this.provinceId = provinceId;
        this.status = status;
    }
    restaurantId: number;
    provinceId: number;
    status: ERestaurantOperatingStatus;
}
