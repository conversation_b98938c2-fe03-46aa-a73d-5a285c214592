import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { IShiftWork } from 'src/entities/appSetting.entity';
import { FoodOrder } from 'src/entities/foodOrder.entity';
import { FoodSalesLimitManagement } from 'src/entities/foodSalesLimitManagement.entity';
import { ShiftWork } from 'src/entities/shiftWork.entity';
import { User } from 'src/entities/user.entity';
import {
    EAdsCampaignEventNames,
    EAdsSearchEventNames,
    EFoodEventNames,
    EOrderEventNames,
    ERestaurantEventNames,
    EShiftWorkEventNames,
    EUserEventNames,
} from './constant';
import {
    FoodSalesLimitUpdatedDto,
    FoodUpdatedEvent,
    OrderCancelEvent,
    ShiftWorkEndDto,
    ShiftWorkUpdatedDto,
    UserCreatedEvent,
    UserUpdatedEvent,
} from './dto';
import { RestaurantUpdateReopenTime } from './dto/restaurantUpdateReopenTime.dto';
import { Logger } from '@nestjs/common';
import { ERestaurantOperatingStatus } from 'src/entities/restaurant.entity';
import { RestaurantStatusChangeDto } from './dto/restaurantStatusChange.dto';
import { RestaurantMerchantRemoveDto } from './dto/restaurantMerchantRemove.dto';
import { AdsKeyword } from 'src/entities/adsKeyword.entity';
import { OrderUpdatedEvent } from './dto/orderUpdated.dto';
import { AdsCampaignUpdateDto } from './dto/adsCampaignUpdate.dto';

@Injectable()
export class EventService {
    private logger = new Logger(EventService.name);
    constructor(private eventEmitter: EventEmitter2) {}

    shiftWorkUpdatedEvent(shifWork: ShiftWork, provinceId: string) {
        const shiftWorkUpdatedDto = new ShiftWorkUpdatedDto();
        shiftWorkUpdatedDto.provinceId = provinceId;
        shiftWorkUpdatedDto.shiftWork = shifWork;
        return this.eventEmitter.emitAsync(EShiftWorkEventNames.SHIFT_WORK_UPDATED, shiftWorkUpdatedDto);
    }

    shiftWorkEndEvent(shiftWork: ShiftWork, provinceId: string) {
        const shiftWorkEndDto = new ShiftWorkEndDto();
        shiftWorkEndDto.provinceId = provinceId;
        shiftWorkEndDto.shiftWork = shiftWork;
        return this.eventEmitter.emitAsync(EShiftWorkEventNames.SHIFT_WORK_END, shiftWorkEndDto);
    }

    // findAndRemoveFrameRestaurantsEmptyPromotionsEvent(provinceId: string) {
    //     return this.eventEmitter.emitAsync(ERestaurantEventNames.RESTAURANT_REMOVE_INVALID_FRAME, provinceId);
    // }

    findAndRemoveRestaurantsHasExpiredEvent(provinceId: string) {
        return this.eventEmitter.emitAsync(ERestaurantEventNames.RESTAURANT_REMOVE_FRAME_HAD_EXPIRE, provinceId);
    }

    shiftWorkSettingChange(setting: IShiftWork, provinceId: string) {
        return this.eventEmitter.emitAsync(EShiftWorkEventNames.SHIFT_WORK_SETTING_CHANGE, { setting, provinceId });
    }

    foodSalesLimitUpdatedEvent(foodSalesLimitManagement: FoodSalesLimitManagement, provinceId: string) {
        const eventData = new FoodSalesLimitUpdatedDto();
        eventData.data = foodSalesLimitManagement;
        eventData.provinceId = provinceId;
        return this.eventEmitter.emitAsync(EFoodEventNames.FOOD_SALES_LIMIT_UPDATED, eventData);
    }

    orderCancelEvent(orderId: number, foodOrders: FoodOrder[], provinceId: string) {
        const event = new OrderCancelEvent(orderId, foodOrders, provinceId);
        return this.eventEmitter.emitAsync(EOrderEventNames.ORDER_CANCEL, event);
    }

    orderUpdatedEvent(orderId: number, provinceId: string) {
        const event = new OrderUpdatedEvent(orderId, provinceId);
        console.log('orderUpdatedEvent', event);
        return this.eventEmitter.emitAsync(EOrderEventNames.ORDER_UPDATED, event);
    }

    foodUpdatedEvent(foodId: number, restaurantId: number, provinceId: string) {
        const event = new FoodUpdatedEvent(foodId, restaurantId, provinceId);
        return this.eventEmitter.emitAsync(EFoodEventNames.FOOD_UPDATED, event);
    }

    userCreatedEvent(user: User) {
        const event = new UserCreatedEvent({
            user,
        });
        return this.eventEmitter.emitAsync(EUserEventNames.USER_CREATED, event);
    }

    userUpdatedEvent(user: User, provinceId: number) {
        const event = new UserUpdatedEvent({
            user,
            provinceId,
        });
        return this.eventEmitter.emitAsync(EUserEventNames.USER_UPDATED, event);
    }

    restaurantUpdateReopenTime(restaurant_id: number, province_id: number) {
        this.logger.log('restaurantUpdateReopenTime: ---- ' + restaurant_id + '----' + province_id);
        const event = new RestaurantUpdateReopenTime(restaurant_id, province_id);
        return this.eventEmitter.emitAsync(ERestaurantEventNames.RESTAURANT_UPDATE_REOPEN_TIME, event);
    }

    deleteRestaurantMerchantEvent(merchantId: number, restaurantId: number, provinceId: number) {
        this.logger.log(
            'deleteRestaurantMerchantEvent: ---- ' + merchantId + '----' + restaurantId + '----' + provinceId,
        );
        const event = new RestaurantMerchantRemoveDto(merchantId, restaurantId, provinceId);
        return this.eventEmitter.emitAsync(ERestaurantEventNames.RESTAURANT_REMOVE_MERCHANT, event);
    }

    restaurantOperationStatusChangeEvent(restaurantId: number, status: ERestaurantOperatingStatus, provinceId: number) {
        const event = new RestaurantStatusChangeDto(restaurantId, status, provinceId);
        return this.eventEmitter.emitAsync(ERestaurantEventNames.RESTAURANT_OPERATION_STATUS_CHANGE, event);
    }

    updateAdsCampaign(adsCampaignId: number, provinceId: string) {
        const eventData = new AdsCampaignUpdateDto()
        eventData.adsCampaignId = adsCampaignId;
        eventData.provinceId = provinceId;
        return this.eventEmitter.emitAsync(EAdsCampaignEventNames.ADS_CAMPAIGN_UPDATE_STATUS, eventData);
    }

    
}
