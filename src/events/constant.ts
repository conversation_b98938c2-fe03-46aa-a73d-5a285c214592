export enum EShiftWorkEventNames {
    SHIFT_WORK_UPDATED = 'shiftWork.updated',
    SHIFT_WORK_END = 'shiftWork.end',
    SHIFT_WORK_SETTING_CHANGE = 'shiftwork.setting.change',
}

export enum ERestaurantEventNames {
    RESTAURANT_REMOVE_INVALID_FRAME = 'restaurant.removeInvalidFrame',
    RESTAURANT_REMOVE_FRAME_HAD_EXPIRE = 'restaurant.removeFrameHadExpire',
    RESTAURANT_UPDATE_REOPEN_TIME = 'restaurant.update.reopen',
    RESTAURANT_REMOVE_MERCHANT = 'restaurant.removeMerchant',
    RESTAURANT_OPERATION_STATUS_CHANGE = 'restaurant.operationStatusChange',
}

export enum EFoodEventNames {
    FOOD_SALES_LIMIT_UPDATED = 'foodSalesLimit.updated',
    FOOD_UPDATED = 'food.updated',
}

export enum EOrderEventNames {
    ORDER_CANCEL = 'order.cancel',
    ORDER_UPDATED = 'order.updated',
}

export enum EUserEventNames {
    USER_CREATED = 'user.created',
    USER_UPDATED = 'user.updated',
}

export enum EAdsSearchEventNames {
    ADS_SEARCH_UPDATED = 'AdsSearch.updated',
}

export enum EAdsCampaignEventNames {
    ADS_CAMPAIGN_UPDATE_STATUS = 'AdsCampaign.update.status',
}
