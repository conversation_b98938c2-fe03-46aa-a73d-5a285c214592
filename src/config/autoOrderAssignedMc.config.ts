export enum ENUM_AUTO_ORDER_ASSIGNED_PROTOCOL {
    AMQP = 'amqp', // RabbitMQ
    HTTP = 'http',
}

export const AUTO_ORDER_ASSIGNED_PROTOCOL = ENUM_AUTO_ORDER_ASSIGNED_PROTOCOL.HTTP;

function parseAutoOrderAssignmentProtocol(envProtocol: string): ENUM_AUTO_ORDER_ASSIGNED_PROTOCOL {
    const lowerCaseEnvProtocol = envProtocol?.toLowerCase();
    if (!lowerCaseEnvProtocol) {
        return AUTO_ORDER_ASSIGNED_PROTOCOL;
    }
    if (
        lowerCaseEnvProtocol === ENUM_AUTO_ORDER_ASSIGNED_PROTOCOL.AMQP ||
        lowerCaseEnvProtocol === ENUM_AUTO_ORDER_ASSIGNED_PROTOCOL.HTTP
    ) {
        return lowerCaseEnvProtocol;
    }
}

export default (): Record<string, any> => ({
    AUTO_ORDER_ASSIGNED_SERVICE_DOMAIN: process.env.AUTO_ORDER_ASSIGNED_SERVICE_DOMAIN,
    AUTO_ORDER_ASSIGNED_SERVICE_TOKEN: process.env.AUTO_ORDER_ASSIGNED_SERVICE_TOKEN,
    AUTO_ORDER_ASSIGNED_PROTOCOL: parseAutoOrderAssignmentProtocol(process.env.AUTO_ORDER_ASSIGNED_PROTOCOL),
});
