import { registerAs } from '@nestjs/config';

export default registerAs('dynamicLinkConfig', () => ({
    domainUrlPrefix: process.env.DYNAMIC_LINK_DOMAIN,
    AndroidPackageName: process.env.DYNAMIC_LINK_ANDROID_PACKAGE_NAME,
    iOSBundleId: process.env.DYNAMIC_LINK_IOS_BUNDLE_ID,
    iOSAppStoreId: process.env.DYNAMIC_LINK_IOS_APP_STORE_ID,
    firebaseDynamicLinksAPIKey: process.env.DYNAMIC_LINK_API_KEY,
    desktopFallbackLink: process.env.DYNAMIC_LINK_DESKTOP_FALLBACK_LINK,
    domainUrlBranchIO: process.env.BRANCH_IO_LINK_DOMAIN,
    branchIOKey: process.env.BRANCH_IO_KEY,
    villDomain: process.env.VILL_DYNAMIC_LINK_SHORT_LINK_DOMAIN,
    villDeepLinkDomain: process.env.VILL_DYNAMIC_LINK_DEEP_LINK_DOMAIN,
}));
