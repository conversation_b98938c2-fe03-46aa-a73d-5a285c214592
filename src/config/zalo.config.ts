export default (): Record<string, any> => ({
    zaloPayAppid: process.env.ZALOPAY_APPID,
    zaloPayKey1: process.env.ZALOPAY_KEY1,
    zaloPayKey2: process.env.ZALOPAY_KEY2,
    zaloPayEndpoint: process.env.ZALOPAY_ENDPOINT,
    zaloPayCallbackUrl: process.env.ZALOPAY_CALLBACK_URL,
    zaloPayRedirectUrl: process.env.ZALOPAY_REDIRECT_URL,
    vietqrAppId: process.env.ZALOPAY_VIET_QR_APPID,
    internationalCardAppId: process.env.ZALOPAY_INTERNATIONAL_CARD_APPID,
    domesticCardAppId: process.env.ZALOPAY_DOMESTIC_CARD_APPID,
    zaloWalletAppId: process.env.ZALOPAY_WALLET_APPID,
    applePayAppId: process.env.ZALOPAY_APPLE_PAY_APPID,

    vietQrPrivateKey: process.env.ZALOPAY_VIET_QR_KEY1,
    vietQrPublicKey: process.env.ZALOPAY_VIET_QR_KEY2,
    internationalCardPrivateKey: process.env.ZALOPAY_INTERNATIONAL_CARD_KEY1,
    internationalCardPublicKey: process.env.ZALOPAY_INTERNATIONAL_CARD_KEY2,
    domesticCardPrivateKey: process.env.ZALOPAY_DOMESTIC_CARD_KEY1,
    domesticCardPublicKey: process.env.ZALOPAY_DOMESTIC_CARD_KEY2,
    zaloWalletPrivateKey: process.env.ZALOPAY_WALLET_KEY1,
    zaloWalletPublicKey: process.env.ZALOPAY_WALLET_KEY2,
    applePayPrivateKey: process.env.ZALOPAY_APPLE_PAY_KEY1,
    applePayPublicKey: process.env.ZALOPAY_APPLE_PAY_KEY2,
});
