import { registerAs } from '@nestjs/config';
import { LOKI_CONFIG } from 'src/winston-loki/winston-loki.constant';

export interface ILokiConfig {
    host: string;
    username: string;
    password: string;
}

export default registerAs(
    LOKI_CONFIG,
    (): ILokiConfig => ({
        host: process.env.LOKI_HOST,
        username: process.env.LOKI_USERNAME,
        password: process.env.LOKI_PASSWORD,
    }),
);
