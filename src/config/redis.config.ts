export default (): Record<string, any> => ({
    redisHost: process.env.REDIS_HOST,
    redisPassword: process.env.REDIS_PASSWORD,
    redisPort: process.env.REDIS_PORT,
    redisDB: process.env.REDIS_DB,
    redisSearchDB: +process.env.REDIS_SEARCH_DB,
    redisSearchHost: process.env.REDIS_SEARCH_HOST,
    redisSearchPort: process.env.REDIS_SEARCH_PORT,
    redisSearchPassword: process.env.REDIS_SEARCH_PASSWORD,
});
