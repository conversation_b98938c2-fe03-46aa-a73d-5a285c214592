import { registerAs } from '@nestjs/config';
import { Logger } from '@nestjs/common';

function parseRSAKeyFromRawEnvValue(rawEnvValue: string): string {
    if (!rawEnvValue) {
        return '';
    }
    try {
        const jsonData: string[] = JSON.parse(rawEnvValue);
        return jsonData.join('\n');
    } catch (error) {
        Logger.error(
            `Error parsing RSA key: ${error.message} | stack: ${error.stack} | args: ${rawEnvValue}`,
            'NPAY_CONFIG',
        );
        return '';
    }
}

export default registerAs('NPAY_CONFIG', () => {
    const privateKey = parseRSAKeyFromRawEnvValue(process.env.NPAY_PRIVATE_KEY);
    const publicKey = parseRSAKeyFromRawEnvValue(process.env.NPAY_PUBLIC_KEY);
    return {
        merchantKey: process.env.NPAY_MERCHANT_KEY,
        secretKey: process.env.NPAY_SECRET_KEY,
        domain: process.env.NPAY_DOMAIN,
        privateKey,
        publicKey,
    };
});
