import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';
import { ERestaurantAdPlatform } from 'src/models/restaurantAd/dto/restaurantAd.dto';

@Entity('ads_seller_management')
export class AdSellerManagement {
    constructor(partial: Partial<AdSellerManagement>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    ad_id: number;

    @Column()
    seller_id: number;

    @Column()
    price: number;

    @Column()
    commission: number;

    @Column()
    platform: ERestaurantAdPlatform;

    // @OneToOne(() => User)
    // @JoinColumn({
    //     name: 'seller_id',
    //     referencedColumnName: 'id',
    // })
    user?: User;
}
