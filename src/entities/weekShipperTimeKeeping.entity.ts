import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, PrimaryColumn } from 'typeorm';
import { Driver } from './driver.entity';

@Entity('weekly_shipper_timekeepings')
export class WeekShipperTimeKeeping {
    constructor(partial: Partial<WeekShipperTimeKeeping>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn({
        type: 'int',
        width: 11,
        nullable: false,
    })
    driver_id: number;

    @PrimaryColumn({
        type: 'int',
        width: 2,
        nullable: false,
    })
    week: number;

    @PrimaryColumn({
        type: 'year',
        width: 4,
        nullable: false,
    })
    year: number;

    @Column({
        type: 'date',
        nullable: false,
    })
    from_date: string;

    @Column({
        type: 'date',
        nullable: false,
    })
    to_date: string;

    @Column({
        type: 'int',
        default: 0,
        nullable: false,
    })
    actual_received_bonus_percentage: number;

    @Column({
        type: 'int',
        default: 0,
        nullable: false,
    })
    total_bonus: number;

    @Column({
        type: 'float',
        default: 0,
        nullable: false,
    })
    actual_bonus: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_mon_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_tue_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_wed_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_thu_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_fri_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_sat_active: number;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_sun_active: number;

    @Column({
        type: 'int',
        width: 11,
    })
    trans_id: number;

    @Column({
        type: 'int',
        default: null,
    })
    disciplinary_action_id: number;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;
}
