import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('user_orders_rating')
export class UserOrdersRating {
    constructor(partial: Partial<UserOrdersRating>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    user_id: number;

    @Column()
    province_id: number;

    @Column()
    total_orders: number;

    @Column()
    total_orders_bombed: number;

    @Column()
    total_orders_completed: number;

    @Column()
    total_orders_cancelled: number;

    @Column()
    date_of_stats: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
