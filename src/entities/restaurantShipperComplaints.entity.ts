import {
    <PERSON><PERSON><PERSON>,
    CreateDateC<PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order } from './order.entity';
import { User } from './user.entity';

@Entity('restaurant_shipper_complaints')
export class RestaurantShipperComplaint {
    constructor(orderId: number, userId: number, description: string) {
        this.order_id = orderId;
        this.description = description;
        this.user_id = userId;
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    description: string;

    @Column()
    order_id: number;

    @Column()
    user_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order?: Order;

    // @OneToOne(() => User)
    // @JoinColumn({
    //     name: 'user_id',
    //     referencedColumnName: 'id',
    // })
    user?: User;
}
