import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { TaxOrder } from './taxOrder.entity';

@Entity('tax_revenue')
export class TaxRevenue {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'timestamp', nullable: true })
    from_date: string;

    @Column({ type: 'timestamp', nullable: true })
    to_date: string;

    @Column({ type: 'int', nullable: true })
    cod_revenue: number;

    @Column({ type: 'int', nullable: true })
    online_revenue: number;

    @Column({ type: 'int', nullable: true })
    cod_tax_percent: number;

    @Column({ type: 'int', nullable: true })
    cod_tax_revenue: number;

    @Column({ type: 'int', nullable: true })
    actual_cod_tax: number;

    @Column({ type: 'int', nullable: true })
    total_restaurants: number;

    @Column({ type: 'json', nullable: true })
    excluded_restaurant_ids: number[];

    @Column()
    status: ETaxRevenueStatus;

    @Column()
    min_order: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => TaxOrder, (taxOrder) => taxOrder.taxRevenue)
    taxOrders: TaxOrder[];

    calculateCodTaxRevenue() {
        this.cod_tax_revenue = (this.cod_revenue * this.cod_tax_percent) / 100;
    }
}

export enum ETaxRevenueStatus {
    pending = 'pending',
    success = 'success',
    failed = 'failed',
}
