import { TinyInt } from 'src/common/constants';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { OrderType } from './order.entity';
import { IDriverTimeKeepingBonusConfig } from 'src/models/appSetting/models/DriverWeekTimeKeepingSetting.model';
import { EDriverWalletProvider } from './driver.entity';
@Entity('app_settings')
export class AppSetting {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    key: string;

    @Column()
    value: string;

    @Column()
    api: number;

    @Column({ type: 'int', default: null })
    province_id: number;
}

export enum EAppSettingKey {
    ENABLE_APP_ORDER = 'enable_order_app',
    ORDER_POINT = 'order_point',
    MOMO_FEE = 'MOMO_free',
    ZALOPAY_FEE = 'ZaloPay_fee',
    FREESHIP_3KM = 'freeship_3km',
    FEESHIP_LESS_THAN_OR_EQUAL_2KM = 'fee_2km',
    FEESHIP_PER_1KM = 'fee_1km',
    FEESHIP_RAINNY = 'default_tax',
    SERVICE_FOOD_FEE = 'field_custom_1',
    ENABLE_NEWBIE_COUPON = 'field_custom_2',
    APP_NOTI_CONTENT = 'app_custom_content',
    FOOD_CATALOG_TITLE_1 = 'food_catalog_title_1',
    FOOD_CATALOG_TITLE_2 = 'food_catalog_title_2',
    FOOD_CATALOG_TITLE_3 = 'food_catalog_title_3',
    FOOD_CATALOG_TITLE_4 = 'food_catalog_title_4',
    FOOD_CATALOG_TITLE_5 = 'food_catalog_title_5',
    MOTOBIKE_STANDARD = 'motobike_standard',
    MOTOBIKE_PREMIUM = 'motobike_premium',
    CARRIER_SERVICE_FEE = 'carrier_service_fee',
    CARRIER_FEE_PER_KM = 'carrier_fee_per_km',
    VILLFOOD_SETTING_FEES = 'VILLFOOD_SETTING_FEES',
    VILLBIKE_SETTING_FEES = 'VILLBIKE_SETTING_FEES',
    VILLEXPRESS_SETTING_FEES = 'VILLEXPRESS_SETTING_FEES',
    WORK_TIME = 'WORK_TIME',
    ENABLE_AUTO_ORDER_ASSIGNED_SERVICE = 'auto_order',
    ORDER_LIFECYCLE_MAX_COUNT = 'shipper_max_retry',
    ORDER_ASSIGNED_MAX_COUNT = 'shipper_max_order',
    AUTO_ORDER_ASSIGNED_SETTING = 'auto_order_assigned_settings',
    TRADE_DISCOUNT_BONUS = 'trade_discount_bonus',
    HOLIDAY_ORDER_BONUS = 'holiday_order_bonus_conf',
    RAISE_RACCOON_PROGRAM = 'raise_raccoon_program',
    WEEK_SHIPPER_TIMEKEEPING_CONF = 'week_shipper_timekeeping_conf',
    WEEK_SHIPPER_TIMEKEEPING_CONF_V2 = 'week_shipper_timekeeping_conf_v2',
    SYSTEM_MAINTENANCE = 'system_maintenance',
    TIPS_CONF = 'tips_conf',
    FOOD_CATALOG_ALERT = 'food_catalog_alert',
    GOOGLE_MAPS_KEY = 'google_maps_key',
    GOOGLE_MAPS_KEY_2 = 'google_maps_key2',
    GOOGLE_MAPS_KEY_3 = 'google_maps_key3',
    MAPS_BOX_KEY = 'mapbox_api_key',
    VIETMAP_API_KEY = 'vietmap_api_key',
    VIETMAP_API_DOMAIN = 'vietmap_api_domain',
    DISTANCE_CALC_TOOL_TYPE = 'distance_calc_tool_type',
    DISTANCE_CALC_TOOL_TYPE_V2 = 'distance_calc_tool_type_v2',
    GEOCODING_TOOL_TYPE = 'geocoding_tool_type',
    GEOCODING_TOOL_TYPE_V2 = 'geocoding_tool_type_v2',
    APP_VERSION = 'app_version',
    ENABLE_VERSION = 'enable_version',
    LARGE_ORDER_BONUS_CONF = 'large_order_bonus_conf',
    HOTLINE = 'hotline',
    SUPPORT_PHONE = 'support_phone',
    LONG_DISTANCE_ORDER_BONUS_CONF = 'long_distance_order_bonus_conf',
    COLLECTION_AUTO_ACTIVE_CONF = 'collection_auto_active_conf',
    DRIVER_APP_VERSION = 'driver_app_version',
    CLIENT_APP_VERSION = 'CLIENT_APP_VERSION',
    COLLECTION_BROADCAST_NOTI = 'collection_broadcast_noti',
    TRADE_DISCOUNT_DEFAULT = 'trade_discount_default',
    DRIVER_WALLET_CONF = 'driver_wallet_conf',
    SMS_OTP_PARTNER = 'sms_otp_partner',
    PRIORITY_TIMES_ORDER_BONUS_CONF = 'priority_times_order_bonus_conf',
    SHIFT_WORK = 'shift_work',
    HEREMAP_API_KEY = 'heremap_api_key',
    AUTO_SUGGEST_TOOL = 'auto_suggest_tool',
    AUTO_SUGGEST_TOOL_V2 = 'auto_suggest_tool_v2',
    API_KEY_MAP4D = 'api_key_map4d',
    API_KEY_MAP4D_V2 = 'api_key_map4d_v2',
    IS_REMOVE_TRACKING_PATH = 'is_remove_tracking_path',
    RESTAURANT_CODE_CONF = 'restaurant_code_conf',
    RESTAURANT_CODE_COUNTER = 'restaurant_code_counter',
    GROUP_ORDER_FEATURE_CONF = 'group_order_feature_conf',
    DRIVER_MAX_DEVICE_LOGIN = 'driver_max_device_login',
    FEED_ENABLED = 'feed_enabled',
    VILLCAR_SETTING_FEES = 'VILLCAR_SETTING_FEE',
    DRIVER_TRADE_DISCOUNT_PERCENT = 'driver_trade_discount_percent',
    MAX_TRANSPORT_ORDER_SETTING = 'max_transport_order',
    CLIENT_MAP_CONF = 'client_map_conf',
    DRIVER_ORDER_NOTIFICATION_CONF = 'driver_order_notification_conf',

    VILL_FOOD_SETTING_FEES_V2 = 'VILL_FOOD_SETTING_FEES_V2',
    VILL_BIKE_SETTING_FEES_V2 = 'VILL_BIKE_SETTING_FEES_V2',
    VILL_EXPRESS_SETTING_FEES_V2 = 'VILL_EXPRESS_SETTING_FEES_V2',
    FOOD_DESCRIPTION_BY_CHATGPT = 'FOOD_DESCRIPTION_BY_CHATGPT',
    WORK_TIMES = 'WORK_TIMES',
    CLIENT_FOOD_DESCRIPTION_BY_CHATGPT = 'CLIENT_FOOD_DESCRIPTION_BY_CHATGPT',
    MERCHANT_APP_ALERT = 'MERCHANT_APP_ALERT',
    DRIVER_TRADE_DISCOUNT_SETTING = 'DRIVER_TRADE_DISCOUNT_SETTING',

    order_point_template = 'order_point_template',
    RESTAURANT_TRADE_DISCOUNT_DEFAULT = 'RESTAURANT_TRADE_DISCOUNT_DEFAULT',
    DRIVER_HOLIDAY_BONUS = 'DRIVER_HOLIDAY_BONUS',
    DRIVER_WALLET_WORK_TIMES = 'DRIVER_WALLET_WORK_TIMES',
    DRIVER_BONUS_TIME = 'DRIVER_BONUS_TIME',
    SOLD_OUT_UNTIL = 'SOLD_OUT_UNTIL',
    DRIVER_WALLET_PROVIDER = 'DRIVER_WALLET_PROVIDER',
    RESTAURANT_TAX_SETTING = 'RESTAURANT_TAX_SETTING',
    DRIVER_RANKING_SETTING = 'DRIVER_RANKING_SETTING',
    USER_ORDER_INVOICE_CONFIG = 'user_order_invoice_config',
    VIETMAP_CONFIG = 'vietmap_config',
    CLIENT_ADDRESS_CONFIG = 'client_address_config',
}

export interface IAppSetting {
    [EAppSettingKey.TRADE_DISCOUNT_BONUS]: ITradeDiscountBonus;
    [EAppSettingKey.HOLIDAY_ORDER_BONUS]: IHolidayBonus;
    [EAppSettingKey.ORDER_POINT]: IShipperPoint;
    [EAppSettingKey.VILLFOOD_SETTING_FEES]: IVillfoodSettingFee;
    [EAppSettingKey.VILLBIKE_SETTING_FEES]: IVillBikeSettingFee;
    [EAppSettingKey.VILLEXPRESS_SETTING_FEES]: IVillExpressSettingFee;
    [EAppSettingKey.VILLCAR_SETTING_FEES]: IVillCarSettingFee;
    [EAppSettingKey.WORK_TIME]: IWorkTime;
    [EAppSettingKey.AUTO_ORDER_ASSIGNED_SETTING]: IAutoAssignedOrder;
    [EAppSettingKey.RAISE_RACCOON_PROGRAM]: IRaiseRaccoonProgram;
    [EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF]: IWeekShipperTimeKeeping;
    [EAppSettingKey.SYSTEM_MAINTENANCE]: ISystemMaintenance;
    [EAppSettingKey.TIPS_CONF]: ITipsConf;
    [EAppSettingKey.FOOD_CATALOG_ALERT]: IFoodCatalogAlert;
    [EAppSettingKey.LARGE_ORDER_BONUS_CONF]: ILargeOrderBonus;
    [EAppSettingKey.LONG_DISTANCE_ORDER_BONUS_CONF]: ILongDistanceOrderBonusConf;
    [EAppSettingKey.DRIVER_APP_VERSION]: IDriverAppVersion;
    [EAppSettingKey.DRIVER_WALLET_CONF]: IDriverWalletConfig;
    [EAppSettingKey.PRIORITY_TIMES_ORDER_BONUS_CONF]: IPriorityTimeOrderBonusConf;
    [EAppSettingKey.SHIFT_WORK]: IShiftWork;
    [EAppSettingKey.RESTAURANT_CODE_CONF]: IRestaurantCodeConfig;
    [EAppSettingKey.GROUP_ORDER_FEATURE_CONF]: IGroupOrderFeatureConfiguration;
    [EAppSettingKey.DRIVER_TRADE_DISCOUNT_PERCENT]: number;
    [EAppSettingKey.DRIVER_WALLET_PROVIDER]: IDriverWalletProvider[];
    [EAppSettingKey.USER_ORDER_INVOICE_CONFIG]: IUserOrderInvoiceConfig;
    [EAppSettingKey.VIETMAP_CONFIG]: IVietmapConfig;
    [EAppSettingKey.CLIENT_ADDRESS_CONFIG]: IClientAddressConfig;
    [key: string]: string | number | Record<string, any>;
}

export interface ISystemMaintenance {
    enable: boolean;
    message: string;
}

export interface ITipsConf {
    enable: boolean;
}
export interface ITradeDiscountBonus {
    enable: boolean;
    bonus_percentage: number;
    minimum_required_sub_orders: number;
}

export interface IHolidayBonus {
    enable: boolean;
    value: number;
    con_min_subtotal: number;
    // con_order_types: OrderType[];
}

export interface IVillfoodSettingFee {
    DEFAULT_LT_3KM_DELIVERY_FEE: number;
    GT_3KM_DELIVERY_FEE: number;
    DEFAULT_SUB_ORDER_DELIVERY_FEE: number;
    SERVICE_FEE: number;
    SUBORDER_EXTENDED_DISTANCE_FEE_PER_KM: number;
    MAX_SUBORDER_EXTRA_DISTANCE: number;
    SERVICE_FEE_CONF: {
        SERVICE_FEE: number;
        CONDITION: {
            COND_LABEL: 'LT' | 'GTE' | 'LTE' | 'EQUAL';
            TOTAL_PRICE: number;
        };
    }[];
    FEE_AUTO_UPDATE_CONF: {
        IS_ENABLE: boolean;
        TIME_1: string;
        DEFAULT_1: number;
        VAL_1: number;
        TIME_2: string;
        DEFAULT_2: number;
        VAL_2: number;
    };
}
export interface IVillBikeSettingFee {
    LT_3KM_STANDARD_LEVEL_DELIVERY_FEE: number;
    LT_3KM_PREMIUM_LEVEL_DELIVERY_FEE: number;
    GT_3KM_STANDARD_LEVEL_DELIVERY_FEE: number;
    GT_3KM_PREMIUM_LEVEL_DELIVERY_FEE: number;
    SERVICE_FEE: number;
}

export interface IVillExpressSettingFee {
    DEFAULT_LT_3KM_DELIVERY_FEE: number;
    GT_3KM_DELIVERY_FEE: number;
    GTE_6KM_DELIVERY_FEE: number;
    SERVICE_FEE: number;
    FEE_AUTO_UPDATE_CONF: {
        IS_ENABLE: boolean;
        TIME_1: string;
        DEFAULT_1: number;
        VAL_1: number;
        TIME_2: string;
        DEFAULT_2: number;
        VAL_2: number;
    };
}

export interface IVillCarDeliveryFees {
    distanceFrom: number;
    fee: number;
}

export interface IVillCARDefaultFees {
    distanceTo: number;
    fee: number;
}

export interface IVillCarTypeFee {
    id: number;
    code: string;
    defaultFee: IVillCARDefaultFees;
    deliveryFees: IVillCarDeliveryFees[];
}

export interface IVillCarSettingFee {
    CAR_TYPE_LIST: IVillCarTypeFee[];
    SERVICE_FEE: number;
    VAT_PERCENT: number;
    PERSONAL_INCOME_TAX_PERCENT: number;
    SURCHARGE_VAT_PERCENT: number;
}

export interface IWorkTime {
    OPEN: number;
    CLOSE: number;
}

export interface IShipperPoint {
    food1: string;
    food2: string;
    express: string;
    bike: string;
    bikePersonalTaxPercent: number;
    carPersonalTaxPercent: number;
    exchange: {
        [key: string]: string;
    };
    carExchange: {
        [key: string]: string;
    };
}

export interface IWeekShipperTimeKeeping {
    daily_minimum_required_orders: number;
    daily_minimum_required_car_orders: number;
    enable: boolean;
    levels: {
        [key: string]: number;
    };
}
export interface IWeekShipperTimeKeepingV2 {
    daily_minimum_required_orders: number;
    daily_minimum_required_car_orders: number;
    enable: boolean;
    bonus_configs: IDriverTimeKeepingBonusConfig[];
}
export interface IAutoAssignedOrder {
    orders_assigned_max_count: number;
    order_lifecycle_max_count: number;
    turn_on: number;
    priority_assignment_by_rank_enable: boolean;
    priority_assignment_by_last_order_done_and_rank_enable: boolean;
    auto_cancel_order_enable: boolean;
    distance_radius: number;
    max_order_expired_seconds: number;
    max_auto_assigned_order_expired_seconds: number;
    max_manually_select_orders_count: number;
    manual_selected_order_enabled: boolean;
    canceling_order_confirmation_enable: boolean;
    canceling_order_confirmation_minutes: number;
    enabled_first_job_assignment_priority_by_rank: boolean;
    enabled_job_rejectiion_discipline: boolean;
    max_bonus_orders_count: number;
}

export interface IRaiseRaccoonProgram {
    enable: number;
    view_link_url: string;
    maximum_coupons: string;
    maximum_coupons_client_can_take: number;
    minimum_required_sub_total_order_price: number;
}

export interface IFoodCatalogAlert {
    message: string;
    enable: boolean;
}

export interface ILargeOrderBonusLevel {
    min_order_price: number;
    value: number;
    monthly_reward_configs: {
        total_order: number;
        reward: number;
    };
}
export interface ILargeOrderBonus {
    enable: boolean;
    // bonus_percentage: number;
    // minimum_required_sub_orders: number;
    // bonus_amount: number;
    exchange: ILargeOrderBonusLevel[];
}

export interface ILongDistanceOrderBonusConf {
    enable: boolean;
    min_distance: number;
    bonus_per_km: number;
    order_type: OrderType;
}

interface IAppVersion {
    version: string;
    build: number;
    isForceUpdate: boolean;
    link: string;
}
export interface IDriverAppVersion {
    android: IAppVersion;
    ios: IAppVersion;
}

export interface IDriverWalletTransactionFeeValue {
    value: number;
    enabled: boolean;
}

export interface IDriverWalletNinePayWalletPayoutFee extends IDriverWalletTransactionFeeValue {
    npay_withdraw_full_on_set_default: TinyInt;
}

export interface IDriverWalletFeeConfig {
    transaction_types: Record<string, IDriverWalletTransactionFeeValue | IDriverWalletNinePayWalletPayoutFee>;
    default_value: number;
}
export interface IDriverWalletConfig {
    fee: IDriverWalletFeeConfig;
}

export interface IPriorityTimeOrderBonusConf {
    enable: boolean;
    priority_times: number;
    con_min_order_subtotal: number;
    con_restaurant_long_preparing_time: TinyInt;
    con_min_distance: number;
}

export interface IShiftWork {
    enable: boolean;
}

export interface IRestaurantCodeConfig {
    prefix: string;
    suffixLength: number;
}

export interface IGroupOrderFeatureConfiguration {
    enable: boolean; // bật/tắt
    max_item_per_member: number; // số lượng món tối đa mỗi thành viên có thể đặt
    max_member: number; // số lượng thành viên tham gia group tối đa
}

export interface IMerchantAppAlert {
    message: string;
    enable: TinyInt;
}

export interface IDriverWalletProvider {
    provider: EDriverWalletProvider;
    warning_text: string;
    // balance amount must be warning if balance is lower than this amount
    warning_threshold: number;
    // balance amount must be critical if balance is lower than this amount. Example: 100000
    critical_threshold: number;
    enabled: boolean;
    link_popup_config?: IDriverWalletProviderPopupConfig;
}

export interface IDriverWalletProviderPopupConfig {
    enabled: boolean;
    title: string;
    description: string;
    primary_button_text: string;
    secondary_button_text: string;
    dismissible: boolean;
    display_interval: number;
}

export interface IUserOrderInvoiceConfig {
    enable: TinyInt;
    enable_company_profile_management: TinyInt;
    enable_invoice_option_at_checkout: TinyInt;
    checkout_invoice_description: string;
    profile_invoice_description: string;
    split_invoice: TinyInt;
    delivery_vat_percent: number;
    service_vat_percent: number;
    surcharge_vat_percent: number;
}

export interface IVietmapConfig {
    primary_address_display_key: string;
    secondary_address_display_key: string;
    api_domain: string;
    api_key: string;
}

export interface IClientAddressConfig {
    primary_address_display_key: string;
    secondary_address_display_key: string;
}
