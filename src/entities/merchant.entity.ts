import { ApiProperty } from '@nestjs/swagger';
import * as bcrypt from 'bcrypt';
import { classToPlain, Exclude } from 'class-transformer';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { MerchantHasRole } from './merchantHasRole.entity';
import { MerchantIdCard } from './merchant-id-card.entity';
import { MerchantBusinessType } from './merchantBusinessType.entity';
import { CompanyProfile } from './CompanyProfile.entity';
import { MerchantContract } from './merchantContract.entity';
import { TinyInt } from 'src/common/constants';

@Entity('merchants')
export class Merchant {
    constructor(partial: Partial<Merchant>) {
        Object.assign(this, partial);
    }
    @ApiProperty({
        type: 'integer',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty({
        type: 'string',
        example: '<PERSON>uyễn Văn A',
        description: 'Full name of merchant',
    })
    @Column({ type: 'varchar', length: 255, nullable: false })
    real_name: string;

    @ApiProperty({
        type: 'string',
        example: '<EMAIL>',
        description: 'Email of merchant',
    })
    @Column({ type: 'varchar', length: 255, nullable: true, unique: true })
    email: string;

    @Exclude({ toPlainOnly: true })
    @Column({ type: 'varchar', length: 255, nullable: false })
    password: string;

    @ApiProperty({
        type: 'string',
        example: '0339810001',
        description: 'Phone number of merchant',
    })
    @Column({ type: 'varchar', length: 10, nullable: true, unique: true })
    phone: string;

    @ApiProperty({
        type: 'string',
        example: 's3 link',
        description: 'Avatar of merchant',
    })
    @Column({ type: 'varchar', length: 1000, nullable: false })
    photo: string;

    @ApiProperty({
        type: 'integer',
        example: 1,
        description: 'Id of user',
    })
    @Column({ type: 'integer', unsigned: true, width: 11, nullable: true })
    user_id: number;

    @Column({ nullable: false })
    business_type: number;

    @Column()
    email_verified: TinyInt;

    @ManyToOne(() => MerchantBusinessType, (merchantBusinessType) => merchantBusinessType.id)
    @JoinColumn({ name: 'business_type' })
    merchantBusinessType?: MerchantBusinessType;

    @OneToOne(() => CompanyProfile)
    @JoinColumn({ name: 'id', referencedColumnName: 'merchant_id' })
    companyProfile?: CompanyProfile;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => MerchantHasRole, (merchantHasRole) => merchantHasRole.merchant)
    @JoinColumn({ name: 'merchant_id', referencedColumnName: 'id' })
    merchantHasRoles: MerchantHasRole[];

    @OneToMany(() => MerchantContract, (merchantContract) => merchantContract.merchant)
    @JoinColumn({ name: 'merchant_id', referencedColumnName: 'id' })
    contracts: MerchantContract[];

    @ApiProperty()
    @DeleteDateColumn()
    deleted_at: Date;

    @OneToOne(() => MerchantIdCard)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'merchant_id',
    })
    idCard?: MerchantIdCard;

    toJSON() {
        return classToPlain(this);
    }

    async passwordMatches(password: string): Promise<boolean> {
        return bcrypt.compareSync(password, this.password);
    }

    async setPassword(newPassword: string) {
        this.password = await bcrypt.hash(newPassword, 10);
        console.log(this.password);
    }
}
