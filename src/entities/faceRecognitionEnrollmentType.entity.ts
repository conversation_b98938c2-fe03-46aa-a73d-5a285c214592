import { TinyInt } from 'src/common/constants';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum EFaceRecognitionEnrollmentTypeCode {
    work_time = 'work_time',
    start_working = 'start_working',
}

export interface ITimeRange {
    is_active: TinyInt;
    start_time: string;
    end_time: string;
    max_verification: number;
    max_driver_count: number;
}

@Entity('face_recognition_enrollment_types')
export class FaceRecognitionEnrollmentType {
    @PrimaryGeneratedColumn({
        type: 'int',
    })
    id: number;

    @Column({
        type: 'varchar',
        length: 100,
        nullable: true,
        default: null,
    })
    name: string;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.TRUE,
    })
    is_active: TinyInt;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    code: EFaceRecognitionEnrollmentTypeCode;

    @Column({
        type: 'int',
        default: null,
    })
    max_attempt_count: number;

    @Column({
        type: 'int',
        default: null,
    })
    max_duration_in_minutes: number;

    @Column({
        type: 'int',
        default: 0,
        unsigned: true,
        nullable: true,
    })
    daily_request_count: number;

    @Column({
        type: 'int',
    })
    min_req_duration_in_minutes: number;

    @Column({
        type: 'int',
    })
    max_concurrent_requests_count: number;

    @Column({
        type: 'int',
    })
    max_requests_count_in_day: number; // vietnam timezone

    @Column({
        type: 'simple-json',
    })
    time_ranges: ITimeRange[];

    @Column({
        type: 'tinyint',
    })
    eligible_random: TinyInt;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: true,
    })
    max_driver_count: number;
}
