import { ApiProperty } from '@nestjs/swagger';
import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export enum EVehicleType {
    car = 'car',
    bike = 'bike',
}

export enum EVehicleTypeCode {
    motorbike = 'motorbike',
    four_seats = 'four_seats',
    seven_seats = 'seven_seats',
}

@Entity('vehicle_types')
export class VehicleType {
    @ApiProperty({
        description: 'id of vehicle type',
        required: true,
        type: Number,
    })
    @PrimaryColumn({
        type: 'int',
        nullable: false,
        unsigned: true,
    })
    id: number;

    @ApiProperty({
        description: 'name of vehicle type',
        required: true,
        type: String,
    })
    @Column({
        type: 'varchar',
        length: 60,
        nullable: false,
    })
    name: string;

    @ApiProperty({
        description: 'name of vehicle type',
        required: true,
        enum: EVehicleType,
    })
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    type: EVehicleType;

    @ApiProperty({
        description: 'code of vehicle type',
        required: true,
        type: String,
        uniqueItems: true,
    })
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EVehicleTypeCode;

    @ApiProperty({
        description: 'number of seats',
        required: false,
        type: Number,
        nullable: true,
    })
    @Column({
        type: 'int',
        unsigned: true,
        nullable: true,
        default: null,
    })
    seats: number;

    @ApiProperty({
        description: 'Is active',
        required: false,
        type: TinyInt,
        nullable: true,
    })
    @Column({
        type: 'tinyint',
        default: TinyInt.YES,
    })
    is_active: TinyInt;

    @ApiProperty({
        description: 'warning text',
        required: false,
        type: String,
        nullable: true,
    })
    @Column({
        type: 'varchar',
        length: 1000,
        nullable: true,
        default: null,
    })
    warning_text: string;

    @ApiProperty({
        description: 'created at',
        required: false,
        type: String,
        nullable: true,
    })
    @CreateDateColumn()
    created_at: string;

    @ApiProperty({
        description: 'updated at',
        required: false,
        type: String,
        nullable: true,
    })
    @UpdateDateColumn()
    updated_at: string;
}
