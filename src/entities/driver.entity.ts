import { TinyInt } from 'src/common/constants';
import { DriverJobSetting } from 'src/entities/driverJobSetting.entity';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { DriverIdCard } from './driverIdCard.entity';
import { DriverTaxInformation } from './driverTaxInformation.entity';
import { DriverVehicle } from './driverVehicle.entity';
import { Province } from './province.entity';
import { ShiftWork } from './shiftWork.entity';
import { ShipperRanking } from './shipperRankings.entity';
import { User } from './user.entity';
import { Wallet } from './wallet.entity';
import { DriverNpayWalletAccountLinking } from './npayWalletAccountLinking.entity';
import { DriverContactAddress } from './driverContactAddress.entity';
import { DriverScore } from './driverScore.entity';

@Entity('drivers')
export class Driver {
    constructor(partial: Partial<Driver>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    user_id: number;

    @Column()
    wallet_id: number;

    @Column({ type: 'float', default: 0 })
    delivery_fee = 0;

    @Column({ type: 'float', default: 0 })
    total_orders = 0;

    @Column({ type: 'float', default: 0 })
    earning = 0;

    @Column({ type: 'tinyint', default: 0 })
    available = true;

    @Column({
        type: 'tinyint',
        width: 1,
    })
    is_online = 0;

    @Column({
        type: 'tinyint',
        width: 1,
    })
    is_active = 0;

    @Column()
    lat: string;

    @Column()
    lng: string;

    @Column({
        type: 'tinyint',
        width: 1,
    })
    empty_order_slots = 0;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
        unique: true,
    })
    license_plate: string;

    @Column()
    month_total_successful_orders: number;

    @Column()
    month_rating: number;

    @Column()
    month_total_canceled_orders: number;

    @Column()
    month_total_reviews: number;

    @Column({ type: 'int', width: 10, default: 0 })
    week_total_successful_orders: number;

    @Column({ type: 'double', width: 3, default: 0, precision: 2 })
    week_rating: number;

    @Column({ type: 'int', width: 10, default: 0 })
    week_total_canceled_orders: number;

    @Column({ type: 'int', width: 10, default: 0 })
    week_total_reviews: number;

    @Column()
    rating: number;

    @Column()
    ranking_id: number;

    @Column()
    last_online_at: Date;

    @Column({ type: 'varchar', width: 50, unique: true })
    onepay_user_id: string;

    @Column({ type: 'varchar', width: 50, unique: true })
    onepay_reference_id: string;

    @Column()
    last_order_done_at: Date;

    @Column()
    province_id: number;

    @Column()
    code: string;

    @Column({ type: 'tinyint', default: 0 })
    tax_declared: TinyInt;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
    })
    wallet_provider: EDriverWalletProvider;

    // @Column({ type: 'varchar', width: 50, unique: true })
    // personal_tax_code: string;

    // @Column()
    // address: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user: User;

    @OneToOne(() => Wallet)
    @JoinColumn({
        name: 'wallet_id',
        referencedColumnName: 'id',
    })
    wallet?: Wallet;

    @ManyToOne(() => ShipperRanking)
    @JoinColumn({
        name: 'ranking_id',
        referencedColumnName: 'id',
    })
    ranking?: ShipperRanking;

    @OneToOne(() => DriverIdCard)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'driver_id',
    })
    idCard?: DriverIdCard;

    @OneToOne(() => DriverJobSetting)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'driver_id',
    })
    jobSetting?: DriverJobSetting;

    @ManyToMany(() => ShiftWork)
    @JoinTable({
        name: 'driver_shift_works',
        joinColumn: { name: 'driver_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'shift_work_id', referencedColumnName: 'id' },
    })
    shiftWorks: ShiftWork[];

    @OneToOne(() => Province)
    @JoinColumn({ name: 'province_id', referencedColumnName: 'id' })
    province: Province;

    @OneToOne(() => DriverTaxInformation, {
        createForeignKeyConstraints: false,
    })
    @JoinColumn({ name: 'id', referencedColumnName: 'driver_id' })
    taxInformation: DriverTaxInformation;

    @OneToMany(() => DriverVehicle, (driverVehicle) => driverVehicle.driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    vehicles?: DriverVehicle[];

    @OneToOne(() => DriverNpayWalletAccountLinking)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'driver_id',
    })
    nPayWalletLinking: DriverNpayWalletAccountLinking;

    @OneToOne(() => DriverContactAddress, {
        createForeignKeyConstraints: false,
    })
    @JoinColumn({ name: 'id', referencedColumnName: 'driver_id' })
    contactAddress: DriverContactAddress;

    @OneToMany(() => DriverScore, (driverScore) => driverScore.driver)
    driver_scores: DriverScore[];
}

export enum EDriverWalletProvider {
    NPAY_WALLET = '9pay_wallet',
    VILL_WALLET = 'vill_wallet',
}
