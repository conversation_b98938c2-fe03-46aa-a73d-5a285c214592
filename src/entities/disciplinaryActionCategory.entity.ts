import { TinyInt } from 'src/common/constants';
import { decimalColumnTransformer } from 'src/common/typorm/decimalColumnTransformer';
import { Column, Entity, JoinColumn, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DisciplinaryAction } from './disciplinaryAction.entity';

export const enum EJobDisciplinaryActionCategoryCode {
    FACE_RECOGNITION = 'FACE_RECOGNITION',
    ORDER_REJECTION = 'ORDER_REJECTION',
    WALLET_BALANCE = 'WALLET_BALANCE', // subtract wallet balance
}

@Entity('disciplinary_action_categories')
export class DisciplinaryActionCategory {
    @PrimaryGeneratedColumn({
        type: 'int',
    })
    id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EJobDisciplinaryActionCategoryCode;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
        default: null,
    })
    name: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    description: string;

    @Column({
        type: 'tinyint',
        nullable: true,
        default: 1,
    })
    is_active: TinyInt;

    @Column({
        type: 'decimal',
        precision: 10,
        scale: 3,
        unsigned: true,
        transformer: decimalColumnTransformer,
    })
    personal_tax_percentage: number; // percentage of personal tax to be deducted from the driver's wallet balance

    @OneToMany(() => DisciplinaryAction, (action) => action.category)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'category_id',
    })
    types: DisciplinaryAction[];
}
