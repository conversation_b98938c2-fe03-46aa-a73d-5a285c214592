
export enum EScoringSourceType {
    ORDER = 'Order',
    DRIVER_ORDER_PLAN_REGISTRATION = 'DriverOrderPlanWeeklyRegistration',
    SHIPPER_REVIEW = 'ShipperReview',
    MANUAL = 'Manual',
    REFUND = 'Refund',
}

/**
 * Enum for standardized field names used in scoring conditions
 */
export enum EScoringFieldName {
    // Order fields
    DISTANCE = 'distance',
    TOTAL_PRICE = 'total_price',
    TYPE = 'type',
    ORDER_TYPE = 'order_type',
    PROVINCE_ID = 'province_id',
    DELIVERY_FEE = 'delivery_fee',
    SUB_TOTAL_PRICE = 'sub_total_price',
    ORDER_STATUS_ID = 'order_status_id',
    TYPES_OF_GOOD_ID = 'types_of_good_id',
    VEHICLE_TYPE_ID = 'vehicle_type_id',
    LONG_DISTANCE_BONUS = 'long_distance_bonus',
    HOLIDAY_BONUS = 'holiday_bonus',
    LARGE_ORDER_BONUS = 'large_order_bonus',
    DELIVERY_HOUR = 'delivery_hour',
    DELIVERY_DAY_OF_WEEK = 'delivery_day_of_week',
    DELIVERY_TIME = 'delivery_time',
    ORDER_DATE = 'order_date',
    SURCHARGE = 'surcharge',
    PAYMENT_METHOD_CODE = 'payment_method_code',

    // Driver Mission fields
    MISSION_CODE = 'code',
    MISSION_IS_ACTIVE = 'is_active',
    MISSION_TITLE = 'title',
    MISSION_REQUIRE_RANK_IDS = 'require_rank_ids',
    MISSION_CONFIG = 'config',

    // Driver Order Plan fields
    PLAN_NAME = 'name',
    PLAN_MIN_ORDERS = 'min_orders',
    PLAN_ACTIVE = 'active',
    PLAN_REQUIRED_RANK_IDS = 'required_rank_ids',
    PLAN_IMAGE = 'image',
    PLAN_DESC = 'desc',

    // Driver Order Plan Weekly Registration fields
    REGISTRATION_ID = 'id',
    REGISTRATION_DRIVER_ID = 'driver_id',
    REGISTRATION_PLAN_ID = 'plan_id',
    REGISTRATION_BONUS_AMOUNT = 'bonus_amount',
    REGISTRATION_WEEK_START_DATE = 'week_start_date',
    REGISTRATION_WEEK_END_DATE = 'week_end_date',
    REGISTRATION_TOTAL_PLANNED_ORDERS = 'total_planned_orders',
    REGISTRATION_COMPLETION_PERCENTAGE = 'completion_rate',
    REGISTRATION_IS_COMPLETED_DAILY = 'is_completed',
    REGISTRATION_REWARD_ID = 'reward_id',
    REGISTRATION_TIME = 'registration_time',
    REGISTRATION_DEADLINE = 'deadline',

    // Driver Order Plan Bonus fields
    BONUS_RANK_ID = 'rank_id',
    BONUS_PLAN_ID = 'plan_id',
    BONUS_AMOUNT = 'bonus_amount',
    BONUS_IMAGE = 'image',
    BONUS_DESC = 'desc',

    // Shipper Review fields
    REVIEW_RATE = 'rate',
}

export enum EConditionOperator {
    GREATER_THAN = '>',
    GREATER_THAN_OR_EQUAL = '>=',
    LESS_THAN = '<',
    LESS_THAN_OR_EQUAL = '<=',
    EQUAL = '==',
    IN = 'in',
    NOT_IN = 'not_in',
}

export enum ELogicalOperator {
    AND = 'AND',
    OR = 'OR',
}

export enum ECalculationType {
    FIXED = 'FIXED',
    TIERED = 'TIERED',
}

export enum EPointType {
    ADD = 'ADD',
    SUBTRACT = 'SUBTRACT',
    REFUND = 'REFUND',
}

/**
 * Enum for scoring history status
 * Manages the lifecycle of scoring history records
 */
export enum EScoringHistoryStatus {
    SUCCESS = 'SUCCESS', // Bản ghi gốc, điểm đang được tính vào bảng xếp hạng
    REFUNDED = 'REFUNDED', // Điểm đã được đảo ngược (đã chạy refund)
    DELETED = 'DELETED', // Xoá mềm (khi admin "delete history" nhưng muốn giữ log)
}

/**
 * Interface for tiered calculation configuration
 */
export interface ITierConfig {
    min_value: number;
    max_value: number;
    points: number;
}

/**
 * Interface for detailed calculation breakdown (for internal use only)
 */
export interface ICalculationDetails {
    rule_type: string;
    conditions_met: any[];
    calculation_method: string;
    input_values: any;
    formula_used: string;
    result: number;
    timestamp: string;
}

/**
 * Interface for scoring result
 * calculation_details is now always a plain text description
 */
export interface IScoringResult {
    rule_id: number;
    rule_name: string;
    source_type: EScoringSourceType;
    points_awarded: number;
    calculation_details: string;
    order_id: number;
    driver_id: number;
}

/**
 * Interface for driver order plan registration scoring context
 */
export interface IDriverOrderPlanRegistrationContext {
    registration_id: number;
    driver_id: number;
    plan_id: number;
    bonus_amount: number;
    week_start_date: string;
    week_end_date: string;
    total_planned_orders: number;
    registration_time: string;
    deadline: string;
    // Related plan data
    plan_name: string;
    plan_min_orders: number;
    plan_active: boolean;
    plan_required_rank_ids: number[];
    // Driver rank info
    driver_rank_id: number;
}

/**
 * Interface for driver order plan completion scoring context
 */
export interface IDriverOrderPlanCompletionContext {
    registration_id: number;
    driver_id: number;
    plan_id: number;
    bonus_amount: number;
    week_start_date: string;
    week_end_date: string;
    total_planned_orders: number;
    total_completed_orders: number;
    is_completed: boolean;
    reward_id: number;
    completion_rate: number; // calculated field
    // Related plan data
    plan_name: string;
    plan_min_orders: number;
    plan_active: boolean;
    plan_required_rank_ids: number[];
    // Driver rank info
    driver_rank_id: number;
}

/**
 * Interface for shipper review scoring context
 */
export interface IShipperReviewContext {
    review_id: number;
    rate: number;
    review: string;
    user_id: number;
    order_id: number;
    shipper_id: number;
    created_at: string;
    updated_at: string;
    // Related order data
    order_total_price: number;
    order_distance: number;
    order_province_id: number;
}
