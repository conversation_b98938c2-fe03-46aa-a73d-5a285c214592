import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('uploads')
export class PublicFile {
    constructor(partial: Partial<PublicFile>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    uuid: string;

    @Column()
    url: string;

    @Column()
    key: string;

    @Column()
    type: EUploadType = EUploadType.IMAGE;

    @Column()
    origin_url: string;

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @CreateDateColumn()
    created_at: Date = new Date();
}

export enum EUploadType {
    AVATAR = 'AVATAR',
    FOOD = 'FOOD',
    COLLECTION = 'COLLECTION',
    AD_BANNER = 'AD_BANNER',
    CATEGORY = 'CATEGORY',
    RESTAURANT = 'RESTAURANT',
    EXTRA = 'EXTRA',
    FAQ = 'FAQ',
    IMAGE = 'IMAGE',
    GALLERY = 'GALLERY',
    FCMImages = 'FCMImages',
    FRAME = 'FRAME',
    COUPON = 'COUPON',
    VIDEO = 'VIDEO',
    FACE_RECOGNITION = 'FACE_RECOGNITION',
    SOCIAL = 'SOCIAL',
}
