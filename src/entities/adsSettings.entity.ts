import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('ads_settings')
export class AdsSettings {
    constructor(partial: Partial<AdsSettings>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_use_buyer_id_number: number;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_auto_create_ads_invoice_by_admin: number;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_auto_create_ads_invoice_by_merchant: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
