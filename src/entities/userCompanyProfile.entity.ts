import { ApiProperty } from '@nestjs/swagger';
import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { TinyInt } from 'src/common/constants';

@Entity('user_company_profiles')
export class UserCompanyProfile {
    constructor(partial: Partial<UserCompanyProfile>) {
        Object.assign(this, {
            is_default: TinyInt.NO,
            is_active: TinyInt.YES,
            created_at: new Date(),
            updated_at: new Date(),
            ...partial,
        });
    }

    @ApiProperty({
        description: 'Company profile ID',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty({
        description: 'User ID',
        example: 1,
    })
    @Column()
    user_id: number;

    @ApiProperty({
        description: 'Company name',
        example: 'Công ty TNHH ABC',
        maxLength: 255,
    })
    @Column({ length: 255 })
    company_name: string;

    @ApiProperty({
        description: 'Company tax code',
        example: '1234567890',
        maxLength: 50,
    })
    @Column({ length: 50 })
    company_tax_code: string;

    @ApiProperty({
        description: 'Company address',
        example: '123 Đường ABC, Quận 1, TP.HCM',
    })
    @Column('text')
    company_address: string;

    @ApiProperty({
        description: 'Email address for invoice delivery',
        example: '<EMAIL>',
        required: false,
        maxLength: 255,
    })
    @Column({ length: 255, nullable: true })
    invoice_email: string;

    @ApiProperty({
        description: 'Name of the buyer/purchaser for invoice purposes',
        example: 'Nguyễn Văn A',
        required: false,
        maxLength: 255,
    })
    @Column({ length: 255, nullable: true })
    buyer_name: string;

    @ApiProperty({
        description: 'Is default company profile',
        example: 1,
        enum: TinyInt,
    })
    @Column({ type: 'tinyint', default: 0 })
    is_default: TinyInt;

    @ApiProperty({
        description: 'Is active company profile',
        example: 1,
        enum: TinyInt,
    })
    @Column({ type: 'tinyint', default: 1 })
    is_active: TinyInt;

    @ApiProperty({
        description: 'Created at',
    })
    @CreateDateColumn()
    created_at: Date;

    @ApiProperty({
        description: 'Updated at',
    })
    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'user_id' })
    user?: User;
}
