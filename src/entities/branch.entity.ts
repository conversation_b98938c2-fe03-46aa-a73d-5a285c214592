import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Restaurant } from './restaurant.entity';

@Entity('branch')
export class Branch {
    constructor(partial: Partial<Branch>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    description: string;

    @Column()
    image: string;

    @Column()
    api: string;

    @OneToMany(() => Restaurant, (restaurant) => restaurant.branch)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'branch_id',
    })
    restaurants?: Restaurant[];
}
