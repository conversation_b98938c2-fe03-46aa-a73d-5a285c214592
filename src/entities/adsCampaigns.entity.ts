import {
    <PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    Column,
    OneToMany,
    UpdateDateColumn,
    CreateDateColumn,
    ManyToOne,
    JoinColumn,
    OneToOne,
} from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { AdsCampaignItem } from './adsCampaignItem.entity';
import { AdsPayment } from './adsPayment.entity';
import { EAdsCampaignStatus } from 'src/models/restaurantAd/dto/adsCampaign.dto';
import { AdSeller } from './adsSellers.entity';
import { AdsContractInfo } from './adsContractInfo.entity';
import { Invoice } from './invoice.entity';
import { AdsRequestInfo } from './adsRequestInfo.entity';

@Entity('ads_campaigns')
export class AdsCampaign {
    constructor(partial: Partial<AdsCampaign>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
    })
    name: string;

    @Column()
    description: string;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: true,
        comment: 'Tổng tiền chưa thuế của campaign (đơn vị VND thực tế) - ví dụ: 100000 = 100,000 VND',
    })
    sub_total_price: number;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: true,
        default: 0,
        comment: 'Giảm giá (luôn = 0, không áp dụng discount cho ads campaign)',
    })
    discount_price: number;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: true,
        comment: 'Tổng tiền cuối cùng của campaign (đơn vị VND thực tế) - ví dụ: 100000 = 100,000 VND',
    })
    total_price: number;

    @Column({ type: 'varchar' })
    status: EAdsCampaignStatus;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column({ type: 'int' })
    restaurant_id: number;

    @OneToMany(() => AdsCampaignItem, (item) => item.ads_campaign)
    ads_items: AdsCampaignItem[];

    @OneToMany(() => AdsPayment, (payment) => payment.ads_campaign)
    ads_payments: AdsPayment[];

    @OneToOne(() => AdSeller)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_campaign_id',
    })
    sellerManagement: AdSeller;

    @ManyToOne(() => Restaurant, (restaurant) => restaurant.ads_campaigns)
    @JoinColumn({ name: 'restaurant_id' })
    restaurant: Restaurant;

    @OneToOne(() => AdsContractInfo)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_campaign_id',
    })
    adsContractInfo: AdsContractInfo;

    @OneToOne(() => Invoice)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_campaign_id',
    })
    adsInvoiceInfo: Invoice;

    @OneToOne(() => AdsRequestInfo)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_campaign_id',
    })
    adsRequestInfo: AdsRequestInfo;
}
