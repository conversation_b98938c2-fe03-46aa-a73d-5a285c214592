import { Column, CreateDate<PERSON><PERSON>umn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum Type {
    AGREEMENT = 0,
    CONTRACT = 1,
}

export enum Active {
    INACTIVE = 0,
    ACTIVE = 1,
}

export class DataCoordinate {
    key: string;
    x: number;
    y: number;
    page: number;
}

export class NameCoordinate {
    x: number;
    y: number;
    page: number;
}

export class SignatureParameter {
    x: number;
    y: number;
    page: number;
    width: number;
    height: number;
}

@Entity('driver_agreement_type')
export class DriverAgreementType {
    constructor(partial: Partial<DriverAgreementType>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 1000,
    })
    name: string;

    @Column({
        type: 'varchar',
        length: 1000,
        nullable: true,
    })
    template_url: string;

    @Column({
        type: 'json',
        nullable: true,
    })
    name_coordinate: NameCoordinate;

    @Column({
        type: 'json',
        nullable: true,
    })
    signature_parameters: SignatureParameter;

    /*  @Column({
        type: 'json',
        nullable: true,
    })
    vill_signature_parameters: number[];

    @Column({
        type: 'int',
        nullable: true,
    })
    vill_signature_type: number; */

    @Column({
        type: 'json',
        nullable: true,
    })
    data_coordinates: DataCoordinate[];

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    type: Type;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_active: Active;

    @Column({
        type: 'tinyint',
        nullable: true,
        unsigned: true,
    })
    status: Active;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
