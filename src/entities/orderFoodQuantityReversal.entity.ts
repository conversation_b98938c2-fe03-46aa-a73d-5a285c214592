import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryColumn } from 'typeorm';

@Entity('order_food_quantity_reversal')
export class OrderFoodQuantityReversal {
    constructor(partial: Partial<OrderFoodQuantityReversal>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn()
    order_id: number;

    @PrimaryColumn()
    food_id: number;

    @Column({ type: 'int', unsigned: true, nullable: false })
    quantity: number;

    @CreateDateColumn()
    created_at: string;
}
