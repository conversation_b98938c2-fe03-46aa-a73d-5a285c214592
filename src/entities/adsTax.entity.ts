import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    Index,
} from 'typeorm';

@Entity('ads_tax')
@Index(['ads_campaign_item_id'], { unique: true })
export class AdsTax {
    constructor(partial: Partial<AdsTax>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
        comment: 'ID của ads campaign item',
    })
    ads_campaign_item_id: number;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: false,
        comment: 'Tổng tiền từ ads campaign item (đơn vị VND thực tế) - ví dụ: 100000 = 100,000 VND',
    })
    total_item: number;

    @Column({
        type: 'decimal',
        precision: 5,
        scale: 2,
        nullable: false,
        comment: 'Tỷ lệ thuế VAT (%) - ví dụ: 10.00',
    })
    vat_rate: number;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: false,
        comment: 'Thành tiền chưa thuế (đơn vị VND thực tế) - ví dụ: 91000 = 91,000 VND',
    })
    total_order: number;

    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: false,
        comment: 'Tiền thuế GTGT (đơn vị VND thực tế) - ví dụ: 9000 = 9,000 VND',
    })
    vat_amount: number;

    @CreateDateColumn({ type: 'timestamp' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp' })
    updated_at: Date;
}
