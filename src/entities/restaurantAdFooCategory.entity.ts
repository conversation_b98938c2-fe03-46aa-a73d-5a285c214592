import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { RestaurantAd } from './restaurantAd.entity';
import { RestaurantNewsFeed } from './restaurantNewsFeed.entity';
import { Category } from './category.entity';
import { AdsCampaignItem } from './adsCampaignItem.entity';

@Index('uq_restaurant_ad_food_category', ['restaurant_ad_id', 'category_id'], {
    unique: true,
})
@Entity('restaurant_ad_food_categories')
export class RestaurantAdFoodCategory {
    constructor(partial: Partial<RestaurantAdFoodCategory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    category_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    restaurant_ad_id: number;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_from: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_to: Date;

    @ManyToOne(() => RestaurantAd, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'restaurant_ad_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ad_food_categories_food_restaurant_ads',
    })
    restaurantAd: RestaurantAd;

    @ManyToOne(() => Category, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ad_food_category',
    })
    category: Category;

    // @Column()
    // ads_item_id: number;

    // @ManyToOne(() => AdsCampaignItem, {
    //     // createForeignKeyConstraints: true,
    // })
    // @JoinColumn({
    //     name: 'ads_item_id',
    //     referencedColumnName: 'id',
    //     // foreignKeyConstraintName: 'fk_restaurant_ad_food_category_ads_item',
    // })
    // adsItem: AdsCampaignItem;
}
