import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order } from './order.entity';
import { EPaymentMethodCode, PaymentMethod } from './paymentMethod.entity';

@Entity('payments')
export class Payment {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    price: number;

    @Column()
    description: string;

    @Column()
    status: EPaymentStatus;

    @Column()
    method: EPaymentMethodCode;

    @Column()
    note: string;

    @Column()
    payment_method_id: number;

    @Column()
    refund_request_id: string;

    @Column()
    refund_id: string;

    @Column({ type: 'tinyint', default: 0 })
    save_card_info: number;

    @Column({ type: 'json' })
    qr_info: VietQRMetadata;

    @Column({ type: 'json' })
    refund_metadata: Record<string, any>;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => Order, (order) => order.payment)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'payment_id',
    })
    orders: Order[];

    @OneToOne(() => PaymentMethod)
    @JoinColumn({
        name: 'payment_method_id',
        referencedColumnName: 'id',
    })
    paymentMethod: PaymentMethod;
}

export interface VietQRMetadata {
    bankCode: string;
    bankNumber: string;
    amount?: number;
    addInfo?: string;
    accountName: string;
    bankName?: string;
}

export interface IZaloPayMetaData {
    order_id: number;
    user_id: number;
    refund_fee: 0;
    amount: number;
    initiated_by: 'system';
    failure_reason: null | string;
    transaction_id: string;
    refund_id: string;
    province_id: string;
    provinceId: string;
    payment_id: null | number;
    message: string;
}

export enum EPaymentStatus {
    NOT_PAID = 'Not Paid',
    PAID = 'Paid',
    WAITING_FOR_CLIENT = 'Waiting for Client',
    REFUNDED = 'Refunded',
    WAITING_FOR_REFUND = 'Waiting for Refund',
}

// export enum PaymentMethod {
//     'CASH_ON_DELIVERY' = 'Cash on Delivery',
//     'MOMO' = 'MOMO',
//     'ZALO_PAY' = 'ZaloPay',
// }

export const PaymentMethodPaidAfterOrderDelivered = [EPaymentMethodCode.CASH_ON_DELIVERY, EPaymentMethodCode.QR_CODE];
