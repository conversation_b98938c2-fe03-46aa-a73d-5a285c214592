import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('ad_categories')
export class AdCategory {
    constructor(partial: Partial<AdCategory>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EAdCategoryCode;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    name: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    desc: string;

    // @Column({
    //     type: 'varchar',
    //     length: 255,
    //     nullable: true,
    // })
    // title: string;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_active: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    subtitle: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    image: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    thumbnail: string;

    @Column({ type: 'integer', nullable: true })
    frame_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EAdCategoryCode {
    'category_ad_page' = 'category_ad_page',
    'news_feed_ad_page' = 'news_feed_ad_page',
    'search_ad_page' = 'search_ad_page',
    'search_page' = 'search_page',
}
