import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { CartItem } from './cartItem.entity';
import { DeliveryAddress } from './deliveryAddress.entity';
import { MotobikeType, OrderType } from './order.entity';
import { EPaymentMethodCode } from './paymentMethod.entity';
import { Restaurant } from './restaurant.entity';
import { User } from './user.entity';
@Entity('carts')
export class Cart {
    constructor(userId: number, province: number, note: string, type: OrderType, restaurantId?: number) {
        this.user_id = userId;
        this.province = province;
        this.note = note;
        this.tax = 0;
        this.type = type;
        this.restaurant_id = restaurantId || null;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        unsigned: true,
    })
    user_id: number;

    @Column({
        unsigned: true,
        nullable: true,
    })
    address_id: number = null;

    @Column({
        type: 'double',
        nullable: true,
    })
    distance: number;

    @Column({
        type: 'double',
    })
    delivery_fee = 0;

    @Column({
        type: 'double',
    })
    service_fee = 0;

    @Column({
        type: 'double',
        nullable: true,
    })
    sub_total_price: number;

    @Column({
        type: 'double',
    })
    total_price = 0;

    @Column({
        type: 'double',
    })
    discount = 0;

    @Column({
        nullable: true,
    })
    coupon: string;

    @Column({
        unsigned: true,
        nullable: true,
    })
    restaurant_id: number;

    @Column()
    province: number;

    @Column()
    note: string;

    @Column({
        type: 'double',
        nullable: true,
    })
    surcharge: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    trade_discount: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    tax: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    payment_fee: number;

    @Column({
        type: 'varchar',
        nullable: true,
        length: 50,
    })
    payment_method: EPaymentMethodCode;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    origin: string;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    destination: string;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    type: OrderType;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    motobike_type: MotobikeType;

    @Column({
        type: 'text',
        nullable: true,
    })
    destination_address: string;

    @Column({
        type: 'double',
        nullable: true,
    })
    motobike_standard_fee: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    motobike_premium_fee: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => CartItem, (cartItem) => cartItem.cart, { cascade: true })
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'cart_id',
    })
    cart_items?: CartItem[];

    // @ManyToOne(() => User)
    // @JoinColumn({
    //     name: 'user_id',
    //     referencedColumnName: 'id',
    // })
    // user?: User;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => DeliveryAddress)
    @JoinColumn({
        name: 'address_id',
        referencedColumnName: 'id',
    })
    address?: DeliveryAddress;
}
