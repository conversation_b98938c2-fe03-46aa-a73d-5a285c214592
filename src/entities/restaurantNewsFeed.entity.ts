import { Collection } from 'src/entities/collection.entity';
import { TinyInt } from 'src/common/constants';
import { Column, Entity, JoinColumn, JoinTable, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Province } from './province.entity';
import { Restaurant } from './restaurant.entity';

@Entity('restaurant_newsfeeds')
export class RestaurantNewsFeed {
    constructor(partial: Partial<RestaurantNewsFeed>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'text' })
    image: string;

    @Column({ type: 'varchar', length: 255 })
    title: string;

    @Column({ type: 'varchar', length: 255 })
    sub_title: string;

    @Column({ type: 'text' })
    desc: string;

    @Column({ type: 'simple-json' })
    query_params: Record<string, any>;

    @Column({ type: 'text' })
    api: string;

    list: Restaurant[] = [];

    @Column({ type: 'tinyint', default: TinyInt.YES })
    is_active: TinyInt;

    @Column({ type: 'tinyint', default: TinyInt.YES })
    open_24h: TinyInt;

    @Column({ type: 'time', default: null })
    open_time: string;

    @Column({ type: 'time', default: null })
    close_time: string;

    @Column({ type: 'timestamp', default: null })
    from_date: Date;

    @Column({ type: 'timestamp', default: null })
    to_date: Date;

    @Column()
    restaurant_id: number;

    @Column()
    ordinal_number: number;

    @Column({ type: 'varchar', length: 30, default: null })
    screen_pos: ERestaurantNewsFeedScreen | null;

    @Column()
    collection_id: number;

    @Column()
    type: ERestaurantNewsFeedType;

    @Column({ type: 'json' })
    extra_data: IRestaurantNewsFeedExtraData;

    @Column()
    data_type: EDataType;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    only_in_the_newsfeed: TinyInt;

    // @Column({ type: 'json' })
    // custom_style: ICustomStyle;

    @ManyToMany(() => Province)
    @JoinTable({
        name: 'newsfeeds_provinces',
        joinColumn: {
            name: 'newsfeed_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'province_id',
            referencedColumnName: 'id',
        },
    })
    provinces: Province[];

    @ManyToOne(() => Collection, (collection) => collection.newsFeeds)
    @JoinColumn({
        name: 'collection_id',
        referencedColumnName: 'id',
    })
    collection: Collection;
}

export enum ERestaurantNewsFeedScreen {
    HOME = 'HOME',
    // FOOD = 'FOOD',
    COLLECTION = 'COLLECTION',
}

export enum ERestaurantNewsFeedType {
    NORMAL = 'NORMAL',
    FLASH_SALE = 'FLASH_SALE',
    CUSTOM = 'CUSTOM',
}

export interface IRestaurantNewsFeedExtraData {
    flash_sale_active_from?: Date;
    flash_sale_active_to?: Date;
    custom_active_from?: Date;
    custom_active_to?: Date;
    custom_style?: ICustomStyle;
    sub_background_color?: IBackgroundColor;
    thumbnail?: string;
    display_type?: EDisplayType;
    custom_type?: ECustomType;
    first_banner_id?: number;
    second_banner_id?: number;
}

export enum ECustomType {
    NORMAL = 'NORMAL',
    LEFT = 'LEFT',
    SCROLL = 'SCROLL',
    TOP = 'TOP',
    GRADIENT_HORIZONTAL = 'GRADIENT_HORIZONTAL',
    GRADIENT_VERTICAL = 'GRADIENT_VERTICAL',
    MANSORY = 'MANSORY',
}

export enum EDataType {
    RESTAURANT = 'RESTAURANT',
    FOOD = 'FOOD',
    NEWSFEED = 'NEWSFEED',
    BRANCH = 'BRANCH',
}

export interface IBackgroundColor {
    start: string;
    end: string;
}
export interface ICustomRatio {
    width: number;
    height: number;
}

export enum EDisplayType {
    HORIZONTAL = 'horizontal',
    VERTICAL = 'vertical',
}

export interface ICustomStyle {
    custom_item_ratio: ICustomRatio;
    custom_background: string;
    custom_background_ratio: ICustomRatio;
    display_type?: EDisplayType;
}

export enum EBannerEvent {
    REDIRECT_COLLECTION = 'redirect_collection',
    REDIRECT_RESTAURANT = 'redirect_restaurant',
    REDIRECT_WEBSITE = 'redirect_website',
    HTML_RENDERING = 'html_rendering',
}

export interface IBannerMetadata {
    restaurant_id?: number | null;
    url?: string | null;
    open_app_deeplink_android?: string | null;
    open_app_deeplink_ios?: string | null;
    collection_code?: string | null;
    html_content?: string | null;
}
