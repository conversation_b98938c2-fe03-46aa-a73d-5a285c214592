import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON>ty,
    <PERSON>in<PERSON><PERSON>um<PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { OptionGroup } from './optionGroup.entity';
import { TinyInt } from 'src/common/constants';

export enum EPostStatus {
    pending = 1,
    approved = 2,
    rejected = 3,
}

@Entity('options')
export class Option {
    constructor(partial: Partial<Option>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    option_group_id: number;

    @Column()
    price: number;

    @Column({
        type: 'int',
        nullable: true,
    })
    new_price: number;

    @Column()
    sold_out: boolean;

    @Column()
    ordinal_numbers: number;

    @Column({
        type: 'int',
        nullable: true,
    })
    max_qty: number;

    @Column({
        type: 'int',
        nullable: true,
        default: EPostStatus.pending,
    })
    status: EPostStatus;

    @Column({
        type: 'int',
        nullable: true,
        default: EPostStatus.approved,
    })
    price_approval: EPostStatus;

    @Column({
        type: 'tinyint',
        default: 0,
        nullable: false,
    })
    has_many: TinyInt;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => OptionGroup)
    @JoinColumn({
        name: 'option_group_id',
        referencedColumnName: 'id',
    })
    optionGroup: OptionGroup;
}
