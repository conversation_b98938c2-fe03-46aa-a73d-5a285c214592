import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Food } from './food.entity';
import { OptionGroup } from './optionGroup.entity';

@Entity('food_option_groups')
export class FoodOptionGroup {
    constructor(partial: Partial<FoodOptionGroup>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn()
    food_id: number;

    @PrimaryColumn()
    option_group_id: number;

    @Column()
    ordinal_numbers: number;

    @ManyToOne(() => Food, (food) => food.foodOptionGroups)
    @JoinColumn({
        name: 'food_id',
        referencedColumnName: 'id',
    })
    food: Food;

    @ManyToOne(() => OptionGroup, (optionGroup) => optionGroup.foodOptionGroups)
    @JoinColumn({
        name: 'option_group_id',
        referencedColumnName: 'id',
    })
    optionGroup: OptionGroup;
}
