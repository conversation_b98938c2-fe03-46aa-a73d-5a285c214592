import {
    BeforeUpdate,
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';
import { DeliveryAddressLabel } from './delivery-address-label.entity';

@Entity('delivery_addresses')
export class DeliveryAddress {
    constructor(partial: Partial<DeliveryAddress> = {}) {
        Object.assign(this, partial);
        this.created_at = new Date();
        this.updated_at = new Date();
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    description: string;

    @Column()
    address: string;

    @Column()
    latitude: string;

    @Column()
    longitude: string;

    @Column()
    is_default: number;

    @Column()
    user_id: number;

    @Column()
    province_id: number;

    @Column()
    label_code: number;

    @Column()
    driver_note: string;

    @Column()
    address_details: string;

    label: DeliveryAddressLabel;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;

    @BeforeUpdate()
    updateDate() {
        this.updated_at = new Date();
    }
}

export interface IDeliveryExtraData {
    address_details: string;
    note: string;
}
