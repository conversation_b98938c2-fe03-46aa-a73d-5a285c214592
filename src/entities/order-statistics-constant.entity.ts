import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn, VersionColumn } from 'typeorm';

export enum EOrderStatisticsConstantType {
    TARGET = 1,
    TENANT_GROUPS = 2,
}

@Entity('order_statistics_constants')
export class OrderStatisticsConstant {
    constructor(partial: Partial<OrderStatisticsConstant>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'enum',
        enum: EOrderStatisticsConstantType,
        default: EOrderStatisticsConstantType.TARGET,
    })
    type: EOrderStatisticsConstantType;

    @Column()
    province_id: number;

    @Column()
    value: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @VersionColumn()
    version: number;
}
