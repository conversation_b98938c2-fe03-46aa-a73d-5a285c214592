import { ECurrency } from 'src/common/types/currency.enum';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToOne,
    PrimaryColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver, EDriverWalletProvider } from './driver.entity';
import { Order } from './order.entity';

export interface IOrderDriverExpenseVatDetails {
    delivery_vat: number;
    surcharge_vat: number;
}

export enum EOrderDriverExpenseStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
}

// export enum EOrderDriverExpenseReversalStatus {
//     PENDING = 'pending',
//     SUCCESS = 'success',
//     FAILED = 'failed',
// }

@Entity()
export class OrderDriverExpense {
    constructor(partial: Partial<OrderDriverExpense>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn({
        type: 'int',
        unsigned: true,
    })
    order_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    income: number; // thu nhap (income) cua tai xe.

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    trade_discount: number; // chiết khấu thương mại (trade discount) của tài xế.

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    vat: number; // thue VAT (value added tax)

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    income_after_trade_discount: number; // thu nhap sau chiết khấu thương mại (income after trade discount)

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    income_after_vat: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    personal_tax: number; // thue TNCN (personal income tax from income_after_vat)

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    net_income: number; // thu nhap sau thue TNCN (net income after personal income tax)

    /**
     * @description: so tien no cua tai xe (driver) voi cong ty (company)
     *
     */
    @Column({
        type: 'int',
        nullable: false,
    })
    debt_amount: number; // so tien no cua tai xe (driver) voi cong ty (company). if debt_amount > 0, driver need to pay to company. if debt_amount < 0, company need to pay to driver.

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    currency: ECurrency;

    @Column({
        type: 'int',
        unsigned: true,
    })
    delivery_subsidy: number; // phu cap giao hang

    @Column({
        type: 'json',
        nullable: true,
        comment: '',
    })
    vat_details: IOrderDriverExpenseVatDetails;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
    })
    ref_trans_id: string;

    // generated by system
    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
    })
    app_trans_id: string;

    @Column({
        type: 'varchar',
        unsigned: true,
        length: 30,
        nullable: false,
    })
    status: EOrderDriverExpenseStatus;

    @Column({
        type: 'varchar',
        unsigned: true,
        length: 30,
        nullable: false,
    })
    wallet_provider: EDriverWalletProvider;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
    })
    ref_reversal_trans_id: string;

    // generated by system
    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
    })
    app_reversal_trans_id: string;

    @Column({
        type: 'varchar',
        unsigned: true,
        length: 30,
        nullable: false,
    })
    reversal_status: EOrderDriverExpenseStatus;

    @Column({
        type: 'json',
        nullable: true,
        comment: '',
    })
    metadata: Record<string, any>;

    @UpdateDateColumn()
    updated_at: string;

    @CreateDateColumn()
    created_at: string;

    @OneToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order: Order;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;

    setVatDetails(
        order: Order,
        vatPercentage: number,
        surchargeVatPercentage: number,
        tradeDiscountPercentage: number,
    ) {
        let delivery_vat = 0;
        let surcharge_vat = 0;

        if (
            tradeDiscountPercentage &&
            vatPercentage <= 100 &&
            vatPercentage >= 0 &&
            tradeDiscountPercentage <= 100 &&
            tradeDiscountPercentage >= 0
        ) {
            delivery_vat =
                (((order.delivery_fee * (100 - tradeDiscountPercentage)) / (100 + vatPercentage)) * vatPercentage) /
                100;
            delivery_vat = Math.round(delivery_vat * 1000);
        }
        if (surchargeVatPercentage <= 100 && surchargeVatPercentage >= 0 && order.surcharge > 0) {
            surcharge_vat = (order.surcharge / (100 + surchargeVatPercentage)) * surchargeVatPercentage;
            surcharge_vat = Math.round(surcharge_vat * 1000);
        }
        this.vat_details = {
            delivery_vat,
            surcharge_vat,
        };
    }
}
