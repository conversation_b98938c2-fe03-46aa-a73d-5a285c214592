import * as _ from 'lodash';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { ModelHasRole } from './modelHasRole.entity';
import { Permission } from './permission.entity';

@Entity('roles')
export class Role {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    guard_name: string;

    @Column({
        type: 'tinyint',
        transformer: {
            from(value) {
                return Boolean(value);
            },
            to(value) {
                return _.toNumber(value);
            },
        },
    })
    default: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    @ManyToMany(() => Permission)
    @JoinTable({
        name: 'role_has_permissions',
        joinColumn: {
            name: 'role_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'permission_id',
            referencedColumnName: 'id',
        },
    })
    permissions: Permission[];

    @OneToMany(() => ModelHasRole, (modelHasRole) => modelHasRole.role)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'role_id',
    })
    modelHasRoles: ModelHasRole[];
}

export enum ERole {
    client = 'client',
    driver = 'driver',
    manager = 'manager',
    admin = 'admin',
    'TEAM - GDTINH' = 'TEAM - GDTINH',
    'TEAM - LEAD CITY' = 'TEAM - LEAD CITY',
    'TEAM - ADMIN' = 'TEAM - ADMIN',
    'TEAM - MARKETING' = 'TEAM - MARKETING',
    'TEAM - MERCHANT' = 'TEAM - MERCHANT',
    'TEAM - PTTT' = 'TEAM - PTTT',
    'TEAM - QLSP' = 'TEAM - QLSP',
    'TEAM - FINANCE' = 'TEAM - FINANCE',
    'TEAM - SALE' = 'TEAM - SALE',
    'TEAM - CSKH' = 'TEAM - CSKH',
    // assignOrder = 'Nhân viên gán đơn',
    // bussiness = 'Nhân viên kinh doanh',
    // content = 'Nhân Viên Content',
    // shipperManager = 'Quản Lý Shipper',
    // provinceManager = 'Giam Đốc Tỉnh',
    // marketAnalysis = 'Phân Tích Thị Trường',
}
