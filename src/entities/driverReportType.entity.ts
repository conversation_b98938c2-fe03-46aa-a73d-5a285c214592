import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('driver_report_types')
export class DriverReportType {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', length: 255, nullable: false })
    name: string;

    @Column({ type: 'tinyint', width: 1, default: 0 })
    active: 0 | 1;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
