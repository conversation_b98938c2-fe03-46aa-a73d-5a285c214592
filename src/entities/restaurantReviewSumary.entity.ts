import { Entity, PrimaryGeneratedColumn, Column, OneToOne } from 'typeorm';
import { Restaurant } from './restaurant.entity';

@Entity('restaurant_review_sumary')
export class RestaurantReviewSumary {
    constructor(partial: Partial<RestaurantReviewSumary>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    total_reviews_with_image: number;

    @Column()
    total_reviews_with_content: number;

    @Column()
    total_reviews_with_tag: number;

    @Column()
    restaurant_id: number;

    @OneToOne(() => Restaurant, (restaurant) => restaurant.restaurantReviewSumary)
    restaurant: Restaurant;
}
