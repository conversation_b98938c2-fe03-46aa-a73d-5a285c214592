import {
    Column,
    CreateDateColumn,
    Entity,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { RestaurantReview } from './restaurantReview.entity';

@Entity('restaurant_review_replies')
export class RestaurantReviewReply {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review_id: number;

    @Column()
    comment: string;

    @Column()
    is_displayed: boolean;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @OneToOne(() => RestaurantReview, (review) => review.restaurant_reply)
    @JoinColumn({
        name: 'review_id',
        referencedColumnName: 'id',
    })
    review: RestaurantReview;
}
