import {
    Column,
    CreateDateColumn,
    <PERSON><PERSON>ty,
    <PERSON>in<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Restaurant } from './restaurant.entity';

@Entity('restaurant_revenues')
export class RestaurantRevenue {
    constructor(partial: Partial<RestaurantRevenue>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', width: 11, nullable: false })
    restaurant_id: number;

    @Column({ type: 'date', nullable: false })
    from_date: string;

    @Column({ type: 'date', nullable: false })
    to_date: string;

    @Column({ type: 'varchar', nullable: false })
    interval: ERestaurantRevenueInterval;

    @Column()
    total_orders: number;

    @Column()
    total_successful_orders: number;

    @Column()
    total_canceled_orders: number;

    @Column()
    revenue: number;

    @Column()
    trade_discount: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant: Restaurant;
}

export enum ERestaurantRevenueInterval {
    DATE = 'date',
    WEEK = 'week',
    MONTH = 'month',
}
