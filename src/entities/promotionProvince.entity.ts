import { Entity, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, PrimaryColumn } from 'typeorm';
import { Promotion } from './promotion.entity';

@Entity('promotion_provinces')
export class PromotionProvince {
    @PrimaryColumn()
    promotion_id: number;

    @PrimaryColumn()
    province_id: number;

    @ManyToOne(() => Promotion, (promotion) => promotion.provinces)
    @JoinColumn({
        name: 'promotion_id',
        referencedColumnName: 'id',
    })
    promotion?: Promotion;
}
