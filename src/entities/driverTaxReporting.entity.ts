import { TinyInt } from 'src/common/constants';
import { decimalColumnTransformer } from 'src/common/typorm/decimalColumnTransformer';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { TaxReportingDriverPeriod } from './taxReportingDriverPeriod.entity';

export enum EDriverTaxReportStatus {
    NOT_SETTLED = 'not_settled',   // Tax report has not been settled
    // IN_PROCESS = 'in_process',     // Tax report is currently being processed
    SETTLED = 'settled',           // Tax report has been settled
    // ERROR = 'error',               // An error occurred during processing
}

@Entity('driver_tax_reports')
export class DriverTaxReport {
    constructor(partial: Partial<DriverTaxReport>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    period_id: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
    })
    total_income: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
    })
    total_tax: number;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
    })
    note: string;

    /*@Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.NO,
        comment: '0: not settled, 1: settled. means tax has been paid to government. field: debt_tax',
    })
    settled: TinyInt; */

    /* @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
        comment: 'tax paid to government',
    })
    paid_tax: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
    })
    debt_tax: number; */

    @Column({
        type: 'varchar',
        length: 30,
        default: EDriverTaxReportStatus.NOT_SETTLED,
        comment: 'not_settled, in_process, settled, error',
    })
    status: EDriverTaxReportStatus;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => TaxReportingDriverPeriod)
    @JoinColumn({
        name: 'period_id',
        referencedColumnName: 'id',
    })
    period?: TaxReportingDriverPeriod;

    @ManyToOne(() => Driver, (driver) => driver.id)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;
}
