import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Cart } from './cart.entity';
import { CartItemExtra } from './cartItemExtra.entity';
import { CartItemExtraGroupItem } from './cartItemExtraGroupItem.entity';
import { Food } from './food.entity';
@Entity('cart_items')
export class CartItem {
    constructor(foodId: number, price: number, quantity: number, cartId: number, note: string) {
        this.food_id = foodId;
        this.price = price;
        this.quantity = quantity;
        this.cart_id = cartId;
        this.note = note;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        unsigned: true,
    })
    food_id: number;

    @Column({
        nullable: false,
    })
    cart_id: number;

    @Column({
        nullable: false,
    })
    price: number;

    @Column({
        nullable: false,
    })
    quantity: number;

    @Column({
        nullable: true,
    })
    note: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Cart, (cart) => cart.cart_items)
    @JoinColumn({
        name: 'cart_id',
        referencedColumnName: 'id',
    })
    cart?: Cart;

    @OneToMany(() => CartItemExtra, (cartItemExtra) => cartItemExtra.cart_item, { cascade: true })
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'cart_item_id',
    })
    cart_item_extras?: CartItemExtra[];

    @OneToMany(() => CartItemExtraGroupItem, (cartItemExtraGroupItem) => cartItemExtraGroupItem.cart_item, {
        cascade: true,
    })
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'cart_item_id',
    })
    cart_group_extra_items?: CartItemExtraGroupItem[];

    @ManyToOne(() => Food)
    @JoinColumn({
        name: 'food_id',
        referencedColumnName: 'id',
    })
    food?: Food;
}
