import { Column, CreateDate<PERSON>olumn, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { EOrderStatusId } from './orderStatus.entity';
import { ERole } from './role.entity';
import { User } from './user.entity';

@Entity('order_update_histories')
export class OrderUpdateHistory {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    order_id: number;

    @Column()
    user_id: number;

    @Column({ type: 'json' })
    old_data: Record<string, any>;

    @Column({ type: 'json' })
    new_data: Record<string, any>;

    @CreateDateColumn()
    created_at: Date;

    // @ManyToOne(() => User, (user) => user.orderHistory)
    // @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
    user?: User;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
        default: null,
    })
    type: EOrderUpdateType;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
        default: null,
    })
    status_id: EOrderStatusId;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
        default: null,
    })
    curr_latitude: string;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
        default: null,
    })
    curr_longitude: string;

    @Column({
        type: 'varchar',
        length: 600,
        nullable: true,
        default: null,
    })
    note: string;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
        default: null,
    })
    role: ERole;

    // @ManyToOne(() => Order, (restaurant) => restaurant.update_history)
    // @JoinColumn({ name: 'restaurant_id', referencedColumnName: 'id' })
    // restaurant: Restaurant;
}

export enum EOrderUpdateType {
    UPDATE_STATUS = 'UPDATE_STATUS',
}
