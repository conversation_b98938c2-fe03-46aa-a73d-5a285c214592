import { TinyInt } from 'src/common/constants';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinTable,
    ManyToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';

@Entity('shift_works')
export class ShiftWork {
    constructor(partial: Partial<ShiftWork>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', length: 255, default: null })
    name: string;

    @Column({ type: 'text', default: '' })
    desc: string;

    @Column({ type: 'int', width: 1, nullable: false })
    day_of_week: number;

    @Column({
        select: false,
        insert: false,
        transformer: {
            to: null,
            from(value: string) {
                return parseInt(value);
            },
        },
    })
    total_registration: number = 0;

    @Column({
        select: false,
        insert: false,
        transformer: {
            to: null,
            from(value: string) {
                return parseInt(value);
            },
        },
    })
    total_online_driver: number = 0;

    @Column({ type: 'tinyint', default: TinyInt.YES, nullable: false })
    is_active: TinyInt;

    @Column({ type: 'time', nullable: false })
    start_time: string;

    @Column({ type: 'time', nullable: false })
    end_time: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToMany(() => Driver)
    @JoinTable({
        name: 'driver_shift_works',
        inverseJoinColumn: { name: 'driver_id', referencedColumnName: 'id' },
        joinColumn: { name: 'shift_work_id', referencedColumnName: 'id' },
    })
    drivers: Driver[];

    @ManyToMany(() => Driver)
    @JoinTable({
        name: 'driver_shift_works',
        inverseJoinColumn: { name: 'driver_id', referencedColumnName: 'id' },
        joinColumn: { name: 'shift_work_id', referencedColumnName: 'id' },
    })
    offlineDrivers: Driver[];
}
