import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { ReviewTag } from './reviewTag.entity';

@Entity('shipper_reviews_tags')
export class ShipperReviewsTags {
    constructor(partial: Partial<ShipperReviewsTags>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn()
    review_id: number;

    @PrimaryColumn()
    tag_id: number;

    @ManyToOne(() => ReviewTag)
    @JoinColumn({ name: 'tag_id', referencedColumnName: 'id' })
    tags: ReviewTag;
}
