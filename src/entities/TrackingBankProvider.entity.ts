import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from 'typeorm';
import { TrackingBankAccount } from './TrackingBankAccount.entity';

@Entity('tracking_bank_providers')
export class TrackingBankProvider {
    constructor(partial: Partial<TrackingBankProvider>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    bank_name: string;


    @Column()
    icon_url: string;

    @Column({ nullable: false, unique: true })
    bank_code: string;

    @Column({ default: false })
    is_active: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => TrackingBankAccount, (user) => user.tracking_bank_provider)
    
    @JoinColumn({ name: 'id', referencedColumnName: 'tracking_bank_provider_id' })
    tracking_bank_account: TrackingBankAccount;


}


//create sql
    /* CREATE TABLE tracking_bank_providers(
        id            int auto_increment
            primary key,
        bank_name VARCHAR  NULL,
        bank_code VARCHAR  NOT NULL,
        is_active BOOLEAN  NOT NULL,
        created_at TIMESTAMP  NULL,
        updated_at TIMESTAMP  NULL
    ); */