import {
    BeforeU<PERSON><PERSON>,
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { RestaurantBusinessType } from './restaurant-business-types.entity';
import { MerchantIdCard } from './merchant-id-card.entity';

export interface IdCard extends Omit<MerchantIdCard, 'merchant_id' | 'status' | 'rejection_reason' | 'created_at'> {}

@Entity('merchant_registrations')
export class MerchantRegistration {
    constructor(partial: Partial<MerchantRegistration>) {
        this.created_at = new Date().toISOString();
        this.updated_at = new Date().toISOString();
        this.approve_status = EMerchantRegistrationStatus.PENDING;
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', width: 255, nullable: false })
    owner_name: string;

    @Column({ type: 'varchar', width: 255, nullable: false })
    restaurant_name: string;

    @Column({ type: 'varchar', width: 10, nullable: false, unique: true })
    phone: string;

    @Column({ type: 'varchar', width: 255, nullable: false })
    address: string;

    @Column()
    restaurant_business_type: number;

    @Column({ type: 'varchar', width: 255, nullable: false })
    approve_status: EMerchantRegistrationStatus;

    @Column({ type: 'varchar', width: 255, nullable: false })
    email: string;

    @Column({ type: 'json' })
    id_card: IdCard;

    @Column()
    province_id: number;

    @CreateDateColumn({ type: 'varchar', width: 255, nullable: false })
    created_at: string;

    @UpdateDateColumn({ type: 'varchar', width: 255, nullable: false })
    updated_at: string;

    @ManyToOne(() => RestaurantBusinessType)
    @JoinColumn({
        name: 'restaurant_business_type',
        referencedColumnName: 'id',
    })
    restaurantBusinessType: RestaurantBusinessType;

    @BeforeUpdate()
    updateTimestamp() {
        this.updated_at = new Date().toISOString();
    }
}

export enum EMerchantRegistrationStatus {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    IN_PROGRESS = 'IN_PROGRESS',
}
