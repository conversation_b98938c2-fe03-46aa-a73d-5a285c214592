import { <PERSON>um<PERSON>, CreateDate<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';
import { Merchant } from './merchant.entity';

export enum EGender {
    MALE = 1,
    FEMALE = 0,
}

export enum EStatus {
    PENDING = 0,
    APPROVED = 1,
    REJECTED = 2,
}

@Entity('merchant_id_card')
export class MerchantIdCard {
    constructor(partial: Partial<MerchantIdCard>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    merchant_id: number;

    @Column({ unique: true })
    id_card: string;

    @Column({ type: 'date', width: 50 })
    issue_date: string;

    @Column({ type: 'varchar', width: 255 })
    issued_by: string;

    @Column({ type: 'tinyint' })
    sex: EGender;

    @Column({ type: 'varchar', width: 50, nullable: false })
    full_name: string;

    @Column({ type: 'date' })
    date_of_birth: string;

    @Column({ type: 'varchar', width: 50 })
    nationality: string;

    @Column({ type: 'text' })
    place_of_origin: string;

    @Column({ type: 'text' })
    place_of_residence: string;

    @Column({ type: 'date' })
    date_of_expiry: string;

    @Column({ type: 'varchar', width: 1000 })
    front_key_image: string;

    @Column({ type: 'varchar', width: 1000 })
    back_key_image: string;

    @Column({ type: 'tinyint', default: EStatus.PENDING })
    status: EStatus;

    @Column({ type: 'varchar', width: 255, nullable: true })
    rejection_reason: string;

    @CreateDateColumn()
    created_at: Date;

    @OneToOne(() => Merchant, (merchant) => merchant.id)
    @JoinColumn({ name: 'merchant_id' })
    merchant: Merchant;
}
