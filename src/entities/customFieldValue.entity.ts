import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('custom_field_values')
export class CustomFieldValue {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    value: string;

    @Column()
    customizable_type: string;

    @Column()
    customizable_id: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => User, (user) => user.custom_fields)
    @JoinColumn({ name: 'customizable_id' })
    user: User;
}
