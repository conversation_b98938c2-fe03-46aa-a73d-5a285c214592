import { Entity, PrimaryGeneratedColumn, Column, OneToMany, UpdateDateColumn, CreateDateColumn } from 'typeorm';
import { AdsPlanItem } from './adsPlanItem.entity';

@Entity('ads_plans')
export class AdsPlan {
    constructor(partial: Partial<AdsPlan>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
    })
    name: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    description: string;

    @Column({
        default: 0,
        nullable: false,
    })
    is_active: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    sub_total_price: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    discount_price: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    total_price: number;

    @OneToMany(() => AdsPlanItem, (item) => item.ads_plan)
    ads_plan_items: AdsPlanItem[];

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
