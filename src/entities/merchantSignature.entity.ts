import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('merchant_signature')
export class MerchantSignature {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    merchant_id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    contract_id: number;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    status: TinyInt;

    @Column()
    signature_url: string;

    @CreateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    created_at: Date;

    @UpdateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    updated_at: Date;
}
