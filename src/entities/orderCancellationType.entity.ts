import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('order_cancellation_types')
export class OrderCancellationType {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true, length: 30, type: 'varchar' })
    code: EOrderCancellationType;

    @Column({ default: TinyInt.TRUE, type: 'tinyint' })
    active: TinyInt;

    @Column({ length: 255, type: 'varchar', nullable: false })
    name: string;

    @Column({ default: TinyInt.FALSE, type: 'tinyint' })
    internal: TinyInt;

    @UpdateDateColumn()
    updated_at: string;

    @CreateDateColumn()
    created_at: Date;
}

export enum EOrderCancellationType {
    UNABLE_CONTACT = 'UNABLE_CONTACT',
    CUSTOMER_REQUEST_CANCELLATION = 'CUSTOMER_REQUEST_CANCELLATION',
    RESTAURANT_CLOSED = 'RESTAURANT_CLOSED',
    SOLD_OUT_ITEM_OR_CHANGE = 'SOLD_OUT_ITEM_OR_CHANGE',
    CANCELLATION_BY_CUSTOMER = 'CANCELLATION_BY_CUSTOMER',
    OTHER_REASON = 'OTHER_REASON',
    PAYMENT_FAILURE = 'PAYMENT_FAILURE',
    DELIVERY_NOT_FOUND = 'DELIVERY_NOT_FOUND',
    CANCELED_DUE_TO_TIMEOUT = 'CANCELED_DUE_TO_TIMEOUT',
    USER_BOMBED = 'USER_BOMBED',
}

export const OrderCancellationTypeIdMapper: {
    [key in EOrderCancellationType]: number;
} = {
    [EOrderCancellationType.UNABLE_CONTACT]: 1,
    [EOrderCancellationType.CUSTOMER_REQUEST_CANCELLATION]: 2,
    [EOrderCancellationType.RESTAURANT_CLOSED]: 3,
    [EOrderCancellationType.SOLD_OUT_ITEM_OR_CHANGE]: 4,
    [EOrderCancellationType.CANCELLATION_BY_CUSTOMER]: 5,
    [EOrderCancellationType.OTHER_REASON]: 6,
    [EOrderCancellationType.PAYMENT_FAILURE]: 7,
    [EOrderCancellationType.DELIVERY_NOT_FOUND]: 8,
    [EOrderCancellationType.CANCELED_DUE_TO_TIMEOUT]: 11,
    [EOrderCancellationType.USER_BOMBED]: 12,
};
