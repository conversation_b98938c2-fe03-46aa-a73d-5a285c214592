import { <PERSON>umn, CreateDate<PERSON>olumn, <PERSON>tity, <PERSON><PERSON><PERSON>olum<PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Driver } from './driver.entity';
import { ApiProperty } from '@nestjs/swagger';
import { FaceRecognitionEnrollmentType } from './faceRecognitionEnrollmentType.entity';
import { DriverFaceRecognitionEnrollmentLog } from './driverFaceRecognitionEnrollmentLog.entity';

export enum EDriverFaceRecognitionEnrollmentStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
    CANCELLED = 'cancelled',
}

@Entity('driver_face_recognition_enrollment')
export class DriverFaceRecognitionEnrollment {
    constructor(partial: Partial<DriverFaceRecognitionEnrollment>) {
        Object.assign(this, partial);
    }

    @ApiProperty()
    @PrimaryGeneratedColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @ApiProperty()
    @Column({
        type: 'varchar',
        length: 60,
    })
    name: string;

    @ApiProperty()
    @Column({
        type: 'int',
        unsigned: true,
    })
    type_id: number;

    @ApiProperty()
    @Column({
        type: 'int',
        unsigned: true,
    })
    driver_id: number;

    @ApiProperty()
    @Column({
        type: 'int',
        default: 0,
    })
    attempt_count: number;

    @ApiProperty()
    @Column({
        type: 'timestamp',
        nullable: true,
    })
    expired_time: Date;

    @ApiProperty()
    @Column({
        type: 'varchar',
        length: 100,
        nullable: true,
    })
    device_id: string;

    @ApiProperty({
        description: `value: ${Object.values(EDriverFaceRecognitionEnrollmentStatus).join('\n')}`,
    })
    @Column({
        type: 'varchar',
        length: 64,
        default: 'pending',
    })
    status: EDriverFaceRecognitionEnrollmentStatus;

    @ApiProperty({
        description: 'note for failed status or cancelled status',
    })
    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    note: string;

    @ApiProperty()
    @Column({
        type: 'date',
    })
    date: string;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;

    @ManyToOne(() => FaceRecognitionEnrollmentType)
    @JoinColumn({
        name: 'type_id',
        referencedColumnName: 'id',
    })
    type?: FaceRecognitionEnrollmentType;

    @OneToMany(
        () => DriverFaceRecognitionEnrollmentLog,
        (driverFaceRecognitionEnrollmentLog) => driverFaceRecognitionEnrollmentLog.enrollment,
    )
    logs?: DriverFaceRecognitionEnrollmentLog[];
}
