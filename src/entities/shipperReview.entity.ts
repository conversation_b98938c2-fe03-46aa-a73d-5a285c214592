import {
    Column,
    CreateDateColumn,
    Entity,
    <PERSON>inColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order } from './order.entity';
import { User } from './user.entity';
import { ReviewTag } from './reviewTag.entity';

@Entity('shipper_reviews')
export class ShipperReview {
    constructor(review: string, rate: number, userId: number, shipperId: number) {
        this.review = review;
        this.rate = rate;
        this.user_id = userId;
        this.shipper_id = shipperId;
        this.updated_at = new Date();
        this.created_at = new Date();
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review: string;

    @Column()
    rate: number;

    @Column()
    user_id: number;

    @Column()
    order_id: number;

    @Column()
    shipper_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'shipper_id',
        referencedColumnName: 'id',
    })
    shipper?: User;

    @OneToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order: Order;

    @ManyToMany(() => ReviewTag, (reviewTag) => reviewTag.shipperReviews)
    @JoinTable({
        name: 'shipper_reviews_tags',
        joinColumn: {
            name: 'review_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'tag_id',
            referencedColumnName: 'id',
        },
    })
    tags: ReviewTag[];
}
