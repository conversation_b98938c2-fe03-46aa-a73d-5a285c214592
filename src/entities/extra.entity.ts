import {
    Column,
    CreateDate<PERSON>olumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { ExtrasFood } from './extrasFood.entity';
import { FoodOrderExtra } from './foodOrderExtra.entity';

@Entity('extras')
export class Extra {
    constructor() {
        this.updated_at = new Date();
        this.created_at = new Date();
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    image: string;

    @Column()
    food_id: number;

    @Column()
    description: string;

    @Column()
    price: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    // @ManyToMany(() => Food, food => food.extrasFood)
    // @JoinTable({
    //     name: 'extras_food',
    //     joinColumn:{
    //         name: 'extra_id'
    //     },
    //     inverseJoinColumn: {
    //         name: 'food_id'
    //     }
    // })
    // foods?: Food[];

    @OneToMany(() => ExtrasFood, (extrasFood) => extrasFood.extras)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'extra_id',
    })
    extrasFood: ExtrasFood[];

    @OneToMany(() => FoodOrderExtra, (foodOrderExtra) => foodOrderExtra.extra)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'extra_id',
    })
    foodOrderExtras?: FoodOrderExtra[];
}
