import { Column, CreateDateColumn, Entity, JoinColumn, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Restaurant } from './restaurant.entity';

@Entity('restaurant_payment_methods')
export class RestaurantPaymentMethod {
    constructor(partial: Partial<RestaurantPaymentMethod>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    restaurant_id: number;

    @Column()
    payment_method_id: number;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => Restaurant, (restaurant) => restaurant.paymentMethods)
    @JoinColumn({ name: 'restaurant_id' })
    restaurant: Restaurant;

    /*  @ManyToOne(() => PaymentMethod, (paymentMethod) => paymentMethod.id)
    @JoinColumn({ name: 'payment_method_id' })
    paymentMethod: PaymentMethod; */
}
