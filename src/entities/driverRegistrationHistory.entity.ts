import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    Jo<PERSON><PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';
import { DriverRegistration, EDriverRegistrationStatus, EDriverRegistrationReviewStage } from './driverRegistration.entity';
import { User } from './user.entity';

@Entity('driver_registration_history')
export class DriverRegistrationHistory {
    constructor(partial: Partial<DriverRegistrationHistory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @ManyToOne(() => DriverRegistration)
    @JoinColumn({ name: 'driver_registration_id' })
    driver_registration: DriverRegistration;

    @Column()
    driver_registration_id: number;

    @ManyToOne(() => User, { nullable: true })
    @JoinColumn({ name: 'reviewer_id' })
    reviewer: User;

    @Column({ nullable: true })
    reviewer_id: number;

    @Column({ type: 'varchar', width: 255 })
    previous_status: EDriverRegistrationStatus;

    @Column({ type: 'varchar', width: 255 })
    new_status: EDriverRegistrationStatus;

    @Column({ type: 'varchar', nullable: true })
    previous_review_stage: EDriverRegistrationReviewStage | null;

    @Column({ type: 'varchar', nullable: true })
    new_review_stage: EDriverRegistrationReviewStage | null;

    @Column({ type: 'text', nullable: true })
    comment: string;

    @Column({ type: 'text', nullable: true })
    reject_reason: string;

    @CreateDateColumn()
    created_at: Date;
}