import {
    Column,
    CreateDateColumn,
    Entity,
    <PERSON>inColumn,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Faq } from './faq.entity';

@Entity('faq_categories')
export class FaqCategory {
    constructor(name: string) {
        this.name = name;
        this.updated_at = this.created_at = new Date();
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => Faq, (faq) => faq.faqCategory)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'faq_category_id',
    })
    faqs: Faq[];
}
