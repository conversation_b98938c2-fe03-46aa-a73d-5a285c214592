import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    Unique,
} from 'typeorm';

import { DriverOrderPlan } from './driver-order-plan.entity';
import { ShipperRanking } from './shipperRankings.entity';

@Entity('driver_order_plan_bonuses')
@Unique(['rank_id', 'plan_id'])
export class DriverOrderPlanBonus {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false, name: 'rank_id' })
    rank_id: number;

    @Column({ nullable: false, name: 'plan_id' })
    plan_id: number;

    @Column({
        type: 'integer',
        nullable: false,
        name: 'bonus_amount',
    })
    bonus_amount: number;

    @Column({ type: 'varchar', length: 255, nullable: true, name: 'image' })
    image: string | null;

    @Column({ type: 'varchar', length: 255, nullable: true, name: 'desc' })
    desc: string | null;

    @CreateDateColumn({ name: 'created_at' })
    created_at: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: Date;

    @ManyToOne(() => ShipperRanking)
    @JoinColumn({ name: 'rank_id' })
    rank: ShipperRanking;

    @ManyToOne(() => DriverOrderPlan, (plan) => plan.bonuses)
    @JoinColumn({ name: 'plan_id' })
    plan: DriverOrderPlan;
}
