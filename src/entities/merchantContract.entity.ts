import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    InsertEvent,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    AfterInsert,
    DeleteDateColumn,
} from 'typeorm';
import { Merchant } from './merchant.entity';
import { MerchantContractType, RequiredContractData } from './merchantContractType.entity';
import { MerchantSignature } from './merchantSignature.entity';
import { TinyInt } from 'src/common/constants';
import { Province } from './province.entity';

export enum EStatus {
    APPROVED = 4,
    DENIED = 3,
    PENDING = 2,
    NOT_YET_SIGNED = 1,
}

@Entity('merchant_contract')
export class MerchantContract {
    @PrimaryGeneratedColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    merchant_id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    contract_type_id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    restaurant_id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    res_province_id: number;

    @Column()
    source_url: string;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    required_signature: TinyInt;

    @Column({
        type: 'varchar',
        length: 1000,
    })
    code: string;

    @Column({
        type: 'int',
    })
    status: EStatus;

    @Column()
    contract_url: string;

    @Column({
        type: 'int',
        unsigned: true,
    })
    signature_id: number;

    @Column({
        type: 'json',
        default: null,
        nullable: true,
    })
    required_data: RequiredContractData[];

    @Column({
        type: 'varchar',
        length: 1000,
    })
    rejected_reason: string;

    @ManyToOne(() => Merchant, (merchant) => merchant.id)
    @JoinColumn({
        name: 'merchant_id',
        referencedColumnName: 'id',
    })
    merchant: Merchant;

    @ManyToOne(() => MerchantContractType, (merchantContractType) => merchantContractType.id)
    @JoinColumn({
        name: 'contract_type_id',
        referencedColumnName: 'id',
    })
    contract_type: MerchantContractType;

    @ManyToOne(() => Province, (province) => province.id)
    @JoinColumn({
        name: 'res_province_id',
        referencedColumnName: 'id',
    })
    res_province?: Province;

    @ManyToOne(() => MerchantSignature, (merchantSignature) => merchantSignature.id)
    @JoinColumn({
        name: 'signature_id',
        referencedColumnName: 'id',
    })
    signature: MerchantSignature;

    @CreateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    created_at: Date;

    @UpdateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    @AfterInsert()
    afterInsert() {
        this.code = this.code + '-' + (this.id + '').padStart(6, '0');
    }
}
