import {
    Column,
    CreateDate<PERSON><PERSON>umn,
    DeepPartial,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from './user.entity';
import { PendingConfirmLogs } from './pending-confirm-logs.entity';

@Entity('driver_order_assignment_rejection_logs')
export class DriverOrderAssignmentRejectionLog {
    constructor(partial: DeepPartial<DriverOrderAssignmentRejectionLog>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    driver_id: number; // user_id

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    order_id: number;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
    })
    rejection_message: string;

    @Column({
        type: 'int',
        width: 1,
        nullable: false,
    })
    rejection_type: number;

    @Column({
        type: 'datetime',
        nullable: false,
    })
    date: string;

    @Column({
        type: 'tinyint',
    })
    is_priority: number;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => User, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: '',
    })
    driver: User;
}

export enum ERejectionType {
    WAITING_TIME_OUT = 0,
    PARTNER_REJECT = 1,
}
