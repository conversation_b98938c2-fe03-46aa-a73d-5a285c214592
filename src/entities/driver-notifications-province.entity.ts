import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { DriverNotificationObject } from './driver-notifications.entity';
import { Province } from './province.entity';

@Entity('driver_notification_object_provinces')
export class DriverNotificationObjectProvince {
    constructor(partial: Partial<DriverNotificationObjectProvince>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn({
        type: 'int',
        width: 11,
    })
    province_id: number;

    @PrimaryColumn({
        type: 'int',
        unsigned: true,
    })
    object_id: number;

    @ManyToOne(() => DriverNotificationObject)
    @JoinColumn({
        name: 'object_id',
        referencedColumnName: 'id',
    })
    object: DriverNotificationObject;

    @ManyToOne(() => Province)
    @JoinColumn({
        name: 'province_id',
        referencedColumnName: 'id',
    })
    provinceInfo: Province;
}
