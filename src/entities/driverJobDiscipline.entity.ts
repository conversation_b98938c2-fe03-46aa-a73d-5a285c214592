import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('driver_job_disciplines')
export class DriverJobDiscipline {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', default: null })
    waiting_seconds: number;

    @Column({ type: 'tinyint', default: 0 })
    stop_receiving_job: number;

    @Column({ type: 'int', default: 0, unique: true })
    exceeded_num: number;

    @Column({ type: 'text', default: null })
    description: string;
}
