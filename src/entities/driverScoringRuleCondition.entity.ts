import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { EConditionOperator, ELogicalOperator, EScoringFieldName, EScoringSourceType } from './types/EScoringRule.enum';
import { ScoringRule } from './driverScoringRule.entity';

@Entity('driver_scoring_rule_conditions')
export class ScoringRuleCondition {
    constructor(partial: Partial<ScoringRuleCondition>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    rule_id: number;

    // @Column({
    //     type: 'enum',
    //     enum: EScoringSourceType,
    //     default: EScoringSourceType.ORDER,
    //     comment: 'Source type for the condition data (Order, DriverMission, DriverOrderPlan, etc.)',
    // })
    // source_type: EScoringSourceType;

    @Column({ length: 100 })
    field_name: EScoringFieldName; // 'distance', 'total_price', 'type', 'province_id', etc.

    @Column({
        type: 'enum',
        enum: EConditionOperator,
    })
    operator: EConditionOperator; // '>', '>=', '<', '<=', '==', 'in', 'not_in'

    @Column({
        type: 'json',
    })
    value: any; // Flexible value storage (number, string, array)

    @Column({
        type: 'enum',
        enum: ELogicalOperator,
        default: ELogicalOperator.AND,
    })
    logical_operator: ELogicalOperator; // 'AND', 'OR'

    @Column({ default: 0 })
    order_index: number; // Order of condition evaluation

    @ManyToOne(() => ScoringRule, (rule) => rule.conditions, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'rule_id' })
    rule: ScoringRule;
}



