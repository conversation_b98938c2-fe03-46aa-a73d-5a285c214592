import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    OneTo<PERSON><PERSON>,
} from 'typeorm';
import { AdsCampaign } from './adsCampaigns.entity';

@Entity('ads_contract_info')
export class AdsContractInfo {
    constructor(partial: Partial<AdsContractInfo>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn()
    ads_campaign_id: number;

    @Column()
    contract_id: number;

    @Column({ type: 'varchar', length: 50, default: '1' })
    contract_status: EAdsContractStatus;

    @Column({ type: 'varchar', length: 255, nullable: true })
    message: string;

    @OneToOne(() => AdsCampaign)
    @JoinColumn({
        name: 'ads_campaign_id',
        referencedColumnName: 'id',
    })
    campaign: AdsCampaign;
}

export enum EAdsContractStatus {
    APPROVED = 4,
    DENIED = 3,
    PENDING = 2,
    NOT_YET_SIGNED = 1,
    EXPIRED = 5,
    NOT_YET_CREATED = 0,
}