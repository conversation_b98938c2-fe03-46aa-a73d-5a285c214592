import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { RestaurantBusinessHours } from './restaurantBusinessHour.entity';

@Entity('work_times')
export class WorkTime {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'time' })
    open_time: string;

    @Column({ type: 'time' })
    close_time: string;

    @ManyToOne(() => RestaurantBusinessHours)
    @JoinColumn({
        name: 'restaurant_business_hour_id',
        referencedColumnName: 'id',
    })
    businessHour: RestaurantBusinessHours;
}
