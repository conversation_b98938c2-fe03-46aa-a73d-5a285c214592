import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('seller_review_summary')
export class SellerReviewSummary {
    constructor(partial: Partial<SellerReviewSummary>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn()
    seller_id: string;

    @Column()
    province_id: number;

    @Column()
    total_review: number;

    @Column()
    rating: number;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'seller_id',
        referencedColumnName: 'id',
    })
    seller: User;
}
