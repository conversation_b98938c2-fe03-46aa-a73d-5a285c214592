import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { RestaurantAd } from './restaurantAd.entity';
import { RestaurantNewsFeed } from './restaurantNewsFeed.entity';

@Index('uq_restaurant_ad_newsfeeds_news_feed', ['restaurant_ad_id', 'news_feed_id'], {
    unique: true,
})
@Entity('restaurant_ad_newsfeeds')
export class RestaurantAdNewsFeed {
    constructor(partial: Partial<RestaurantAdNewsFeed>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    news_feed_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    restaurant_ad_id: number;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_from: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_to: Date;

    @Column({
        type: 'int',
        nullable: false,
        default: 0,
    })
    position: number;

    @ManyToOne(() => RestaurantAd, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'restaurant_ad_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ad_newsfeeds_ads',
    })
    restaurantAd: RestaurantAd;

    @ManyToOne(() => RestaurantNewsFeed, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'news_feed_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ad_newsfeeds_news_feed',
    })
    newsFeed: RestaurantNewsFeed;
}
