import {
    <PERSON>umn,
    CreateDate<PERSON>olumn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

import { decimalColumnTransformer } from 'src/common/typorm/decimalColumnTransformer';
import { DriverTaxFinalization } from './driverTaxFinalization.entity';

export enum EDriverTaxFinalizationEventType {
    exit = 'exit', // when driver leaves the platform
    rejoin = 'rejoin', // when driver rejoins the platform after leaving it
    final_year = 'final_year', // when driver finalizes the tax for the year
    refund_debt_tax = 'refund_debt_tax', // when driver refunds the debt tax to the platform

    // if tax finalization is done for the year, but the driver has debt tax, then the driver can refund the debt tax to the platform, or the platform can deduct the debt tax from the driver's tax refund
    // final_year_with_debt_tax = 'final_year_with_debt_tax',

    // final_year_with_tax_refund = 'final_year_with_tax_refund', // when last event is final_year and driver has tax refund
}
export enum EDriverTaxFinalizationEventStatus {
    pending = 'pending',
    completed = 'completed',
    failed = 'failed',
    canceled = 'canceled',
    in_progress = 'in_progress',
}

export enum EDriverTaxFinalizationEventTransactionStatus {
    pending = 'pending',
    completed = 'completed',
    failed = 'failed',
}

@Entity('driver_tax_finalization_events')
export class DriverTaxFinalizationEvent {
    constructor(partial: Partial<DriverTaxFinalizationEvent>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    tax_finalization_id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    type: EDriverTaxFinalizationEventType;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    status: EDriverTaxFinalizationEventStatus;

    // @Column({
    //     type: 'varchar',
    //     length: 30,
    //     nullable: false,
    // })
    // transaction_status: EDriverTaxFinalizationEventTransactionStatus;

    @Column({
        type: 'int',
        nullable: false,
    })
    transaction_id: number;

    @Column({
        type: 'datetime',
        nullable: true,
    })
    start_date: string;

    @Column({
        type: 'datetime',
        nullable: true,
    })
    end_date: string;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: true,
        transformer: decimalColumnTransformer,
    })
    total_income: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: true,
        transformer: decimalColumnTransformer,
    })
    total_tax: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: true,
        transformer: decimalColumnTransformer,
    })
    total_final_tax: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: true,
        transformer: decimalColumnTransformer,
    })
    tax_refund: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: true,
        transformer: decimalColumnTransformer,
    })
    tax_gov: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    created_by: number;

    @Column({
        type: 'json',
        default: null,
    })
    debt_tax_details: IDriverTaxDebtDetail[];

    @Column({
        type: 'text',
        nullable: true,
    })
    failure_reason: string;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    note: string;

    @CreateDateColumn()
    created_at: string;

    @UpdateDateColumn()
    updated_at: string;

    @Column({
        select: false,
        insert: false,
        update: false,
    })
    total_debt_tax: number;

    @ManyToOne(() => DriverTaxFinalization, (finalization) => finalization.id)
    @JoinColumn({
        name: 'tax_finalization_id',
        referencedColumnName: 'id',
    })
    tax_finalization?: DriverTaxFinalization;
}


export interface IDriverTaxDebtDetail {
    id: number;
    period_id: number;
    debt_tax: number;
}