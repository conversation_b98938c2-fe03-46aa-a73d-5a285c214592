import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('payment_methods')
export class PaymentMethod {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    code: EPaymentMethodCode;

    @Column()
    name: string;

    @Column()
    description: string;

    @Column()
    is_active: number;

    @Column()
    icon_url: string;

    @Column()
    icon_rect_url: string;

    @Column()
    icon_url_driver_app: string;

    @Column()
    ordinal_numbers: number;
}

export enum EPaymentMethodCode {
    'CASH_ON_DELIVERY' = 'Cash on Delivery',
    'MOMO' = 'MOMO',
    'ZALO_PAY' = 'ZaloPay',
    'ATM_CARD' = 'ATM_CARD',
    'CREDIT_CARD' = 'CREDIT_CARD',
    'QR_CODE' = 'QR_CODE',
    'VIET_QR' = 'VIET_QR',
    'ZP_CREDIT_CARD' = 'ZP_CREDIT_CARD',
    'ZP_ATM_CARD' = 'ZP_ATM_CARD',
}

export enum EPaymentMethodCodeMappingId {
    'CASH_ON_DELIVERY' = 1,
    'ATM_CARD' = 2,
    'CREDIT_CARD' = 3,
    'MOMO' = 4,
    'ZALO_PAY' = 5,
    'QR_CODE' = 6,
    'VIET_QR' = 7,
    'ZP_ATM_CARD' = 8,
    'ZP_CREDIT_CARD' = 9,
}

export const onlinePaymentMethods = [
    EPaymentMethodCode.MOMO,
    EPaymentMethodCode.ZALO_PAY,
    EPaymentMethodCode.CREDIT_CARD,
    EPaymentMethodCode.ATM_CARD,
    EPaymentMethodCode.VIET_QR,
    EPaymentMethodCode.ZP_CREDIT_CARD,
    EPaymentMethodCode.ZP_ATM_CARD,
];
