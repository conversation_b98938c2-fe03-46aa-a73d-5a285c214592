import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON>n,
    <PERSON><PERSON><PERSON>,
    <PERSON>in<PERSON>olum<PERSON>,
    ManyToOne,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { DriverReportType } from './driverReportType.entity';

@Entity('driver_reports')
export class DriverReport {
    constructor(partial: Partial<DriverReport>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', width: 10, nullable: false, unsigned: true })
    author_driver_id: number;

    @Column({ type: 'int', width: 10, nullable: false, unsigned: true })
    report_id: number;

    @Column({ type: 'text', nullable: true, default: null })
    description: string;

    @Column({ type: 'varchar', length: 255, nullable: true, default: null })
    report_driver_name: string;

    @Column({ type: 'varchar', length: 30, nullable: true, default: null })
    report_license_plate: string;

    @Column({ type: 'simple-array', nullable: true, default: null })
    images: string[];

    @Column({ type: 'simple-array', nullable: true, default: null })
    videos: string[];

    @Column({ type: 'simple-array', nullable: true, default: null })
    voices: string[];

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => Driver)
    @JoinColumn({
        name: 'author_driver_id',
        referencedColumnName: 'id',
    })
    authorDriver: Driver;

    @OneToOne(() => DriverReportType)
    @JoinColumn({
        name: 'report_id',
        referencedColumnName: 'id',
    })
    reportType: DriverReportType;

    // @ManyToOne(() => DriverReportType, { createForeignKeyConstraints: true })
    // @JoinColumn({
    //     name: 'report_id',
    //     referencedColumnName: 'id',
    // })
    // report_type: DriverReportType;

    // @ManyToOne(() => Driver, { createForeignKeyConstraints: true })
    // @JoinColumn({
    //     name: 'author_driver_id',
    //     referencedColumnName: 'id',
    // })
    // authorDriver: Driver;
}
