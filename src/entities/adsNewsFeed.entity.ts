import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { RestaurantNewsFeed } from './restaurantNewsFeed.entity';
import { AdsCampaignItem } from './adsCampaignItem.entity';

@Index('uq_ads_newsfeeds_news_feed', ['ads_item_id', 'news_feed_id'], {
    unique: true,
})
@Entity('ads_newsfeeds')
export class AdsNewsFeed {
    constructor(partial: Partial<AdsNewsFeed>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    news_feed_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    ads_item_id: number;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_from: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_to: Date;

    @Column({
        type: 'int',
        nullable: false,
        default: 0,
    })
    position: number;

    @ManyToOne(() => AdsCampaignItem, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ads_item_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_newsfeeds_ads_item',
    })
    adsItem: AdsCampaignItem;

    @ManyToOne(() => RestaurantNewsFeed, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'news_feed_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_newsfeeds_news_feed',
    })
    newsFeed: RestaurantNewsFeed;

    @CreateDateColumn({ type: 'timestamp' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp' })
    updated_at: Date;
}
