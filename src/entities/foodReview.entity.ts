import {
    Column,
    CreateDateColumn,
    Entity,
    <PERSON>inColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Food } from './food.entity';
import { User } from './user.entity';

@Entity('food_reviews')
export class FoodReview {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review: string;

    @Column()
    rate: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Food, (food) => food.reviews)
    @JoinColumn({
        name: 'food_id',
        referencedColumnName: 'id',
    })
    food?: Food;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;
}
