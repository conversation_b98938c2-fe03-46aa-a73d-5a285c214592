import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum EDriverMissionCode {
    WEEKLY_ORDER_PLAN = 'WEEKLY_ORDER_PLAN',
}

export interface IDriverMissionConfig {
    registration_deadline: string;
}

@Entity('driver_missions')
export class DriverMission {
    constructor(partial: Partial<DriverMission>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
        comment: 'Unique code identifying the mission type',
    })
    code: EDriverMissionCode;

    @Column({
        type: 'varchar',
        length: 100,
        nullable: false,
        comment: 'Title of the mission',
    })
    title: string;

    @Column({
        type: 'text',
        nullable: false,
        comment: 'Detailed description of the mission',
    })
    description: string;

    @Column({
        type: 'json',
        nullable: true,
        comment: 'Minimum driver rank required to access this mission',
    })
    require_rank_ids: number[];

    @Column({
        type: 'tinyint',
        width: 1,
        default: 1,
        comment: 'Whether the mission is currently active',
    })
    is_active: number;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
        comment: 'Configuration settings for the mission',
    })
    config: IDriverMissionConfig;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
