import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { ScoringHistory } from './driverScoringHistory.entity';
import { EVehicleType } from './vehicleType.entity';

export enum ELeaderboardType {
    DAILY = 'daily',
    WEEKLY = 'weekly',
    MONTHLY = 'monthly',
}

@Entity('driver_scores')
export class DriverScore {
    constructor(partial: Partial<DriverScore>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', nullable: false })
    driver_id: number;

    @Column({
        type: 'enum',
        enum: ELeaderboardType,
        nullable: false,
    })
    leaderboard_type: ELeaderboardType;

    @Column({ type: 'varchar', length: 20, nullable: false })
    period_key: string; // Format: YYYY-MM-DD (daily), YYYY-WW (weekly), YYYY-MM (monthly)

    @Column({
        type: 'enum',
        enum: EVehicleType,
        nullable: false,
        default: EVehicleType.bike,
        comment: 'Vehicle type for leaderboard separation (bike/car)',
    })
    vehicle_type: EVehicleType;

    @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
    total_points: number;

    // Snapshot ranking fields - captured at the time points were awarded
    @Column({ type: 'int', nullable: true })
    snapshot_rank_id: number;

    @Column({ type: 'varchar', length: 100, nullable: true })
    snapshot_rank_name: string;

    @Column({ type: 'varchar', length: 50, nullable: true })
    snapshot_rank_code: string;

    @Column({ type: 'int', nullable: true })
    snapshot_rank_level: number;

    @Column({ type: 'varchar', length: 500, nullable: true })
    snapshot_rank_image: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    last_updated: Date;

    @ManyToOne(() => Driver, (driver) => driver.driver_scores)
    @JoinColumn({ name: 'driver_id', referencedColumnName: 'id' })
    driver: Driver;

    @ManyToMany(() => ScoringHistory, (scoringHistory) => scoringHistory.driver_scores)
    scoring_histories: ScoringHistory[];
}
