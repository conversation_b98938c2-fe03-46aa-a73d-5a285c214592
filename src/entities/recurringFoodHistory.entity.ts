import { BeforeInsert, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import * as moment from 'moment';
import { VietNamTimeZone } from 'src/jobs';

@Entity('recurring_food_histories')
export class RecurringFoodHistory {
    constructor(partial: Partial<RecurringFoodHistory>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    food_id: number;

    @Column({ type: 'integer', nullable: false, width: 4, unsigned: true })
    quantity: number;

    @Column({ type: 'integer', nullable: false, unsigned: true, width: 1 })
    day_of_week: number;

    @Column({ type: 'integer', nullable: false, unsigned: true, width: 2 })
    day_of_month: number;

    @Column({ type: 'integer', nullable: false, unsigned: true, width: 2 })
    week_of_month: number;

    @Column({ type: 'integer', nullable: false, unsigned: true, width: 2 })
    month_of_year: number;

    @Column({ type: 'time', nullable: false })
    time: string;

    @Column({ type: 'varchar', length: 30, nullable: false })
    status: ERecurringFoodHistoryStatus;

    @Column({ type: 'varchar', length: 255, nullable: true, default: null })
    error_msg: string;

    @CreateDateColumn()
    created_at: string;

    @UpdateDateColumn()
    updated_at: string;

    @BeforeInsert()
    beforeInsert() {
        const nowMoment = moment().utcOffset(VietNamTimeZone);
        this.day_of_week = nowMoment.get('day');
        this.day_of_month = nowMoment.get('date');
        this.week_of_month = nowMoment.get('week');
        this.month_of_year = nowMoment.get('month');
        this.time = nowMoment.format('HH:mm:ss');
    }
}

export enum ERecurringFoodHistoryStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
}
