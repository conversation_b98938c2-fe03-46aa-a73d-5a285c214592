import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('thumbnails_temp')
export class ThumbnailsTemp {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    image: string;

    @Column()
    thumbnail: string;

    @Column()
    image_id: number;

    @Column()
    uuid: string;

    @Column()
    key: string;

    @Column()
    type: string;

    @Column()
    table: string;

    @Column()
    status: string;
}
