import { <PERSON>umn, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON>, OneTo<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { DisciplinaryActionCategory } from './disciplinaryActionCategory.entity';
import { TinyInt } from 'src/common/constants';
import { DisciplinaryActionMeasure } from './disciplinaryActionMeasure.entity';
import { DisciplinePenaltyLevel } from 'src/models/driverDiscipline/dto';

export enum EDisciplinaryActionCode {
    // for discipline action category: face_recognition
    FACE_RECOGNITION_INCONSISTENCY_IN_SHIFT = 'FACE_RECOGNITION_INCONSISTENCY_IN_SHIFT',
    FACE_RECOGNITION_INCONSISTENCY_IN_STARTING_SHIFT = 'FACE_RECOGNITION_INCONSISTENCY_IN_STARTING_SHIFT',
    FACE_RECOGNITION_INCONSISTENCY_IN_LOGGING_IN = 'FACE_RECOGNITION_INCONSISTENCY_IN_LOGGING_IN',

    // for discipline action category: order_rejection
    ORDER_REJECTION_TOO_MANY_WITHOUT_PRIORITY_MODE = 'ORDER_REJECTION_TOO_MANY_WITHOUT_PRIORITY_MODE',
    ORDER_REJECTION_TOO_MANY_WITH_PRIORITY_MODE = 'ORDER_REJECTION_TOO_MANY_WITH_PRIORITY_MODE',
    INSUFFICIENT_WALLET_BALANCE = 'INSUFFICIENT_WALLET_BALANCE',

    // subtract wallet balance
    WEEKLY_REWARD_PENALTY = 'WEEKLY_REWARD_PENALTY', // subtract wallet balance IF driver reject too many orders in a week, subtract 100k vnd.
    FRAUD_PENALTY = 'FRAUD_PENALTY', // subtract wallet balance IF driver commit fraud, subtract 200k vnd.
    UNIFORM_PENALTY = 'UNIFORM_PENALTY', // subtract wallet balance IF driver violate uniform policy, subtract 50k vnd.
    QUICK_ORDER_COMPLETION_PENALTY = 'QUICK_ORDER_COMPLETION_PENALTY', // subtract wallet balance IF driver complete order too quickly, subtract 50k vnd.

    NOT_ACCEPT_PENDING_ORDER_PENALTY = 'NOT_ACCEPT_PENDING_ORDER_PENALTY',
}

export const NotAcceptPendingOrderPenaltyMethod = [
    {
        code: 'WARNING_OF_HIGH_REJECTIONS',
        name: 'Gửi thông báo cảnh báo',
    },
    {
        code: 'DEDUCT_N_PRIORITY_COUNTS',
        name: 'Trừ N số lượt ưu tiên',
    },
    {
        code: 'STOP_RECEIVING_PENDING_ORDER_N_MINUTES',
        name: 'Ngưng nhận đơn hỏi trong N phút',
    },
    {
        code: 'LOCK_ACCOUNT_N_MINUTES',
        name: 'Khoá tài khoản N phút',
    },
];
export interface IOrderRejectTooManyDefinition {
    max_order_rejection_count: number;
    ranking_code: string;
    ranking_level: number;
    violation_consecutive: number;
}

@Entity('disciplinary_actions')
export class DisciplinaryAction {
    constructor(partial: Partial<DisciplinaryAction>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn({
        type: 'int',
    })
    id: number;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: false,
        unique: true,
    })
    code: EDisciplinaryActionCode;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    name: string;

    @Column({
        type: 'text',
    })
    description: string;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: 1,
    })
    is_active: TinyInt;

    @Column({
        type: 'json',
        nullable: true,
    })
    violation_definition: DisciplinePenaltyLevel[] | IOrderRejectTooManyDefinition | Record<string, unknown> | null;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    category_id: number;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.FALSE,
        comment: '1: system, 0: custom action',
    })
    is_system: TinyInt;

    @ManyToOne(() => DisciplinaryActionCategory)
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
    })
    category: DisciplinaryActionCategory;

    @OneToOne(() => DisciplinaryActionMeasure)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'action_id',
    })
    disciplinaryActionMeasure: DisciplinaryActionMeasure;
}
