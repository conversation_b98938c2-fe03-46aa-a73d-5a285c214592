import { PrimaryColumn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AdsCampaignItem } from './adsCampaignItem.entity';

@Entity('ads_search_pages')
export class AdsSearchPage {
    constructor(partial: Partial<AdsSearchPage>) {
        Object.assign(this, partial);
    }
    // @PrimaryGeneratedColumn()
    // id: number;

    @PrimaryColumn()
    keyword_id: number;

    @PrimaryColumn()
    campaign_item_id: number;

    @ManyToOne(() => AdsCampaignItem)
    @JoinColumn({
        name: 'campaign_item_id',
        referencedColumnName: 'id',
    })
    adsCampaignItem: AdsCampaignItem;
}

export enum EKeywordType {
    normal = 'normal',
    individual = 'individual',
}
