import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { AdsCategory } from './adsCategoryV2.entity';
import { IsOptional } from 'class-validator';
import { AdsPlan } from './adsPlans.entity';

@Entity('ads_plan_item')
export class AdsPlanItem {
    constructor(partial: Partial<AdsPlanItem>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    ad_cate_id: number;

    @ManyToOne(() => AdsCategory, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ad_cate_id',
        referencedColumnName: 'id',
    })
    ads_category: AdsCategory;

    @Column()
    duration: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    note: string | null;

    @IsOptional()
    @Column({
        type: 'double',
        nullable: true,
    })
    price: number;

    @Column({
        type: 'int',
        nullable: true,
    })
    ads_plan_id: number;

    @ManyToOne(() => AdsPlan, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ads_plan_id',
        referencedColumnName: 'id',
    })
    ads_plan: AdsPlan;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;
}
