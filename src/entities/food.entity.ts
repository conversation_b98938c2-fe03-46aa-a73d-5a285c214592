import * as _ from 'lodash';
import {
    Column,
    CreateDateColumn,
    DeepPartial,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Category } from './category.entity';
import { ExtraGroup } from './extraGroup.entity';
import { ExtrasFood } from './extrasFood.entity';
import { FoodOrder } from './foodOrder.entity';
import { FoodReview } from './foodReview.entity';
import { FoodSalesLimitManagement } from './foodSalesLimitManagement.entity';
import { Menu } from './menu.entity';
import { OptionGroup } from './optionGroup.entity';
import { Restaurant } from './restaurant.entity';
import { FoodOptionGroup } from './food-option-group.entity';

export enum DescriptionType {
    DEFAULT = 'DEFAULT',
    AI = 'AI',
}

@Entity('foods')
export class Food {
    constructor(deepPartial: DeepPartial<Food>) {
        Object.assign(this, deepPartial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    image: string;

    @Column()
    restaurant_id: number;

    @Column()
    category_id: number = null;

    @Column()
    price: number;

    @Column()
    discount_price: number;

    @Column()
    description: string;

    @Column()
    description_type: DescriptionType;

    @Column()
    ingredients: string;

    @Column()
    weight: number;

    @Column()
    featured: number;

    @Column()
    sold_out_until: string;

    @Column()
    top: boolean;

    @Column({
        type: 'tinyint',
        nullable: true,
        transformer: {
            from(value: number) {
                return Boolean(value);
            },
            to(value: boolean) {
                return _.toNumber(value);
            },
        },
    })
    hot: boolean;

    @Column({
        type: 'tinyint',
        nullable: true,
        transformer: {
            from(value: number) {
                return Boolean(value);
            },
            to(value: boolean) {
                return _.toNumber(value);
            },
        },
    })
    weekly_hot: boolean;

    @Column({
        type: 'tinyint',
        nullable: true,
        transformer: {
            from(value: number) {
                return value ? Boolean(value) : undefined;
            },
            to(value: boolean) {
                return value ? _.toNumber(value) : undefined;
            },
        },
    })
    outstand: boolean;

    @Column()
    subCat: string;

    @Column()
    status: boolean;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    is_active: number;

    @Column()
    pending_approved_price: number;

    @Column()
    ordinal_numbers: number;

    @Column()
    pending_approved_name: string;

    @Column()
    pending_approved_image: string;

    @Column()
    denied_reason: string;

    @Column({
        type: 'varchar',
        length: 30,
    })
    approval_status: EFoodApprovalStatus;

    @Column({
        type: 'int',
        width: 11,
        default: null,
    })
    available_qty: number | null;

    // @Column({
    //     type: 'int',
    //     width: 11,
    //     default: 0,
    // })
    // today_sales: number;

    // @Column({
    //     type: 'int',
    //     width: 11,
    //     default: 0,
    // })
    // today_sold_out: TinyInt = TinyInt.NO;

    @Column({
        type: 'tinyint',
        default: 0,
        nullable: false,
    })
    daily_discount_limit_user: number;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    for_sale: number;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @Column()
    thumbnails: string;

    @Column()
    open_all_time: number;

    @Column()
    open_time: string;

    @Column()
    close_time: string;

    @Column({
        type: 'int',
        default: 0,
    })
    sales: number;

    @Column({
        type: 'int',
    })
    food_menu_id: number;

    @Column({
        type: 'varchar',
        length: 64,
    })
    status_statistics: EStatusStatisticsFood;

    @Column({
        type: 'date',
    })
    last_statistics: string;

    // @Column({
    //     type: 'int',
    //     default: 0,
    //     nullable: false,
    // })
    // discount_limit_user: number;

    @OneToMany(() => FoodOrder, (foodOrder) => foodOrder.food)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_id',
    })
    food_orders: FoodOrder[];

    @ManyToOne(() => Restaurant, (restaurant) => restaurant.foods)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant: Restaurant;

    @OneToMany(() => ExtrasFood, (extrasFood) => extrasFood.food)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_id',
    })
    extrasFood?: ExtrasFood[];

    @OneToMany(() => FoodReview, (foodReview) => foodReview.food)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_id',
    })
    reviews?: FoodReview[];

    @ManyToOne(() => Category, (category) => category.foods)
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
    })
    category?: Category;

    @ManyToMany(() => ExtraGroup, (extraGroup) => extraGroup.foods)
    @JoinTable({
        name: 'extra_group_foods',
        joinColumn: {
            referencedColumnName: 'id',
            name: 'food_id',
        },
        inverseJoinColumn: {
            referencedColumnName: 'id',
            name: 'extra_group_id',
        },
    })
    extraGroups?: ExtraGroup[];

    @ManyToOne(() => Menu)
    @JoinColumn({
        name: 'food_menu_id',
        referencedColumnName: 'id',
    })
    menu: Menu;

    @ManyToMany(() => OptionGroup)
    @JoinTable({
        name: 'food_option_groups',
        joinColumn: {
            name: 'food_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'option_group_id',
            referencedColumnName: 'id',
        },
    })
    optionGroups: OptionGroup[];

    @OneToOne(() => FoodSalesLimitManagement)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_id',
    })
    salesLimitDetails: FoodSalesLimitManagement;

    @OneToMany(() => FoodOptionGroup, (foodOptionGroup) => foodOptionGroup.food)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_id',
    })
    foodOptionGroups: FoodOptionGroup[];
}

export enum EFoodApprovalStatus {
    APPROVED = 'approved',
    DENIED = 'denied',
    PENDING = 'pending',
}

export enum EStatusStatisticsFood {
    PENDING = 'pending',
    DONE = 'done',
}
