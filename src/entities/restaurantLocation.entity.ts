import {
    <PERSON><PERSON>n,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToOne,
    PrimaryColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Location } from './location.entity';
import { Restaurant } from './restaurant.entity';
@Entity('restaurant_locations')
export class RestaurantLocation {
    @PrimaryColumn()
    restaurant_id: number;

    @OneToOne(() => Restaurant)
    @JoinColumn({ name: 'restaurant_id' })
    restaurant: Restaurant;

    @Column()
    location_id: number;

    @ManyToOne(() => Location, (location) => location.restaurantLocations)
    @JoinColumn({ name: 'location_id' })
    location: Location;

    @Column()
    street: string;

    @Column({ nullable: true })
    location_id_v2: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    location_v2: ILocationV2;
}

interface ILocationV2 {
    id: number;
    name: string;
    type: string;
    parent_code?: string;
    level: number;
    code: string;
}
