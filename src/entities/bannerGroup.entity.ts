import { AdBanner } from 'src/entities/adBanner.entity';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Collection } from './collection.entity';
import { EPageDisplay } from './types/EPayDisplay.enum';
import { EDisplayType, EPosition } from 'src/models/bannerGroup/bannerGroup.dto';
import { AdsCampaignItem } from './adsCampaignItem.entity';

// export enum EPosition {
//     TOP = 'top',
//     MIDDLE = 'mid',
//     BOTTOM = 'bottom',
// }

// export enum EDisplayType {
//     HORIZONTAL = 'horizontal',
//     VERTICAL = 'vertical',
// }

// enum for show in which page
@Entity('banner_groups')
export class BannerGroup {
    constructor(partial: Partial<BannerGroup>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    subtitle: string;

    @Column()
    is_activated: boolean;

    @Column()
    position: EPosition;

    @Column()
    display_type: EDisplayType;

    @Column({ default: 1, comment: 'Random Items' })
    random_items: number;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => AdBanner, (adBanner) => adBanner.bannerGroup)
    items: AdBanner[];

    @Column()
    collection_id: number;

    @ManyToOne(() => Collection, (collection) => collection.bannerGroups)
    @JoinColumn({
        name: 'collection_id',
        referencedColumnName: 'id',
    })
    collection: Collection;

    @Column({
        type: 'enum',
        enum: EPageDisplay,
        default: EPageDisplay.HOME,
    })
    page_display: EPageDisplay;

    @Column()
    ads_campaign_item_id: number;

    // relation with ads_campaign_item
    @ManyToOne(() => AdsCampaignItem, (adsCampaignItem) => adsCampaignItem.bannerGroups)
    @JoinColumn({
        name: 'ads_campaign_item_id',
        referencedColumnName: 'id',
    })
    adsCampaignItem: AdsCampaignItem | null;
}
