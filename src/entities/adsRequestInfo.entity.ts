import {
    <PERSON>umn,
    CreateDateColumn,
    <PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { AdsCampaign } from './adsCampaigns.entity';

export const enum ERequestInfoRangeTime {
    MORNING = 'MORNING', // 06:00 - 12:00
    AFTERNOON = 'AFTERNOON', // 12:00 - 18:00
    ANYTIME = 'ANYTIME', // 06:00 - 18:00
}

@Entity('ads_request_info')
export class AdsRequestInfo {
    constructor(partial: Partial<AdsRequestInfo>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    phone: string;

    @Column()
    name: string;

    @Column()
    email: string;

    @Column()
    message: string;

    @Column()
    visit_time_type: ERequestInfoRangeTime;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column()
    ads_campaign_id: number;

    @ManyToOne(() => AdsCampaign)
    @JoinColumn({
        name: 'ads_campaign_id',
        referencedColumnName: 'id',
    })
    ads_campaign: AdsCampaign;
}
