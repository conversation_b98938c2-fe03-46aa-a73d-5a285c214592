import {
    <PERSON><PERSON><PERSON>,
    Create<PERSON>ate<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    Unique,
    UpdateDateColumn,
} from 'typeorm';
import { DriverOrderPlan } from './driver-order-plan.entity';
import { DriverDailyPlan } from './driver-daily-plan.entity';
import { Driver } from './driver.entity';

@Entity('driver_order_plan_weekly_registrations')
@Unique(['driver_id', 'week_start_date'])
export class DriverOrderPlanWeeklyRegistration {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false, name: 'driver_id' })
    driver_id: number;

    @Column({ nullable: false, name: 'plan_id' })
    plan_id: number;

    @Column({
        type: 'integer',
        nullable: false,
        name: 'bonus_amount',
    })
    bonus_amount: number;

    @Column({ type: 'date', nullable: false, name: 'week_start_date' })
    week_start_date: string;

    @Column({ type: 'date', nullable: false, name: 'week_end_date' })
    week_end_date: string;

    @Column({ nullable: false, name: 'total_planned_orders' })
    total_planned_orders: number;

    @Column({ default: 0, name: 'total_completed_orders' })
    total_completed_orders: number;

    @Column({ default: 0, name: 'is_completed' })
    is_completed: boolean;

    @Column({ nullable: true, name: 'reward_id' })
    reward_id: number;

    @Column({
        name: 'registration_time',
        type: 'timestamp',
        default: () => 'CURRENT_TIMESTAMP',
    })
    registration_time: string;

    @Column({
        type: 'timestamp',
        nullable: false,
        name: 'deadline',
    })
    deadline: string;

    @CreateDateColumn({ name: 'created_at' })
    created_at: string;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: string;

    @ManyToOne(() => DriverOrderPlan, (plan) => plan.registrations)
    @JoinColumn({ name: 'plan_id' })
    plan: DriverOrderPlan;

    @OneToMany(() => DriverDailyPlan, (dailyPlan) => dailyPlan.registration)
    dailyPlans: DriverDailyPlan[];

    @ManyToOne(() => Driver, (driver) => driver.id)
    @JoinColumn({ name: 'driver_id', referencedColumnName: 'id' })
    driver: Driver;
}
