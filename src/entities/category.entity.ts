import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Collection } from './collection.entity';
import { Food } from './food.entity';
import { Restaurant } from './restaurant.entity';

@Entity('categories')
export class Category {
    constructor() {
        this.created_at = new Date();
        this.updated_at = new Date();
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    status: number;

    @Column()
    image: string;

    @Column()
    icon: string;

    @Column()
    featured_image: string;

    @Column()
    collection_id: number;

    @Column({
        type: 'json',
        default: null,
        nullable: true,
    })
    query_params: Record<string, any>;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Food, (food) => food.category)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'category_id',
    })
    foods?: Food[];

    @ManyToMany(() => Restaurant)
    @JoinTable({
        name: 'categories_restaurants',
        joinColumn: {
            name: 'category_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'restaurant_id',
            referencedColumnName: 'id',
        },
    })
    restaurants?: Restaurant[];

    @ManyToOne(() => Collection)
    @JoinColumn({
        name: 'collection_id',
        referencedColumnName: 'id',
    })
    collection?: Collection;

    @Column()
    ordinal_numbers: number;

    //type
    @Column()
    type: EFoodCategoryType;

    @Column()
    limit_ads: number;

    @Column()
    used: number;
}

export enum EFoodCategoryType {
    Normal = 'normal',
    Featured = 'featured',
}
