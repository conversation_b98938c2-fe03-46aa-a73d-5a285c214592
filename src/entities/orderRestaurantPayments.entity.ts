import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON>tity,
    <PERSON>inC<PERSON>umn,
    ManyToOne,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order } from './order.entity';
import { INPayAccountLinking } from 'src/providers/microservices/deliveryServiceProxy/interfaces';
import { Restaurant } from './restaurant.entity';

@Entity('order_restaurant_payments')
export class OrderRestaurantPayment {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', nullable: false })
    order_id: number;

    @Column({ type: 'int', nullable: false })
    restaurant_id: number;

    @Column({ type: 'double', precision: 10, scale: 3, nullable: false })
    amount: number;

    @Column({ type: 'double', precision: 10, scale: 3, nullable: false })
    vat: number;

    @Column({ type: 'double', precision: 10, scale: 3, nullable: false })
    personal_income_tax: number;

    @Column({ type: 'double', precision: 10, scale: 3, nullable: false })
    trade_discount: number;

    @Column({ type: 'varchar', length: 50, nullable: false })
    payment_method: EOrderRestaurantPaymentType;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column({ type: 'varchar', length: 255, nullable: true })
    ref_trans_id: string;

    @Column({ type: 'varchar', length: 255, nullable: true })
    app_trans_id: string;

    @Column({ type: 'varchar', length: 50, nullable: true })
    status: EOrderRestaurantPaymentStatus;

    @Column({ type: 'json', nullable: true })
    extra_data: Record<string, any>;

    @Column({ type: 'text', nullable: true })
    note: string;

    @Column({ type: 'json', nullable: true })
    payment_log: IPaymentLog;

    @Column({ type: 'varchar', length: 50, nullable: true })
    reversal_status: EOrderRestaurantPaymentStatus;

    @Column({ type: 'varchar', length: 255, nullable: true })
    ref_reversal_trans_id: string;

    @Column({ type: 'varchar', length: 255, nullable: true })
    app_reversal_trans_id: string;

    @Column({ type: 'timestamp', nullable: true })
    payment_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    reversal_at: Date;

    @Column({ type: 'json', nullable: true })
    npay_account_info: INPayAccountLinking;

    @OneToOne(() => Order, (order) => order.id, { nullable: false })
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order: Order;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;
}

export enum EOrderRestaurantPaymentStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
    PROCESSING = 'processing',
    // REFUNDED = 'refunded',
    // CANCELLED = 'cancelled',
}

export enum EOrderRestaurantPaymentType {
    VIA_DRIVER = 'via_driver',
    VILL_WALLET = 'vill_wallet',
    NPAY_WALLET = 'npay_wallet',
}

export interface IPaymentLog {
    // account_info?: INPayAccountLinking,
    transaction_info?: {},
    reversal_info?: {}
}

export interface IAccountInfo {
    merchant_id: number;
    phone: string;
    frozen_amount: number;
    status: 'SUCCESS' | 'FAILURE' | 'PENDING' | 'CANCEL';
}
