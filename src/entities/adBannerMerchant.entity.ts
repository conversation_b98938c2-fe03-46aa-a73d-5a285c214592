import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>umn,
    <PERSON>reateDate<PERSON><PERSON><PERSON>n,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>in<PERSON><PERSON>,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { TinyInt } from 'src/common/constants';
import { Province } from './province.entity';
import { IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { RestaurantPromotionMarket } from './restaurantPromotionMarket.entity';

export enum EBannerEvent {
    GO_TO_VILL_ADS = 'go_to_vill_ads',
    GO_TO_LIST_MARKETING = 'go_to_list_marketing',
    GO_TO_ID_MARKETING = 'go_to_id_marketing',
    REDIRECT_WEBSITE = 'redirect_website',
    HTML_RENDERING = 'html_rendering',
}
export enum EEventType {
    GO_TO_VILL_ADS = 'go_to_vill_ads',
    GO_TO_LIST_MARKETING = 'go_to_list_marketing',
    GO_TO_ID_MARKETING = 'go_to_id_marketing',
    REDIRECT_WEBSITE = 'redirect_website',
}

export class MetadataDto {
    @IsOptional()
    @IsString()
    url: string;

    @IsOptional()
    @IsNumber()
    restaurant_promotion_market_id: number;

    @IsOptional()
    @IsString()
    html_content: string;
}

export class ExternalDataDto {
    @IsOptional()
    @IsString()
    main_btn_text: string;

    @IsOptional()
    @IsString()
    main_btn_event_type: EEventType;

    @IsOptional()
    @IsString()
    second_btn_text: string;

    @IsOptional()
    @IsString()
    second_btn_event_type: EEventType;

    @IsOptional()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata_main: MetadataDto;

    @IsOptional()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata_second: MetadataDto;
}

@Entity('ads_banner_merchant')
export class AdBannerMerchant {
    constructor(partial: Partial<AdBannerMerchant>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;
    @Column()
    image: string;

    @Column()
    description: string;

    @Column({
        type: 'tinyint',
        default: 0,
        transformer: {
            from(value: number) {
                return Boolean(value);
            },
            to(value: boolean) {
                return +value;
            },
        },
    })
    is_activated = false;

    @Column({ type: 'tinyint', default: TinyInt.YES, width: 1 })
    is_global: TinyInt;

    @Column()
    start_time: Date;

    @Column()
    end_time: Date;
    @Column()
    video_thumbnail_url: string;

    @Column()
    video_url: string;

    @Column()
    event_type: EBannerEvent;

    @Column({ type: 'simple-json' })
    metadata: MetadataDto;

    @Column({ type: 'simple-json' })
    external_data: ExternalDataDto;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToMany(() => Province)
    @JoinTable({
        name: 'province_ads_banners_merchant',
        joinColumn: {
            name: 'ad_banner_merchant_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'province_id',
            referencedColumnName: 'id',
        },
    })
    provinces: Province[];

    @ManyToOne(() => RestaurantPromotionMarket)
    @JoinColumn({
        name: 'restaurant_promotion_market_id',
        referencedColumnName: 'id',
    })
    restaurant_promotion_market: RestaurantPromotionMarket;

    @Column({ nullable: true })
    restaurant_promotion_market_id: number;

    @BeforeInsert()
    beforeInsert() {
        this.created_at = new Date();
    }
}
