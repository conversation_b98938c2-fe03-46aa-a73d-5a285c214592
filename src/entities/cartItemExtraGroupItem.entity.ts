import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { CartItem } from './cartItem.entity';
import { ExtraGroupItem } from './extraGroupItem.entity';

@Entity('cart_item_extra_groups')
export class CartItemExtraGroupItem {
    constructor(cartItemId: number, extraGroupItemId: number) {
        this.cart_item_id = cartItemId;
        this.extra_group_item_id = extraGroupItemId;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        nullable: false,
    })
    cart_item_id: number;

    @Column({
        unsigned: true,
    })
    extra_group_item_id: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => CartItem, (cartItem) => cartItem.cart_item_extras)
    @JoinColumn({
        name: 'cart_item_id',
        referencedColumnName: 'id',
    })
    cart_item?: CartItem;

    @ManyToOne(() => ExtraGroupItem)
    @JoinColumn({
        name: 'extra_group_item_id',
        referencedColumnName: 'id',
    })
    extra_group_item?: ExtraGroupItem;
}
