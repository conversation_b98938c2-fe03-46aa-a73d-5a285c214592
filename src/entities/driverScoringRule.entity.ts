import { Column, CreateDate<PERSON><PERSON>umn, <PERSON>tity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { ScoringRuleAction } from './driverScoringRuleAction.entity';
import { ScoringRuleCondition } from './driverScoringRuleCondition.entity';
import { EScoringSourceType, EPointType } from './types/EScoringRule.enum';

@Entity('driver_scoring_rules')
export class ScoringRule {
    constructor(partial: Partial<ScoringRule>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ length: 255 })
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: EScoringSourceType,
        default: EScoringSourceType.ORDER,
        comment: 'Primary source type for this rule (Order, DriverMission, DriverOrderPlan, etc.)',
    })
    source_type: EScoringSourceType;

    @Column({
        type: 'enum',
        enum: EPointType,
        default: EPointType.ADD,
        comment: 'Scoring type - whether this rule adds or subtracts points',
    })
    scoring_type: EPointType;

    @Column({ default: true })
    is_active: boolean;

    @Column({ default: 0 })
    priority: number;

    @Column({ nullable: true })
    created_by: number;

    @Column({ nullable: true })
    updated_by: number;

    @Column({ default: 1 })
    version: number;

    @Column({ type: 'timestamp', nullable: true })
    valid_from: Date;

    @Column({ type: 'timestamp', nullable: true })
    valid_to: Date;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => ScoringRuleCondition, (condition) => condition.rule, {
        cascade: true,
        eager: false,
    })
    conditions: ScoringRuleCondition[];

    @OneToMany(() => ScoringRuleAction, (action) => action.rule, {
        cascade: true,
        eager: false,
    })
    actions: ScoringRuleAction[];
}
