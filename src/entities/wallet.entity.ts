import {
    Column,
    CreateDateColumn,
    Entity,
    <PERSON>inC<PERSON>umn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';

@Entity('wallets')
export class Wallet {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'double',
        nullable: true,
        default: 0,
    })
    balance: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => Driver)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'wallet_id',
    })
    driver?: Driver;
}
