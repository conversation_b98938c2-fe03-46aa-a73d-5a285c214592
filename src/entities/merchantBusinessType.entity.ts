import { ApiProperty } from '@nestjs/swagger';
import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('merchant_business_types')
export class MerchantBusinessType {
    @ApiProperty({
        type: 'integer',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty({
        type: 'string',
        example: 'Doanh nghiệp/Công ty',
    })
    @Column({ type: 'varchar', width: 255, nullable: false })
    name: string;

    @ApiProperty({
        type: 'integer',
        example: 0,
        description: 'Required company profile',
    })
    @Column({
        type: 'tinyint',
        nullable: false,
    })
    required_company_profile: TinyInt;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;
}
