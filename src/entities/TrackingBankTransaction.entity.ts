import { TinyInt } from "src/common/constants";
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { TrackingBankAccount } from "./TrackingBankAccount.entity";
import { TrackingBankProviderWebhook } from "./TrackingBankProviderWebhook.entity";
import { TrackingBankWebhookHistory } from "./TrackingBankWebhookHistory.entity";

@Entity("tracking_bank_transactions")
export class TrackingBankTransaction {
  constructor(partial: Partial<TrackingBankTransaction>) {
    Object.assign(this, partial);
  }
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  bank_code: string;

  @Column()
  transaction_code: string;

  @Column()
  transaction_time: Date;

  @Column()
  transaction_type: ETrackingBankTransactionType;

  @Column({
    type: "tinyint",
    default: TinyInt.YES,
  })
  is_verified: TinyInt;

  @Column()
  amount: number;

  @Column()
  current_balance: number;

  @Column()
  raw_data: string;

  @Column({ type: "longtext", nullable: true })
  external_transaction_data: string;

  @Column({ type: "integer", nullable: true })
  external_transaction_id: number;

  @Column()
  tracking_bank_transaction_type: ETrackingBankTransactionType;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column()
  tracking_bank_account_id: number;

  @ManyToOne(
    () => TrackingBankAccount,
    (trackingBankAccount) => trackingBankAccount.tracking_bank_transaction
  )
  @JoinColumn({
    name: "tracking_bank_account_id",
    referencedColumnName: "id",
  })
  tracking_bank_account: TrackingBankAccount;

  // @Column({ nullable: true })
  // tracking_bank_provider_id: number;

  // @ManyToOne(() => TrackingBankProvider)
  // @JoinColumn({
  //     name: 'tracking_bank_provider_id',
  //     referencedColumnName: 'id',
  // })
  // tracking_bank_provider: TrackingBankProvider;

  @OneToMany(
    () => TrackingBankWebhookHistory,
    (webhookHistory) => webhookHistory.tracking_bank_transaction
  )
  webhook_histories: TrackingBankWebhookHistory[];

  @Column({ nullable: true })
  tracking_bank_provider_webhook_id: number;

  @ManyToOne(() => TrackingBankProviderWebhook)
  @JoinColumn({
    name: "tracking_bank_provider_webhook_id",
    referencedColumnName: "id",
  })
  tracking_bank_provider_webhook: TrackingBankProviderWebhook;
}

//create sql
/* CREATE TABLE tracking_bank_transactions(
   id            int auto_increment
       primary key,
   bank_code VARCHAR  NULL,
   transaction_code VARCHAR  NULL,
   transaction_type VARCHAR  NULL,
   amount INT  NULL,
   current_balance INT NULL,
   raw_data text NOT NULL,
   external_transaction_id LONGTEXT NULL,
   created_at TIMESTAMP  NULL DEFAULT CURRENT_TIMESTAMP,
   updated_at TIMESTAMP  NULL DEFAULT CURRENT_TIMESTAMP,
   tracking_bank_account_id INT NOT NULL,
   tracking_bank_provider_id INT NULL,
   tracking_bank_provider_webhook_id INT NULL,
   FOREIGN KEY(tracking_bank_account_id) REFERENCES tracking_bank_accounts(id),
   FOREIGN KEY(tracking_bank_provider_id) REFERENCES tracking_bank_providers(id),
   FOREIGN KEY(tracking_bank_provider_webhook_id) REFERENCES tracking_bank_provider_webhook(id)
);
*/

export enum ETrackingBankTransactionType {
  DEPOSIT = "DEPOSIT",
  WITHDRAW = "WITHDRAW",
}
