import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Extra } from './extra.entity';
import { Food } from './food.entity';

@Entity('extras_food')
export class ExtrasFood {
    constructor(foodId: number, extraId: number, isAvailable = 1) {
        this.food_id = foodId;
        this.extra_id = extraId;
        this.is_available = isAvailable;
    }
    @PrimaryColumn()
    food_id: number;

    @PrimaryColumn()
    extra_id: number;

    @Column({ type: 'tinyint', default: 1 })
    is_available: number;

    @ManyToOne(() => Food, (food) => food.extrasFood)
    @JoinColumn({
        name: 'food_id',
        referencedColumnName: 'id',
    })
    food?: Food;

    @ManyToOne(() => Extra, { eager: true })
    @JoinColumn({
        name: 'extra_id',
        referencedColumnName: 'id',
    })
    extras?: Extra;
}
