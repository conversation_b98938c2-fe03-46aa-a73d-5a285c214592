import {
    <PERSON>umn,
    CreateDateColumn,
    <PERSON>tity,
    <PERSON>in<PERSON><PERSON>um<PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { FoodOrder } from './foodOrder.entity';
import { OptionGroupType } from './optionGroup.entity';
import { Option } from './option.entity';

@Entity('order_food_options')
export class OrderFoodOption {
    constructor(partial: Partial<OrderFoodOption>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    price: number;

    @Column()
    name: string;
    @Column({ type: 'varchar', default: OptionGroupType.FOOD_ADDITIVE })
    type: OptionGroupType;

    @Column()
    quantity: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => FoodOrder)
    @JoinColumn({
        name: 'order_food_id',
        referencedColumnName: 'id',
    })
    foodOrder: FoodOrder;

    @Column()
    option_id: number;

    @ManyToOne(() => Option)
    @JoinColumn({
        name: 'option_id',
        referencedColumnName: 'id',
    })
    foodOption: Option;
}
