import {
    <PERSON>umn,
    CreateDate<PERSON>olumn,
    <PERSON><PERSON><PERSON>,
    Join<PERSON><PERSON>um<PERSON>,
    <PERSON>To<PERSON><PERSON>,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

export enum BatchJobRestaurantInsightType {
    AGG_ORDER_BY_DATE_RANGE = 'AGG_ORDER_BY_DATE_RANGE',
    AGG_ORDER_BY_WEEK_RANGE = 'AGG_ORDER_BY_WEEK_RANGE',
    AGG_ORDER_BY_MONTH_RANGE = 'AGG_ORDER_BY_MONTH_RANGE',
    AGG_RESTAURANT_ORDER_BY_DATE_RANGE = 'AGG_RESTAURANT_ORDER_BY_DATE_RANGE',
    AGG_RESTAURANT_ORDER_BY_DATE = 'AGG_RESTAURANT_ORDER_BY_DATE',
    AGG_RESTAURANT_ORDER_BY_WEEK_RANGE = 'AGG_RESTAURANT_ORDER_BY_WEEK_RANGE',
    AGG_RESTAURANT_ORDER_BY_WEEK = 'AGG_RESTAURANT_ORDER_BY_WEEK',
    AGG_RESTAURANT_ORDER_BY_MONTH_RANGE = 'AGG_RESTAURANT_ORDER_BY_MONTH_RANGE',
    AGG_RESTAURANT_ORDER_BY_MONTH = 'AGG_RESTAURANT_ORDER_BY_MONTH',

    AGG_RESTAURANT_ORDER_EXPORT_DATE = 'AGG_RESTAURANT_ORDER_EXPORT_DATE', // per restaurant
    AGG_RESTAURANT_ORDER_EXPORT_WEEK = 'AGG_RESTAURANT_ORDER_EXPORT_WEEK',
    AGG_RESTAURANT_ORDER_EXPORT_MONTH = 'AGG_RESTAURANT_ORDER_EXPORT_MONTH',

    AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_WEEK = 'AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_WEEK',
    AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_MONTH = 'AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_MONTH',

    AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_WEEK_MERCHANT = 'AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_WEEK_MERCHANT',
    AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_MONTH_MERCHANT = 'AGG_RESTAURANT_ORDER_BY_TRADE_DISCOUNT_MONTH_MERCHANT',

    AGG_RESTAURANT_ORDER_EXPORT_BY_RESTAURANTS = 'AGG_RESTAURANT_ORDER_EXPORT_BY_RESTAURANTS',

    AGG_QUANTITY_SOLD_FOODS = 'AGG_QUANTITY_SOLD_FOODS',
    AGG_QUANTITY_SOLD_FOOD = 'AGG_QUANTITY_SOLD_FOOD',

    AGG_QUANTITY_SOLD_FOODS_WEEK = 'AGG_QUANTITY_SOLD_FOODS_WEEK',
    AGG_DAILY_FOOD_SALES = 'AGG_DAILY_FOOD_SALES',

    AGG_QUANTITY_SOLD_FOODS_BY_RANGE_TIME = 'AGG_QUANTITY_SOLD_FOODS_BY_RANGE_TIME',

    AGG_DELIVERY_REVENUE_DRIVERS_BY_WEEK = 'AGG_DELIVERY_REVENUE_BY_DRIVERS_BY_WEEK',
    AGG_DELIVERY_REVENUE_DRIVERS_BY_DATE = 'AGG_DELIVERY_REVENUE_BY_DRIVERS_BY_DATE',

    AGG_DELIVERY_REVENUE_DRIVERS_BY_WEEK_RANGE = 'AGG_DELIVERY_REVENUE_BY_DRIVERS_BY_WEEK_RANGE',
    AGG_DELIVERY_REVENUE_DRIVERS_BY_DATE_RANGE = 'AGG_DELIVERY_REVENUE_BY_DRIVERS_BY_DATE_RANGE',

    AGG_ORDER_BY_PAYMENT_TYPE = 'AGG_ORDER_BY_PAYMENT_TYPE',
    AGG_ORDER_BY_PAYMENT_TYPE_AND_PROVINCE_ID = 'AGG_ORDER_BY_PAYMENT_TYPE_AND_PROVINCE_ID',

    EXPORT_RESTAURANT_INVOICE_MISA = 'EXPORT_RESTAURANT_INVOICE_MISA',

    EXPORT_DRIVER_INVOICE_MISA = 'EXPORT_DRIVER_INVOICE_MISA',
}

export enum EBatchJobRestaurantInsightStatus {
    PENDING = 'PENDING',
    QUEUED = 'QUEUED',
    SUCCESS = 'SUCCESS', // success
    FAILED = 'FAILED',
}

export interface IBaseBatchJobRestaurantInsightExecutionContext {
    from_date: string; // format: YYYY-MM-DD
    to_date: string; // format: YYYY-MM-DD
    restaurant_id?: number;
}

export interface IBatchJobAggRestaurantOrderContext extends IBaseBatchJobRestaurantInsightExecutionContext {
    restaurant_id: number;
}

export interface IBatchJobRestaurantInsightResult {
    error?: Record<string, any>;
    progress: number; // describe the progress of the job
    count: number;
    advancement_count: number;
    execution_result?: Record<string, any>; // the result of the job execution. example: aggregate income of a restaurant
}

@Entity('batch_job_restaurant_insight')
export class BatchJobRestaurantInsight {
    constructor(partial: Partial<BatchJobRestaurantInsight>) {
        this.initial();
        Object.assign(this, partial);
    }

    private initial() {
        this.status = EBatchJobRestaurantInsightStatus.PENDING;
    }

    @PrimaryGeneratedColumn({
        type: 'bigint',
        unsigned: true,
    })
    id: string;

    @Column({
        type: 'varchar',
        length: 700,
        nullable: true,
        default: null,
    })
    name: string;

    @Column({
        type: 'varchar',
        nullable: false,
        length: 60,
    })
    type: BatchJobRestaurantInsightType;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    status: EBatchJobRestaurantInsightStatus;

    @Column({
        type: 'timestamp',
        nullable: false,
        default: null,
    })
    start_time: Date;

    @Column({
        type: 'timestamp',
        nullable: false,
        default: null,
    })
    end_time: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    failed_at: Date;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    failure_reason: string;

    @Column({
        type: 'json',
        default: null,
        nullable: true,
    })
    context?: IBatchJobAggRestaurantOrderContext | IBaseBatchJobRestaurantInsightExecutionContext;

    @Column({
        type: 'bigint',
        default: null,
        nullable: true,
        unsigned: true,
    })
    depends_on_id: string; // id of the job that this job depends on

    @Column({
        type: 'bigint',
        default: null,
        nullable: true,
        unsigned: true,
    })
    source_id: string; // id of the job that this job is created from

    @Column({
        type: 'json',
        default: null,
        nullable: true,
    })
    result: IBatchJobRestaurantInsightResult;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => BatchJobRestaurantInsight)
    @JoinColumn({
        name: 'depends_on_id',
        referencedColumnName: 'id',
    })
    dependent_job?: BatchJobRestaurantInsight;

    get hasDependentJob(): boolean {
        return !!this.depends_on_id;
    }

    get isChildJob(): boolean {
        return !!this.source_id;
    }
}
