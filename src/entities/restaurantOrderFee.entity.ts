import { <PERSON>umn, CreateDateColumn, Entity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Province } from './province.entity';
import { Restaurant } from './restaurant.entity';
import { Fee } from './fee.entity';


@Entity('restaurant_order_fees')
export class RestaurantOrderFee {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    description: string;

    @Column()
    price: number;

    @Column()
    is_active: number;
    
    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
    
    @Column()
    restaurant_id: number;

    @Column()
    fee_id: number;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;


    

    @ManyToOne(() => Fee)
    @JoinColumn({
        name: 'fee_id',
        referencedColumnName: 'id',
    })
    fee?: Fee;

    

}

//sql mysql
// create table restaurant_costs_incurred
// (
//     id            int auto_increment
//         primary key,
//     name          varchar(255)                             not null,
//     description   varchar(255)                             not null,
//     price         int                                      not null,
//     is_active     tinyint(1) default 1                 not null,
//     created_at    datetime(6) default CURRENT_TIMESTAMP(6) not null,
//     updated_at    datetime(6) default CURRENT_TIMESTAMP(6) not null on update CURRENT_TIMESTAMP(6),
//     restaurant_id int unsigned                             null,
//     constraint FK_RESTAURANT_COSTS_INCURRED_RESTAURANT
//         foreign key (restaurant_id) references restaurants (id)
//             on update cascade on delete cascade
// );
/* create index restaurant_id
    on restaurant_costs_incurred (restaurant_id); */