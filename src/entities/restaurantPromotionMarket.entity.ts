import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { PromoMarketsRestaurants } from './promoMarketsRestaurants.entity';
import { Promotion } from './promotion.entity';
import { PromoMarketsPromotions } from './promoMarketsPromotion.entity';

@Entity('restaurant_promotion_market')
export class RestaurantPromotionMarket {
    @PrimaryGeneratedColumn({ type: 'integer' })
    id: number;

    @Column({ type: 'varchar' })
    title: string;

    @Column({ type: 'varchar' })
    subtitle: string;

    @Column({ type: 'varchar' })
    description: string;

    @Column({ type: 'tinyint' })
    is_active: number;

    @Column({ type: 'timestamp' })
    active_from: Date;

    @Column({ type: 'timestamp' })
    active_to: Date;

    @Column({ type: 'varchar' })
    image: string;

    @Column({ type: 'tinyint', default: 1 })
    version: number;

    @CreateDateColumn({ type: 'timestamp' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp' })
    updated_at: Date;

    @ManyToMany(() => Promotion)
    @JoinTable({
        name: 'promo_markets_promotions',
        joinColumn: {
            name: 'promo_market_id',
        },
        inverseJoinColumn: {
            name: 'promotion_id',
        },
    })
    promotions: Promotion[];

    @OneToMany(() => PromoMarketsPromotions, (marketPromotion) => marketPromotion.promoMarket)
    @JoinColumn({ name: 'id', referencedColumnName: 'promo_market_id' })
    marketPromotions: PromoMarketsPromotions[];

    @OneToMany(() => PromoMarketsRestaurants, (marketManagement) => marketManagement.promotionMarket)
    @JoinColumn({ name: 'id', referencedColumnName: 'promo_market_id' })
    marketManagements: PromoMarketsRestaurants[];
}
