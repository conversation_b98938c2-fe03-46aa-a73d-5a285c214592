import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum EFraudDetectionCategoryCode {
    // delivery finish order before delivered to customer
    GO_TO_PICKUP_LOCATION = 'GO_TO_PICKUP_LOCATION',
    GO_TO_DROP_OFF_LOCATION = 'GO_TO_DROP_OFF_LOCATION',
}

export interface IFraudDetectionStatusChangeShortTime {
    // max_time: number; // desc: max time (seconds) from shipping to pickup
    max_radius: number; // desc: max distance (meters) from shipping to pickup
}

@Entity('fraud_detection_categories')
export class FraudDetectionCategory {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
    })
    name: string;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    description: string;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.TRUE,
    })
    is_active: TinyInt;

    @Column({
        type: 'varchar',
        length: 60,
        unique: true,
        nullable: false,
    })
    code: EFraudDetectionCategoryCode;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    policy: IFraudDetectionStatusChangeShortTime;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
