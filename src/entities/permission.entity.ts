import { Column, CreateDate<PERSON>olumn, DeleteDate<PERSON>olumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('permissions')
export class Permission {
    constructor(name: string) {
        this.created_at = new Date();
        this.updated_at = new Date();
        this.guard_name = 'web';
        this.name = name;
    }
    @PrimaryGeneratedColumn()
    id: string;

    @Column()
    name: string;

    @Column()
    guard_name: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;
}

export enum PermissionsAccessAction {
    BANNER_FIND_LIST = 'adsBanner.index',
    BANNER_FIND_ONE = 'adsBanner.show',
    BANNER_UPDATE = 'adsBanner.update',
    BANNER_REMOVE = 'adsBanner.destroy',
    BANNER_CREATE = 'adsBanner.create',

    EXTRA_GROUP_ITEM_FIND_LIST = 'extraGroupItem.index',
    EXTRA_GROUP_ITEM_FIND_ONE = 'extraGroupItem.show',
    EXTRA_GROUP_ITEM_UPDATE = 'extraGroupItem.update',
    EXTRA_GROUP_ITEM_REMOVE = 'extraGroupItem.destroy',
    EXTRA_GROUP_ITEM_CREATE = 'extraGroupItem.create',

    EXTRA_GROUP_FIND_LIST = 'extraGroup.index',
    EXTRA_GROUP_FIND_ONE = 'extraGroup.show',
    EXTRA_GROUP_UPDATE = 'extraGroup.update',
    EXTRA_GROUP_REMOVE = 'extraGroup.destroy',
    EXTRA_GROUP_CREATE = 'extraGroup.create',

    PERMISSION_FIND_LIST = 'permissions.index',
    PERMISSION_FIND_ONE = 'permissions.show',
    PERMISSION_UPDATE = 'permissions.update',
    PERMISSION_REMOVE = 'permissions.destroy',
    PERMISSION_CREATE = 'permissions.create',

    ORDER_FIND_LIST = 'orders.index',
    ORDER_FIND_ONE = 'orders.show',
    ORDER_UPDATE = 'orders.update',
    ORDER_REMOVE = 'orders.destroy',
    ORDER_CREATE = 'orders.create',

    ORDER_DRIVER_EXPENSE_FIND_LIST = 'order-driver-expense.index',
    ORDER_DRIVER_EXPENSE_EXPORT = 'order-driver-expense.export',
    ORDER_DRIVER_EXPENSE_UPDATE = 'order-driver-expense.update',
    ORDER_DRIVER_EXPENSE_REMOVE = 'order-driver-expense.destroy',
    ORDER_DRIVER_EXPENSE_CREATE = 'order-driver-expense.create',

    BASIC_ORDER_FIND_LIST = 'basicOrder.index',

    ORDER_CHANGED_HISTORY_LIST = 'order-changed-history.index',
    ORDER_CHANGED_HISTORY_SHOW = 'order-changed-history.show',

    COLLECTION_FIND_LIST = 'collections.index',
    COLLECTION_FIND_ONE = 'collections.show',
    COLLECTION_UPDATE = 'collections.update',
    COLLECTION_REMOVE = 'collections.destroy',
    COLLECTION_CREATE = 'collections.create',

    FAQ_FIND_LIST = 'faqs.index',
    FAQ_FIND_ONE = 'faqs.show',
    FAQ_UPDATE = 'faqs.update',
    FAQ_REMOVE = 'faqs.destroy',
    FAQ_CREATE = 'faqs.create',

    EXTRA_FIND_LIST = 'extras.index',
    EXTRA_FIND_ONE = 'extras.show',
    EXTRA_UPDATE = 'extras.update',
    EXTRA_REMOVE = 'extras.destroy',
    EXTRA_CREATE = 'extras.create',

    FOOD_FIND_LIST = 'foods.index',
    FOOD_FIND_ONE = 'foods.show',
    FOOD_UPDATE = 'foods.update',
    FOOD_REMOVE = 'foods.destroy',
    FOOD_CREATE = 'foods.create',

    FOOD_REVIEW_FIND_LIST = 'foodReviews.index',
    FOOD_REVIEW_FIND_ONE = 'foodReviews.show',
    FOOD_REVIEW_UPDATE = 'foodReviews.update',
    FOOD_REVIEW_REMOVE = 'foodReviews.destroy',
    FOOD_REVIEW_CREATE = 'foodReviews.create',

    ORDER_STATUS_FIND_LIST = 'orderStatuses.index',
    ORDER_STATUS_FIND_ONE = 'orderStatuses.show',
    ORDER_STATUS_UPDATE = 'orderStatuses.update',
    ORDER_STATUS_REMOVE = 'orderStatuses.destroy',
    ORDER_STATUS_CREATE = 'orderStatuses.create',

    FAQ_CATEGORY_FIND_LIST = 'faqCategories.index',
    FAQ_CATEGORY_FIND_ONE = 'faqCategories.show',
    FAQ_CATEGORY_UPDATE = 'faqCategories.update',
    FAQ_CATEGORY_REMOVE = 'faqCategories.destroy',
    FAQ_CATEGORY_CREATE = 'faqCategories.create',

    CATEGORY_FIND_LIST = 'categories.index',
    CATEGORY_FIND_ONE = 'categories.show',
    CATEGORY_UPDATE = 'categories.update',
    CATEGORY_REMOVE = 'categories.destroy',
    CATEGORY_CREATE = 'categories.create',

    RESTAURANT_FIND_LIST = 'restaurants.index',
    RESTAURANT_FIND_ONE = 'restaurants.show',
    RESTAURANT_UPDATE = 'restaurants.update',
    RESTAURANT_UPDATE_ONTOP = 'restaurants.update-ontop',
    RESTAURANT_REMOVE = 'restaurants.destroy',
    RESTAURANT_CREATE = 'restaurants.create',
    RESTAURANT_EXPORT_SALE_REVENUE = 'restaurants.export-sale-revenue',

    RESTAURANT_REVIEW_FIND_LIST = 'restaurantReviews.index',
    RESTAURANT_REVIEW_FIND_ONE = 'restaurantReviews.show',
    RESTAURANT_REVIEW_UPDATE = 'restaurantReviews.update',
    RESTAURANT_REVIEW_REMOVE = 'restaurantReviews.destroy',
    RESTAURANT_REVIEW_CREATE = 'restaurantReviews.create',

    APP_SETTINGS_FIND_LIST = 'app-settings.index',
    APP_SETTINGS_FIND_ONE = 'app-settings.show',
    APP_SETTINGS_FIND_ONE_SUPPORT_CONTACT = 'app-settings.show-support-contact',
    APP_SETTINGS_UPDATE_SUPPORT_CONTACT = 'app-settings.update-support-contact',
    APP_SETTINGS_FIND_ONE_DEFAULT_TAX = 'app-settings.show-default-tax',
    APP_SETTING_UPDATE_DEFAULT_TAX = 'app-settings.update-default-tax',
    APP_SETTINGS_UPDATE = 'app-settings.update',
    APP_SETTINGS_REMOVE = 'app-settings.destroy',
    APP_SETTINGS_CREATE = 'app-settings.create',
    APP_SETTINGS_GET_TRADE_DISCOUNT = 'app-settings.get-trade-discount',

    USER_FIND_LIST = 'users.index',
    USER_FIND_DRIVER_LIST = 'users.driver-index',
    USER_FIND_ONE = 'users.show',
    USER_FIND_ONE_DRIVER = 'users.show-driver',
    USER_UPDATE = 'users.update',
    USER_UPDATE_DRIVER = 'users.update-driver',
    USER_UPDATE_DRIVER_RANKING = 'users.update-driver-ranking',
    USER_EXPORT_DRIVER_BANK_ACCOUNT = 'users.export_driver_bank_account',
    USER_EXPORT_DRIVER_INFO = 'users.export_driver_info',
    USER_REMOVE = 'users.destroy',
    USER_CREATE = 'users.create',
    USER_CREATE_DRIVER = 'users.create-driver',
    USER_CREATE_MANAGER = 'users.create-manager',
    USER_FIND_LIST_ADMIN = 'users.index-admin',
    USER_UPDATE_ADMIN = 'users.update-admin',
    USER_CREATE_ADMIN = 'users.create-admin',

    // DRIVER_FIND_LIST = 'drivers.index',
    // DRIVER_FIND_ONE = 'drivers.show',
    // DRIVER_UPDATE = 'drivers.update',

    ROLE_FIND_LIST = 'roles.index',
    ROLE_FIND_ONE = 'roles.show',
    ROLE_UPDATE = 'roles.update',
    ROLE_REMOVE = 'roles.destroy',
    ROLE_CREATE = 'roles.create',

    GALLERY_FIND_LIST = 'galleries.index',
    GALLERY_FIND_ONE = 'galleries.show',
    GALLERY_UPDATE = 'galleries.update',
    GALLERY_REMOVE = 'galleries.destroy',
    GALLERY_CREATE = 'galleries.create',

    DELIVERY_ADDRESS_FIND_LIST = 'deliveryAddresses.index',
    DELIVERY_ADDRESS_FIND_ONE = 'deliveryAddresses.show',
    DELIVERY_ADDRESS_UPDATE = 'deliveryAddresses.update',
    DELIVERY_ADDRESS_REMOVE = 'deliveryAddresses.destroy',
    DELIVERY_ADDRESS_CREATE = 'deliveryAddresses.create',

    SHIPPER_REVIEW_FIND_LIST = 'shipperReviews.index',
    SHIPPER_REVIEW_FIND_ONE = 'shipperReviews.show',
    SHIPPER_REVIEW_UPDATE = 'shipperReviews.update',
    SHIPPER_REVIEW_REMOVE = 'shipperReviews.destroy',
    SHIPPER_REVIEW_CREATE = 'shipperReviews.create',

    TRANSACTION_FIND_LIST = 'transactions.index',
    TRANSACTION_FIND_ONE = 'transactions.show',
    TRANSACTION_UPDATE = 'transactions.update',
    TRANSACTION_REMOVE = 'transactions.destroy',
    TRANSACTION_CREATE = 'transactions.create',

    PROMOTION_FIND_LIST = 'promotions.index',
    PROMOTION_FIND_ONE = 'promotions.show',
    PROMOTION_UPDATE = 'promotions.update',
    PROMOTION_REMOVE = 'promotions.destroy',
    PROMOTION_CREATE = 'promotions.create',

    NOTIFICATION_PLAN_FIND_LIST = 'notification-plans.index',
    NOTIFICATION_PLAN_FIND_ONE = 'notification-plans.show',
    NOTIFICATION_PLAN_UPDATE = 'notification-plans.update',
    NOTIFICATION_PLAN_REMOVE = 'notification-plans.destroy',
    NOTIFICATION_PLAN_CREATE = 'notification-plans.create',

    RESTAURANT_SHIPPER_COMPLAINT_FIND_LIST = 'restaurant-shipper-complaints.index',
    RESTAURANT_SHIPPER_COMPLAINT_FIND_ONE = 'restaurant-shipper-complaints.show',
    RESTAURANT_SHIPPER_COMPLAINT_UPDATE = 'restaurant-shipper-complaints.update',
    RESTAURANT_SHIPPER_COMPLAINT_REMOVE = 'restaurant-shipper-complaints.destroy',
    RESTAURANT_SHIPPER_COMPLAINT_CREATE = 'restaurant-shipper-complaints.create',

    WALLET_FIND_LIST = 'wallets.index',
    WALLET_FIND_ONE = 'wallets.show',
    WALLET_UPDATE = 'wallets.update',
    WALLET_REMOVE = 'wallets.destroy',
    WALLET_CREATE = 'wallets.create',

    PAYMENT_METHOD_FIND_LIST = 'payment-methods.index',
    PAYMENT_METHOD_FIND_ONE = 'payment-methods.show',
    PAYMENT_METHOD_UPDATE = 'payment-methods.update',
    PAYMENT_METHOD_REMOVE = 'payment-methods.destroy',
    PAYMENT_METHOD_CREATE = 'payment-methods.create',

    CARD_FIND_LIST = 'cards.index',
    CARD_FIND_ONE = 'cards.show',
    CARD_UPDATE = 'cards.update',
    CARD_REMOVE = 'cards.destroy',
    CARD_CREATE = 'cards.create',

    FRAME_FIND_LIST = 'frames.index',
    FRAME_FIND_ONE = 'frames.show',
    FRAME_UPDATE = 'frames.update',
    FRAME_REMOVE = 'frames.destroy',
    FRAME_CREATE = 'frames.create',

    WEEKLY_SHIPPER_TIMEKEEPING_FIND_LIST = 'weekly-shipper-timekeepings.index',
    WEEKLY_SHIPPER_TIMEKEEPING_FIND_ONE = 'weekly-shipper-timekeepings.show',
    WEEKLY_SHIPPER_TIMEKEEPING_UPDATE = 'weekly-shipper-timekeepings.update',
    WEEKLY_SHIPPER_TIMEKEEPING_REMOVE = 'weekly-shipper-timekeepings.destroy',
    WEEKLY_SHIPPER_TIMEKEEPING_CREATE = 'weekly-shipper-timekeepings.create',

    FOOD_MENU_FIND_LIST = 'food-menu.index',
    FOOD_MENU_FIND_ONE = 'food-menu.show',
    FOOD_MENU_UPDATE = 'food-menu.update',
    FOOD_MENU_REMOVE = 'food-menu.destroy',
    FOOD_MENU_CREATE = 'food-menu.create',

    POPUP_FIND_LIST = 'popup.index',
    POPUP_FIND_ONE = 'popup.show',
    POPUP_UPDATE = 'popup.update',
    POPUP_REMOVE = 'popup.destroy',
    POPUP_CREATE = 'popup.create',

    DRIVER_REPORT_TYPE_FIND_LIST = 'driver-report-type.index',
    DRIVER_REPORT_TYPE_FIND_ONE = 'driver-report-type.show',
    DRIVER_REPORT_TYPE_UPDATE = 'driver-report-type.update',
    DRIVER_REPORT_TYPE_REMOVE = 'driver-report-type.destroy',
    DRIVER_REPORT_TYPE_CREATE = 'driver-report-type.create',

    DRIVER_REPORT_FIND_LIST = 'driver-report.index',
    DRIVER_REPORT_FIND_ONE = 'driver-report.show',
    DRIVER_REPORT_UPDATE = 'driver-report.update',
    DRIVER_REPORT_REMOVE = 'driver-report.destroy',
    DRIVER_REPORT_CREATE = 'driver-report.create',

    SHIFT_WORK_FIND_LIST = 'shift-work.index',
    SHIFT_WORK_FIND_ONE = 'shift-work.show',
    SHIFT_WORK_UPDATE = 'shift-work.update',
    SHIFT_WORK_REMOVE = 'shift-work.destroy',
    SHIFT_WORK_CREATE = 'shift-work.create',

    MERCHANT_WALLET_FIND_LIST = 'merchant-wallet.index',
    MERCHANT_WALLET_FIND_ONE = 'merchant-wallet.show',
    MERCHANT_WALLET_UPDATE = 'merchant-wallet.update',
    MERCHANT_WALLET_CREATE = 'merchant-wallet.create',

    MERCHANT_TRANSACTION_FIND_LIST = 'merchant-transaction.index',
    MERCHANT_TRANSACTION_FIND_ONE = 'merchant-transaction.show',
    MERCHANT_TRANSACTION_UPDATE = 'merchant-transaction.update',
    MERCHANT_TRANSACTION_CREATE = 'merchant-transaction.create',

    MERCHANT_ACCOUNT_FIND_LIST = 'merchant-account.index',
    MERCHANT_ACCOUNT_FIND_ONE = 'merchant-account.show',
    MERCHANT_ACCOUNT_UPDATE = 'merchant-account.update',
    MERCHANT_ACCOUNT_CREATE = 'merchant-account.create',
    MERCHANT_ACCOUNT_DELETE = 'merchant-account.delete',

    MERCHANT_REGISTRATION_FIND_LIST = 'merchant-registration.index',
    MERCHANT_REGISTRATION_FIND_ONE = 'merchant-registration.show',
    MERCHANT_REGISTRATION_UPDATE = 'merchant-registration.update',
    MERCHANT_REGISTRATION_DELETE = 'merchant-registration.delete',

    LOYALTY_FIND_LIST = 'loyalty.index',
    LOYALTY_FIND_ONE = 'loyalty.show',
    LOYALTY_UPDATE = 'loyalty.update',
    LOYALTY_REMOVE = 'loyalty.destroy',
    LOYALTY_CREATE = 'loyalty.create',

    RESTAURANT_ADS_FIND_LIST = 'restaurant-ads.index',
    RESTAURANT_ADS_FIND_ONE = 'restaurant-ads.show',
    RESTAURANT_ADS_UPDATE = 'restaurant-ads.update',
    RESTAURANT_ADS_REMOVE = 'restaurant-ads.destroy',
    RESTAURANT_ADS_CREATE = 'restaurant-ads.create',

    RESTAURANT_PAYMENT_METHODS_INDEX = 'restaurants.index',
    RESTAURANT_PAYMENT_METHODS_UPDATE = 'restaurants.update',

    REFUND_LOGS_FIND_ONE = 'refund-logs.show',

    PROMO_MARKET_FIND_LIST = 'promo-market.index',
    PROMO_MARKET_FIND_ONE = 'promo-market.show',
    PROMO_MARKET_UPDATE = 'promo-market.update',
    PROMO_MARKET_REMOVE = 'promo-market.destroy',
    PROMO_MARKET_CREATE = 'promo-market.create',

    VILL_ADS_FIND_LIST = 'vill-ads.index',
    VILL_ADS_FIND_ONE = 'vill-ads.show',
    VILL_ADS_UPDATE = 'vill-ads.update',
    VILL_ADS_REMOVE = 'vill-ads.destroy',
    VILL_ADS_CREATE = 'vill-ads.create',

    ADS_KEYWORD_FIND_LIST = 'adsKeyword.index',
    ADS_KEYWORD_FIND_ONE = 'adsKeyword.show',
    ADS_KEYWORD_UPDATE = 'adsKeyword.update',
    ADS_KEYWORD_REMOVE = 'adsKeyword.destroy',
    ADS_KEYWORD_CREATE = 'adsKeyword.create',

    FACE_RECOGNITION_FIND_LIST = 'face-recognition.index',
    FACE_RECOGNITION_FIND_ONE = 'face-recognition.show',
    FACE_RECOGNITION_UPDATE = 'face-recognition.update',
    FACE_RECOGNITION_REMOVE = 'face-recognition.destroy',
    FACE_RECOGNITION_CREATE = 'face-recognition.create',

    FACE_RECOGNITION_ENROLLMENT_FIND_LIST = 'face-recognition-enrollment.index',
    FACE_RECOGNITION_ENROLLMENT_FIND_ONE = 'face-recognition-enrollment.show',
    FACE_RECOGNITION_ENROLLMENT_UPDATE = 'face-recognition-enrollment.update',
    FACE_RECOGNITION_ENROLLMENT_REMOVE = 'face-recognition-enrollment.destroy',
    FACE_RECOGNITION_ENROLLMENT_CREATE = 'face-recognition-enrollment.create',

    FACE_RECOGNITION_ENROLLMENT_TYPE_FIND_LIST = 'face-recognition-enrollment-type.index',
    FACE_RECOGNITION_ENROLLMENT_TYPE_FIND_ONE = 'face-recognition-enrollment-type.show',
    FACE_RECOGNITION_ENROLLMENT_TYPE_UPDATE = 'face-recognition-enrollment-type.update',
    FACE_RECOGNITION_ENROLLMENT_TYPE_REMOVE = 'face-recognition-enrollment-type.destroy',
    FACE_RECOGNITION_ENROLLMENT_TYPE_CREATE = 'face-recognition-enrollment-type.create',

    DRIVER_DISCIPLINE_FIND_LIST = 'driver-discipline.index',
    DRIVER_DISCIPLINE_FIND_ONE = 'driver-discipline.show',
    DRIVER_DISCIPLINE_UPDATE = 'driver-discipline.update',
    DRIVER_DISCIPLINE_EXPORT = 'driver-discipline.export',

    FRAUD_CATEGORY_FIND_LIST = 'fraud-category.index',
    FRAUD_CATEGORY_FIND_ONE = 'fraud-category.show',
    FRAUD_CATEGORY_UPDATE = 'fraud-category.update',

    FRAUD_DETECTION_REPORT_FIND_LIST = 'fraud-detection-report.index',
    FRAUD_DETECTION_REPORT_FIND_ONE = 'fraud-detection-report.show',
    FRAUD_DETECTION_REPORT_UPDATE = 'fraud-detection-report.update',

    BATCH_JOBS_FIND_LIST = 'batch-jobs.index',
    BATCH_JOBS_FIND_ONE = 'batch-jobs.show',
    BATCH_JOBS_CREATE = 'batch-jobs.create',

    USER_ACTIVITY_FIND_LIST = 'user-activity.index',
    USER_ACTIVITY_FIND_ONE = 'user-activity.show',

    CHAT_FIND_LIST = 'chat.index',
    CHAT_FIND_ONE = 'chat.show',
    CHAT_UPDATE = 'chat.update',

    TESTER = 'developer.tester',

    MERCHANT_DEVICE_FIND_LIST = 'merchant-device.index',
    MERCHANT_DEVICE_UPDATE = 'merchant-device.update',
    MERCHANT_DEVICE_REMOVE = 'merchant-device.destroy',

    RESTAURANT_SHEET_STATEMENT_FIND_LIST = 'insight.restaurant-sheet-statement.index',
    RESTAURANT_SHEET_STATEMENT_FIND_ONE = 'insight.restaurant-sheet-statement.show',
    RESTAURANT_SHEET_STATEMENT_DOWNLOAD = 'insight.restaurant-sheet-statement.download',
    RESTAURANT_SHEET_STATEMENT_MANAGER_FILE = 'insight.restaurant-sheet-statement.manager-file',

    DRIVER_REGISTRATION_FIND_LIST = 'driver-registration.index',
    DRIVER_REGISTRATION_FIND_ONE = 'driver-registration.show',
    DRIVER_REGISTRATION_UPDATE = 'driver-registration.update',

    ORDER_CANCELLATION_TYPE_FIND_LIST = 'order-cancellation-types.index',
    ORDER_CANCELLATION_TYPE_FIND_ONE = 'order-cancellation-types.show',
    ORDER_CANCELLATION_TYPE_UPDATE = 'order-cancellation-types.update',

    USER_STATISTIC_FIND_LIST = 'user-statistic.index',

    USER_OTP_FIND_LIST = 'user-otp.index',
    MERCHANT_OTP_FIND_LIST = 'merchant-otp.index',
    MERCHANT_OTP_SEND_MANUAL = 'merchant-otp.send',
    MERCHANT_LOCATION_FIND = 'merchant-location.index',
    MERCHANT_LOCATION_UPDATE = 'merchant-location.update',

    DRIVER_NOTIFICATION_FIND_LIST = 'driver-notifications.index',
    DRIVER_NOTIFICATION_FIND_ONE = 'driver-notifications.show',
    DRIVER_NOTIFICATION_UPDATE = 'driver-notifications.update',
    DRIVER_NOTIFICATION_CREATE = 'driver-notifications.create',
    DRIVER_NOTIFICATION_REMOVE = 'driver-notifications.remove',

    MERCHANT_ACCOUNT_CHANGE_PHOTO = 'merchant-account.change-photo',

    MERCHANT_ROLE_FIND_LIST = 'merchant-roles.index',
    MERCHANT_ROLE_FIND_ONE = 'merchant-roles.show',
    MERCHANT_ROLE_UPDATE = 'merchant-roles.update',

    COMMENT_GET_LIST = 'comments.get-list',
    COMMENT_REPLY_GET_LIST = 'comments.reply.get-list',
    COMMENT_GET_FULL_DETAIL = 'comments.get-full-detail',
    COMMENT_DELETE = 'comments.delete',
    COMMENT_GET_POST_DETAIL = 'comments.get-post-detail',

    FEED_LIST = 'feeds.index',
    FEED_UPDATE = 'feeds.edit',
    FEED_CREATE = 'feeds.create',
    FEED_DELETE = 'feeds.delete',
    FEED_FIND_ONE = 'feeds.show',

    MERCHANT_CONTRACT_INDEX = 'merchant-contract.index',
    MERCHANT_CONTRACT_UPDATE = 'merchant-contract.update',
    MERCHANT_CONTRACT_CREATE = 'merchant-contract.create',
    MERCHANT_CONTRACT_DELETE = 'merchant-contract.delete',
    MERCHANT_CONTRACT_APPROVE = 'merchant-contract.approve',

    ADS_CONTRACT_DELETE = 'ads-contract.delete',
    ADS_CONTRACT_UPDATE = 'ads-contract.update',
    ADS_CONTRACT_APPROVE = 'ads-contract.approve',

    DRIVER_AGREEMENT_INDEX = 'driver-agreement.index',
    DRIVER_AGREEMENT_UPDATE = 'driver-agreement.update',
    DRIVER_AGREEMENT_CREATE = 'driver-agreement.create',
    DRIVER_AGREEMENT_DELETE = 'driver-agreement.delete',

    DRIVER_AGREEMENT_TYPE_CREATE = 'driver-agreement-type.create',
    DRIVER_AGREEMENT_TYPE_INDEX = 'driver-agreement-type.index',
    DRIVER_AGREEMENT_TYPE_DELETE = 'driver-agreement-type.delete',
    DRIVER_AGREEMENT_TYPE_UPDATE = 'driver-agreement-type.update',

    DRIVER_QRPAY_INDEX = 'driver-qrpay.index',
    DRIVER_QRPAY_UPDATE = 'driver-qrpay.update',
    DRIVER_QRPAY_CREATE = 'driver-qrpay.create',
    DRIVER_QRPAY_DELETE = 'driver-qrpay.delete',

    ORDER_STATISTIC_INDEX = 'order-statistic.index',
    ORDER_STATISTIC_UPDATE = 'order-statistic.update',
    ORDER_STATISTIC_CREATE = 'order-statistic.create',
    ORDER_STATISTIC_DELETE = 'order-statistic.delete',

    ARCHIVE_EXPORT_FILE_INDEX = 'archive-export-files.index',
    ARCHIVE_EXPORT_FILE_UPDATE = 'archive-export-files.update',
    ARCHIVE_EXPORT_FILE_CREATE = 'archive-export-files.create',
    ARCHIVE_EXPORT_FILE_DELETE = 'archive-export-files.delete',

    ARCHIVE_HISTORY_INDEX = 'archive-histories.index',
    ARCHIVE_HISTORY_UPDATE = 'archive-histories.update',
    ARCHIVE_HISTORY_CREATE = 'archive-histories.create',

    ARCHIVE_ORDER_BOX_INDEX = 'archive-order-box.index',
    ARCHIVE_ORDER_BOX_UPDATE = 'archive-order-box.update',
    ARCHIVE_ORDER_BOX_CREATE = 'archive-order-box.create',

    ARCHIVE_BATCH_JOB_INDEX = 'archive-batch-jobs.index',
    ARCHIVE_BATCH_JOB_UPDATE = 'archive-batch-jobs.update',
    ARCHIVE_BATCH_JOB_CREATE = 'archive-batch-jobs.create',

    ARCHIVE_ORDER_BOX_ORDERS_INDEX = 'archive-order-box-orders.index',
    ARCHIVE_ORDER_BOX_ORDERS_UPDATE = 'archive-order-box-orders.update',
    ARCHIVE_ORDER_BOX_ORDERS_CREATE = 'archive-order-box-orders.create',

    ARCHIVE_CONFIGURATIONS_INDEX = 'archive-configurations.index',
    ARCHIVE_CONFIGURATIONS_UPDATE = 'archive-configurations.update',
    ARCHIVE_CONFIGURATIONS_CREATE = 'archive-configurations.create',

    MIGRATION_DB_EXPORT_CONFIGURATION_INDEX = 'migration-db-export-configurations.index',
    MIGRATION_DB_EXPORT_CONFIGURATION_UPDATE = 'migration-db-export-configurations.update',
    MIGRATION_DB_EXPORT_CONFIGURATION_CREATE = 'migration-db-export-configurations.create',

    TAX_REVENUE_INDEX = 'tax-revenue.index',
    TAX_REVENUE_FIND_ONE = 'tax-revenue.show',
    TAX_REVENUE_UPDATE = 'tax-revenue.update',
    TAX_REVENUE_CREATE = 'tax-revenue.create',
    TAX_REVENUE_DELETE = 'tax-revenue.delete',
    TAX_REVENUE_DOWNLOAD = 'tax-revenue.download',
    TAX_REVENUE_SEND_MAIL = 'tax-revenue.send-mail',
    ARCHIVE_DB_EXPORT_CONFIGURATION_INDEX = 'archive-db-export-configurations.index',
    ARCHIVE_DB_EXPORT_CONFIGURATION_UPDATE = 'archive-db-export-configurations.update',
    ARCHIVE_DB_EXPORT_CONFIGURATION_CREATE = 'archive-db-export-configurations.create',

    RESTAURANT_TRADE_DISCOUNT_UPDATE = 'restaurants.trade-discount-update',

    FEE_FIND_LIST = 'fee.index',
    FEE_FIND_ONE = 'fee.show',
    FEE_UPDATE = 'fee.update',
    FEE_REMOVE = 'fee.destroy',
    FEE_CREATE = 'fee.create',

    SELLER_REVIEW_FIND_LIST = 'seller_reviews.index',
    SELLER_REVIEW_FIND_ONE = 'seller_reviews.show',
    SELLER_REVIEW_UPDATE = 'seller_reviews.update',
    SELLER_REVIEW_DELETE = 'seller_reviews.delete',
    SELLER_REVIEW_CREATE = 'seller_reviews.create',

    USER_ORDERS_RATING_INDEX = 'user-orders-rating.index',
    USER_ORDERS_RATING_DELETE = 'user-orders-rating.delete',

    TRACKING_TRANSACTION_BANKING_INDEX = 'tracking-transaction-banking.index',
    TRACKING_TRANSACTION_BANKING_CURRENT_BALANCE = 'tracking-transaction-banking.current-balance',
    TRACKING_TRANSACTION_BANKING_UPDATE = 'tracking-transaction-banking.update',
    TRACKING_TRANSACTION_BANKING_CREATE = 'tracking-transaction-banking.create',
    TRACKING_TRANSACTION_BANKING_DELETE = 'tracking-transaction-banking.delete',

    DRIVER_REWARD_INDEX = 'driver-reward.index',
    DRIVER_REWARD_UPDATE = 'driver-reward.update',
    DRIVER_REWARD_CREATE = 'driver-reward.create',
    DRIVER_REWARD_DELETE = 'driver-reward.delete',
    DRIVER_REWARD_FIND_LIST = 'driver-reward.show',
    DRIVER_REWARD_EXPORT = 'driver-reward.export',

    DRIVER_TAX_REPORT_INDEX = 'driver-tax-report.index',
    DRIVER_TAX_REPORT_UPDATE = 'driver-tax-report.update',
    DRIVER_TAX_REPORT_CREATE = 'driver-tax-report.create',
    DRIVER_TAX_REPORT_DELETE = 'driver-tax-report.delete',

    DRIVER_TAX_FINALIZATION_EVENT_INDEX = 'driver-tax-finalization-event.index',
    DRIVER_TAX_FINALIZATION_EVENT_UPDATE = 'driver-tax-finalization-event.update',
    DRIVER_TAX_FINALIZATION_EVENT_CREATE = 'driver-tax-finalization-event.create',
    DRIVER_TAX_FINALIZATION_EVENT_DELETE = 'driver-tax-finalization-event.delete',

    DRIVER_TAX_FINALIZATION_INDEX = 'driver-tax-finalization.index',
    DRIVER_TAX_FINALIZATION_UPDATE = 'driver-tax-finalization.update',
    DRIVER_TAX_FINALIZATION_CREATE = 'driver-tax-finalization.create',
    DRIVER_TAX_FINALIZATION_DELETE = 'driver-tax-finalization.delete',

    TAX_DRIVER_PERIOD_INDEX = 'tax-driver-period.index',
    TAX_DRIVER_PERIOD_UPDATE = 'tax-driver-period.update',
    TAX_DRIVER_PERIOD_CREATE = 'tax-driver-period.create',
    TAX_DRIVER_PERIOD_DELETE = 'tax-driver-period.delete',

    RESTAURANT_TAX_FIND_LIST = 'restaurant-tax.index',
    RESTAURANT_TAX_FIND_ONE = 'restaurant-tax.show',
    RESTAURANT_TAX_UPDATE = 'restaurant-tax.update',
    RESTAURANT_TAX_CREATE = 'restaurant-tax.create',
    RESTAURANT_TAX_DELETE = 'restaurant-tax.delete',

    // INVOICE_FIND_LIST = 'invoices.index',
    // INVOICE_FIND_ONE = 'invoices.show',
    // INVOICE_UPDATE = 'invoices.update',
    // INVOICE_CREATE = 'invoices.create',
    // INVOICE_DELETE = 'invoices.delete',
    // INVOICE_EXPORT = 'invoices.export',

    MERCHANT_NOTIFICATION_PLAN_FIND_LIST = 'merchant-notification-plan.index',
    MERCHANT_NOTIFICATION_PLAN_FIND_ONE = 'merchant-notification-plan.show',
    MERCHANT_NOTIFICATION_PLAN_UPDATE = 'merchant-notification-plan.update',
    MERCHANT_NOTIFICATION_PLAN_CREATE = 'merchant-notification-plan.create',
    MERCHANT_NOTIFICATION_PLAN_DELETE = 'merchant-notification-plan.delete',

    DYNAMIC_LINK_FIND_LIST = 'dynamic-link.index',
    DYNAMIC_LINK_FIND_ONE = 'dynamic-link.show',
    DYNAMIC_LINK_UPDATE = 'dynamic-link.update',
    DYNAMIC_LINK_CREATE = 'dynamic-link.create',
    DYNAMIC_LINK_DELETE = 'dynamic-link.delete',

    DYNAMIC_LINK_DOMAIN_FIND_LIST = 'dynamic-link-domain.index',
    DYNAMIC_LINK_DOMAIN_FIND_ONE = 'dynamic-link-domain.show',
    DYNAMIC_LINK_DOMAIN_UPDATE = 'dynamic-link-domain.update',
    DYNAMIC_LINK_DOMAIN_CREATE = 'dynamic-link-domain.create',
    DYNAMIC_LINK_DOMAIN_DELETE = 'dynamic-link-domain.delete',

    DYNAMIC_LINK_METADATA_FIND_LIST = 'dynamic-link-metadata.index',
    DYNAMIC_LINK_APP_FIND_LIST = 'dynamic-link-app.index',
    DYNAMIC_LINK_APP_FIND_ONE = 'dynamic-link-app.show',
    DYNAMIC_LINK_APP_UPDATE = 'dynamic-link-app.update',
    DYNAMIC_LINK_APP_CREATE = 'dynamic-link-app.create',
    DYNAMIC_LINK_APP_DELETE = 'dynamic-link-app.delete',

    DRIVER_MISSION_FIND_LIST = 'driver-mission.index',
    DRIVER_MISSION_FIND_ONE = 'driver-mission.show',
    DRIVER_MISSION_UPDATE = 'driver-mission.update',
    DRIVER_MISSION_CREATE = 'driver-mission.create',
    DRIVER_MISSION_DELETE = 'driver-mission.delete',

    DRIVER_NPAY_WALLET_LINK_FIND_ONE = 'npay-wallet.show',
    DRIVER_NPAY_WALLET_LINK_FIND_LIST = 'npay-wallet.index',
    DRIVER_NPAY_WALLET_LINK_FIND_UPDATE = 'npay-wallet.update',
    DRIVER_NPAY_WALLET_LINK_FIND_EXPORT = 'npay-wallet.export',

    COMPANY_REPORT_SUMMARY_GET = 'company-report.summary-get',

    // Scoring Rules Permissions
    SCORING_RULES_READ = 'scoring-rules.read',
    SCORING_RULES_CREATE = 'scoring-rules.create',
    SCORING_RULES_UPDATE = 'scoring-rules.update',
    SCORING_RULES_DELETE = 'scoring-rules.delete',
    SCORING_RULES_TEST = 'scoring-rules.test',
    SCORING_RULES_GLOBAL = 'scoring-rules.global',

    // Inventory Permissions
    INVENTORY_CATEGORY_FIND_LIST = 'inventory-category.index',
    INVENTORY_CATEGORY_FIND_ONE = 'inventory-category.show',
    INVENTORY_CATEGORY_CREATE = 'inventory-category.create',
    INVENTORY_CATEGORY_UPDATE = 'inventory-category.update',
    INVENTORY_CATEGORY_DELETE = 'inventory-category.delete',

    INVENTORY_PRODUCT_FIND_LIST = 'inventory-product.index',
    INVENTORY_PRODUCT_FIND_ONE = 'inventory-product.show',
    INVENTORY_PRODUCT_CREATE = 'inventory-product.create',
    INVENTORY_PRODUCT_UPDATE = 'inventory-product.update',
    INVENTORY_PRODUCT_DELETE = 'inventory-product.delete',

    INVENTORY_SUPPLIER_FIND_LIST = 'inventory-supplier.index',
    INVENTORY_SUPPLIER_FIND_ONE = 'inventory-supplier.show',
    INVENTORY_SUPPLIER_CREATE = 'inventory-supplier.create',
    INVENTORY_SUPPLIER_UPDATE = 'inventory-supplier.update',
    INVENTORY_SUPPLIER_DELETE = 'inventory-supplier.delete',

    INVENTORY_PURCHASE_ORDER_FIND_LIST = 'inventory-purchase-order.index',
    INVENTORY_PURCHASE_ORDER_FIND_ONE = 'inventory-purchase-order.show',
    INVENTORY_PURCHASE_ORDER_CREATE = 'inventory-purchase-order.create',
    INVENTORY_PURCHASE_ORDER_UPDATE = 'inventory-purchase-order.update',
    INVENTORY_PURCHASE_ORDER_DELETE = 'inventory-purchase-order.delete',

    INVENTORY_RETURN_PURCHASE_ORDER_FIND_LIST = 'inventory-return-purchase-order.index',
    INVENTORY_RETURN_PURCHASE_ORDER_FIND_ONE = 'inventory-return-purchase-order.show',
    INVENTORY_RETURN_PURCHASE_ORDER_CREATE = 'inventory-return-purchase-order.create',
    INVENTORY_RETURN_PURCHASE_ORDER_UPDATE = 'inventory-return-purchase-order.update',
    INVENTORY_RETURN_PURCHASE_ORDER_DELETE = 'inventory-return-purchase-order.delete',

    INVENTORY_TRANSFER_FIND_LIST = 'inventory-transfer.index',
    INVENTORY_TRANSFER_FIND_ONE = 'inventory-transfer.show',
    INVENTORY_TRANSFER_CREATE = 'inventory-transfer.create',
    INVENTORY_TRANSFER_UPDATE = 'inventory-transfer.update',
    INVENTORY_TRANSFER_DELETE = 'inventory-transfer.delete',

    INVENTORY_ORDER_FIND_LIST = 'inventory-order.index',
    INVENTORY_ORDER_FIND_ONE = 'inventory-order.show',
    INVENTORY_ORDER_CREATE = 'inventory-order.create',
    INVENTORY_ORDER_QUICK_CREATE = 'inventory-order.quick-create',
    INVENTORY_ORDER_ADVANCED_CREATE = 'inventory-order.advanced-create',
    INVENTORY_ORDER_BUNDLE_CREATE = 'inventory-order.bundle-create',
    INVENTORY_ORDER_UPDATE = 'inventory-order.update',
    INVENTORY_ORDER_DELETE = 'inventory-order.delete',
    INVENTORY_ORDER_INVOICE_CREATE = 'inventory-order.invoice-create',
    INVENTORY_ORDER_INVOICE_UPDATE_BUYER_INFO = 'inventory-order.invoice-update',

    INVENTORY_STOCK_TAKE_FIND_LIST = 'inventory-stock-take.index',
    INVENTORY_STOCK_TAKE_FIND_ONE = 'inventory-stock-take.show',
    INVENTORY_STOCK_TAKE_CREATE = 'inventory-stock-take.create',
    INVENTORY_STOCK_TAKE_UPDATE = 'inventory-stock-take.update',
    INVENTORY_STOCK_TAKE_DELETE = 'inventory-stock-take.delete',

    INVENTORY_DAMAGE_FIND_LIST = 'inventory-damage.index',
    INVENTORY_DAMAGE_FIND_ONE = 'inventory-damage.show',
    INVENTORY_DAMAGE_CREATE = 'inventory-damage.create',
    INVENTORY_DAMAGE_UPDATE = 'inventory-damage.update',
    INVENTORY_DAMAGE_DELETE = 'inventory-damage.delete',

    INVENTORY_PRODUCT_ON_HAND_FIND_LIST = 'inventory-product-on-hand.index',
    INVENTORY_PRODUCT_ON_HAND_FIND_ONE = 'inventory-product-on-hand.show',
    INVENTORY_PRODUCT_ON_HAND_UPDATE = 'inventory-product-on-hand.update',

    INVENTORY_CUSTOMER_FIND_LIST = 'inventory-customer.index',
    INVENTORY_CUSTOMER_FIND_ONE = 'inventory-customer.show',
    INVENTORY_CUSTOMER_CREATE = 'inventory-customer.create',
    INVENTORY_CUSTOMER_UPDATE = 'inventory-customer.update',
    INVENTORY_CUSTOMER_DELETE = 'inventory-customer.delete',

    INVENTORY_BANK_ACCOUNT_FIND_LIST = 'inventory-bank-account.index',
    INVENTORY_BANK_ACCOUNT_FIND_ONE = 'inventory-bank-account.show',
    INVENTORY_BANK_ACCOUNT_CREATE = 'inventory-bank-account.create',
    INVENTORY_BANK_ACCOUNT_UPDATE = 'inventory-bank-account.update',
    INVENTORY_BANK_ACCOUNT_DELETE = 'inventory-bank-account.delete',

    INVENTORY_TAX_FIND_LIST = 'inventory-tax.index',
    INVENTORY_TAX_FIND_ONE = 'inventory-tax.show',
    INVENTORY_TAX_CREATE = 'inventory-tax.create',
    INVENTORY_TAX_UPDATE = 'inventory-tax.update',
    INVENTORY_TAX_DELETE = 'inventory-tax.delete',

    INVENTORY_BUNDLE_FIND_LIST = 'inventory-bundle.index',
    INVENTORY_BUNDLE_FIND_ONE = 'inventory-bundle.show',
    INVENTORY_BUNDLE_CREATE = 'inventory-bundle.create',
    INVENTORY_BUNDLE_UPDATE = 'inventory-bundle.update',
    INVENTORY_BUNDLE_DELETE = 'inventory-bundle.delete',

    // Delivery Invoice Permissions
    DELIVERY_INVOICE_FIND_LIST = 'delivery-invoice.index',
    DELIVERY_INVOICE_FIND_ONE = 'delivery-invoice.show',
    DELIVERY_INVOICE_CREATE = 'delivery-invoice.create',
    DELIVERY_INVOICE_UPDATE = 'delivery-invoice.update',
    DELIVERY_INVOICE_DELETE = 'delivery-invoice.delete',
}
