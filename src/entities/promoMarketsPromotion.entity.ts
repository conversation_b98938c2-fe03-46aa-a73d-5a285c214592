import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { RestaurantPromotionMarket } from './restaurantPromotionMarket.entity';

@Entity('promo_markets_promotions')
export class PromoMarketsPromotions {
    @PrimaryColumn()
    promo_market_id: number;

    @PrimaryColumn()
    promotion_id: number;

    @ManyToOne(() => RestaurantPromotionMarket, (promoMarket) => promoMarket.marketPromotions)
    @JoinColumn({ name: 'promo_market_id', referencedColumnName: 'id' })
    promoMarket: PromoMarketsPromotions;
}
