import { RestaurantReviewReply } from './restaurantReviewReplies.entity';
import {
    Column,
    Entity,
    JoinTable,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    JoinColumn,
    ManyToOne,
    ManyToMany,
    OneToOne,
} from 'typeorm';
import * as _ from 'lodash';

import { User } from './user.entity';
import { Restaurant } from './restaurant.entity';
import { Order } from './order.entity';
import { ReviewTag } from './reviewTag.entity';

@Entity('restaurant_reviews')
export class RestaurantReview {
    constructor(partial: Partial<RestaurantReview>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review: string;

    @Column()
    rate: number;

    @Column()
    user_id: number;

    @Column()
    restaurant_id: number;

    @Column()
    order_id: number;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @Column()
    author_name: string;

    @Column({ type: 'simple-array' })
    images: string[];

    // @Column({ type: 'simple-array' })
    @Column('simple-json', { array: true })
    foods: IFood[];

    @Column()
    is_displayed: boolean;

    @Column()
    has_image: boolean;

    @Column()
    has_content: boolean;

    @Column()
    has_tag: boolean;

    @Column()
    is_review_sumary_counted: boolean;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;

    @OneToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order?: Order;

    @OneToOne(() => RestaurantReviewReply)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'review_id',
    })
    restaurant_reply: RestaurantReviewReply;

    @ManyToMany(() => ReviewTag)
    @JoinTable({
        name: 'restaurant_user_review_tags',
        joinColumn: {
            name: 'review_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'tag_id',
            referencedColumnName: 'id',
        },
    })
    tags: ReviewTag[];
}

export interface IFood {
    id: number;
    name: string;
}
