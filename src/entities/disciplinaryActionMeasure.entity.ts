import { <PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { DisciplinaryAction } from './disciplinaryAction.entity';
import { TinyInt } from 'src/common/constants';

@Entity('disciplinary_action_measures')
export class DisciplinaryActionMeasure {
    constructor(partial: Partial<DisciplinaryActionMeasure>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn({
        type: 'int',
    })
    id: number;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: 0,
    })
    is_suspension: TinyInt;

    @Column({
        type: 'int',
        nullable: false,
        unsigned: true,
        default: 0,
    })
    priority_subtractions: number;

    @Column({
        type: 'int',
        nullable: true,
        unsigned: true,
    })
    suspension_duration_in_minutes: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    action_id: number;

    @ManyToOne(() => DisciplinaryAction)
    @JoinColumn({
        name: 'action_id',
        referencedColumnName: 'id',
    })
    action?: DisciplinaryAction;
}
