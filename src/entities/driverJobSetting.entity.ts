import { TinyInt } from 'src/common/constants';
import { Column, En<PERSON><PERSON>, JoinColumn, OneToMany, OneToOne, PrimaryColumn } from 'typeorm';
import { Driver } from './driver.entity';
import { DriverDisciplinaryAction } from './driverDisciplinaryAction.entity';

@Entity('driver_job_settings')
export class DriverJobSetting {
    constructor(partial: Partial<DriverJobSetting>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn()
    driver_id: number;

    @Column({ type: 'tinyint', default: 0 })
    in_discipline: TinyInt;

    @Column({ type: 'timestamp', default: null })
    waiting_time: Date;

    @Column({ type: 'timestamp', default: null })
    waiting_time_in_discipline: Date;

    @Column({ type: 'tinyint', default: 0 })
    can_receiving_job: TinyInt;

    @Column({ type: 'tinyint', default: 1 })
    enabled_receiving_job: TinyInt;

    @Column({ type: 'tinyint', default: 0 })
    assignment_without_consent: TinyInt;

    @Column({ type: 'tinyint', default: 0 })
    enabled_priority: TinyInt;

    @Column({ type: 'tinyint', default: 0 })
    job_paused: TinyInt;

    @Column({ type: 'tinyint', default: 0 })
    stop_suspend_if_sufficient_balance: TinyInt;

    @Column({ type: 'varchar', length: 255 })
    message: string;

    @OneToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;

    @OneToMany(() => DriverDisciplinaryAction, (action) => action.driverJobSetting)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'driver_id',
    })
    driverDisciplinaryActions: DriverDisciplinaryAction[];
}
