
import { <PERSON>umn, CreateDateColumn, Entity, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Province } from './province.entity';
import { Restaurant } from './restaurant.entity';


@Entity('fees')
export class Fee {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    description: string;

    @Column()
    is_active: number;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();



    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    object_type: EFeeObjectType;


    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EFeeCode;
}


export enum EFeeObjectType {
    restaurant = 'restaurant',
    driver = 'driver',
    customer = 'customer',
    normal = 'normal',
}



export enum EFeeCode {
   parking_fee = 'parking_fee',
}

//insert fees
//insert into fees (name, description, is_active, object_type) values ('Parking Fee', 'Parking Fee', 1, 'restaurant');



//mysql 
// create table fees
// (
//     id            int auto_increment
//         primary key,
//     name          varchar(255)                             not null,
//     description   varchar(255)                             not null,
//     is_active     tinyint(1) default 1                 not null,
//     created_at    datetime(6) default CURRENT_TIMESTAMP(6) not null,
//     updated_at    datetime(6) default CURRENT_TIMESTAMP(6) not null,
//     object_type   enum ('restaurant', 'driver', 'customer', 'normal') not null
// );
