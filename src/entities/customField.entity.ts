import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('custom_fields')
export class Role {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false })
    name: string;

    @Column({ nullable: false })
    type: string;

    @Column()
    values: string;

    @Column()
    disabled: number;

    @Column()
    required: number;

    @Column()
    in_table: number;

    @Column()
    bootstrap_column: number;

    @Column()
    order: number;

    @Column()
    custom_field_model: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
