import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { Driver } from './driver.entity';

@Entity('driver_id_cards')
export class DriverIdCard {
    constructor(partial: Partial<DriverIdCard>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn({ type: 'int', width: 10, nullable: false, unique: true })
    driver_id: number;

    @Column({ type: 'varchar', width: 50, unique: true })
    id_card: string;

    @Column({ type: 'date', width: 50 })
    issue_date: string;

    @Column({ type: 'varchar', width: 50 })
    issue_by: string;

    @Column({ type: 'varchar', width: 50 })
    sex: EGender;

    @Column({ type: 'varchar', width: 50 })
    full_name: string;

    @Column({ type: 'date', nullable: false })
    date_of_birth: string;

    @Column({ type: 'varchar', width: 50, nullable: false })
    nationality: string;

    @Column({ type: 'text', nullable: false })
    place_of_origin: string;

    @Column({ type: 'text', nullable: false })
    place_of_residence: string;

    @Column({ type: 'date', nullable: false })
    date_of_expiry: string;

    @OneToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;

    //image_font
    @Column({ type: 'text', nullable: true })
    image_front: string;

    //image_back
    @Column({ type: 'text', nullable: true })
    image_back: string;

    // Số CCCD cũ (old ID card number)
    @Column({ type: 'varchar', width: 50, nullable: true })
    old_id_card: string;
}

export enum EGender {
    MALE = 'Nam',
    FEMALE = 'Nữ',
}
