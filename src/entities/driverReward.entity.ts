import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    Unique,
    UpdateDateColumn,
} from 'typeorm';
import { Driver, EDriverWalletProvider } from './driver.entity';
import { EVehicleType } from './vehicleType.entity';
import { DeliveryRewardType } from './deliveryRewardType.entity';
import { ECurrency } from 'src/common/types/currency.enum';
import { TaxReportingDriverPeriod } from './taxReportingDriverPeriod.entity';

@Unique('uq_driver_reward_driver_date_time', [
    'driver_id',
    'reward_id',
    'date',
    'month',
    'year',
])
@Entity()
export class DriverReward {
    constructor(partial: Partial<DriverReward>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn({
        type: 'bigint',
        unsigned: true,
    })
    id: string;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    reward_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    amount: number;

    @Column({
        type: 'json',
        nullable: true,
    })
    metadata: Record<string, any>;

    @Column({
        type: 'int',
        nullable: false,
    })
    personal_tax: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    net_amount: number; // amount after tax

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    currency: ECurrency;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: false,
    })
    description: string;

    // start for order_daily_reward only
    @Column({
        type: 'date',
        nullable: true,
        comment: 'for order_daily_reward only',
    })
    reward_date: string;

    @Column({
        type: 'int',
        nullable: false,
    })
    point: number;

    @Column({
        type: 'json',
        nullable: false,
    })
    exchange_details: IPointRewardExchange[];
    // end for order_daily_reward only

    @Column({
        type: 'int',
        nullable: false,
        comment: 'administrator who created this reward',
    })
    created_by: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    period_id: number;

    @Column({
        type: 'int',
        default: null,
    })
    date: number;

    @Column({
        type: 'int',
        default: null,
    })
    month: number;

    @Column({
        type: 'int',
        default: null,
    })
    year: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    status: EDriverRewardStatus;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    wallet_provider: EDriverWalletProvider;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    app_trans_id: string;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    ref_trans_id: string;

    @UpdateDateColumn()
    updated_at: string;

    @CreateDateColumn()
    created_at: string;

    @ManyToOne(() => TaxReportingDriverPeriod)
    @JoinColumn({
        name: 'period_id',
        referencedColumnName: 'id',
    })
    period?: TaxReportingDriverPeriod;

    @ManyToOne(() => DeliveryRewardType)
    @JoinColumn({
        name: 'reward_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_driver_reward_reward_id',
    })
    type: DeliveryRewardType;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_driver_reward_driver_id',
    })
    driver?: Driver;
}

export interface IPointRewardExchange {
    type: EVehicleType;
    point: number;
    amount: number;
    personal_tax: number;
    personal_tax_percent: number | null;
    currency: ECurrency;
}

export enum EDriverRewardStatus {
    PENDING = 'pending',
    PAID = 'paid',
    FAILED = 'failed',
}

