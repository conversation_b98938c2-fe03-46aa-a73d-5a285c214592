import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, OneToOne, PrimaryColumn } from 'typeorm';
import { PaymentMethod } from './paymentMethod.entity';
import { User } from './user.entity';

@Entity('mgpay_ic_payment_tokens')
export class MgpayIcPaymentToken {
    constructor(userId: number, cardNo: string, payToken: string, paymentMethodId: number) {
        this.user_id = userId;
        this.card_no = cardNo;
        this.pay_token = payToken;
        this.payment_method_id = paymentMethodId;
    }
    @PrimaryColumn()
    user_id: number;

    @PrimaryColumn()
    card_no: string;

    @Column()
    pay_token: string;

    @Column()
    payment_method_id: number;

    @OneToOne(() => PaymentMethod)
    @JoinColumn({
        name: 'payment_method_id',
        referencedColumnName: 'id',
    })
    paymentMethod: PaymentMethod;

    @ManyToOne(() => User, (user) => user.mgIcPaymentTokens)
    @JoinColumn({
        name: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;
}
