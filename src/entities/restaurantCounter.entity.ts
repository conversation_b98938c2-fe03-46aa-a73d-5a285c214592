import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Province } from './province.entity';

export enum EKeyRestaurantCounter {
    GENERATE_CODE = 'GENERATE_CODE',
}

@Entity('restaurant_counter')
export class RestaurantCounter {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    counter: number;

    @Column()
    prefix: string;

    @Column()
    postfix: string;

    @Column()
    pattern_length: number;

    @Column()
    key: EKeyRestaurantCounter;

    @Column()
    province_id: number;

    @OneToOne(() => Province)
    @JoinColumn({ name: 'province_id', referencedColumnName: 'id' })
    province: Province;
}
