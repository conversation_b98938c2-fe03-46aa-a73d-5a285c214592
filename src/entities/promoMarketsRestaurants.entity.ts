import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { RestaurantPromotionMarket } from './restaurantPromotionMarket.entity';

@Entity('promo_markets_restaurants')
export class PromoMarketsRestaurants {
    @PrimaryColumn()
    promo_market_id: number;

    @PrimaryColumn()
    restaurant_id: number;

    @ManyToOne(() => RestaurantPromotionMarket, (promotionMarket) => promotionMarket.marketManagements)
    @JoinColumn({
        name: 'promo_market_id',
        referencedColumnName: 'id',
    })
    promotionMarket: RestaurantPromotionMarket;

    @ManyToOne(() => Restaurant, (restaurant) => restaurant.marketManagements)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurants: Restaurant[];
}
