import { ApiProperty } from '@nestjs/swagger';
import { isNil } from 'lodash';
import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum EOrderDeliveryMethodCode {
    STANDARD = 'STANDARD',
    IN_TWO_HOURS = 'IN_TWO_HOURS',
    SUPER_FAST = 'SUPER_FAST',
}

export interface IOrderDeliveryMethodConfig {
    /* Estimated Time of Arrival for drop off */
    eta_drop_off: number;

    /* Estimated Time of Arrival for pick up */
    eta_pick_up: number | null | undefined;

    /* Max distance for this delivery method */
    max_distance: number;

    /* Min distance for this delivery method */
    min_distance: number;

    /* Max weight for this delivery method */
    max_weight: number;

    /* Min weight for this delivery method */
    min_weight: number;
}

@Entity('order_delivery_methods')
export class OrderDeliveryMethod {
    @ApiProperty({
        description: 'Order delivery method ID',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty({
        description: 'Order delivery method name',
        example: 'Standard Delivery',
    })
    @Column({
        type: 'varchar',
        length: 60,
    })
    name: string;

    @ApiProperty({
        description: 'Order delivery method title',
        example: 'Delivery within 60 minutes',
        nullable: true,
    })
    @Column({
        type: 'varchar',
        length: 255,
        default: null,
        nullable: true,
    })
    title: string;

    @ApiProperty({
        description: 'Icon URL for delivery method',
        example: 'https://example.com/icon.png',
        nullable: true,
    })
    @Column({
        type: 'varchar',
        length: 1000,
        default: null,
        nullable: true,
    })
    icon_url: string;

    @ApiProperty({
        description: 'Order delivery method code',
        enum: EOrderDeliveryMethodCode,
        example: EOrderDeliveryMethodCode.STANDARD,
    })
    @Column({
        type: 'varchar',
        length: 30,
        unique: true,
    })
    code: EOrderDeliveryMethodCode;

    @ApiProperty({
        description: 'Delivery method configuration',
        type: 'object',
    })
    @Column({
        type: 'json',
    })
    config: IOrderDeliveryMethodConfig;

    @ApiProperty({
        description: 'Active status',
        enum: TinyInt,
        example: TinyInt.YES,
    })
    @Column({
        type: 'tinyint',
        default: TinyInt.YES,
    })
    is_active: TinyInt;

    @ApiProperty({
        description: 'Delivery method description',
        example: 'Standard delivery within city area',
        nullable: true,
    })
    @Column({
        type: 'varchar',
        length: 1000,
        default: null,
        nullable: true,
    })
    description: string;

    @ApiProperty({
        description: 'Created timestamp',
        example: '2023-01-01T00:00:00.000Z',
    })
    @CreateDateColumn()
    created_at: Date;

    @ApiProperty({
        description: 'Updated timestamp',
        example: '2023-01-01T00:00:00.000Z',
    })
    @UpdateDateColumn()
    updated_at: Date;
}
