import { <PERSON><PERSON>n, Create<PERSON><PERSON><PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn, VersionColumn } from 'typeorm';

export class OrderFailureStatisticData {
    constructor(partial: Partial<OrderFailureStatisticData>) {
        Object.assign(this, partial);
    }

    id: number;
    branch_name: string;
    total_order_cancelled: number;
    order_cancelled_by_driver_not_found: number;
    percentage_order_cancelled_by_driver_not_found: number;
    order_cancelled_by_payment_failed: number;
    percentage_order_cancelled_by_payment_failed: number;
    order_cancelled_by_customer: number;
    percentage_order_cancelled_by_customer: number;
    order_cancelled_by_restaurant_closed: number;
    percentage_order_cancelled_by_restaurant_closed: number;
    order_cancelled_by_sold_out_or_changed_menu: number;
    percentage_order_cancelled_by_sold_out_or_changed_menu: number;
    order_cancelled_by_other_reason: number;
    percentage_order_cancelled_by_other_reason: number;
    province_id: number;
    parent_province_id: number;
}

export class OrderSuccessStatisticData {
    constructor(partial: Partial<OrderSuccessStatisticData>) {
        Object.assign(this, partial);
    }

    id: number;
    branch_name: string;
    total_driver_working: number;
    average_driver_in_month: number;
    percentage_order_success_in_day: number;
    total_order_success_in_day: number;
    total_order_success_in_month: number;
    total_order_in_month: number;
    total_order_in_day: number;
    percentage_order_success_in_month: number;
    rank: number;
    target: string;
    province_id: number;
    parent_province_id: number;
}

export class UnprocessedOrder {
    province_id?: number;
    parent_province_id?: number;
    id: number;
    branch_name: string;
    province_ids: number[];
}

export enum OrderStatisticType {
    SUCCESS = 'SUCCESS',
    FAILURE = 'FAILURE',
}

@Entity('order_statistics_daily')
export class OrderStatisticDaily {
    constructor(partial: Partial<OrderStatisticDaily>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    date_of_report: string;

    @Column({
        type: 'json',
    })
    data: OrderFailureStatisticData[] | OrderSuccessStatisticData[];

    @Column({
        type: 'json',
    })
    unprocessed_orders: UnprocessedOrder[];

    @Column({
        type: 'enum',
        enum: OrderStatisticType,
        default: OrderStatisticType.SUCCESS,
    })
    type: OrderStatisticType;

    @CreateDateColumn()
    created_at: Date;
}
