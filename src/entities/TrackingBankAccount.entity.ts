import {
    <PERSON>umn,
    CreateDateColumn,
    <PERSON><PERSON>ty,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn
} from 'typeorm';
import { TrackingBankProvider } from './TrackingBankProvider.entity';
import { TrackingBankTransaction } from './TrackingBankTransaction.entity';

@Entity('tracking_bank_accounts')
export class TrackingBankAccount {
    constructor(partial: Partial<TrackingBankAccount>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    account_number: string;


    @Column()

    current_balance: number;


    @Column({ default: false })
    is_active: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
    /* 
        @OneToMany(() => TrackingBankProvider, (trackingBankTransaction) => trackingBankTransaction)
        @JoinColumn({
            name: 'tranking_bank_providers',
            referencedColumnName: 'id',
        })
        tranking_bank_provider: TrackingBankProvider; */
    @Column()
    tracking_bank_provider_id: number;

    @ManyToOne(() => TrackingBankProvider, (user) => user.tracking_bank_account)
    @JoinColumn({ name: 'tracking_bank_provider_id', referencedColumnName: 'id' })
    tracking_bank_provider: TrackingBankProvider;


    @ManyToOne(() => TrackingBankTransaction, (user) => user.tracking_bank_account)
    @JoinColumn({ name: 'id', referencedColumnName: 'tracking_bank_account_id' })
    tracking_bank_transaction: TrackingBankTransaction;
}

//create sql
/* CREATE TABLE tracking_bank_accounts(
    id            int auto_increment
        primary key,
    account_number VARCHAR  NULL,
    level INT  NULL,
    current_balance INT  NULL,
    is_active BOOLEAN  NOT NULL,
    created_at TIMESTAMP  NULL,
    updated_at TIMESTAMP  NULL
); */

