import { TinyInt } from 'src/common/constants';
import { arrayNumberTransformer } from 'src/common/typorm/arrayNumberTranformer';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('food_sales_limit_management')
export class FoodSalesLimitManagement {
    constructor(partial: Partial<FoodSalesLimitManagement>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'integer', nullable: false, width: 11 })
    food_id: number;

    @Column({ type: 'tinyint', default: TinyInt.NO, nullable: false })
    has_reset_sales_limit: TinyInt;

    @Column()
    recurring_sales_limit: number;

    @Column({ type: 'varchar', length: 10 })
    reset_period: EFoodSalesLimitPeriod;

    @Column({ type: 'time' })
    reset_time: string;

    @Column({
        type: 'text',
        transformer: arrayNumberTransformer,
    })
    reset_day_of_weeks: number[];

    @CreateDateColumn()
    created_at: string;

    @UpdateDateColumn()
    updated_at: string;

    @Column({ type: 'simple-json' })
    metadata: Record<string, any> | IFoodSalesLimitManagementMetadata;
}

export interface IFoodSalesLimitManagementMetadata {
    nextRepeatJobId: string;
    repeatJobKey: string;
}

export enum EFoodSalesLimitPeriod {
    DAILY = 'daily',
    WEEKLY = 'weekly',
    HOURLY = 'hourly',
}
