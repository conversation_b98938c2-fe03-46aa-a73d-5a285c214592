import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('restaurant_business_types')
export class RestaurantBusinessType {
    @ApiProperty({
        type: 'integer',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty({
        type: 'string',
        example: 'Doanh nghiệp/Công ty',
    })
    @Column({ type: 'varchar', width: 255, nullable: false })
    name: string;
}
