import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('driver_registrations')
export class DriverRegistration {
    constructor(partial: Partial<DriverRegistration>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', width: 255, nullable: false })
    name: string;

    @Column({ type: 'varchar', width: 10, nullable: false })
    phone: string;

    @Column({ type: 'varchar', width: 255, nullable: false })
    address: string;

    @Column({ type: 'date', nullable: false })
    birthday: Date;

    @Column({ type: 'varchar', width: 255, nullable: true })
    email: string;

    @Column({ type: 'json' })
    vehicle_type_ids: number[];

    @CreateDateColumn({ type: 'varchar', width: 255, nullable: false })
    created_at: string;

    @UpdateDateColumn({ type: 'varchar', width: 255, nullable: false })
    updated_at: string;

    //identity json
    @Column({ type: 'json', nullable: true })
    identity: DriverIdCard | null;

    @Column({ type: 'json', nullable: true })
    bank: Bank | null;
    //  team đang duyệt
    @Column({ type: 'json', nullable: true })
    tax: Tax | null;

    @Column({ type: 'json', nullable: true })
    contact_address: ContactAddress | null;

    //lý do từ chối
    @Column({ type: 'varchar', nullable: true })
    reject_reason: string | null;

    //trạng thái có thể submit lại
    @Column({ type: 'boolean', nullable: true })
    can_resubmit: boolean | null;

    @Column({ type: 'varchar', nullable: true })
    review_stage: EDriverRegistrationReviewStage | null;

    @Column({ type: 'varchar', width: 255, nullable: false })
    status: EDriverRegistrationStatus;

    //create bằng platform nào
    @Column({ type: 'varchar', width: 255, nullable: false })
    platform: EDriverRegistrationPlatform;

    /* license_plate */
    @Column({ type: 'varchar', width: 255, nullable: false })
    license_plate: string | null;

    /* verified các thogno tin */
    /* profile verify status */
    @Column({ type: 'varchar', width: 255, nullable: false })
    profile_verify_status: EDriverRegistrationVerificationStatus | null;

    //isCreatedAccount
    @Column({ type: 'boolean', nullable: false })
    is_created_account: boolean;

    @Column({ type: 'int', nullable: true })
    province_id: number;

    @Column({ type: 'text', nullable: true })
    note: string | null;
}

export enum EDriverRegistrationStatus {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    //reject vĩnh viễn
    REJECTED_PERMANENTLY = 'REJECTED_PERMANENTLY',
    IN_PROGRESS = 'IN_PROGRESS',
    RESUBMITTED = 'RESUBMITTED',
}

export enum EDriverRegistrationVerificationStatus {
    PENDING = 'pending',
    VERIFIED = 'verified',
    REJECTED = 'rejected',
}

export enum EDriverRegistrationReviewStage {
    MANAGER = 'MANAGER',
    FINANCE = 'FINANCE',
}

export enum EApproveStatus {
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    PENDING = 'PENDING',
}

export enum EDriverRegistrationPlatform {
    WEB = 'WEB',
    APP = 'APP',
}

interface Bank {
    account_number: string;
    card_number?: string | null;
    holder_name: string;
    bank_id: number;
    verify_status: EDriverRegistrationVerificationStatus;
}
interface Tax {
    personal_tax_code: string;
    place_of_residence?: string | null;
    district: number;
    ward: number;
    street: string;
    province: number;
    verify_status: EDriverRegistrationVerificationStatus;
}

interface ContactAddress {
    contact_name: string;
    province: number;
    district: number;
    ward: number;
    street: string;
    note?: string | null;
    verify_status: EDriverRegistrationVerificationStatus;
}

export interface DriverIdCard {
    id_card: string;
    issue_date: string;
    issue_by: string;
    sex: EGender;
    full_name: string;
    date_of_birth: string;
    nationality: string;
    place_of_origin: string;
    place_of_residence: string;
    date_of_expiry: string;
    image_front: string;
    image_back: string;
    verify_status: EDriverRegistrationVerificationStatus;
}

export enum EGender {
    MALE = 'Nam',
    FEMALE = 'Nữ',
}
