import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Promotion } from './promotion.entity';
import { User } from './user.entity';

@Entity('users_promotions')
export class UserPromotion {
    constructor(userPromotion: Partial<UserPromotion>) {
        Object.assign(this, userPromotion);
    }
    @PrimaryColumn()
    user_id: number;

    @PrimaryColumn()
    promotion_id: number;

    // @ManyToOne(() => User)
    // @JoinColumn({
    //     name: 'user_id',
    //     referencedColumnName: 'id',
    // })
    user?: User;

    @ManyToOne(() => Promotion)
    @JoinColumn({
        name: 'promotion_id',
        referencedColumnName: 'id',
    })
    promotion?: Promotion;
}
