import {
    <PERSON>umn,
    CreateDateColumn,
    <PERSON>tity,
    <PERSON>inColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { CartItem } from './cartItem.entity';
import { Extra } from './extra.entity';
@Entity('cart_item_extras')
export class CartItemExtra {
    constructor(extraId: number, price: number, cartItemId: number) {
        this.price = price;
        this.extra_id = extraId;
        this.cart_item_id = cartItemId;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        nullable: false,
    })
    cart_item_id: number;

    @Column({
        unsigned: true,
    })
    extra_id: number;

    @Column()
    price: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => CartItem, (cartItem) => cartItem.cart_item_extras)
    @JoinColumn({
        name: 'cart_item_id',
        referencedColumnName: 'id',
    })
    cart_item?: CartItem;

    @ManyToOne(() => Extra)
    @JoinColumn({
        name: 'extra_id',
        referencedColumnName: 'id',
    })
    extra?: Extra;
}
