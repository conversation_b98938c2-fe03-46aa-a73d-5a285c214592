import {
    <PERSON>um<PERSON>,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { EAdsPaymentMethod, EAdsPaymentStatus } from 'src/models/restaurantAd/dto/adsPayment.dto';
import { AdsCampaign } from './adsCampaigns.entity';

@Entity('ads_payments')
export class AdsPayment {
    constructor(partial: Partial<AdsPayment>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    ads_campaign_id: number;

    @Column({
        comment: '<PERSON><PERSON> tiền thanh toán (đơn vị VND thực tế) - ví dụ: 100000 = 100,000 VND',
    })
    amount: number;

    @Column()
    payment_method: EAdsPaymentMethod;

    @Column()
    status: EAdsPaymentStatus;

    @Column({ nullable: true })
    transaction_id_ref?: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        unique: true,
    })
    payment_code?: string;

    @CreateDateColumn({ type: 'timestamp' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp' })
    updated_at: Date;

    @ManyToOne(() => AdsCampaign, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({ name: 'ads_campaign_id' })
    ads_campaign: AdsCampaign;
}
