import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('notification_plans')
export class NotificationPlan {
    constructor(partial: Partial<NotificationPlan>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    message: string;

    @Column()
    schedule_type: ENotificationPlanType;

    @Column()
    status: ENotificationPlanStatus;

    @Column('simple-array')
    topics: string[];

    @Column({
        type: 'time',
    })
    time: string;

    @Column({
        type: 'longtext',
    })
    image: string;

    @Column({
        type: 'simple-json',
        nullable: true,
    })
    metadata: INotificationMetadata;

    @Column({
        type: 'date',
    })
    start_time: string;

    @Column({
        type: 'date',
    })
    end_time: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum ENotificationPlanType {
    DAILY = 'DAILY',
    NOW = 'NOW',
    SCHEDULED = 'SCHEDULED',
}

export enum ENotificationPlanStatus {
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    IN_PROGRESS = 'IN_PROGRESS',
    SCHEDULED = 'SCHEDULED',
}

export enum ENotiType {
    PNS_REDIRECT_TO_RESTAURANT = 'PNS_REDIRECT_TO_RESTAURANT',
    PNS_REDIRECT_TO_COLLECTION = 'PNS_REDIRECT_TO_COLLECTION',
    PNS_REDIRECT_TO_NOTIFICATION = 'PNS_REDIRECT_TO_NOTIFICATION',
}
export interface INotificationMetadataPayload {
    background_color: string | null;
    notification_id: string | null;
    restaurant_id: string | null;
    collection_code: string | null;
}
export interface INotificationMetadata {
    pns_type: ENotiType;
    title: string | null;
    message: string | null;
    payload: INotificationMetadataPayload;
    external_data: INotificationExternalData | null;
}

export interface INotificationExternalData {
    main_btn_event_type: EBtnEvent;
    main_btn_text: string;
    metadata_main: {
        collection_code: string | null;
        html_content: string | null;
        restaurant_id: number | null;
        url: string | null;
    };
    second_btn_event_type: EBtnEvent;
    second_btn_text: string;
    metadata_second: {
        collection_code: string | null;
        html_content: string | null;
        restaurant_id: number | null;
        url: string | null;
    };
}

export enum EBtnEvent {
    redirect_collection = 'redirect_collection',
    redirect_website = 'redirect_website',
    redirect_restaurant = 'redirect_restaurant',
    html_rendering = 'html_rendering',
}
