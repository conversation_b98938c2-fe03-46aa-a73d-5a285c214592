import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('order_statuses')
export class OrderStatus {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    status: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EOrderStatusId {
    REPARING = 1,
    REPARED = 2,
    COMMING_TO_RESTAURANT = 3,
    SHIPPING = 4,
    ARRIVED = 5,
    CANCELED = 6,
    ASSIGNED = 7,
    IN_PROGRESS = 98, // order is in progress (saga pattern)
    FAILED = 99, // failed to create order via saga pattern
}
