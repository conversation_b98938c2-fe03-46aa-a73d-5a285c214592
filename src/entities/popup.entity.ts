import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON>n,
    <PERSON>tity,
    <PERSON><PERSON><PERSON>olum<PERSON>,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Collection } from './collection.entity';
import { Restaurant } from './restaurant.entity';
import { PopupProvince } from './popup-province.entity';
import { EPageDisplay } from './types/EPayDisplay.enum';

@Entity('popup')
export class Popup {
    constructor(parital: Partial<Popup>) {
        Object.assign(this, parital);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    subtitle: string;

    @Column()
    description: string;

    @Column()
    primary_button_text: string;

    @Column()
    primary_button_text_color: string;

    @Column()
    primary_button_bg_color: string;

    @Column()
    secondary_button_text: string;

    @Column()
    secondary_button_text_color: string;

    @Column()
    secondary_button_bg_color: string;

    @Column()
    bg_color: string;

    @Column()
    primary_text_color: string;

    @Column()
    image: string;

    @Column()
    restaurant_id: number;

    @Column()
    collection_id: number;

    @Column()
    displayed_on_app: EPopupDisplayedOnApp;

    @Column()
    start_time: Date;

    @Column()
    end_time: Date;

    @Column()
    event_type: EPopupEventType;

    @Column()
    url: string;

    @Column()
    is_active: boolean;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => Collection)
    @JoinColumn({
        name: 'collection_id',
        referencedColumnName: 'id',
    })
    collection?: Collection;

    @OneToMany(() => PopupProvince, (popupProvince) => popupProvince.popup)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'popup_id',
    })
    provinces?: PopupProvince[];

    @Column({
        type: 'enum',
        enum: EPageDisplay,
        default: EPageDisplay.HOME,
    })
    page_display: EPageDisplay;
}

export enum EPopupEventType {
    DISPLAY_IMAGE_ONLY = 'display_image_only',
    REDIRECT_TO_RESTAURANT = 'redirect_to_restaurant',
    REDIRECT_TO_WEBSITE = 'redirect_to_website',
    RENDER_HTML = 'render_html',
}

export enum EPopupDisplayedOnApp {
    DRIVER = 'driver',
    CLIENT = 'client',
    MERCHANT = 'merchant',
}
