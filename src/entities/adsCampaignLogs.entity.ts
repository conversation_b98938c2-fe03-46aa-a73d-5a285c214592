import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum EAdCampaignIssuerRole {
    MERCHANT = 'merchant',
    ADMIN = 'admin',
}

export enum EAdCampaignLogAction {
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
}

@Entity('ads_campaign_logs')
export class AdCampaignLogs {
    constructor(partial: Partial<AdCampaignLogs>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    restaurant_id: number;

    @Column()
    status: string;

    @Column()
    issuer_role: EAdCampaignIssuerRole;

    @Column()
    issuer_id: number;

    @Column()
    action: EAdCampaignLogAction;

    @Column()
    ads_campaign_id: number;

    @Column()
    message: string;

    @CreateDateColumn()
    created_at: Date;
}
