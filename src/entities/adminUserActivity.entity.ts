import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('admin_user_activities')
export class AdminUserActivity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    type: EAdminEmployeeActivityType;

    @Column()
    action: EAdminEmployeeActivityAction;

    @Column()
    user_id: number;

    @Column({ type: 'json' })
    old_data: Record<string, any>;

    @Column({ type: 'json' })
    new_data: Record<string, any>;

    @Column()
    effected_id: number;

    @CreateDateColumn()
    created_at: Date;

    user: Record<string, any>;
}

export enum EAdminEmployeeActivityType {
    RESTAURANT_FOOD = 'restaurant_food',
    PROMOTION_DETAILS = 'promotion_details',
    PROMOTION_TARGET = 'promotion_target',
    DRIVER_JOB_SETTING = 'driver_job_setting',
    DRIVER_JOB_STATS = 'driver_job_stats',
    DRIVER_DEVICE = 'driver_device',
    DRIVER_TRANSACTION = 'driver_transaction',
    USER_DETAILS = 'user_details',
    SETTING_ROLE = 'setting_role',
    SETTING_APP_SETTING = 'setting_app_setting',
}

export enum EAdminEmployeeActivityAction {
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
}
