import {
    <PERSON>um<PERSON>,
    CreateDate<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order } from './order.entity';
import { User } from './user.entity';
import { Wallet } from './wallet.entity';

@Entity('transactions')
export class Transaction {
    constructor(partial: Partial<Transaction>) {
        Object.assign(this, partial);
        this.net_amount = this.amount - this.fee;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        nullable: true,
        unique: true,
    })
    order_id: number;

    @Column()
    wallet_id: number;

    @Column({
        type: 'double',
        nullable: true,
        default: 0,
    })
    amount: number;

    @Column({
        type: 'double',
        nullable: true,
        default: 0,
    })
    balance_change: number;

    @Column({
        type: 'double',
        nullable: true,
        default: 0,
    })
    bonus_change: number;

    @Column({
        type: 'int',
        nullable: true,
        default: 0,
    })
    point: number;

    @Column({
        type: 'nvarchar',
        length: 50,
        nullable: true,
    })
    type: ETransactionType;

    @Column({
        type: 'nvarchar',
        length: 50,
        nullable: true,
    })
    status: ETransactionStatus = ETransactionStatus.PENDING;

    @Column({
        type: 'text',
        nullable: true,
    })
    note: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    hint: string;

    @Column()
    validator_id: number;

    @Column()
    transactor_id: number;

    @Column()
    cash_transactor_id: number;

    @Column({
        type: 'text',
        nullable: true,
    })
    order_ids: string;

    @Column({
        type: 'date',
        nullable: true,
    })
    bonus_day: string;

    @Column({
        type: 'date',
        nullable: true,
    })
    order_day: string;

    @Column({ type: 'varchar', width: 50, unique: true })
    trans_ref_id: string;

    @Column('simple-json')
    trans_ref_payload: Record<string, any>;

    @Column({ type: 'int', unique: true, default: null })
    source: number;

    @Column({ type: 'double', unique: false, default: 0 })
    fee = 0;

    @Column({ type: 'double', unique: false, default: 0 })
    net_amount = 0;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order?: Order;

    @ManyToOne(() => Wallet)
    @JoinColumn({
        name: 'wallet_id',
        referencedColumnName: 'id',
    })
    wallet?: Wallet;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'validator_id',
        referencedColumnName: 'id',
    })
    validator?: User;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'transactor_id',
        referencedColumnName: 'id',
    })
    transactor?: User;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'cash_transactor_id',
        referencedColumnName: 'id',
    })
    cash_transactor?: User;

    public get isPending(): boolean {
        return this.status === ETransactionStatus.PENDING;
    }

    public get isInTransit(): boolean {
        return this.status === ETransactionStatus.IN_TRANSIT;
    }

    public get isSuccess(): boolean {
        return this.status === ETransactionStatus.SUCCESS;
    }
}

export enum ETransactionType {
    EXCHANGE = 'EXCHANGE',
    DEPOSIT = 'DEPOSIT',
    WITHDRAW = 'WITHDRAW',
    ORDER_DEPOSIT = 'ORDER_DEPOSIT',
    ORDER_WITHDRAW = 'ORDER_WITHDRAW',
    BONUS = 'BONUS',
    PUNISH = 'PUNISH',
    ONEPAY_PAYCOLLECT = 'ONEPAY_PAYCOLLECT',
    ONEPAY_PAYOUT = 'ONEPAY_PAYOUT',
    ONEPAY_PAYOUT_FAILURE = 'ONEPAY_PAYOUT_FAILURE',
    ORDER_DEPOSIT_REVERSAL = 'ORDER_DEPOSIT_REVERSAL',
    ORDER_WITHDRAW_REVERSAL = 'ORDER_WITHDRAW_REVERSAL',
    EPAY_PAYCOLLECT = 'EPAY_PAYCOLLECT',
    EPAY_PAYOUT = 'EPAY_PAYOUT',
    EPAY_PAYOUT_FAILURE = 'EPAY_PAYOUT_FAILURE',
    _9PAY_PAYCOLLECT = '_9PAY_PAYCOLLECT',
    _9PAY_PAYOUT = '_9PAY_PAYOUT',
    _9PAY_PAYOUT_FAILURE = '_9PAY_PAYOUT_FAILURE',
    NINE_PAY_WALLET_PAYOUT = 'NINE_PAY_WALLET_PAYOUT',
    NINE_PAY_WALLET_PAYOUT_FAILURE = 'NINE_PAY_WALLET_PAYOUT_FAILURE',
    PERSONAL_TAX = 'PERSONAL_TAX',
    RETURN_INCOME_TAX_ADJUSTMENT = 'RETURN_INCOME_TAX_ADJUSTMENT',
    FINALIZATION_TAX_END_FUND = 'FINALIZATION_TAX_END_FUND', // tax fund from finalization (cash wallet to tax wallet), because calculate tax wrong. e.g: 1.5% -> 1.6%
    FINALIZATION_TAX_FUND = 'FINALIZATION_TAX_FUND', // tax fund from finalization (cash wallet to tax wallet)
    FINALIZATION_DEBT_TAX_FUND = 'FINALIZATION_DEBT_TAX_FUND', // tax fund from finalization (cash wallet to tax wallet)
    REWARD_DAILY = 'REWARD_DAILY',
    REWARD_TO_CASH_TRANSFER = 'REWARD_TO_CASH_TRANSFER', // reward to cash wallet
    REWARD_TOP_MONTHLY = 'REWARD_TOP_MONTHLY',
    REWARD_FAIR_PLAY_REPORT = 'REWARD_FAIR_PLAY_REPORT',
    REWARD_LAUNCH_BONUS = 'REWARD_LAUNCH_BONUS',
    REWARD_OTHER = 'REWARD_OTHER', // reward from other source
    REWARD_LARGE_ORDER_MONTHLY = 'REWARD_LARGE_ORDER_MONTHLY',
    INCOME_TAX_REFUND = 'INCOME_TAX_REFUND',
    INCOME_ORDER_TAX = 'INCOME_ORDER_TAX',
    INCOME_REWARD_TAX = 'INCOME_REWARD_TAX',
    INCOME_ORDER_TAX_REVERSAL = 'INCOME_ORDER_TAX_REVERSAL',
    INCOME_TAX_DEDUCTION = 'INCOME_TAX_DEDUCTION', // FOR DISCIPLINE TAX
    FINALIZATION_TAX_REFUND = 'FINALIZATION_TAX_REFUND', // refund tax from finalization (tax wallet to cash wallet)
    FINALIZATION_TAX_GOV = 'FINALIZATION_TAX_GOV', // tax fund from finalization (cash wallet to tax wallet)
    FINALIZATION_TAX_END_REFUND = 'FINALIZATION_TAX_END_REFUND', // tax fund from finalization (tax wallet to cash wallet), because calculate tax wrong. e.g: 1.6% -> 1.5%
    REWARD_WEEKLY_ORDER_PLAN = 'REWARD_WEEKLY_ORDER_PLAN',
}

export enum ETransactionStatus {
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
    CANCELED = 'CANCELED',
    TRANSIT_FAILURE = 'TRANSIT_FAILURE',
    IN_TRANSIT = 'IN_TRANSIT',
    TRANSIT_SUCCESS = 'TRANSIT_SUCCESS',
    PENDING = 'PENDING',
}
