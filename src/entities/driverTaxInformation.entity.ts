import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    JoinColumn,
    ManyToOne,
    PrimaryColumn,
    // PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { Province } from './province.entity';
// import { DriverIdCard } from './driverIdCard.entity';
export enum ETaxInformationStatus {
    HAS_TAX_CODE = 'HAS_TAX_CODE',
}

@Entity('driver_tax_information')
export class DriverTaxInformation {
    constructor(partial: Partial<DriverTaxInformation>) {
        Object.assign(this, partial);
    }
    // @PrimaryGeneratedColumn()
    // id: number;

    @PrimaryColumn({ type: 'int', width: 10, nullable: false, unique: true })
    driver_id: number;

    @Column({ type: 'varchar', width: 50, unique: true })
    personal_tax_code: string;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Place of residence',
    })
    place_of_residence: string;

    // @Column({
    //     type: 'text',
    //     nullable: false,
    //     comment: 'Place of residence',
    // })
    // current_address: string;

    @Column()
    district: number;

    @Column()
    ward: number;

    @Column()
    street: string;

    @Column()
    province: number;

    @Column({ type: 'date', nullable: true })
    issue_date: Date;

    // @Column()
    // current_province: string;

    // @Column()
    // current_city: string;

    // @Column()
    // current_ward: string;

    // @Column()
    // current_street: string;

    // @Column({
    //     type: 'varchar',
    //     length: 50,
    //     nullable: false,
    // })
    // status: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Driver, (driver) => driver.id)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver: Driver;

    @ManyToOne(() => Province, (province) => province.id)
    @JoinColumn({
        name: 'province',
        referencedColumnName: 'id',
    })
    provinceInfo: Province;
}
