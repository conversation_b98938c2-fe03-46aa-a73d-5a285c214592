import { Column, <PERSON><PERSON>ty, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToOne, PrimaryColumn } from 'typeorm';
import { Merchant } from './merchant.entity';
import { MerchantRole } from './merchantRole.entity';
import { GlobalRestaurant } from './globalRestaurant.entity';

@Entity('merchant_has_roles')
export class MerchantHasRole {
    @PrimaryColumn()
    merchant_id: number;

    @PrimaryColumn()
    role_id: number;

    @PrimaryColumn()
    restaurant_id: number;

    @PrimaryColumn()
    province_id: number;

    @Column()
    is_active: number;

    @Column()
    global_restaurant_id: number;

    @ManyToOne(() => Merchant, (merchant) => merchant.merchantHasRoles)
    @JoinColumn({ name: 'merchant_id', referencedColumnName: 'id' })
    merchant: Merchant;

    @OneToOne(() => MerchantRole)
    @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })
    role: MerchantRole;

    @ManyToOne(() => GlobalRestaurant)
    @JoinColumn({ name: 'global_restaurant_id', referencedColumnName: 'id' })
    // @JoinColumn({ name: 'restaurant_id', referencedColumnName: 'restaurant_id' })
    // @JoinColumn({ name: 'province_id', referencedColumnName: 'province_id' })
    restaurant?: GlobalRestaurant;
}
