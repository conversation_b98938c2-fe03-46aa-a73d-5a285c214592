import { Entity, PrimaryGeneratedColumn, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { EPaymentMethodCode } from './paymentMethod.entity';
import { TaxRevenue } from './taxRevenue.entity';
import { Restaurant } from './restaurant.entity';

@Entity('tax_orders')
export class TaxOrder {
    constructor(partial: Partial<TaxOrder>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: true })
    tax_revenue_id: number;

    @Column({ nullable: true })
    order_id: number;

    @Column({ nullable: true })
    restaurant_id: number;

    @Column({ nullable: true })
    sub_total_price: number;

    @Column()
    payment_type: EPaymentType;

    setPaymentType(paymentType: EPaymentMethodCode) {
        if (paymentType === EPaymentMethodCode.CASH_ON_DELIVERY) {
            this.payment_type = EPaymentType.COD;
        } else {
            this.payment_type = EPaymentType.ONLINE;
        }
    }

    @ManyToOne(() => TaxRevenue)
    @JoinColumn({ name: 'tax_revenue_id' })
    taxRevenue: TaxRevenue;

    @ManyToOne(() => Restaurant)
    @JoinColumn({ name: 'restaurant_id' })
    restaurant: Restaurant;

    // @ManyToOne(() => Order)
    // @JoinColumn({ name: 'order_id' })
    // order: Order;

    // @CreateDateColumn()
    // created_at: Date;

    // @UpdateDateColumn()
    // updated_at: Date;
}

export enum EPaymentType {
    COD = 'COD',
    ONLINE = 'ONLINE',
}
