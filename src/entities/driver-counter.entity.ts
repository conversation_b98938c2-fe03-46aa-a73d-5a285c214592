import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Province } from './province.entity';

export enum EKeyDriverCounter {
    BIKE = 'BIKE',
    CAR = 'CAR',
}

@Entity('driver_counter')
export class DriverCounter {
    constructor(partial: Partial<DriverCounter>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    counter: number;

    @Column()
    prefix: string;

    @Column()
    old_prefix: string;

    @Column()
    postfix: string;

    @Column()
    pattern_length: number;

    @Column()
    old_pattern_length: number;

    @Column()
    key: EKeyDriverCounter;

    @Column()
    province_id: number;

    @OneToOne(() => Province)
    @JoinColumn({ name: 'province_id', referencedColumnName: 'id' })
    province: Province;
}
