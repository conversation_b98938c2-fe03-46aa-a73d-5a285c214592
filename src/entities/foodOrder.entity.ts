import {
    After<PERSON>oad,
    Column,
    CreateDateC<PERSON>umn,
    <PERSON>tity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Food } from './food.entity';
import { FoodOrderExtra } from './foodOrderExtra.entity';
import { Order } from './order.entity';
import { OrderFoodOption } from './orderFoodOption.entity';

@Entity('food_orders')
export class FoodOrder {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    price: number;

    @Column()
    quantity: number;

    @Column()
    note: string;

    @Column()
    food_id: number;

    @Column()
    order_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Order, (order) => order.foodOrders)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order?: Order;

    @ManyToOne(() => Food, (food) => food.food_orders)
    @JoinColumn({
        name: 'food_id',
        referencedColumnName: 'id',
    })
    food?: Food;

    @OneToMany(() => FoodOrderExtra, (foodOrderExtra) => foodOrderExtra.foodOrder)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_order_id',
    })
    extras?: FoodOrderExtra[];

    @OneToMany(() => OrderFoodOption, (orderFoodOption) => orderFoodOption.foodOrder)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_food_id',
    })
    orderFoodOptions: OrderFoodOption[];

    @AfterLoad()
    formatData() {
        if (this.extras) {
            this.extras = this.extras.map(({ extra, price }: FoodOrderExtra) => {
                return {
                    ...extra,
                    price,
                };
            }) as Array<any>;
        }
    }
}
