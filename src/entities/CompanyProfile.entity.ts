import { ApiProperty } from '@nestjs/swagger';
// import { TinyInt } from '@vill-merchant/common';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Merchant } from './merchant.entity';
export enum EBusinessStatus {
    pending = 2,
    approved = 3,
    rejected = 1,
}

@Entity('company_profile')
export class CompanyProfile {
    @ApiProperty({
        type: 'integer',
        example: 1,
    })
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
        unsigned: true,
    })
    merchant_id: number;

    @Column({ type: 'varchar', width: 255, nullable: false })
    name: string;

    @Column({
        type: 'varchar',
        length: 300,
        nullable: false,
        unique: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    address: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    tax_code: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
        unique: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    email: string;

    @ApiProperty({
        type: 'string',
        example: '0339810001',
        description: 'Phone number of enterprise',
    })
    @Column({ type: 'varchar', length: 10, nullable: true, unique: true })
    phone: string;

    @Column({
        type: 'int',
        nullable: true,
        default: EBusinessStatus.pending,
    })
    status: EBusinessStatus;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    enterprise_code: string;

    @Column({ type: 'varchar', width: 255, nullable: false })
    legal_first_name: string;

    @Column({ type: 'varchar', width: 255, nullable: false })
    legal_role: string;

    @Column({ type: 'date', width: 50 })
    issuance_enterprise_code_date: string;

    @Column({ type: 'varchar', width: 255 })
    issuance_enterprise_code_by: string;

    @Column({ type: 'varchar', width: 500, nullable: true })
    rejected_reason: string;

    @OneToOne(() => Merchant, (merchant) => merchant.id)
    @JoinColumn({
        name: 'merchant_id',
        referencedColumnName: 'id',
    })
    merchant: Merchant;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;
}
