import {
    Column,
    CreateDateColumn,
    Entity,
    JoinC<PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { GlobalRestaurant } from './globalRestaurant.entity';
import { User } from './user.entity';
import { Merchant } from './merchant.entity';

@Entity('seller_reviews')
export class SellerReview {
    constructor(partial: Partial<SellerReview>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review: string;

    @Column()
    rate: number;

    @Column()
    seller_id: number;

    @Column()
    province_id: number;

    @Column()
    restaurant_id: number;

    @Column()
    merchant_id: number;

    @Column()
    interval_type: EIntervalType;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @ManyToOne(() => GlobalRestaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant: GlobalRestaurant;

    @ManyToOne(() => User)
    @JoinColumn({
        name: 'seller_id',
        referencedColumnName: 'id',
    })
    seller: User;

    @ManyToOne(() => Merchant)
    @JoinColumn({
        name: 'merchant_id',
        referencedColumnName: 'id',
    })
    merchant: Merchant;
}

export enum EIntervalType {
    DATE = 'date',
    WEEK = 'week',
    MONTH = 'month',
}
