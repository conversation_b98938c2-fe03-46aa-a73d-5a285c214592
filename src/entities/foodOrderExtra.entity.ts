import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Extra } from './extra.entity';
import { FoodOrder } from './foodOrder.entity';

@Entity('food_order_extras')
export class FoodOrderExtra {
    @PrimaryColumn()
    food_order_id: number;

    @PrimaryColumn()
    extra_id: number;

    @Column()
    price: number;

    @ManyToOne(() => FoodOrder, (foodOrder) => foodOrder.extras)
    @JoinColumn({
        name: 'food_order_id',
        referencedColumnName: 'id',
    })
    foodOrder: FoodOrder;

    @ManyToOne(() => Extra, (extra) => extra.foodOrderExtras)
    @JoinColumn({
        name: 'extra_id',
        referencedColumnName: 'id',
    })
    extra?: Extra;
}
