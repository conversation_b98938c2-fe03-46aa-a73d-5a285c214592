import {
    Column,
    CreateDateColumn,
    <PERSON><PERSON>ty,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order, OrderType } from './order.entity';
import { AdsCampaign } from './adsCampaigns.entity';

export enum EOrderInvoiceStatus {
    PENDING = 'pending',
    CREATED = 'created',
    PUBLISHED = 'published',
    FAILED = 'failed',
}

export enum EMisaInvoiceSeries {
    '1C25TVL' = '1C25TVL',
    '1C25MVS' = '1C25MVS',
}

export interface IInvoiceMetadata {
    info: IInvoiceMetadataInfo;
    items: IInvoiceItem[];
}

export interface IInvoiceItem {
    name: string;
    code: EInvoiceItemCode;
    quantity: number;
    amount_without_vat: number;
    amount: number;
    vat_rate: number;
    vat_amount: number;
}

export enum EInvoiceItemCode {
    DELIVERY_FEE = 'DELIVERY_FEE',
    SERVICE_FEE = 'SERVICE_FEE',
    SURCHARGE = 'SURCHARGE',
    VILLSHIPPER = 'VILLSHIPPER',
    VILLDVNT = 'VILLDVNT',
    GTGT = 'GTGT',
    // Ads Campaign Item Codes - Map 1:1 với EAdCategoryCode
    ADS_BANNER = 'BANNER',
    ADS_CHUANVILL = 'CHUANVILL',
    ADS_GGMAP = 'GGMAP',
    ADS_ICON = 'ICON',
    ADS_NEWFEED = 'NEWFEED',
    ADS_POPUP = 'POPUP',
    ADS_TOP = 'TOP',
    ADS_VIDEO = 'VIDEO',
    ADS_PR_FACEBOOK = 'PR FACEBOOK',
    ADS_PUSH_NOTIFICATION = 'PUSH TIN',
    ADS_MERFREESHIP = 'ADS_MERFREESHIP',
}

export interface IInvoiceMetadataInfo {
    order_id: number;
    order_code: string;
    order_type: OrderType;
    order_date: string;
    total_amount: number;
    total_amount_without_vat: number;
    total_amount_vat: number;
}

export enum EInvoiceType {
    ORDER = 'order',
    ADS_CAMPAIGN = 'ads_campaign',
}

@Entity('invoices')
export class Invoice {
    constructor(partial: Partial<Invoice>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: true,
        comment: 'Order ID from orders table, nullable for ads invoices',
    })
    order_id: number;

    @Column({
        type: 'int',
        nullable: true,
        comment: 'Ads Campaign ID for ads invoices',
    })
    ads_campaign_id: number;

    @Column({ type: 'varchar', nullable: false, comment: 'Type of invoice', default: EInvoiceType.ORDER })
    type: EInvoiceType;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
        comment: 'Reference ID for tracking',
    })
    ref_id: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: 'MISA transaction ID',
    })
    transaction_id: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: 'Invoice number',
    })
    invoice_number: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: 'Invoice series',
    })
    invoice_series: EMisaInvoiceSeries;

    @Column({
        type: 'datetime',
        nullable: true,
        comment: 'Invoice date',
    })
    invoice_date: Date;

    @Column({
        type: 'varchar',
        default: EOrderInvoiceStatus.PENDING,
        comment: 'Current status of the invoice',
    })
    status: EOrderInvoiceStatus;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: 'Buyer name',
    })
    buyer_name: string;

    @Column({
        type: 'varchar',
        length: 20,
        nullable: true,
        comment: 'Buyer tax code',
    })
    buyer_tax_code: string;

    @Column({
        type: 'varchar',
        length: 20,
        nullable: true,
        comment: 'Buyer ID card number (for non-company merchants)',
    })
    buyer_id_number: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: 'Buyer email',
    })
    buyer_email: string;

    @Column({
        type: 'varchar',
        length: 20,
        nullable: true,
        comment: 'Buyer phone number',
    })
    buyer_phone: string;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Buyer address',
        collation: 'utf8mb4_unicode_ci',
    })
    buyer_address: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: 'Buyer code from restaurant.code',
    })
    buyer_code: string;

    @Column({
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: 'Error code if invoice creation failed',
    })
    error_code: string;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'Error message if invoice creation failed',
        collation: 'utf8mb4_unicode_ci',
    })
    error_message: string;

    @Column({
        type: 'json',
        nullable: true,
        comment: 'Additional data specific to invoice',
    })
    metadata: IInvoiceMetadata;

    // @Column({
    //     type: 'boolean',
    //     default: false,
    //     comment: 'Whether email has been sent for this invoice',
    // })
    // email_sent: boolean;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        comment: 'PDF file URL in S3 storage',
    })
    pdf_url: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: 'Invoice name',
    })
    name: string;

    @CreateDateColumn({
        comment: 'Record creation timestamp',
    })
    created_at: Date;

    @UpdateDateColumn({
        comment: 'Record last update timestamp',
    })
    updated_at: Date;

    @ManyToOne(() => Order)
    @JoinColumn({ name: 'order_id' })
    order: Order;

    @ManyToOne(() => AdsCampaign)
    @JoinColumn({ name: 'ads_campaign_id' })
    adsCampaign: AdsCampaign;
}

export enum EInvoiceErrorCode {
    BUYER_TAX_CODE_INVALID = 'BuyerTaxCode_TaxCodeInvalid',
}
