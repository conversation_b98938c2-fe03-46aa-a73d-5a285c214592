import { Column, En<PERSON><PERSON>, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { RestaurantLocation } from './restaurantLocation.entity';

@Entity('locations')
export class Location {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'tinyint',
        width: 1,
    })
    level: LocationLevel;

    @Column()
    name: string;

    @Column({
        nullable: true,
        unsigned: true,
        name: 'parent_id',
    })
    parentId?: number;

    @ManyToOne(() => Location, (location) => location.parent)
    @JoinColumn({
        name: 'parent_id',
        referencedColumnName: 'id',
    })
    parent?: Location;

    @OneToOne(() => Location)
    @JoinColumn({
        name: 'parent_id',
        referencedColumnName: 'id',
    })
    city?: Location;

    @OneToOne(() => Location)
    @JoinColumn({
        name: 'parent_id',
        referencedColumnName: 'id',
    })
    district?: Location;

    @OneToMany(() => RestaurantLocation, (restaurantLocation) => restaurantLocation.location)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'location_id',
    })
    restaurantLocations?: RestaurantLocation[];
}

export enum LocationLevel {
    City = 1,
    District = 2,
    Ward = 3,
}
