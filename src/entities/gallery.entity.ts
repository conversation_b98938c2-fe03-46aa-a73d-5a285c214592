import { TinyInt } from 'src/common/constants';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Collection } from './collection.entity';
import { Province } from './province.entity';
import { Restaurant } from './restaurant.entity';

@Entity('galleries')
export class Gallery {
    constructor(partial: Partial<Gallery>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    description: string;

    @Column()
    restaurant_id: number;

    @Column()
    image: string;

    @Column()
    collection_id: number;

    @Column({ type: 'tinyint', default: TinyInt.YES, width: 1 })
    is_global: TinyInt;

    @ManyToMany(() => Province)
    @JoinTable({
        name: 'province_galleries',
        joinColumn: {
            name: 'gallery_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'province_id',
            referencedColumnName: 'id',
        },
    })
    provinces: Province[];

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @CreateDateColumn()
    created_at: Date = new Date();

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => Collection)
    @JoinColumn({
        name: 'collection_id',
        referencedColumnName: 'id',
    })
    collection?: Collection;
}
