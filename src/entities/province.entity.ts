import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('provinces')
export class Province {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    code: string;

    @Column({ type: 'decimal', precision: 10, scale: 8, default: null })
    latitude: number;

    @Column({ type: 'decimal', precision: 11, scale: 8, default: null })
    longitude: number;

    @Column({ type: 'int', unsigned: true, default: 0 })
    max_radius_in_meters: number;

    @Column({ type: 'int', unsigned: true, default: null })
    parent_id: number;

    @Column({
        type: 'tinyint',
        default: TinyInt.TRUE,
    })
    is_active: TinyInt;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
