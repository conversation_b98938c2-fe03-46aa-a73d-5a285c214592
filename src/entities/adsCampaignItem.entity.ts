import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { AdsCategory } from './adsCategoryV2.entity';
import { AdsNewsFeed } from './adsNewsFeed.entity';
import { AdsCampaign } from './adsCampaigns.entity';
import { AdsFoodCategory } from './adsFoodCategory.entity';
import { IsOptional } from 'class-validator';
import { AdsStatus } from './adsStatus.entity';
import { BannerGroup } from './bannerGroup.entity';
import { Frame } from './frame.entity';
import { AdsKeyword } from './adsKeyword.entity';
import { AdsSearchPage } from './adsSearch.entity';

@Entity('ads_campaigns_item')
export class AdsCampaignItem {
    constructor(partial: Partial<AdsCampaignItem>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    ad_cate_id: number;

    @Column()
    active_from: Date;

    @Column()
    active_to: Date;

    @Column({
        default: 0,
        nullable: false,
    })
    is_active: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    object_type: ERestaurantAdObjectType;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    note: string | null;

    @Column({
        type: 'int',
        nullable: true,
        default: null,
    })
    ads_status_code: number;

    @ManyToOne(() => AdsStatus, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ads_status_code',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_campaign_item_status',
    })
    status: AdsStatus;

    @Column({ type: 'json' })
    attached_images: string[];

    @Column({ type: 'integer' })
    frame_id: number;

    @Column()
    frame_active_from: Date;

    @Column()
    frame_active_to: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => AdsNewsFeed, (newsFeedAds) => newsFeedAds.adsItem)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_item_id',
    })
    newsFeedAds: AdsNewsFeed[];

    @OneToMany(() => AdsFoodCategory, (foodCategoryAds) => foodCategoryAds.adsItem)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ads_item_id',
    })
    foodCategoryAds: AdsFoodCategory[];

    @ManyToOne(() => AdsCategory, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ad_cate_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_item_categories',
    })
    adCategory: AdsCategory;

    @IsOptional()
    @Column({
        type: 'decimal',
        precision: 15,
        scale: 0,
        nullable: true,
        comment: 'Giá của ads campaign item (đơn vị VND thực tế) - ví dụ: 100000 = 100,000 VND',
    })
    price: number;

    @IsOptional()
    @Column({
        type: 'decimal',
        precision: 5,
        scale: 2,
        nullable: true,
        default: 8.00,
        comment: 'Tỷ lệ thuế VAT cho item này (%) - ví dụ: 8.00 = 8%',
    })
    vat_rate: number;

    @Column({
        type: 'int',
        nullable: true,
    })
    ads_campaign_id: number;

    @ManyToOne(() => AdsCampaign, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ads_campaign_id',
        referencedColumnName: 'id',
    })
    ads_campaign: AdsCampaign;

    @OneToMany(() => BannerGroup, (banner) => banner.adsCampaignItem)
    bannerGroups: BannerGroup[];

    @OneToOne(() => Frame)
    @JoinColumn({
        name: 'frame_id',
        referencedColumnName: 'id',
    })
    frame: Frame;

    @OneToMany(() => AdsSearchPage, (adsSearchPage) => adsSearchPage.adsCampaignItem)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'campaign_item_id',
    })
    adsSearchPages: AdsSearchPage[];

    @ManyToMany(() => AdsKeyword)
    @JoinTable({
        name: 'ads_search_pages',
        joinColumn: {
            name: 'campaign_item_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'keyword_id',
            referencedColumnName: 'id',
        },
    })
    keywords: AdsKeyword[];
}

export enum ERestaurantAdObjectType {
    'normal_ad' = 'normal_ad', // normal ad for ad category : category_ad_page or search_ad_page
    'newsFeed_ad' = 'newsFeed_ad', // ad for ad category : news_feed_ad_page
    'foodCategory_ad' = 'foodCategory_ad', // ad for ad category : food_category_ad_page
    'search_ad' = 'search_ad', // ad for ad category : search_ad_page
}
