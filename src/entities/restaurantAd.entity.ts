import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { AdCategory } from './adCategory.entity';
import { Restaurant } from './restaurant.entity';
import { RestaurantAdNewsFeed } from './restaurantAdNewsFeed.entity';
import { AdSellerManagement } from './adSellerManagement.entity';
import { Frame } from './frame.entity';
import { RestaurantAdFoodCategory } from './restaurantAdFooCategory.entity';
import { ERestaurantAdStatus } from 'src/models/restaurantAd/dto/restaurantAd.dto';

@Entity('restaurant_ads')
export class RestaurantAd {
    constructor(partial: Partial<RestaurantAd>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
    })
    restaurant_id: number;

    @Column()
    ad_cate_id: number;

    @Column({ type: 'datetime' })
    active_from: Date;

    @Column({ type: 'datetime' })
    active_to: Date;

    @Column({
        type: 'boolean',
        default: false,
        nullable: false,
    })
    is_active: boolean;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    object_type: ERestaurantAdObjectType;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    note: string | null;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    status: ERestaurantAdStatus;

    @Column({ type: 'json' })
    attached_images: string[];

    @Column({ type: 'integer' })
    frame_id: number;

    @Column()
    frame_active_from: Date;

    @Column()
    frame_active_to: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => Restaurant, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ads_restaurant',
    })
    restaurant?: Restaurant;

    @OneToMany(() => RestaurantAdNewsFeed, (newsFeedAds) => newsFeedAds.restaurantAd)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_ad_id',
    })
    newsFeedAds: RestaurantAdNewsFeed[];

    @OneToMany(() => RestaurantAdFoodCategory, (adFoodCategory) => adFoodCategory.restaurantAd)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_ad_id',
    })
    foodCategoryAds: RestaurantAdFoodCategory[];

    @ManyToOne(() => AdCategory, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ad_cate_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_restaurant_ads_ad_category',
    })
    adCategory: AdCategory;

    @OneToOne(() => AdSellerManagement)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'ad_id',
    })
    sellerManagement: AdSellerManagement;

    @OneToOne(() => Frame)
    @JoinColumn({
        name: 'frame_id',
        referencedColumnName: 'id',
    })
    frame: Frame;
}

export enum ERestaurantAdObjectType {
    'normal_ad' = 'normal_ad', // normal ad for ad category : category_ad_page or search_ad_page
    'newsFeed_ad' = 'newsFeed_ad', // ad for ad category : news_feed_ad_page
    'foodCategory_ad' = 'foodCategory_ad', // ad for ad category : food_category_ad_page
    'search_ad' = 'search_ad', // ad for ad category : search_ad_page
}
