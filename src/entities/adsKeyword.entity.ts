import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>T<PERSON>, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { AdsCampaignItem } from './adsCampaignItem.entity';

@Entity('ads_keywords')
export class AdsKeyword {
    constructor(partial: Partial<AdsKeyword>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    keyword: string;

    // @Column()
    // type: EKeywordType;

    @Column()
    limit: number;

    @ManyToMany(() => AdsCampaignItem)
    @JoinTable({
        name: 'ads_search_pages',
        joinColumn: {
            name: 'keyword_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'campaign_item_id',
            referencedColumnName: 'id',
        },
    })
    adsCampaignItems: AdsCampaignItem[];
}

export enum EKeywordType {
    normal = 'normal',
    individual = 'individual',
}
