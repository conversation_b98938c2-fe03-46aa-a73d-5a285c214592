import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON>ty,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    ManyToOne,
    OneToMany,
    JoinColumn,
} from 'typeorm';
import { AdsCollection } from './adsCollections.entity';
import { AdsCollectionCategory } from './adsCollectionCategory.entity';

@Entity('ads_categories')
export class AdsCategory {
    constructor(partial: Partial<AdsCategory>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EAdsCategoryCode;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    name: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    desc: string;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_active: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    subtitle: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    image: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    thumbnail: string;

    @Column({ type: 'integer', nullable: true })
    frame_id: number;

    @Column({ type: 'integer', nullable: true })
    collection_id: number;

    @Column({ type: 'integer', nullable: true })
    cate_limit: number;

    @Column({ type: 'integer', nullable: true })
    used: number;

    @Column({ type: 'varchar', length: 255, nullable: true })
    type: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    // Legacy relationship (deprecated - use collection_categories instead)
    @ManyToOne(() => AdsCollection, (collection) => collection.ads_categories)
    @JoinColumn({ name: 'collection_id' })
    ads_collection: AdsCollection;

    // New many-to-many relationship through junction table
    @OneToMany(() => AdsCollectionCategory, (collectionCategory) => collectionCategory.ads_category)
    collection_categories: AdsCollectionCategory[];

    // @OneToMany(() => AdsCatalogItem, (catalog) => catalog.ads_category)
    // ads_catalog_items: AdsCatalogItem[];

    // @OneToMany(() => RestaurantAd, (res) => res.adCategory)
    // ads_items: RestaurantAd[];
}

export enum EAdsCategoryCode {
    'category_ad_page' = 'category_ad_page',
    'news_feed_ad_page' = 'news_feed_ad_page',
    'search_ad_page' = 'search_ad_page',
    'home_banner' = 'home_banner',
    'category_banner' = 'category_banner',
    'category_icon' = 'category_icon',
    'newsfeed_banner' = 'newsfeed_banner',
    'food_notification' = 'food_notification',
    'push_notification' = 'push_notification',
    'app_notification' = 'app_notification',
    'pin_google_map' = 'pin_google_map',
    'facebook_PR' = 'facebook_PR',
    'collection_popup' = 'collection_popup',
    'top_rating' = 'top_rating',
    'video_ads' = 'video_ads',
    'cate_ads_banner_follow_order' = 'cate_ads_banner_follow_order',
    'freeship_5k_fl_40k' = 'freeship_5k_fl_40k',
    'freeship_10k_fl_60k' = 'freeship_10k_fl_60k',
    'freeship_12k_fl_80k' = 'freeship_12k_fl_80k',
    'freeship_15k_fl_100k' = 'freeship_15k_fl_100k',
}
