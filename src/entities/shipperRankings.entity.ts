import {
    <PERSON>umn,
    CreateDateC<PERSON>umn,
    <PERSON>tity,
    <PERSON>in<PERSON><PERSON>umn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { JobRankBenefit } from './jobRankBenefit.entity';

@Entity('shipper_rankings')
export class ShipperRanking {
    constructor(partial: Partial<ShipperRanking>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column({ nullable: false, unique: true })
    code: string;

    @Column()
    level: number;

    @Column({ default: false })
    is_active: boolean;

    @Column()
    image: string;

    @Column({ type: 'simple-json' })
    conditions: IShipperRakingCondition;

    @Column()
    description: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => JobRankBenefit)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'rank_id',
    })
    job_rank_benefit: JobRankBenefit;
}

export interface IShipperRakingCondition {
    min_total_successful_orders: number;
    min_rating: number;
}
