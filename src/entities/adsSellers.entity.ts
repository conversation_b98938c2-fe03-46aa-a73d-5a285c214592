import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';
import { EAdsCampaignPlatform } from 'src/models/restaurantAd/dto/adsCampaign.dto';

@Entity('ads_sellers')
export class AdSeller {
    constructor(partial: Partial<AdSeller>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    ads_campaign_id: number;

    @Column()
    seller_id: number;

    @Column()
    commission: number;

    @Column()
    platform: EAdsCampaignPlatform;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'seller_id',
        referencedColumnName: 'id',
    })
    user?: User;
}
