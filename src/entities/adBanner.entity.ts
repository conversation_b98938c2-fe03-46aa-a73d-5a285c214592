import {
    EObjType,
    EBannerEvent,
    EBannerTypes,
    MetadataDto,
    ExternalDataDto,
} from './../models/bannerGroup/bannerGroup.dto';
import {
    BeforeInsert,
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { BannerGroup } from './bannerGroup.entity';
import { TinyInt } from 'src/common/constants';
import { Province } from './province.entity';

@Entity('ads_banner')
export class AdBanner {
    constructor(partial: Partial<AdBanner>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    headline: string;

    @Column()
    subtitle: string;

    @Column()
    image: string;

    @Column()
    url: string;

    @Column({
        type: 'tinyint',
        default: 0,
        transformer: {
            from(value: number) {
                return Boolean(value);
            },
            to(value: boolean) {
                return +value;
            },
        },
    })
    is_activated = false;

    @Column()
    type: EBannerTypes;

    @Column()
    start_time: Date;

    @Column()
    end_time: Date;

    @Column()
    position: string;

    @Column()
    restaurant_id: number;

    @Column()
    main_bg_color: string;

    @Column()
    description: string;

    @Column()
    video_thumbnail_url: string;

    @Column()
    video_url: string;

    @Column()
    event_type: EBannerEvent;

    @Column()
    group_id: number;

    @Column()
    obj_type: EObjType;

    @Column()
    promo_end_time: Date;

    @Column()
    read_time: number;

    @Column({ type: 'simple-json' })
    metadata: MetadataDto;

    @Column({ type: 'simple-json' })
    external_data: ExternalDataDto;

    @Column()
    ads_campaign_item_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => BannerGroup, (bannerGroup) => bannerGroup.items)
    @JoinColumn({ name: 'group_id', referencedColumnName: 'id' })
    bannerGroup: BannerGroup;

    @Column({ type: 'tinyint', default: TinyInt.YES, width: 1 })
    is_global: TinyInt;

    @ManyToMany(() => Province)
    @JoinTable({
        name: 'province_ads_banners',
        joinColumn: {
            name: 'ad_banner_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'province_id',
            referencedColumnName: 'id',
        },
    })
    provinces: Province[];

    @BeforeInsert()
    beforeInsert() {
        this.created_at = new Date();
    }
}

// export enum EBannerEvent {
//     REDIRECT_RESTAURANT = 'redirect_restaurant',
//     REDIRECT_WEBSITE = 'redirect_website',
//     HTML_RENDERING = 'html_rendering',
//     REDIRECT_COLLECTION = 'redirect_collection',
// }

// export enum EBannerTypes {
//     CLIENT = 'CLIENT',
//     RESTAURANT = 'RESTAURANT',
// }

export enum EBannerPosition {
    TOP = 'top',
    BOTTOM = 'bottom',
}
