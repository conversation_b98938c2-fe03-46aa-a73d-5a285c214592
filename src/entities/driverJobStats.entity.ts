import { TinyInt } from 'src/common/constants';
import { arrayNumberTransformer } from 'src/common/typorm/arrayNumberTranformer';
import { Column, Entity, PrimaryColumn } from 'typeorm';
@Entity('driver_job_stats')
export class DriverJobStats {
    constructor(partial: Partial<DriverJobStats>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn()
    driver_id: number;

    @Column({ type: 'int', default: 0 })
    auto_jobs: number;

    @Column({ type: 'int', default: 0 })
    manual_jobs: number;

    @Column({ type: 'int', default: 0 })
    bonus_jobs: number;

    @Column({ type: 'int', default: 0 })
    rejected_jobs_count: number;

    @Column({ type: 'int', default: 0 })
    consecutive_rejected_jobs_count: number;

    @Column({ type: 'tinyint', default: 0 })
    in_job_cycle: TinyInt;

    @Column({ type: 'int', default: 0 })
    unconfirmed_jobs_count: number;

    @Column({ type: 'int', default: 0, unsigned: true })
    in_app_timeout_jobs_count: number;

    @Column({ type: 'int', default: 0, unsigned: true })
    proactively_reject_jobs_count: number;

    @Column({ type: 'text', default: null, transformer: arrayNumberTransformer })
    auto_job_ids: number[];

    @Column({ type: 'text', default: null, transformer: arrayNumberTransformer })
    manual_job_ids: number[];

    @Column({ type: 'text', default: null, transformer: arrayNumberTransformer })
    bonus_job_ids: number[];

    @Column({ type: 'int', default: 0 })
    priority_times: number;

    @Column({ type: 'date' })
    stats_date: string;

    @Column({ type: 'int', default: 0 })
    priority_mode_rejected_jobs_count: number;

    @Column({ type: 'tinyint', default: 0 })
    is_received_bonus_time_in_prev_order: number;
}
