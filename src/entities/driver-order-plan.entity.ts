import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { DriverOrderPlanBonus } from './driver-order-plan-bonus.entity';
import { DriverOrderPlanWeeklyRegistration } from './driver-order-plan-weekly-registration.entity';

@Entity('driver_order_plans')
export class DriverOrderPlan {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ length: 100, nullable: false })
    name: string;

    @Column({ nullable: false, name: 'min_orders' })
    min_orders: number;

    @Column({ default: 1 })
    active: boolean;

    @Column({ type: 'simple-json', nullable: true, name: 'required_rank_ids' })
    required_rank_ids: number[];

    @Column({ type: 'varchar', length: 255, nullable: true })
    image: string;

    @Column({ type: 'varchar', length: 255, nullable: true, name: 'desc' })
    desc: string;

    @CreateDateColumn({ name: 'created_at' })
    created_at: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: Date;

    @OneToMany(() => DriverOrderPlanBonus, (bonus) => bonus.plan)
    bonuses: DriverOrderPlanBonus[];

    @OneToMany(() => DriverOrderPlanWeeklyRegistration, (registration) => registration.plan)
    registrations: DriverOrderPlanWeeklyRegistration[];
}
