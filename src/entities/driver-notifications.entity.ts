import { TinyInt } from 'src/common/constants';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { DriverNotificationObjectProvince } from './driver-notifications-province.entity';

@Entity('driver_notification_objects')
export class DriverNotificationObject {
    constructor(partial: Partial<DriverNotificationObject>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', length: 1000, default: null })
    icon_url: string;

    @Column({ type: 'varchar', length: 1000, default: null })
    thumbnail_url: string;

    @Column({ type: 'varchar', length: 255, default: null })
    title: string;

    @Column({ type: 'varchar', length: 255, default: null })
    subtitle: string;

    @Column({ type: 'text' })
    content: string;

    @Column({ type: 'varchar', default: null })
    url_redirect: string;

    @Column({
        type: 'tinyint',
        nullable: false,
    })
    is_show: TinyInt;

    @Column({
        type: 'tinyint',
        nullable: false,
    })
    is_global: TinyInt;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(
        () => DriverNotificationObjectProvince,
        (notificationObjectProvince) => notificationObjectProvince.object,
    )
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'object_id',
    })
    provinces?: DriverNotificationObjectProvince[];
}
