import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { EAdsCategoryCode } from './adsCategoryV2.entity';

@Entity('ad_categories')
export class AdCategory {
    constructor(partial: Partial<AdCategory>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
        unique: true,
    })
    code: EAdsCategoryCode;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    name: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    desc: string;

    // @Column({
    //     type: 'varchar',
    //     length: 255,
    //     nullable: true,
    // })
    // title: string;

    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_active: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    subtitle: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    image: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    thumbnail: string;

    @Column({ type: 'integer', nullable: true })
    frame_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EAdCategoryCode {
    'category_ad_page' = 'category_ad_page',
    'news_feed_ad_page' = 'news_feed_ad_page',
    'search_ad_page' = 'search_ad_page',
    'search_page' = 'search_page',
    'home_banner' = 'home_banner',
    'category_banner' = 'category_banner',
    'category_icon' = 'category_icon',
    'newsfeed_banner' = 'newsfeed_banner',
    'food_notification' = 'food_notification',
    'push_notification' = 'push_notification',
    'app_notification' = 'app_notification',
    'pin_google_map' = 'pin_google_map',
    'facebook_PR' = 'facebook_PR',
    'collection_popup' = 'collection_popup',
    'top_rating' = 'top_rating',
    'video_ads' = 'video_ads',
}
