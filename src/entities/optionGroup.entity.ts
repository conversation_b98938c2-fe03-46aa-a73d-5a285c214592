import {
    Column,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON>ty,
    Join<PERSON><PERSON>umn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Food } from './food.entity';
import { Option } from './option.entity';
import { Restaurant } from './restaurant.entity';
import { FoodOptionGroup } from './food-option-group.entity';

export enum OptionGroupType {
    FOOD_ADDITIVE = 'FOOD_ADDITIVE',
    EATING_UTENSILS = 'EATING_UTENSILS',
}

@Entity('option_groups')
export class OptionGroup {
    constructor(partial: Partial<OptionGroup>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    required: boolean;

    @Column()
    single_option: boolean;

    @Column('simple-array')
    images: string[];

    @Column({ type: 'varchar', default: OptionGroupType.FOOD_ADDITIVE })
    type: OptionGroupType;

    @Column()
    restaurant_id: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant: Restaurant;

    @ManyToMany(() => Food)
    @JoinTable({
        name: 'food_option_groups',
        joinColumn: {
            name: 'option_group_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'food_id',
            referencedColumnName: 'id',
        },
    })
    foods: Food[];

    @OneToMany(() => Option, (option) => option.optionGroup)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'option_group_id',
    })
    options: Option[];

    @OneToMany(() => FoodOptionGroup, (foodOptionGroup) => foodOptionGroup.optionGroup)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'option_group_id',
    })
    foodOptionGroups: FoodOptionGroup[];
}
