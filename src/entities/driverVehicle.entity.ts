import { <PERSON><PERSON>n, CreateDateColumn, Entity, JoinColumn, ManyToOne, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { Driver } from './driver.entity';
import { VehicleType } from './vehicleType.entity';
import { ApiProperty } from '@nestjs/swagger';
import { TinyInt } from 'src/common/constants';

@Entity('drivers_vehicles')
export class DriverVehicle {
    constructor(partial: Partial<DriverVehicle>) {
        Object.assign(this, partial);
    }

    @ApiProperty({
        description: 'id of the driver',
        required: true,
        type: Number,
    })
    @PrimaryColumn()
    driver_id: number;

    @ApiProperty({
        description: 'id of vehicle type',
        required: true,
        type: Number,
    })
    @PrimaryColumn({
        type: 'int',
        nullable: false,
        unsigned: true,
    })
    vehicle_type_id: number;

    // image of vehicle
    @ApiProperty({
        description: 'image of vehicle front',
        required: false,
        type: String,
    })
    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    front_image: string;

    // image of vehicle
    @ApiProperty({
        description: 'image of vehicle back',
        required: false,
        type: String,
    })
    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    back_image: string;

    @ApiProperty({
        description: 'license plate of vehicle',
        required: false,
        type: String,
        uniqueItems: true,
    })
    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
        unique: true,
    })
    license_plate: string;

    // field to check if the vehicle is used by the driver
    @ApiProperty({
        description: 'field to check if the vehicle is used by the driver',
        required: false,
        type: Number,
        enum: TinyInt,
    })
    @Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.NO,
    })
    in_use: TinyInt;

    @ApiProperty({
        description: 'updated at',
        required: false,
        type: String,
    })
    @UpdateDateColumn()
    updated_at: string;

    @ApiProperty({
        description: 'created at',
        required: false,
        type: String,
    })
    @CreateDateColumn()
    created_at: string;

    @ManyToOne(() => VehicleType)
    @JoinColumn({
        name: 'vehicle_type_id',
        referencedColumnName: 'id',
    })
    vehicleType?: VehicleType;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;
}
