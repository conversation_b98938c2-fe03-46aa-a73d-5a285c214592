import * as _ from 'lodash';
import { TinyInt } from 'src/common/constants';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Order, OrderType } from './order.entity';
import { EPaymentMethodCode } from './paymentMethod.entity';
import { PromotionActiveWeekday } from './promotionActiveWeekday.entity';
import { PromotionProvince } from './promotionProvince.entity';
import { Restaurant } from './restaurant.entity';
import { UserPromotion } from './userPromotion.entity';

@Entity('promotions')
export class Promotion {
    constructor(partial: Partial<Promotion>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    description: string;

    @Column()
    active_from: Date;

    @Column()
    active_to: Date;

    @Column()
    code: string;

    @Column()
    max_discount: number;

    @Column()
    minimum_order_value: number;

    @Column()
    promotion_type: EPromotionType;

    @Column()
    order_type: OrderType;

    @Column({
        type: 'int',
    })
    per_user_active_times: number;

    @Column({
        type: 'int',
    })
    per_user_daily_active_times: number;

    @Column({
        type: 'int',
    })
    value: number;

    @Column({
        type: 'varchar',
        length: 20,
    })
    supplier: EPromotionSupplier;

    @Column({
        type: 'int',
    })
    limit_times: number;

    @Column({
        type: 'varchar',
        length: 20,
    })
    target: EPromotionTarget;

    @Column({
        type: 'tinyint',
        transformer: {
            from(value: number) {
                return Boolean(value);
            },
            to(value: boolean) {
                return _.toNumber(value);
            },
        },
    })
    active: boolean;

    @Column()
    is_secret: boolean;

    @Column({
        type: 'int',
    })
    used: number;

    @Column()
    payment_method: EPaymentMethodCode;

    @Column()
    discount_applies_on: EDiscountAppliesOn;

    @Column()
    vill_discount_percent: number;

    @Column()
    merchant_discount_percent: number;

    @Column()
    is_raccoon_raising_program: number;

    @Column()
    apply_for_the_first_order: boolean;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    can_have_conjunction: TinyInt;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    auto_applied: TinyInt;

    @Column({
        type: 'varchar',
        length: 255,
    })
    thumbnail_url: string;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
    })
    author_type: EPromotionAuthorType;

    @Column({
        type: 'int',
        width: 10,
        unsigned: true,
        nullable: true,
    })
    author_id: number;

    /* @Column({
        type: 'json',
        nullable: true,
    }) */
    author?: Record<string, any>;

    /* @Column({
        type: 'int',
        width: 10,
        unsigned: true,
        nullable: true,
    })
    old_author_id: number; */

    @Column({ type: 'tinyint', default: TinyInt.YES })
    active_full_week: TinyInt;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    // @ManyToMany(() => User, (users) => users.promotions, { cascade: true })
    // @JoinTable({
    //     name: 'users_promotions',
    //     joinColumn: {
    //         name: 'promotion_id',
    //         referencedColumnName: 'id',
    //     },
    //     inverseJoinColumn: {
    //         name: 'user_id',
    //         referencedColumnName: 'id',
    //     },
    // })
    // users?: User[];

    @OneToMany(() => UserPromotion, (userPromotion) => userPromotion.promotion)
    @JoinColumn({
        referencedColumnName: 'promotion_id',
        name: 'id',
    })
    users?: UserPromotion[];

    @ManyToMany(() => Restaurant, (restaurant) => restaurant.promotions, { cascade: true })
    @JoinTable({
        name: 'restaurants_promotions',
        joinColumn: {
            name: 'promotion_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'restaurant_id',
            referencedColumnName: 'id',
        },
    })
    restaurants?: Restaurant[];

    @ManyToOne(() => Order)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'promotion_id',
    })
    orders?: Order[];

    @OneToMany(() => PromotionProvince, (promotionProvince) => promotionProvince.promotion)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'promotion_id',
    })
    provinces?: PromotionProvince[];

    @OneToMany(() => PromotionActiveWeekday, (promotionActiveWeekday) => promotionActiveWeekday.promotion)
    @JoinColumn({ name: 'id', referencedColumnName: 'promotion_id' })
    activeWeekdays?: PromotionActiveWeekday[];
}

export enum EPromotionType {
    PERCENT = 'PERCENT',
    FIXED = 'FIXED',
}

export enum EPromotionTarget {
    ALL_CUSTOMERS = 'ALL_CUSTOMERS',
    SPECIFIC_CUSTOMERS = 'SPECIFIC_CUSTOMERS',
    SPECIFIC_RESTAURANTS = 'SPECIFIC_RESTAURANTS',
    RESTAURANT_CUSTOMER_COUPONS = 'RESTAURANT_CUSTOMER_COUPONS',
}

export enum EPromotionSupplier {
    VILL = 'VILL',
    MERCHANT = 'MERCHANT',
    VILL_MERCHANT = 'VILL_MERCHANT',
}

export enum EDiscountAppliesOn {
    BILL = 'BILL',
    SHIPPING_FEE = 'SHIPPING_FEE',
}

export enum EPromotionAuthorType {
    VILL = 'VILL',
    MERCHANT = 'MERCHANT',
}
