import {
    BeforeInsert,
    Column,
    CreateDateColumn,
    <PERSON>tity,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>inTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { ExtraGroupItem } from './extraGroupItem.entity';
import { Food } from './food.entity';

@Entity('extra_group')
export class ExtraGroup {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => ExtraGroupItem, (extraGroupItem) => extraGroupItem.extraGroup)
    @JoinColumn({
        name: '',
        referencedColumnName: '',
    })
    extraGroupItems?: ExtraGroupItem[];

    @ManyToMany(() => Food, (food) => food.extraGroups)
    @JoinTable({
        name: 'extra_group_foods',
        joinColumn: {
            referencedColumnName: 'id',
            name: 'extra_group_id',
        },
        inverseJoinColumn: {
            referencedColumnName: 'id',
            name: 'food_id',
        },
    })
    foods?: Food[];

    @BeforeInsert()
    beforeInsert() {
        this.updated_at = new Date();
        this.created_at = new Date();
    }
}
