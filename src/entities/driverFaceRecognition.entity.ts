import { ApiProperty } from '@nestjs/swagger';
import { TinyInt } from 'src/common/constants';
import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    JoinColumn,
    ManyToOne,
} from 'typeorm';
import { Driver } from './driver.entity';

@Entity('driver_face_recognitions')
export class DriverFaceRecognition {
    constructor(partial: Partial<DriverFaceRecognition>) {
        Object.assign(this, partial);
    }

    @ApiProperty()
    @PrimaryGeneratedColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @ApiProperty()
    @Column({
        type: 'int',
        unsigned: true,
    })
    driver_id: number;

    @ApiProperty()
    @Column({
        type: 'varchar',
        length: 700,
    })
    face_url: string;

    @ApiProperty({
        enum: TinyInt,
        description: '0: inactive, 1: active',
    })
    @Column({
        type: 'tinyint',
        nullable: true,
    })
    is_active: TinyInt;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @Column({
        type: 'varchar',
        length: 30,
        default: 'pending',
    })
    approval_status: EFaceRecognitionApprovalStatus;

    @ManyToOne(() => Driver)
    @JoinColumn({ name: 'driver_id' })
    driver: Driver;
}

export enum EFaceRecognitionApprovalStatus {
    APPROVED = 'approved',
    DENIED = 'denied',
    PENDING = 'pending',
}
