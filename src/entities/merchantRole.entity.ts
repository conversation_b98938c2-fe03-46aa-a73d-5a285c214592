import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('merchant_roles')
export class MerchantRole {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    role: EMerchantRole;

    @Column()
    desc: string;

    permissions?: EMerchantPermission[];

    getPermissions() {
        this.permissions = MERCHANT_ROLE_PERMISSIONS[this.role];
        return this;
    }
}

export enum EMerchantRole {
    OWNER = 'owner',
    MANAGER = 'manager',
    CASHIER = 'cashier',
}

export enum EMerchantPermission {
    BANK_ACCOUNT_GET = 'bank_account.get',

    RESTAURANT_REVIEW_GET = 'restaurant_review.get',
    RESTAURANT_REVIEW_UPDATE = 'restaurant_review.update',

    WALLET_GET = 'wallet.get',

    STAFF_MANAGEMENT_GET = 'staff_management.get',
    STAFF_MANAGEMENT_CREATE = 'staff_management.create',
    STAFF_MANAGEMENT_UPDATE = 'staff_management.update',
    STAFF_MANAGEMENT_REMOVE = 'staff_management.remove',

    RESTAURANT_DETAIL_GET = 'restaurant_detail.get',
    RESTAURANT_DETAIL_UPDATE = 'restaurant_detail.update',
    RESTAURANT_STATS_GET = 'restaurant_stats.get',

    PROMOTION_GET = 'promotion.get',
    PROMOTION_CREATE = 'promotion.create',
    PROMOTION_UPDATE = 'promotion.update',

    MENU_GET = 'menu.get',
    MENU_UPDATE = 'menu.update',
    MENU_CREATE = 'menu.create',
    MENU_REMOVE = 'menu.remove',

    FOOD_GET = 'food.get',
    FOOD_CREATE = 'food.create',
    FOOD_UPDATE = 'food.update',
    FOOD_REMOVE = 'food.remove',

    ORDER_GET = 'order.get',
    ORDER_COMPLAIN_CREATE = 'order_complain.create',

    MARKETING_GET = 'marketing.get',
    MARKETING_CREATE = 'marketing.create',
    MARKETING_REMOVE = 'marketing.remove',

    ADS_GET = 'ads.get',
    ADS_CREATE = 'ads.create',
}

export const MERCHANT_ROLE_PERMISSIONS = {
    [EMerchantRole.OWNER]: [
        EMerchantPermission.BANK_ACCOUNT_GET,

        EMerchantPermission.RESTAURANT_REVIEW_GET,
        EMerchantPermission.RESTAURANT_REVIEW_UPDATE,

        EMerchantPermission.WALLET_GET,

        EMerchantPermission.STAFF_MANAGEMENT_GET,
        EMerchantPermission.STAFF_MANAGEMENT_CREATE,
        EMerchantPermission.STAFF_MANAGEMENT_UPDATE,
        EMerchantPermission.STAFF_MANAGEMENT_REMOVE,

        EMerchantPermission.RESTAURANT_DETAIL_GET,
        EMerchantPermission.RESTAURANT_DETAIL_UPDATE,
        EMerchantPermission.RESTAURANT_STATS_GET,

        EMerchantPermission.PROMOTION_GET,
        EMerchantPermission.PROMOTION_CREATE,
        EMerchantPermission.PROMOTION_UPDATE,

        EMerchantPermission.MENU_GET,
        EMerchantPermission.MENU_UPDATE,
        EMerchantPermission.MENU_CREATE,
        EMerchantPermission.MENU_REMOVE,

        EMerchantPermission.FOOD_GET,
        EMerchantPermission.FOOD_CREATE,
        EMerchantPermission.FOOD_UPDATE,
        EMerchantPermission.FOOD_REMOVE,

        EMerchantPermission.ORDER_GET,
        EMerchantPermission.ORDER_COMPLAIN_CREATE,

        EMerchantPermission.MARKETING_GET,
        EMerchantPermission.MARKETING_CREATE,
        EMerchantPermission.MARKETING_REMOVE,

        EMerchantPermission.ADS_GET,
        EMerchantPermission.ADS_CREATE,
    ],
    [EMerchantRole.MANAGER]: [
        EMerchantPermission.RESTAURANT_REVIEW_GET,
        EMerchantPermission.RESTAURANT_REVIEW_UPDATE,

        EMerchantPermission.MARKETING_GET,
        EMerchantPermission.MARKETING_CREATE,
        EMerchantPermission.MARKETING_REMOVE,

        EMerchantPermission.ORDER_GET,
        EMerchantPermission.ORDER_COMPLAIN_CREATE,

        EMerchantPermission.ADS_GET,
        EMerchantPermission.ADS_CREATE,

        EMerchantPermission.PROMOTION_GET,
        EMerchantPermission.PROMOTION_CREATE,
        EMerchantPermission.PROMOTION_UPDATE,

        EMerchantPermission.MENU_GET,
        EMerchantPermission.MENU_UPDATE,
        EMerchantPermission.MENU_CREATE,
        EMerchantPermission.MENU_REMOVE,

        EMerchantPermission.FOOD_GET,
        EMerchantPermission.FOOD_CREATE,
        EMerchantPermission.FOOD_UPDATE,
        EMerchantPermission.FOOD_REMOVE,
    ],
    [EMerchantRole.CASHIER]: [
        EMerchantPermission.MENU_GET,
        EMerchantPermission.MENU_UPDATE,
        EMerchantPermission.MENU_CREATE,
        EMerchantPermission.MENU_REMOVE,

        EMerchantPermission.FOOD_GET,
        EMerchantPermission.FOOD_CREATE,
        EMerchantPermission.FOOD_UPDATE,
        EMerchantPermission.FOOD_REMOVE,

        EMerchantPermission.ORDER_GET,
        EMerchantPermission.ORDER_COMPLAIN_CREATE,
    ],
};
