import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ReviewTag } from './reviewTag.entity';

@Entity('restaurant_review_tags')
export class RestaurantReviewTag {
    constructor(partial: Partial<RestaurantReviewTag>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    restaurant_id: number;

    @Column()
    tag_id: number;

    @Column()
    count_reviews: number;

    @OneToOne(() => ReviewTag, (tag) => tag.restaurantReviewTag)
    @JoinColumn({
        name: 'tag_id',
        referencedColumnName: 'id',
    })
    tag: ReviewTag;
}
