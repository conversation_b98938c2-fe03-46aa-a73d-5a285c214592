import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum MigrationStatus {
    SUCCESS = 'success',
    FAILED = 'failed',
    PENDING = 'pending',
}

@Entity('ads_migration_logs')
export class AdsMigrationLog {
    constructor(partial: Partial<AdsMigrationLog>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', nullable: true })
    campaign_id: number;

    @Column({ type: 'int' })
    restaurant_id: number;

    @Column({ type: 'int' })
    user_id: number;

    @Column({ type: 'varchar', length: 255, nullable: true })
    province_id: string;

    @Column({ type: 'tinyint', default: 0 })
    is_rollbacked: number;

    @Column({
        type: 'enum',
        enum: MigrationStatus,
        default: MigrationStatus.PENDING,
    })
    status: MigrationStatus;

    @Column({ type: 'text', nullable: true })
    error_message: string;

    @Column({ type: 'int', nullable: true })
    ads_count: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
