import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Popup } from './popup.entity';

@Entity('popup_provinces')
export class PopupProvince {
    constructor(popupProvince: Partial<PopupProvince>) {
        Object.assign(this, popupProvince);
    }

    @PrimaryColumn()
    popup_id: number;

    @PrimaryColumn()
    province_id: number;

    @ManyToOne(() => Popup, (popup) => popup.id)
    @JoinColumn({
        name: 'popup_id',
        referencedColumnName: 'id',
    })
    popup?: Popup;
}
