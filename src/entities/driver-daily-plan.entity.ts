import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    Unique,
    UpdateDateColumn,
} from 'typeorm';
import { DriverOrderPlanWeeklyRegistration } from './driver-order-plan-weekly-registration.entity';

@Entity('driver_daily_plans')
@Unique(['driver_id', 'plan_date'])
export class DriverDailyPlan {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ nullable: false, name: 'registration_id' })
    registration_id: number;

    @Column({ nullable: false, name: 'driver_id' })
    driver_id: number;

    @Column({ type: 'date', nullable: false, name: 'plan_date' })
    plan_date: string;

    @Column({ nullable: false, name: 'planned_orders' })
    planned_orders: number;

    @Column({ default: 0, name: 'completed_orders' })
    completed_orders: number;

    @Column({
        type: 'tinyint',
        nullable: false,
        name: 'day_of_week',
        default: 1,
    })
    day_of_week: number;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
        name: 'completed_order_ids',
    })
    completed_order_ids: number[];

    @CreateDateColumn({ name: 'created_at' })
    created_at: string;

    @UpdateDateColumn({ name: 'updated_at' })
    updated_at: string;

    @ManyToOne(() => DriverOrderPlanWeeklyRegistration, (registration) => registration.dailyPlans)
    @JoinColumn({ name: 'registration_id' })
    registration: DriverOrderPlanWeeklyRegistration;
}
