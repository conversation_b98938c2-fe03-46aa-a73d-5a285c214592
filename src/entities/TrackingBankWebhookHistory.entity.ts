import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { TrackingBankTransaction } from './TrackingBankTransaction.entity';
import { TrackingBankNotificationSetting } from './TrackingBankNotificationSetting.entity';

export enum EWebhookStatus {
    PENDING = 'PENDING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
    RETRY = 'RETRY',
}

@Entity('tracking_bank_webhook_history')
export class TrackingBankWebhookHistory {
    constructor(partial: Partial<TrackingBankWebhookHistory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;
    @Column({ type: 'varchar', length: 255, nullable: true })
    webhook_url: string;    


    @Column({
        type: 'enum',
        enum: EWebhookStatus,
        default: EWebhookStatus.PENDING,
    })
    status: EWebhookStatus;

    @Column({ type: 'text' })
    request_payload: string;

    @Column({ type: 'text', nullable: true })
    response_data: string;

    @Column({ nullable: true })
    response_status_code: number;

    @Column({ type: 'text', nullable: true })
    error_message: string;

    @Column({ default: 0 })
    retry_count: number;

    @Column({ nullable: true })
    next_retry_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column()
    tracking_bank_transaction_id: number;

    @ManyToOne(() => TrackingBankTransaction)
    @JoinColumn({
        name: 'tracking_bank_transaction_id',
        referencedColumnName: 'id',
    })
    tracking_bank_transaction: TrackingBankTransaction;

    @Column({ nullable: true })
    notification_setting_id: number;

    @ManyToOne(() => TrackingBankNotificationSetting)
    @JoinColumn({
        name: 'notification_setting_id',
        referencedColumnName: 'id',
    })
    notification_setting: TrackingBankNotificationSetting;
}

/*
CREATE TABLE tracking_bank_webhook_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'RETRY') DEFAULT 'PENDING',
    request_payload TEXT NOT NULL,
    response_data TEXT NULL,
    response_status_code INT NULL,
    error_message TEXT NULL,
    retry_count INT DEFAULT 0,
    next_retry_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    tracking_bank_transaction_id INT NOT NULL,
    notification_setting_id INT NULL,
    FOREIGN KEY (tracking_bank_transaction_id) REFERENCES tracking_bank_transactions(id)
);
*/
