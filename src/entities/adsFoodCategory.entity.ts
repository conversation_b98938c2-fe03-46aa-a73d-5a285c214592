import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Category } from './category.entity';
import { AdsCampaignItem } from './adsCampaignItem.entity';

@Index('uq_ads_food_category', ['ads_item_id', 'category_id'], {
    unique: true,
})
@Entity('ads_food_categories')
export class AdsFoodCategory {
    constructor(partial: Partial<AdsFoodCategory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    category_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    ads_item_id: number;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_from: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    active_to: Date;

    @ManyToOne(() => AdsCampaignItem, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'ads_item_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_food_categories_ads_item',
    })
    adsItem: AdsCampaignItem;

    @ManyToOne(() => Category, {
        createForeignKeyConstraints: true,
    })
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
        foreignKeyConstraintName: 'fk_ads_food_category',
    })
    category: Category;
}
