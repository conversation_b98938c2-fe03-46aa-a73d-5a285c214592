import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Province } from './province.entity';

@Entity('user_statistics')
export class UserStatistic {
    constructor(partial: Partial<UserStatistic>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar' })
    interval_type: EUserStatisticInterval;

    @Column({ type: 'int' })
    week: number;

    @Column({ type: 'int' })
    year: number;

    @Column({ type: 'date' })
    from_date: string;

    @Column({ type: 'date' })
    to_date: string;

    @Column({ type: 'int' })
    total_users: number;

    @Column({ type: 'int' })
    province_id: number;

    @ManyToOne(() => Province)
    @JoinColumn({ name: 'province_id', referencedColumnName: 'id' })
    province: Province;
}

export enum EUserStatisticInterval {
    WEEK = 'WEEK',
}
