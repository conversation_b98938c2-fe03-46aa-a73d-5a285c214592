import {
    Column,
    CreateDateColumn,
    <PERSON>tity,
    <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { IShipperRakingCondition, ShipperRanking } from './shipperRankings.entity';

@Entity('driver_rank_histories')
export class DriverRankHistory {
    constructor(partial: Partial<DriverRankHistory>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    driver_id: number;

    @Column()
    rating: number;

    @Column()
    total_successful_orders: number;

    @Column()
    total_canceled_orders: number;

    @Column()
    total_reviews: number;

    @Column({ type: 'simple-json' })
    conditions: IShipperRakingCondition;

    @Column()
    rank_id: number;

    @Column()
    month: number;

    @Column()
    week_year: number;

    @Column()
    year: number;

    @Column()
    from_date: string;

    @Column()
    to_date: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => ShipperRanking)
    @JoinColumn({
        name: 'rank_id',
        referencedColumnName: 'id',
    })
    rank?: ShipperRanking;

    setRank(rank: ShipperRanking) {
        if (rank) {
            this.rank = rank;
            this.conditions = rank.conditions;
        }
    }
}
