import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { TinyInt } from 'src/common/constants';
import { DriverAgreementType, NameCoordinate } from './driver-agreement-type.entity';
import { Driver } from './driver.entity';

export enum DriverAgreementStatus {
    NOT_REQUIRED = 0,
    UNSIGNED = 1,
    GENERATED = 2,
    PENDING = 3,
    SIGNED = 4,
    REJECTED = 5,
    GENERATED_FAILED = 6,
}

export enum SignatureType {
    SELF = -1,
    VILL = 1,
}

export class SignatureParameter {
    x: number;
    y: number;
    page: number;
    width: number;
    height: number;
}

@Entity('driver_agreement')
export class DriverAgreement {
    @PrimaryGeneratedColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    driver_id: number;

    @Column({
        type: 'int',
        unsigned: true,
    })
    agreement_type_id: number;

    @Column()
    source_url: string;

    @Column()
    signature_url: string;

    @Column()
    contract_url: string;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    is_show: TinyInt;

    @Column()
    status: DriverAgreementStatus;

    @Column()
    approved_at: Date;

    @Column()
    signed_at: Date;

    @Column()
    cancellation_reason: string;

    @Column({
        type: 'json',
    })
    signature_parameters: SignatureParameter;

    @Column({
        type: 'json',
    })
    name_coordinate: NameCoordinate;

    @Column({
        type: 'varchar',
    })
    generate_message: string;

    @ManyToOne(() => Driver, (driver) => driver.id)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver: Driver;

    @ManyToOne(() => DriverAgreementType, (driverAgreementType) => driverAgreementType.id)
    @JoinColumn({
        name: 'agreement_type_id',
        referencedColumnName: 'id',
    })
    agreement_type: DriverAgreementType;

    @CreateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    created_at: Date;

    @UpdateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    updated_at: Date;
}
