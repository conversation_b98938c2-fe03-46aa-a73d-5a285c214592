import { ModelHasRole } from 'src/entities/modelHasRole.entity';
import { <PERSON><PERSON>ty, <PERSON>in<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';

@Entity('model_roles_provinces')
export class ModelRolesProvinces {
    // @PrimaryGeneratedColumn()
    // id: number;

    @PrimaryColumn()
    model_role_id: string;

    @PrimaryColumn()
    province_id: number;

    @ManyToOne(() => ModelHasRole, (modelHasRole) => modelHasRole.modelRolesProvinces)
    @JoinColumn({
        name: 'model_role_id',
        referencedColumnName: 'id',
    })
    modelRole: ModelHasRole;
}
