import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    OneToOne,
    PrimaryColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';

@Entity('driver_contact_addresses')
export class DriverContactAddress {
    constructor(partial: Partial<DriverContactAddress>) {
        Object.assign(this, partial);
    }

    @PrimaryColumn({ type: 'int', unsigned: true, nullable: false })
    driver_id: number;


    @Column({ type: 'int', unsigned: true, nullable: true })
    province: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    district: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    ward: number;

    @Column({ type: 'varchar', length: 1000, nullable: true })
    street: string;

    @Column({ type: 'text', nullable: true })
    note: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToOne(() => Driver)
    @<PERSON>inC<PERSON>umn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver: Driver;
}
