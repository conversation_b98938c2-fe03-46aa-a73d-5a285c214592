import { Column, CreateDate<PERSON>olumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('media')
export class Media {
    @PrimaryGeneratedColumn()
    id: string;

    @Column()
    model_type: string;

    @Column()
    collection_name: string;

    @Column()
    model_id: number;

    @Column()
    name: string;

    @Column()
    file_name: string;

    @Column()
    mime_type: string;

    @Column()
    disk: string;

    @Column()
    size: number;

    @Column({
        type: 'varchar',
        nullable: true,
        transformer: {
            from(value: string) {
                return JSON.parse(value);
            },
            to(value: Record<string, any>) {
                return JSON.stringify(value);
            },
        },
    })
    manipulations: Record<string, any>;

    @Column({
        type: 'varchar',
        nullable: true,
        transformer: {
            from(value: string) {
                return JSON.parse(value);
            },
            to(value: Record<string, any>) {
                return JSON.stringify(value);
            },
        },
    })
    custom_properties: Record<string, any>;

    @Column({
        type: 'varchar',
        nullable: true,
        transformer: {
            from(value: string) {
                return JSON.parse(value);
            },
            to(value: Record<string, any>) {
                return JSON.stringify(value);
            },
        },
    })
    responsive_images: Record<string, any>;

    @Column()
    order_column: number;

    @Column()
    has_bucket: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    // @ManyToOne(() => User, user => user.media)
    // @JoinColumn({name: 'model_id'})
    // user: User;
}
