import { TinyInt } from 'src/common/constants';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { FraudDetectionCategory, IFraudDetectionStatusChangeShortTime } from './fraudDetectionCategory.entity';

export enum FraudDetectionReportStatus {
    PENDING = 1, // desc: report is pending
    FINISHED = 2, // desc: report is executed
    CANCELED = 3,
}

export interface IBaseFraudDetectionReportResult {
    driver_location: {
        lat: number;
        lng: number;
    };
    distance: number;
    time: number;
    updated_at: Date;
    restaurant_location: {
        lat: number;
        lng: number;
    };
    customer_location: {
        lat: number;
        lng: number;
    };
}

export interface IFraudDetectionFinishBeforeDeliveredResult extends IBaseFraudDetectionReportResult {
    restaurant_location: {
        lat: number;
        lng: number;
    };
}

export interface IFraudDetectionShippingBeforePickupResult extends IBaseFraudDetectionReportResult {
    customer_location: {
        lat: number;
        lng: number;
    };
}

@Entity('fraud_detection_reports')
export class FraudDetectionReport {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    order_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    category_id: number;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    policy: IFraudDetectionStatusChangeShortTime;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    note: string;

    @Column({
        type: 'tinyint',
        nullable: false,
    })
    is_fraud: TinyInt;

    @Column({
        type: 'int',
        nullable: false,
        default: FraudDetectionReportStatus.PENDING,
    })
    status: FraudDetectionReportStatus;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
    })
    canceled_reason: string;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    result: IBaseFraudDetectionReportResult;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    canceled_at: Date;

    @Column({
        type: 'timestamp',
        nullable: true,
        default: null,
    })
    finished_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => FraudDetectionCategory)
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
    })
    category: FraudDetectionCategory;
}
