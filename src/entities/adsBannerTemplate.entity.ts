import {
    Column,
    CreateDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity('ads_banner_templates')
export class AdsBannertemplate {
    constructor(partial: Partial<AdsBannertemplate>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    title: string;

    @Column()
    sub_title: string;

    @Column()
    url: string;

    @Column(
        {
            type: 'tinyint',
            default: 0,
        },
    )
    is_active: number;

    @Column()
    position_slot: EBannerPositionSlot;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EBannerPositionSlot {
    TOP = 'top',
    BOTTOM = 'bottom',
    MIDDLE = 'middle',
    LEFT = 'left',
    RIGHT = 'right',
}
