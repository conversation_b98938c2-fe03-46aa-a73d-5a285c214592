import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON>ty, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { DisciplinaryAction } from './disciplinaryAction.entity';
import { DriverJobSetting } from './driverJobSetting.entity';
import { TaxReportingDriverPeriod } from './taxReportingDriverPeriod.entity';
import { Driver, EDriverWalletProvider } from './driver.entity';

export enum EDisciplinaryActionConductStatus { // trạng thái thực hiện hành vi kỷ luật
    PENDING = 'PENDING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
}

@Entity('driver_disciplinary_actions')
export class DriverDisciplinaryAction {
    constructor(partial: Partial<DriverDisciplinaryAction>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn({
        type: 'int',
    })
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    action_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: true,
        default: null,
        unique: true,
    })
    face_recognition_enrollment_id: number;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
        default: null,
    })
    note: string;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    action: Record<string, unknown | any>;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
        default: 0,
    })
    amount: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
        default: 0,
    })
    personal_tax_amount: number; // số tiền thuế cá nhân (personal tax amount) - số tiền thuế cá nhân bị trừ. Số tiền này sẽ được trừ vào số tiền ròng

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
        default: 0,
    })
    net_amount: number; // số tiền ròng (net amount) - số tiền ròng sau khi trừ thuế cá nhân. Số tiền này sẽ được trừ vào số dư ví của tài xế

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    violation_definition: Record<string, unknown> | null | undefined | any;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: true,
        default: null,
    })
    author_id: number;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    author: IAuthor;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    status: EDisciplinaryActionConductStatus;

    @Column({
        type: 'varchar',
        length: 700,
        nullable: true,
        default: null,
    })
    failure_reason: string;

    @Column({
        type: 'int',
        nullable: true,
        default: null,
        unsigned: true,
        comment: 'deprecated',
    })
    cash_transaction_id: number;

    @Column({
        type: 'int',
        nullable: true,
        default: null,
        unsigned: true,
        comment: 'deprecated',
    })
    tax_transaction_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    period_id: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    wallet_provider: EDriverWalletProvider;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    app_trans_id: string;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    ref_trans_id: string;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => TaxReportingDriverPeriod)
    @JoinColumn({
        name: 'period_id',
        referencedColumnName: 'id',
    })
    period?: TaxReportingDriverPeriod;

    @OneToOne(() => DriverJobSetting)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'driver_id',
    })
    driverJobSetting: DriverJobSetting;

    @ManyToOne(() => DisciplinaryAction)
    @JoinColumn({
        name: 'action_id',
        referencedColumnName: 'id',
    })
    disciplineAction?: DisciplinaryAction;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver: Driver;
}

export interface IAuthor {
    id: number;
    name: string;
    avatar?: string;
    email?: string;
}
