import { ApiProperty } from '@nestjs/swagger';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
/**
 * Enum for wallet linking status
 */
export enum EWalletLinkingStatus {
    SUCCESS = 'SUCCESS',
    FAILURE = 'FAILURE',
    CANCEL = 'CANCEL',
    PENDING = 'PENDING',
}
/**
 * Entity for storing e-wallet account linking information
 * Used for linking user accounts with external e-wallets like Momo, ZaloPay, etc.
 */
@Entity('driver_npay_wallet_account_linking')
export class DriverNpayWalletAccountLinking {
    constructor(partial: Partial<DriverNpayWalletAccountLinking>) {
        Object.assign(this, partial);
    }

    @ApiProperty()
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty()
    @Column({
        type: 'int',
        nullable: false,
    })
    driver_id: number;

    @ApiProperty()
    @Column({
        type: 'int',
        nullable: false,
        default: 0,
    })
    frozen_amount: number;

    @ApiProperty({
        description: 'Phone number associated with the e-wallet account',
        example: '**********',
    })
    @Column({
        type: 'varchar',
        length: 20,
        nullable: false,
    })
    phone: string;

    @ApiProperty({
        description: 'Status of the wallet linking',
        enum: EWalletLinkingStatus,
        default: EWalletLinkingStatus.PENDING,
    })
    @Column({
        type: 'varchar',
        length: 20,
        nullable: false,
        default: EWalletLinkingStatus.PENDING,
    })
    status: EWalletLinkingStatus;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @ApiProperty({
        type: () => Driver,
    })
    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;
}
