import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

export enum IntervalType {
    DATE = 'date',
    WEEK = 'week',
    MONTH = 'month',
}

export interface IDeliveryRevenuePayment {
    payment_method: string;
    amount: number;
}

@Entity('delivery_revenues')
export class DeliveryRevenue {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    gross_amount: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    net_amount: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    total_order: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
        default: 0,
    })
    total_delivered_order: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    total_canceled_order: number;

    @Column({ type: 'varchar', length: 64, nullable: true })
    interval_type: IntervalType;

    @Column({ type: 'date', nullable: true })
    start_date: string;

    @Column({ type: 'date', nullable: true })
    end_date: string;

    @Column({ type: 'int', unsigned: true, nullable: true })
    driver_id: number;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'driver_id', referencedColumnName: 'id' })
    driver: User;

    @Column({ type: 'double', unsigned: true, default: 0 })
    trade_discount_delivery: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    large_order_bonus: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    surcharge: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    long_distance_bonus: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    holiday_bonus: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    tips: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    delivery_fee: number;

    @Column({
        type: 'json',
        nullable: true,
        default: null,
    })
    payments: IDeliveryRevenuePayment[];

    @Column({ type: 'int', unsigned: true, nullable: true })
    trade_discount_amount: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    driver_vat_amount: number;

    @Column({ type: 'int', unsigned: true, nullable: true })
    income_tax_amount: number;
}
