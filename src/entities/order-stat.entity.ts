import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { UnprocessedOrder } from './order-statistics-daily.entity';

@Entity('order_stats')
export class OrderStat {
    constructor(partial: Partial<OrderStat>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    province_id: number;

    @Column()
    parent_province_id: number;

    @Column()
    date_of_stats: string;

    @Column()
    total_orders_success: number;

    @Column()
    total_orders_cancelled: number;

    @Column({
        type: 'json',
    })
    unprocessed_orders: UnprocessedOrder[];

    @CreateDateColumn()
    created_at: Date;
}
