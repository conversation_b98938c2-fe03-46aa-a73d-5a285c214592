import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('driver_order_pending_confirm_logs')
export class PendingConfirmLogs {
    constructor(partial: Partial<PendingConfirmLogs>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    driver_id: number;

    @Column()
    order_id: number;

    @Column()
    date: Date;

    @Column()
    received_at: Date;

    @Column()
    received_coordinates: string;

    @Column()
    received_app_state: EAppState;

    @Column()
    received_countdown_remain: number;

    @Column()
    displayed_at: Date;

    @Column()
    displayed_coordinates: string;

    @Column()
    displayed_app_state: EAppState;

    @Column()
    displayed_countdown_remain: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    setReceived(
        received_at: Date,
        received_coordinates: string,
        received_app_state: EAppState,
        received_countdown_remain: number,
    ) {
        this.received_at = received_at;
        this.received_coordinates = received_coordinates;
        this.received_app_state = received_app_state;
        this.received_countdown_remain = received_countdown_remain;
    }

    setDisplayed(
        displayed_at: Date,
        displayed_coordinates: string,
        displayed_app_state: EAppState,
        displayed_countdown_remain: number,
    ) {
        this.displayed_at = displayed_at;
        this.displayed_coordinates = displayed_coordinates;
        this.displayed_app_state = displayed_app_state;
        this.displayed_countdown_remain = displayed_countdown_remain;
    }
}

export enum EAppState {
    FOREGROUND = 1,
    BACKGROUND = 2,
}
