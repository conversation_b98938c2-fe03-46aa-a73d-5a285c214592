import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { AdsCategory } from './adsCategoryV2.entity';
import { AdsCollectionCategory } from './adsCollectionCategory.entity';

@Entity('ads_collections')
export class AdsCollection {
    constructor(partial: Partial<AdsCollection>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 100,
        nullable: false,
    })
    name: string;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
        unique: true,
    })
    code: string;

    @Column({
        type: 'text',
        nullable: true,
    })
    description: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    image: string;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: true,
    })
    icon: string;

    @Column({
        type: 'tinyint',
        default: 1,
    })
    is_active: number;

    @Column({
        type: 'int',
        default: 0,
    })
    ordinal_number: number;

    @Column({
        type: 'varchar',
        length: 50,
        nullable: true,
    })
    display_type: string; // 'grid', 'list', 'carousel', etc.

    @Column({
        type: 'json',
        nullable: true,
    })
    metadata: Record<string, any>; // Additional configuration

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    // Legacy relationship (deprecated - use collection_categories instead)
    @OneToMany(() => AdsCategory, (category) => category.ads_collection)
    ads_categories: AdsCategory[];

    // New many-to-many relationship through junction table
    @OneToMany(() => AdsCollectionCategory, (collectionCategory) => collectionCategory.ads_collection)
    collection_categories: AdsCollectionCategory[];
}

export enum EAdsCollectionDisplayType {
    GRID = 'grid',
    LIST = 'list',
    CAROUSEL = 'carousel',
    TABS = 'tabs',
}
