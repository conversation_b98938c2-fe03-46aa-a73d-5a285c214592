import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    Unique,
} from 'typeorm';
import { AdsCollection } from './adsCollections.entity';
import { AdsCategory } from './adsCategoryV2.entity';

@Entity('ads_collection_categories')
@Unique(['collection_id', 'category_id']) // Prevent duplicate assignments
export class AdsCollectionCategory {
    constructor(partial: Partial<AdsCollectionCategory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
        comment: 'Collection ID',
    })
    collection_id: number;

    @Column({
        type: 'int',
        nullable: false,
        comment: 'Category ID',
    })
    category_id: number;

    @Column({
        type: 'int',
        default: 0,
        comment: 'Order of category within collection',
    })
    ordinal_number: number;

    @Column({
        type: 'tinyint',
        default: 1,
        comment: 'Active status of this assignment',
    })
    is_active: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    // Relationships
    @ManyToOne(() => AdsCollection, (collection) => collection.collection_categories, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'collection_id' })
    ads_collection: AdsCollection;

    @ManyToOne(() => AdsCategory, (category) => category.collection_categories, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'category_id' })
    ads_category: AdsCategory;
}
