import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum ETaxReportingPeriodType {
    month = 'month',
    year = 'year',
    daily = 'daily',
}

@Entity('tax_reporting_driver_periods')
export class TaxReportingDriverPeriod {
    constructor(partial: Partial<TaxReportingDriverPeriod>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: false,
    })
    name: string;

    @Column({
        type: 'date',
        nullable: false,
    })
    start_date: string;

    @Column({
        type: 'date',
        nullable: false,
    })
    end_date: string;

    @Column({
        type: 'int',
        nullable: true,
    })
    month: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    year: number;

    @Column({
        type: 'enum',
        enum: ETaxReportingPeriodType,
        nullable: false,
    })
    type: ETaxReportingPeriodType;

    @Column({
        type: 'int',
        nullable: true,
        unsigned: true,
    })
    parent_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
