import { <PERSON>umn, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { UnprocessedOrder } from './order-statistics-daily.entity';
import { OrderType } from './order.entity';

@Entity('order_stats_v2')
export class OrderStatsV2 {
    constructor(partial: Partial<OrderStatsV2>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    province_id: number;

    @Column()
    parent_province_id: number;

    @Column()
    branch_name: string;

    @Column()
    date_of_stats: string;

    @Column({
        type: 'json',
    })
    unprocessed_orders: UnprocessedOrder[];

    @Column()
    order_type: 'VILLMOTO' | 'VILLCAR';

    @Column()
    stats_type: EStatsType;

    @Column()
    total_orders: number;

    @Column()
    total_orders_in_month: number;

    @Column()
    total_successful_orders: number;

    @Column()
    total_successful_orders_in_month: number;

    @Column()
    total_cancelled_orders: number;

    @Column()
    total_cancelled_orders_in_month: number;

    @Column()
    total_driver_working: number;

    @Column()
    avg_driver_working_in_month: number;

    @Column()
    total_cancelled_orders_by_driver_not_found: number;

    @Column()
    total_cancelled_orders_by_payment_failed: number;

    @Column()
    total_cancelled_orders_by_restaurant_closed: number;

    @Column()
    total_cancelled_orders_by_customer: number;

    @Column()
    total_cancelled_orders_by_sold_out_or_changed_menu: number;

    @Column()
    total_cancelled_orders_by_other_reason: number;

    @Column()
    target: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EStatsType {
    TENANT_GROUPS = 'TENANT_GROUPS',
    TENANT = 'TENANT',
}
