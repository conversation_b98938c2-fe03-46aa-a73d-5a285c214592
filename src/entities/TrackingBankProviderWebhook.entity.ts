import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany } from 'typeorm';

export enum WebhookProviderType {
    SEPAY = 'SEPAY',
    SMS = 'SMS',
    ACB = 'ACB',
}

// Import TrackingBankTransaction để tránh circular dependency
import { TrackingBankTransaction } from './TrackingBankTransaction.entity';

@Entity('tracking_bank_provider_webhook')
@Index(['provider_name', 'is_enabled'])
export class TrackingBankProviderWebhook {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'enum',
        enum: WebhookProviderType,
        nullable: false,
    })
    provider_name: WebhookProviderType;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
    })
    api_key: string;

    @Column({
        type: 'boolean',
        default: false,
    })
    is_enabled: boolean;

    @Column({
        type: 'json',
        nullable: true,
    })
    configuration: Record<string, any>;

    @Column({
        type: 'text',
        nullable: true,
    })
    description: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => TrackingBankTransaction, (transaction) => transaction.tracking_bank_provider_webhook)
    tracking_bank_transactions: TrackingBankTransaction[];
}
