import { RestaurantHistories } from 'src/entities/restaurantUpdateHistory.entity';
import { RestaurantReviewSumary } from './restaurantReviewSumary.entity';
import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Category } from './category.entity';
import { Food } from './food.entity';
import { Frame } from './frame.entity';
import { Menu } from './menu.entity';
import { Promotion } from './promotion.entity';
import { RestaurantBusinessHours } from './restaurantBusinessHour.entity';
import { TinyInt } from 'src/common/constants';
import { RestaurantAd } from './restaurantAd.entity';
import { PromoMarketsRestaurants } from './promoMarketsRestaurants.entity';
import { RestaurantLocation } from './restaurantLocation.entity';
import { Province } from './province.entity';
import { RestaurantPaymentMethod } from './restaurant-payment-method.entity';
import { Branch } from './branch.entity';
import { Order } from './order.entity';
import { AdsCampaign } from './adsCampaigns.entity';

@Entity('restaurants')
export class Restaurant {
    constructor(partial: Partial<Restaurant>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    image: string;

    @Column()
    background: string;

    @Column()
    description: string;

    @Column()
    address: string;

    @Column()
    address_note: string;

    @OneToOne(() => RestaurantLocation)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    location: RestaurantLocation;

    @Column()
    latitude: string;

    @Column()
    longitude: string;

    @Column()
    phone: string;

    @Column()
    mobile: string;

    @Column()
    information: string;

    @Column()
    delivery_fee: number;

    @Column()
    admin_commission: number;

    @Column()
    time_open: string;

    @Column()
    time_close: string;

    @Column()
    star: boolean;

    @Column()
    coupon: string;

    @Column()
    coupon_order: number;

    @Column()
    coupon_type: string;

    @Column()
    coupon_value: number;

    @Column()
    coupon_exp: Date;

    @Column()
    coupon_time: number;

    @Column()
    status: boolean;

    @Column()
    freeship: boolean;

    @Column()
    top: boolean;

    @Column()
    mondoc: boolean;

    @Column()
    buoisang: boolean;

    @Column()
    buoitrua: boolean;

    @Column()
    buoitoi: boolean;

    @Column()
    cooperating: boolean;

    @Column()
    province_id: number;

    @Column()
    on_top: boolean;

    @Column()
    is_trial: boolean;

    @Column()
    trade_discount_period_type: RestaurantTradeDiscountPeriodType;

    @Column()
    trade_discount_type: ERestaurantTradeDiscountType;

    @Column({
        type: 'double',
        default: 0,
        nullable: false,
    })
    trade_discount: number;

    @Column({
        type: 'double',
        nullable: true,
        default: 0,
    })
    rating: number;

    @Column()
    total_reviews: number;

    @Column()
    total_orders: number;

    @Column({
        type: 'tinyint',
    })
    top_trending: number;

    @Column({
        type: 'varchar',
        length: 30,
    })
    operating_status: ERestaurantOperatingStatus;

    @Column({
        type: 'timestamp',
    })
    reopen_time: Date;

    @Column({
        type: 'varchar',
        length: 30,
    })
    code: string;

    @Column({
        type: 'varchar',
        length: 200,
    })
    identifier_code: string;

    @Column({
        type: 'varchar',
        length: 30,
    })
    approval_status: ERestaurantApprovalStatus;

    @Column()
    frame_id: number;

    @Column()
    seller_id: number;

    @Column()
    branch_id: number;

    @Column()
    thumbnails: string;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    is_open: TinyInt;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    long_preparing: TinyInt;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    mid_afternoon: TinyInt;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    ad_promo: TinyInt;

    // @Column()
    // ads_keyword: string;
    // @Column({
    //     type: 'json',
    // })
    // ads_keyword: Record<string, any>;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    newsfeed_ads: TinyInt;

    @Column()
    round_trade_discount: TinyInt;

    @Column()
    author_id: number;

    @Column({
        type: 'json',
    })
    author: Record<string, any>;

    /* @Column({
        type: 'json',
    }) */
    seller: Record<string, any>;

    @Column()
    business_category: EBusinessCategory;

    @Column()
    business_type: EBusinessType;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    // @ManyToMany(() => User, (user) => user.restaurants, { cascade: true })
    // @JoinTable({
    //     name: 'user_restaurants',
    //     joinColumn: {
    //         name: 'restaurant_id',
    //         referencedColumnName: 'id',
    //     },
    //     inverseJoinColumn: {
    //         name: 'user_id',
    //         referencedColumnName: 'id',
    //     },
    // })
    // users?: User[];

    @OneToMany(() => Food, (food) => food.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    foods?: Food[];

    @ManyToMany(() => Category)
    @JoinTable({
        name: 'categories_restaurants',
        joinColumn: {
            name: 'restaurant_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'category_id',
            referencedColumnName: 'id',
        },
    })
    categories?: Category[];

    @ManyToMany(() => Promotion)
    @JoinTable({
        name: 'restaurants_promotions',
        joinColumn: {
            name: 'restaurant_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'promotion_id',
            referencedColumnName: 'id',
        },
    })
    promotions?: Promotion[];

    @OneToMany(() => RestaurantBusinessHours, (businessHour) => businessHour.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    businessHours: RestaurantBusinessHours[];

    // @OneToMany(()=> AdsSearchPage, (adsSearchPage)=>adsSearchPage.restaurant)
    // @JoinColumn({
    //     name: 'id',
    //     referencedColumnName: 'restaurant_id',
    // })
    // adsSearchPages: AdsSearchPage[];

    @OneToOne(() => Province)
    @JoinColumn({
        name: 'province_id',
        referencedColumnName: 'id',
    })
    province?: Province;

    @OneToOne(() => Frame)
    @JoinColumn({
        name: 'frame_id',
        referencedColumnName: 'id',
    })
    frame?: Frame;

    @OneToMany(() => Menu, (menu) => menu.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    menu: Menu[];

    @OneToOne(() => RestaurantReviewSumary, (reviewSumary) => reviewSumary.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    restaurantReviewSumary?: RestaurantReviewSumary;

    @OneToMany(() => RestaurantHistories, (history) => history.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    update_history?: RestaurantHistories[];

    @OneToMany(() => RestaurantAd, (restaurantAd) => restaurantAd.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    restaurantAds?: RestaurantAd[];

    @OneToMany(() => PromoMarketsRestaurants, (management) => management.restaurants)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    marketManagements: PromoMarketsRestaurants[];

    @OneToMany(() => RestaurantPaymentMethod, (paymentMethod) => paymentMethod.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    paymentMethods: RestaurantPaymentMethod[];

    // @ManyToOne(() => User)
    // @JoinColumn({
    //     name: 'seller_id',
    //     referencedColumnName: 'id',
    // })
    // seller: User;

    // @OneToMany(() => FilterRestaurantInComeStatementDto)
    // @JoinColumn({
    //     name: 'id',
    //     referencedColumnName: 'restaurant_id',
    // })
    // incomeStatements: FilterRestaurantInComeStatementDto[];

    @ManyToOne(() => Branch)
    @JoinColumn({
        name: 'branch_id',
        referencedColumnName: 'id',
    })
    branch: Branch;

    @OneToMany(() => Order, (order) => order.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    orders: Order[];

    @OneToMany(() => AdsCampaign, (adsCampaign) => adsCampaign.restaurant)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_id',
    })
    ads_campaigns: AdsCampaign[];
}

export enum ERestaurantTradeDiscountType {
    TRADE_DISCOUNT_TYPE_PERCENT = 0,
    TRADE_DISCOUNT_TYPE_PRICE = 1,
}

export enum ERestaurantOperatingStatus {
    OPEN = 'open',
    TEMPT_CLOSE = 'tempt_close',
    CONTACT_TERMINATED = 'contact_terminated',
    APPROVAL_PENDING = 'approval_pending',
    INACTIVE_BY_SYSTEM = 'inactive_by_system',
}

export enum ERestaurantApprovalStatus {
    APPROVED = 'approved',
    DENIED = 'denied',
    PENDING = 'pending',
}

export enum RestaurantTradeDiscountPeriodType {
    DIRECT = 'direct',
    WEEK = 'week',
    MONTH = 'month',
    DIRECT_WALLET = 'dr_wallet',
    NPAY_WALLET = 'np_wallet',
}

export enum ETaxableCategory {
    FOOD = 'FOOD',
    GOODS = 'GOODS',
    NONE = 'NONE',
}

export enum EBusinessCategory {
    FOOD = 'FOOD',
    GOODS = 'GOODS',
}

export enum EBusinessType {
    INDIVIDUAL = 'INDIVIDUAL',
    HOUSEHOLD = 'HOUSEHOLD',
    COMPANY = 'COMPANY',
}
