import { Column, CreateDate<PERSON>olumn, <PERSON>tity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { TrackingBankWebhookHistory } from './TrackingBankWebhookHistory.entity';

export enum ENotificationTrigger {
    ALL_TRANSACTIONS = 'ALL_TRANSACTIONS',
    DEPOSIT_ONLY = 'DEPOSIT_ONLY',
    WITHDRAW_ONLY = 'WITHDRAW_ONLY',
    AMOUNT_THRESHOLD = 'AMOUNT_THRESHOLD',
    CUSTOM_CODE = 'CUSTOM_CODE',
}

export enum EWebhookProvider {
    SMS = 'SMS',
    SEPAY = 'SEPAY',
}

@Entity('tracking_bank_notification_settings')
export class TrackingBankNotificationSetting {
    constructor(partial: Partial<TrackingBankNotificationSetting>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    name: string;

    @Column({ type: 'text', nullable: true })
    webhook_url: string;

    @Column({
        type: 'enum',
        enum: ENotificationTrigger,
        default: ENotificationTrigger.ALL_TRANSACTIONS,
    })
    trigger_type: ENotificationTrigger;

    @Column({ type: 'text', nullable: true })
    custom_code: string;

    @Column({ nullable: true })
    custom_code_suffix_length: number;

    @Column({ nullable: true })
    amount_threshold: number;

    @Column({ type: 'json', nullable: true })
    filter_conditions: any;

    @Column({ default: true })
    is_active: boolean;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ default: 3 })
    max_retry_attempts: number;

    @Column({ default: 30 })
    retry_delay_seconds: number;

    @Column({ type: 'json', nullable: true })
    headers: any;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => TrackingBankWebhookHistory, (history) => history.notification_setting_id)
    webhook_histories: TrackingBankWebhookHistory[];
}

/*
CREATE TABLE tracking_bank_notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    trigger_type ENUM('ALL_TRANSACTIONS', 'DEPOSIT_ONLY', 'WITHDRAW_ONLY', 'AMOUNT_THRESHOLD', 'CUSTOM_CODE') DEFAULT 'ALL_TRANSACTIONS',
    custom_code TEXT NULL,
    custom_code_suffix_length INT NULL,
    amount_threshold DECIMAL(15,2) NULL,
    filter_conditions JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT NULL,
    max_retry_attempts INT DEFAULT 3,
    retry_delay_seconds INT DEFAULT 30,
    headers JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
*/
