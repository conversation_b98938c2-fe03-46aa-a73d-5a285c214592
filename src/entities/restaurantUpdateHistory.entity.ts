import { <PERSON><PERSON>n, CreateDateColumn, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { User } from './user.entity';

@Entity('restaurant_histories')
export class RestaurantHistories {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    restaurant_id: number;

    @Column()
    user_id: number;

    /*  @Column()
    old_user_id: number; */

    @Column({ type: 'simple-json' })
    old_data: Record<string, any>;

    @Column({ type: 'simple-json' })
    new_data: Record<string, any>;

    @CreateDateColumn()
    created_at: Date;

    /*  @Column({ type: 'json' }) */
    user: Record<string, any>;

    @ManyToOne(() => Restaurant, (restaurant) => restaurant.update_history)
    @JoinColumn({ name: 'restaurant_id', referencedColumnName: 'id' })
    restaurant: Restaurant;
}
