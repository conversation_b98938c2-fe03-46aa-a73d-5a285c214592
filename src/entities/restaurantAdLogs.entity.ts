import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum ERestaurantAdIssuerRole {
    MERCHANT = 'merchant',
    ADMIN = 'admin',
}

@Entity('restaurant_ads_logs')
export class RestaurantAdLogs {
    constructor(partial: Partial<RestaurantAdLogs>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    restaurant_id: number;

    @Column()
    status: string;

    @Column()
    active_from: Date;

    @Column()
    active_to: Date;

    @Column()
    ad_cate_id: number;

    @Column()
    is_active: number;

    @Column()
    issuer_role: ERestaurantAdIssuerRole;

    @Column()
    issuer_id: number;

    @Column()
    action: ERestaurantAdLogAction;

    @Column()
    restaurant_ad_id: number;

    @CreateDateColumn()
    created_at: Date;
}

export enum ERestaurantAdLogAction {
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
}
