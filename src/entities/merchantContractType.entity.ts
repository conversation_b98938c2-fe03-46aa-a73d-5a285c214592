import { TinyInt } from 'src/common/constants';
import {
    Col<PERSON>n,
    CreateDateC<PERSON>umn,
    <PERSON>tity,
    Join<PERSON><PERSON>umn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { MerchantBusinessType } from './merchantBusinessType.entity';
import { Signature } from './signature.entity';

export interface RequiredContractData {
    key: keyRequiredData;
    x: number;
    y: number;
    page: number;
    maxWidth?: number;
    width?: number;
    height?: number;
    type?: RequiredContractType;
    value?: string;
}

export enum RequiredContractType {
    system = 'system',
    user = 'user',
}

export enum keyRequiredData {
    code = 'code',
    full_name = 'full_name',
    date_of_birth = 'date_of_birth',
    place_of_origin = 'place_of_origin',
    sex = 'sex',
    phone = 'phone',
    cmnd = 'cmnd',
    issue_date = 'issue_date',
    issued_by = 'issued_by',
    date_of_expiry = 'date_of_expiry',
    place_of_residence = 'place_of_residence',
    email = 'email',
    nationality = 'nationality',
    signature = 'signature',
    restaurant_name = 'restaurant_name',
    restaurant_address = 'restaurant_address',
    trade_discount = 'trade_discount',
    percent_trade_discount = 'percent_trade_discount',
    trade_discount2 = 'trade_discount2',
    percent_trade_discount2 = 'percent_trade_discount2',
    pick_trade_discount = 'pick_trade_discount',
    pick_percent_trade_discount = 'pick_percent_trade_discount',
    created_at = 'created_at',
    signed_at = 'signed_at',
    // profile company
    company_name = 'company_name',
    company_address = 'company_address',
    tax_code = 'tax_code',
    company_email = 'company_email',
    company_phone = 'company_phone',
    enterprise_code = 'enterprise_code',
    legal_first_name = 'legal_first_name',
    legal_role = 'legal_role',
    issuance_enterprise_code_date = 'issuance_enterprise_code_date',
    issuance_enterprise_code_by = 'issuance_enterprise_code_by',
    signature_V = 'signature_V',
    text = 'text',
}

@Entity('merchant_contract_type')
export class MerchantContractType {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'varchar',
        length: 1000,
    })
    name: string;

    @Column()
    template_url: string;

    @Column({
        type: 'json',
        default: null,
        nullable: true,
    })
    required_data: RequiredContractData[];

    @Column({
        type: 'tinyint',
        default: 1,
    })
    required_signature: TinyInt;

    @Column({
        type: 'tinyint',
        default: 0,
    })
    is_ads_contract: TinyInt;

    @Column({ nullable: false })
    business_type: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    representation_id: number;

    @ManyToOne(() => MerchantBusinessType, (merchantBusinessType) => merchantBusinessType.id)
    @JoinColumn({ name: 'business_type' })
    merchantBusinessType: MerchantBusinessType;

    @ManyToOne(() => Signature, (signature) => signature.id)
    @JoinColumn({ name: 'representation_id' })
    representation: Signature;

    @Column()
    description: string;

    @CreateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    created_at: Date;

    @UpdateDateColumn({
        default: () => 'CURRENT_TIMESTAMP',
    })
    updated_at: Date;
}
