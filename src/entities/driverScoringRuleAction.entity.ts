import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ECalculationType, EPointType, ITierConfig } from './types/EScoringRule.enum';
import { ScoringRule } from './driverScoringRule.entity';

@Entity('driver_scoring_rule_actions')
export class ScoringRuleAction {
    constructor(partial: Partial<ScoringRuleAction>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    rule_id: number;

    @Column({
        type: 'enum',
        enum: ECalculationType,
    })
    calculation_type: ECalculationType; // 'FIXED', 'TIERED'

    @Column({
        type: 'enum',
        enum: EPointType,
        default: EPointType.ADD,
    })
    point_type: EPointType; // 'ADD', 'SUBTRACT'

    @Column({ type: 'int', default: 1 })
    base_points: number;

    @Column({ type: 'decimal', precision: 10, scale: 4, default: 1 })
    multiplier: number;

    @Column({
        type: 'json',
        nullable: true,
    })
    tiers: ITierConfig[]; // For tiered calculations

    @Column({ length: 100, nullable: true })
    target_field: string; // Field to calculate from (distance, total_price, etc.)

    @ManyToOne(() => ScoringRule, (rule) => rule.actions, {
        onDelete: 'CASCADE',
    })
    @JoinColumn({ name: 'rule_id' })
    rule: ScoringRule;
}
