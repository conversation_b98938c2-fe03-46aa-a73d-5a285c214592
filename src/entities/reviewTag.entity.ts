import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
    OneToOne,
    ManyToMany,
    JoinTable,
} from 'typeorm';
import * as _ from 'lodash';
import { RestaurantReviewTag } from './restaurantReviewTags.entity';
import { RestaurantUserReviewTags } from './restaurantUserReviewTags.entity';
import { ShipperReview } from './shipperReview.entity';

@Entity('review_tags')
export class ReviewTag {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    desc: string;

    @Column()
    icon: string;

    @Column()
    name: string;

    @Column({
        type: 'simple-array',
        transformer: {
            from: (value: string[] | null) => (value ? value.map((v) => parseInt(v)) : []),
            to: (value) => value,
        },
    })
    levels_required: number[];

    @Column()
    active: boolean;

    @Column()
    type: EReviewTagType;

    @CreateDateColumn()
    created_at: Date = new Date();

    @UpdateDateColumn()
    updated_at: Date = new Date();

    @OneToOne(() => RestaurantReviewTag, (restaurantReviewTag) => restaurantReviewTag.tag)
    restaurantReviewTag: RestaurantReviewTag;

    @OneToOne(() => RestaurantUserReviewTags, (restaurantUserReviewTags) => restaurantUserReviewTags.tag)
    restaurantUserReviewTags: RestaurantUserReviewTags;

    @ManyToMany(() => ShipperReview, (shipperReview) => shipperReview.tags)
    @JoinTable({
        name: 'shipper_reviews_tags',
        joinColumn: {
            name: 'tag_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'review_id',
            referencedColumnName: 'id',
        },
    })
    shipperReviews: ShipperReview[];
}

export enum EReviewTagType {
    REVIEW_RESTAURANT = 'REVIEW_RESTAURANT',
    REVIEW_SHIPPER = 'REVIEW_SHIPPER',
}
