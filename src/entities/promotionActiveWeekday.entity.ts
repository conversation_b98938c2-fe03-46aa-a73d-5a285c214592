import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { Promotion } from './promotion.entity';

@Entity('promotion_active_weekdays')
export class PromotionActiveWeekday {
    @PrimaryGeneratedColumn({ type: 'int' })
    id?: number;

    @Column({ type: 'int' })
    promotion_id?: number;

    @Column({ type: 'int' })
    weekday: number;

    @Column({ type: 'tinyint', default: 1 })
    active_full_day: number;

    @Column({ type: 'json' })
    active_times: IPromotionActiveTime[];

    @ManyToOne(() => Promotion, (promotion) => promotion.activeWeekdays)
    @JoinColumn({ name: 'promotion_id', referencedColumnName: 'id' })
    promotion?: Promotion;

    // @OneToMany(() => PromotionActiveTime, (promotionActiveTime) => promotionActiveTime.activeWeekday)
    // @JoinColumn({ name: 'id', referencedColumnName: 'active_weekday_id' })
    // activeTimes?: PromotionActiveTime[];
}

export interface IPromotionActiveTime {
    from_time: string;
    to_time: string;
}
