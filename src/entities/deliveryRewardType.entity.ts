import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class DeliveryRewardType {
    @PrimaryColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @Column({
        type: 'varchar',
        length: 255,
        unique: true,
        nullable: false,
    })
    code: EDeliveryRewardTypeCode;

    @Column({
        type: 'varchar',
        length: 255,
        nullable: false,
    })
    name: string;

    @Column({
        type: 'tinyint',
        nullable: false,
        default: TinyInt.NO,
    })
    is_active: TinyInt;

    @Column({
        type: 'json',
        nullable: true,
    })
    rules: Record<string, any>;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
        default: null,
    })
    description: string;

    @Column({
        type: 'decimal',
        precision: 10,
        scale: 2,
        nullable: true,
        default: null,
    })
    personal_tax_percentage: number;

    @CreateDateColumn()
    created_at: string;

    @UpdateDateColumn()
    updated_at: string;
}

export interface IOrderRewardDailyRules {}

export enum EDeliveryRewardTypeCode {
    order_daily_reward = 'order_daily_reward',
    top_monthly_reward = 'top_monthly_reward', // top 10 drivers with the highest number of orders in a month
    fair_play_report_reward = 'fair_play_report_reward', // reward for drivers report fair play of other drivers
    launch_bonus = 'launch_bonus', // reward for drivers who join the platform in the first month
    other_reward = 'other_reward', // other rewards that are not included in the above types
    weekly_order_plan_reward = 'weekly_order_plan_reward', // reward for drivers who complete the weekly order plan
}
