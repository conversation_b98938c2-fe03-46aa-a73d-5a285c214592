import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';

@Entity('driver_devices')
export class DriverDevice {
    @ApiProperty()
    @PrimaryGeneratedColumn({ unsigned: true })
    id: number;

    @ApiProperty()
    @Column({ type: 'int', unsigned: true, nullable: false })
    driver_id: number;

    @ApiProperty()
    @Column({ type: 'text' })
    device_id: string;

    @ApiProperty()
    @Column({ type: 'varchar', length: 60, nullable: true })
    ip: string;

    @ApiProperty()
    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    os: string;

    @Column()
    auth_status: string;

    @ApiProperty()
    @Exclude({ toPlainOnly: true })
    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
        charset: 'utf8',
        collation: 'utf8_unicode_ci',
    })
    api_key: string;

    @ApiProperty()
    @Exclude({ toPlainOnly: true })
    @Column({ type: 'timestamp' })
    api_key_expired: Date;

    @ApiProperty()
    @Column({ type: 'varchar', nullable: true })
    os_version: string;

    @ApiProperty()
    @Column({ type: 'varchar', nullable: true })
    model: string;

    @ApiProperty()
    @Column({ type: 'varchar', nullable: true })
    manufacturer: string;

    @ApiProperty()
    @Column({ type: 'varchar', nullable: true })
    app: string;

    @ApiProperty()
    @Column({ type: 'varchar', nullable: true })
    app_version: string;

    @ApiProperty()
    @Column({ type: 'timestamp', nullable: true })
    last_login: Date;

    @ApiProperty()
    @CreateDateColumn()
    created_at: Date;

    @ApiProperty()
    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;
}
export enum EAuthStatus {
    ENABLED = 'enabled',
    DISABLED = 'disabled',
    REVOKED = 'revoked',
}
