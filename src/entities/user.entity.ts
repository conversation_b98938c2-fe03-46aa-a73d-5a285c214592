import * as cryptoRandom from 'crypto-random-string';
import * as moment from 'moment';
import {
    <PERSON><PERSON>n,
    CreateDateColumn,
    Entity,
    <PERSON>inC<PERSON><PERSON>n,
    JoinTable,
    ManyToMany,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { CustomFieldValue } from './customFieldValue.entity';
import { Driver } from './driver.entity';
import { MgpayIcPaymentToken } from './mgpayIcPaymentToken.entity';
import { ModelHasRole } from './modelHasRole.entity';
import { Order } from './order.entity';
// import { OrderUpdateHistory } from './orderUpdateHistory.entity';
// import { Promotion } from './promotion.entity';
// import { Restaurant } from './restaurant.entity';
// import { RestaurantHistories } from './restaurantUpdateHistory.entity';
import { Role } from './role.entity';
import { SellerReviewSummary } from './sellerReviewSummary.entity';
import { TinyInt } from 'src/common/constants';

@Entity('users')
export class User {
    constructor(
        name: string,
        email: string,
        phone: string,
        hashPassword: string,
        provinceId: number,
        is_locked: number,
        referralCode: string | null,
    ) {
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.password = hashPassword;
        this.device_token = '';
        this.province_id = provinceId;
        this.avatar = null;
        this.api_token = cryptoRandom({ length: 60, type: 'alphanumeric' });
        this.created_at = this.updated_at = moment().utc().toDate();
        this.is_locked = is_locked;
        this.referral_code = referralCode;
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    avatar: string;

    @Column({
        nullable: false,
        unique: true,
    })
    email: string;

    @Column({
        nullable: false,
        unique: true,
    })
    phone: string;

    @Column({
        nullable: false,
    })
    password: string;

    @Column()
    api_token: string;

    @Column()
    player_id_onesignal: string;

    @Column()
    braintree_id: string;

    @Column()
    paypal_email: string;

    @Column()
    stripe_id: string;

    @Column()
    card_brand: string;

    @Column()
    card_last_four: string;

    @Column()
    trial_ends_at: Date;

    @Column()
    device_token: string;

    @Column()
    lat: string;

    @Column()
    lng: string;

    @Column()
    province_id: number;

    @Column()
    is_locked: number;

    @Column({ type: 'tinyint', default: 0, nullable: false })
    has_ordered?: TinyInt;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column({
        select: false,
        insert: false,
        update: false,
        transformer: {
            from: (value: string) => +value,
            to: undefined,
        },
    })
    processingOrderCount?: number;

    @Column()
    total_monthly_villfood_orders: number;

    @Column({
        type: 'varchar',
        nullable: true,
        unique: true,
        width: 10,
    })
    referral_code: string | null;

    @Column({
        type: 'varchar',
        nullable: true,
        width: 11,
        unsigned: true,
    })
    referrer_id: number | null;

    @Column({ type: 'tinyint', default: 0 })
    is_email_confirmed: number;

    @ManyToMany(() => Role)
    @JoinTable({
        name: 'model_has_roles',
        joinColumn: {
            name: 'model_id',
            referencedColumnName: 'id',
        },
        inverseJoinColumn: {
            name: 'role_id',
            referencedColumnName: 'id',
        },
    })
    roles?: Role[];

    @OneToMany(() => CustomFieldValue, (customFieldValue) => customFieldValue.user)
    custom_fields?: CustomFieldValue[];

    @OneToMany(() => ModelHasRole, (modelHasRole) => modelHasRole.user)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'model_id',
    })
    modelHasRoles?: ModelHasRole[];

    @OneToOne(() => Driver, (driver) => driver.user)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'user_id',
    })
    driver?: Driver;

    @OneToMany(() => Order, (order) => order.driver)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'driver_id',
    })
    driverOrders?: Order[];

    // @OneToMany(() => Order, (order) => order.user)
    // @JoinColumn({
    //     name: 'id',
    //     referencedColumnName: 'user_id',
    // })
    // userOrders?: Order[];

    // @ManyToMany(() => Restaurant)
    // @JoinTable({
    //     name: 'user_restaurants',
    //     joinColumn: {
    //         name: 'user_id',
    //         referencedColumnName: 'id',
    //     },
    //     inverseJoinColumn: {
    //         name: 'restaurant_id',
    //         referencedColumnName: 'id',
    //     },
    // })
    // restaurants?: Restaurant[];

    // @ManyToMany(() => Promotion)
    // @JoinTable({
    //     name: 'users_promotions',
    //     joinColumn: {
    //         name: 'user_id',
    //         referencedColumnName: 'id',
    //     },
    //     inverseJoinColumn: {
    //         name: 'promotion_id',
    //         referencedColumnName: 'id',
    //     },
    // })
    // promotions?: Promotion[];

    @OneToMany(() => MgpayIcPaymentToken, (mgIcPaymentTokens) => mgIcPaymentTokens.user)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'user_id',
    })
    mgIcPaymentTokens?: MgpayIcPaymentToken[];

    // @OneToMany(() => RestaurantHistories, (restaurantUpdateHistory) => restaurantUpdateHistory.user)
    // @JoinColumn({
    //     name: 'id',
    //     referencedColumnName: 'user_id',
    // })
    // restaurantUpdateHistories?: RestaurantHistories[];

    // @OneToMany(() => OrderUpdateHistory, (orderHistory) => orderHistory.user)
    // @JoinColumn({
    //     name: 'id',
    //     referencedColumnName: 'user_id',
    // })
    // orderHistory?: OrderUpdateHistory[];
}
