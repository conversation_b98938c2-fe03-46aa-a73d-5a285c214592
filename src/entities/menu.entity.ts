import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Food } from './food.entity';
import { Restaurant } from './restaurant.entity';

@Entity('menu')
export class Menu {
    constructor(partial: Partial<Menu>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    restaurant_id: number;

    @Column()
    ordinal_numbers: number;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @OneToMany(() => Food, (food) => food.menu)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'food_menu_id',
    })
    foods: Food[];

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant: Restaurant;
}
