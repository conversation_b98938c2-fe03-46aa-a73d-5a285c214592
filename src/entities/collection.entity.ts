import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>um<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { RestaurantNewsFeed } from './restaurantNewsFeed.entity';
import { TinyInt } from 'src/common/constants';
import { BannerGroup } from './bannerGroup.entity';

@Entity('collections')
export class Collection {
    constructor(name: string, code: string) {
        this.name = name;
        this.code = code;
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    code: string;

    @Column({ type: 'tinyint', default: 0 })
    active = 0 | 1;

    @Column()
    image: string;

    @Column()
    inactive_image: string;

    @Column()
    new_image: string;

    @Column()
    ordinal_numbers: number;

    @Column({ type: 'tinyint', default: 1 })
    open_24h: 0 | 1;

    @Column({ type: 'time' })
    open_time: string;

    @Column({ type: 'time' })
    close_time: string;

    @Column({ type: 'j<PERSON>', nullable: true, comment: '' })
    badge: ICollectionBadge;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => RestaurantNewsFeed, (newsFeed) => newsFeed.collection)
    @JoinColumn({ name: 'id', referencedColumnName: 'collection_id' })
    newsFeeds: RestaurantNewsFeed[];

    @OneToMany(() => BannerGroup, (banner) => banner.collection)
    @JoinColumn({ name: 'id', referencedColumnName: 'collection_id' })
    bannerGroups: BannerGroup[];

    setBadge(badge: ICollectionBadge) {
        this.badge = badge;
    }
}

export interface ICollectionBadge {
    text: string;
    active: TinyInt;
    active_from: string;
    active_to: string;
    bg_color: string;
    text_color: string;
}
