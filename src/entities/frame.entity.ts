import { Column, CreateDateColumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('frames')
export class Frame {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    frame_url: string;

    @Column({
        type: 'datetime',
    })
    from_date: string;

    @Column({
        type: 'datetime',
    })
    to_date: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}
