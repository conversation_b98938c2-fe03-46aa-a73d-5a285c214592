import * as _ from 'lodash';
import * as moment from 'moment';
import { TinyInt } from 'src/common/constants';
import { IMegapayInquiry } from 'src/models/order/orderPayments/interface/megapay.interface';
import {
    AfterLoad,
    BeforeUpdate,
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    ManyToOne,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { DeliveryAddress } from './deliveryAddress.entity';
import { FoodOrder } from './foodOrder.entity';
import { OrderPromotion } from './orderPromotion.entity';
import { OrderReciever } from './orderReciever.entity';
import { EOrderStatusId, OrderStatus } from './orderStatus.entity';
import { OrderTypeOfGood } from './orderTypeOfGood.entity';
import { EPaymentStatus, Payment } from './payment.entity';
import { Promotion } from './promotion.entity';
import { ERestaurantTradeDiscountType, Restaurant, RestaurantTradeDiscountPeriodType } from './restaurant.entity';
import { ShipperReview } from './shipperReview.entity';
import { User } from './user.entity';
import { OrderCancellationType } from './orderCancellationType.entity';
import { Province } from './province.entity';
import { VehicleType } from './vehicleType.entity';
import { OrderDriverExpense } from './orderDriverExpense.entity';
import { decimalColumnTransformer } from 'src/common/typorm/decimalColumnTransformer';
import { UserCompanyProfile } from './userCompanyProfile.entity';
import { OrderRestaurantPayment } from './orderRestaurantPayments.entity';
import { OrderDeliveryMethod } from './orderDeliveryMethod.entity';

export interface ZaloPayCheck {
    amount: number;
    pmcid: number;
    discountamount: number;
    apptime: number;
    ccbankcode: string;
    bankcode: string;
    bccode: number;
    bctransstatus: number;
    zptransid: number;
    isprocessing: boolean;
    suggestmessage: string;
    suggestaction: Array<any>;
    sub_error_code: string;
    returncode: number;
    returnmessage: string;
    refundid?: string;
    return_code?: number;
    return_message?: string;
    refund_id?: string;
    sub_return_message?: string;
    zp_trans_token?: string;
    zp_trans_id?: number;
}

export class AddressLocation {
    constructor(partial: Partial<AddressLocation>) {
        Object.assign(this, partial);
    }
    name: string = null;
    address: string = null;
    latitude: string;
    longitude: string;
}
@Entity('orders')
export class Order {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar' })
    code: string;

    @Column({ type: 'int', nullable: false })
    user_id: number;

    @Column({ type: 'int', nullable: false })
    order_status_id: number;

    @Column({ type: 'double', width: 5, precision: 2, nullable: false, default: 0.0 })
    tax: number;

    @Column({ type: 'double', width: 5, precision: 2, nullable: false, default: 0.0 })
    delivery_fee: number;

    @Column({ type: 'double', nullable: true })
    parking_fee: number;

    @Column({ type: 'int', default: 0 })
    service_fee: number;

    @Column({ type: 'int', nullable: false })
    hint: string;

    @Column({ type: 'int' })
    payment_id: number;

    @Column({ type: 'int' })
    delivery_address_id: number;

    @Column({ type: 'int' })
    driver_id: number;

    @Column({ type: 'varchar', length: 200 })
    order_coupon: string;

    @Column({ type: 'int', nullable: false, default: 0 })
    discount: number;

    @Column({ type: 'varchar', length: 100 })
    payment_type: string;

    @Column({ type: 'int', default: 0 })
    payment_status: number;

    @Column({
        type: 'text',
        transformer: {
            from(value) {
                try {
                    const json = JSON.parse(value);
                    if (json && json.item) {
                        json.item = JSON.parse(json.item);
                    }
                    return json;
                } catch (error) {
                    return value;
                }
            },
            to(value: Record<string, any>) {
                return JSON.stringify(value);
            },
        },
    })
    payment_info: Record<string, any> | null;

    @Column({
        type: 'text',
        transformer: {
            from(value) {
                try {
                    return JSON.parse(value);
                } catch (error) {
                    return value;
                }
            },
            to(value: Record<string, any>) {
                return JSON.stringify(value);
            },
        },
    })
    payment_check: ZaloPayCheck | IMegapayInquiry | Record<string, any> | null;

    @Column({ type: 'varchar', length: 250 })
    payment_gate_id: string;

    @Column({ type: 'int', default: 1 })
    province_id: number;

    @Column({ type: 'varchar', length: 250 })
    note_price: string;

    @Column({ type: 'text' })
    note: string;

    @Column({ type: 'int', nullable: false, default: 0 })
    point: number;

    @Column({ type: 'bigint' })
    shipper_debt_id: number;

    @Column({ type: 'double', width: 10, precision: 3 })
    trade_discount: number;

    @Column({ type: 'varchar', length: 30, nullable: true, default: null })
    restaurant_trade_discount_period_type: RestaurantTradeDiscountPeriodType;

    @Column({
        type: 'double',
        nullable: true,
    })
    surcharge: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    sub_total_price: number;

    @Column({
        name: 'total',
        type: 'double',
        nullable: true,
    })
    total_price: number;

    @Column({
        type: 'double',
        nullable: true,
    })
    distance: number;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    origin: string;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    destination: string;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    type: OrderType;

    @Column({
        type: 'int',
        nullable: true,
        unsigned: true,
        default: null,
    })
    delivery_method_id: number;

    @Column({
        type: 'varchar',
        nullable: true,
    })
    motobike_type: MotobikeType;

    @Column({
        type: 'text',
        nullable: true,
    })
    destination_address: string;

    @Column()
    promotion_id: number;

    @Column({ type: 'float', width: 10, precision: 3, default: 0 })
    vill_promotion_fee = 0;

    @Column({ type: 'float', width: 10, precision: 3, default: 0 })
    merchant_promotion_fee = 0;

    @Column()
    bill_image_url: string;

    @Column({
        type: 'int',
        width: 11,
        default: 0,
    })
    large_order_bonus: number;

    @Column({ type: 'double', width: 10, precision: 3, default: 0 })
    holiday_bonus: number;

    @Column({
        type: 'int',
        nullable: true,
        width: 11,
    })
    restaurant_id: number;

    @Column({ type: 'timestamp', nullable: true })
    assigned_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    confirmed_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    delivery_confirmed_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    purchased_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    canceled_at: Date;

    @Column({ type: 'timestamp', nullable: true })
    done_at: Date;

    @Column({ type: 'double', width: 10, precision: 2, default: 0 })
    tips: number;

    @Column({ type: 'double', width: 10, precision: 3, default: 0 })
    driver_payment_for_restaurant: number;

    @Column({ type: 'double', width: 10, precision: 3, default: 0 })
    long_distance_bonus: number;

    @Column({ type: 'int', default: null })
    main_id: number | null;

    @Column({ type: 'tinyint', default: 0 })
    is_suborder: TinyInt;

    @Column({ type: 'tinyint', default: 0 })
    has_suborder: TinyInt;

    @Column({ type: 'int', width: 10, default: 0 })
    priority_times_bonus: number;

    @Column({ type: 'tinyint', default: TinyInt.NO })
    calculated_driver_bonus: TinyInt;

    @Column({ type: 'date', default: TinyInt.NO })
    @Index('order_order_date')
    order_date: string;

    @Column({ type: 'simple-json' })
    pickup_location: AddressLocation | null;

    @Column({ type: 'simple-json' })
    destination_location: AddressLocation | null;

    @Column({ type: 'double', default: null })
    cod_amount: number | null;

    @Column({ type: 'int', default: null, unsigned: true })
    types_of_good_id: number;

    @Column({ type: 'json' })
    group_order: Record<string, any>;

    @Column({ type: 'tinyint', nullable: false })
    user_hidden: TinyInt;

    @Column({ type: 'varchar', nullable: true })
    user_name: string;

    @Column({ type: 'int', nullable: true })
    vehicle_type_id: number;

    @Column('decimal', {
        precision: 10,
        scale: 3,
        default: null,
        transformer: decimalColumnTransformer,
    })
    driver_vat: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: true,
    })
    cancellation_id: number;

    @Column({
        type: 'json',
    })
    restaurant_trade_discount_info: IOrderTradeDiscountInfo;

    @ManyToOne(() => OrderCancellationType)
    @JoinColumn({
        name: 'cancellation_id',
        referencedColumnName: 'id',
    })
    orderCancellationType?: OrderCancellationType;

    @Column({ type: 'varchar', length: 500, nullable: true })
    cancellation_reason_desc: string;

    @ManyToOne(() => OrderTypeOfGood)
    @JoinColumn({
        name: 'types_of_good_id',
        referencedColumnName: 'id',
    })
    typesOfGood?: OrderTypeOfGood;

    @Column({ type: 'json', nullable: true })
    package_details: IOrderPackageDetails;

    @Column({ type: 'json', nullable: true })
    eta: IEta | null;

    @ManyToOne(() => OrderDeliveryMethod)
    @JoinColumn({
        foreignKeyConstraintName: 'fk_order_delivery_method_id',
        name: 'delivery_method_id',
        referencedColumnName: 'id',
    })
    deliveryMethod?: OrderDeliveryMethod;

    @ManyToOne(() => User, (user) => user.driverOrders)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: User;

    // @ManyToOne(() => User, (user) => user.userOrders)
    // @JoinColumn({
    //     name: 'user_id',
    //     referencedColumnName: 'id',
    // })
    // user?: User;

    @Column({ type: 'json', nullable: true })
    user?: IOrderUserInfo;

    @Column({ type: 'decimal', precision: 10, scale: 3, default: 0.0 })
    delivery_discount: number;

    @Column({ type: 'decimal', precision: 10, scale: 3, default: 0.0 })
    sub_total_discount: number;

    @Column({ type: 'double', width: 5, precision: 2, nullable: false, default: 0.0 })
    delivery_subsidy: number;

    @Column({ type: 'timestamp', nullable: true })
    restaurant_prepared_at: Date;

    @Column({ nullable: true })
    invoice_company_profile_id: number;

    @ManyToOne(() => UserCompanyProfile)
    @JoinColumn({ name: 'invoice_company_profile_id' })
    companyProfile?: UserCompanyProfile;
    @Column('decimal', {
        precision: 10,
        scale: 3,
        default: null,
        transformer: decimalColumnTransformer,
    })
    restaurant_vat = 0;

    @Column('decimal', {
        precision: 10,
        scale: 3,
        default: null,
        transformer: decimalColumnTransformer,
    })
    restaurant_personal_income_tax = 0;

    @Column({ type: 'json', nullable: true })
    restaurant_tax_setting: IRestaurantTaxSetting;

    @OneToMany(() => FoodOrder, (foodOrder) => foodOrder.order)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    foodOrders?: FoodOrder[];

    @ManyToOne(() => OrderStatus)
    @JoinColumn({
        name: 'order_status_id',
        referencedColumnName: 'id',
    })
    status?: OrderStatus;

    // @OneToOne(() => DeliveryAddress)
    // @JoinColumn({
    //     name: 'delivery_address_id',
    //     referencedColumnName: 'id',
    // })
    deliveryAddress?: DeliveryAddress;

    @ManyToOne(() => Payment, (payment) => payment.orders)
    @JoinColumn({
        name: 'payment_id',
        referencedColumnName: 'id',
    })
    payment?: Payment;

    @ManyToOne(() => Promotion)
    @JoinColumn({
        name: 'promotion_id',
        referencedColumnName: 'id',
    })
    promotion?: Promotion;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @OneToMany(() => OrderPromotion, (orderPromotion) => orderPromotion.order)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    promotions?: OrderPromotion[];

    @OneToOne(() => OrderReciever)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    reciever?: OrderReciever;

    @OneToOne(() => ShipperReview)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    shipperReview: ShipperReview;

    @OneToOne(() => Province)
    @JoinColumn({
        name: 'province_id',
        referencedColumnName: 'id',
    })
    province?: Province;

    @OneToOne(() => VehicleType)
    @JoinColumn({
        name: 'vehicle_type_id',
        referencedColumnName: 'id',
    })
    vehicleType?: VehicleType;

    @OneToOne(() => OrderDriverExpense)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    driverExpense: OrderDriverExpense;

    @OneToOne(() => OrderRestaurantPayment)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'order_id',
    })
    orderRestaurantPayment: OrderRestaurantPayment;

    get driverIncome(): number {
        return _.sum([
            this.delivery_fee,
            this.tips,
            this.surcharge,
            this.long_distance_bonus,
            this.holiday_bonus,
            this.large_order_bonus,
        ]);
    }

    private calcSubTotalPrice() {
        let subTotalPrice = 0;
        this.foodOrders.forEach(({ price, extras, quantity, orderFoodOptions }) => {
            subTotalPrice += (_.sumBy(extras, 'price') + _.sumBy(orderFoodOptions, 'price') + price) * quantity;
        });
        this.sub_total_price = subTotalPrice;
        this.calcTotalPrice();
    }

    private calcTotalPrice() {
        this.total_price =
            this.sub_total_price +
            this.delivery_fee +
            this.surcharge +
            this.tax +
            this.service_fee -
            this.discount +
            this.tips;
        if (this.parking_fee) this.total_price += this.parking_fee;
    }

    setTax(tax: number) {
        this.tax = tax;
        this.calcTotalPrice();
    }

    setDeliveryFee(deliveryFee: number) {
        this.delivery_fee = deliveryFee;
        this.calcTotalPrice();
    }

    setDriverVat(vatPercentage: number, tradeDiscountPercentage: number, surchargeVatPercentage: number) {
        let delivery_vat = 0;
        let surcharge_vat = 0;
        if (
            tradeDiscountPercentage &&
            vatPercentage <= 100 &&
            vatPercentage >= 0 &&
            tradeDiscountPercentage <= 100 &&
            tradeDiscountPercentage >= 0
        ) {
            delivery_vat =
                (((this.delivery_fee * (100 - tradeDiscountPercentage)) / (100 + vatPercentage)) * vatPercentage) / 100;
        }
        if (surchargeVatPercentage <= 100 && surchargeVatPercentage >= 0 && this.surcharge > 0) {
            surcharge_vat = (this.surcharge / (100 + surchargeVatPercentage)) * surchargeVatPercentage;
        }
        const vatAmount = _.round(delivery_vat, 3) + _.round(surcharge_vat, 3);
        this.driver_vat = _.round(vatAmount, 3); // fix sum error in js float number, xp: 0.1 + 0.2 = 0.30000000000000004
    }

    setParkingFee(parkingFee: number) {
        if (!_.isNil(parkingFee)) this.parking_fee = parkingFee;
        this.calcTotalPrice();
    }

    assignDriver(user: User) {
        if (user && user.id != this.driver_id) {
            this.assigned_at = new Date();
        }
        this.driver = user;
        this.driver_id = user?.id;
    }

    changeStatus(status: OrderStatus) {
        if (status && status.id != this.order_status_id) {
            switch (status.id) {
                case EOrderStatusId.COMMING_TO_RESTAURANT:
                    this.confirmed_at = new Date();
                    break;
                case EOrderStatusId.SHIPPING:
                    this.delivery_confirmed_at = new Date();
                    break;
                case EOrderStatusId.ARRIVED:
                    this.done_at = new Date();
                    break;
                case EOrderStatusId.CANCELED:
                    this.canceled_at = new Date();
                    break;
            }
        }
        this.status = status;
        this.order_status_id = status?.id;
    }

    get isTradeDiscountPeriodTypeDirectToWallet(): boolean {
        return this.restaurant_trade_discount_period_type === RestaurantTradeDiscountPeriodType.DIRECT_WALLET;
    }

    changePaymentStatus(paymentStatus: EPaymentStatus) {
        if (this.payment) {
            if (this.payment.status != paymentStatus && paymentStatus == EPaymentStatus.PAID) {
                this.purchased_at = new Date();
            }
            this.payment.status = paymentStatus;
        }
    }

    @BeforeUpdate()
    setUpdatedField() {
        this.updated_at = moment(new Date()).utc().add(7, 'hour').toDate();
    }

    @AfterLoad()
    transformDate() {
        this.updated_at = moment(this.updated_at).utc().subtract(7, 'hour').toDate();
        this.created_at = moment(this.created_at).utc().subtract(7, 'hour').toDate();

        if (this.delivery_address_id && this.destination_location) {
            this.deliveryAddress = new DeliveryAddress({
                id: this.delivery_address_id,
                address: this.destination_location?.address,
                latitude: this.destination_location?.latitude,
                longitude: this.destination_location?.longitude,
                user_id: this.user_id,
                province_id: this.province_id,
            });
        }
    }
}

export enum OrderType {
    VILLFOOD = 'VILLFOOD',
    VILLBIKE = 'VILLBIKE',
    VILLCAR = 'VILLCAR',
    VILLEXPRESS = 'VILLEXPRESS',
}

export enum MotobikeType {
    STANDARD = 'STANDARD',
    PREMINUM = 'PREMIUM',
}

export interface IOrderUserInfo {
    id: number;
    name: string;
    phone: string;
    email: string;
    avatar: string;
}
export interface IOrderPackageDetails {
    length: number;
    width: number;
    height: number;
    weight: number;
    type_of_good: string;
}
export interface IOrderTradeDiscountInfo {
    trade_discount: number;
    trade_discount_type: ERestaurantTradeDiscountType;
}

export interface IRestaurantTaxSetting {
    vat_percentage: number;
    personal_income_tax_percentage: number;
    business_category: string;
}

export interface IEta {
    /* Estimated Time of Arrival for drop off */
    drop_off: number;

    /* Estimated Time of Arrival for pick up */
    pick_up: number;
}
