import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    <PERSON>inColumn,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Restaurant } from './restaurant.entity';
import { WorkTime } from './workTime.entity';

@Entity('restaurant_business_hours')
export class RestaurantBusinessHours {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'int' })
    day: number;

    @Column({ type: 'int' })
    restaurant_id: number;

    @Column({ type: 'tinyint' })
    is_active: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @OneToMany(() => WorkTime, (workTime) => workTime.businessHour)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'restaurant_business_hour_id',
    })
    workTimes?: WorkTime[];

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;
}
