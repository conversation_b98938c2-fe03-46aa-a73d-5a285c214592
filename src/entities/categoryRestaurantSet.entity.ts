import { Category } from 'src/entities/category.entity';
import { Restaurant } from 'src/entities/restaurant.entity';
import { Entity, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';

@Entity('category_restaurant_sets')
export class CategoryRestaurantSet {
    @PrimaryColumn()
    restaurant_id: number;

    @PrimaryColumn()
    category_id: number;

    @ManyToOne(() => Restaurant)
    @JoinColumn({
        name: 'restaurant_id',
        referencedColumnName: 'id',
    })
    restaurant?: Restaurant;

    @ManyToOne(() => Category)
    @JoinColumn({
        name: 'category_id',
        referencedColumnName: 'id',
    })
    category?: Category;
}
