import { ApiProperty } from '@nestjs/swagger';
import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('order_recievers')
export class OrderReciever {
    constructor(partial: Partial<OrderReciever>) {
        Object.assign(this, partial);
    }
    @PrimaryColumn()
    order_id: number;

    @Column({ type: 'varchar', length: 20, nullable: false })
    phone_number: string;

    @Column({ type: 'varchar', length: 60, nullable: false })
    name: string;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;
}
