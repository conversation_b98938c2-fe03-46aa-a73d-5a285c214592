import {
    Column,
    CreateDateColumn,
    <PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { FaqCategory } from './faqCategory.entity';
import { INotificationExternalData } from './notificationPlan.entity';

@Entity('faqs')
export class Faq {
    constructor(
        question: string,
        answer: string,
        routeName: string,
        routeId: string,
        faqCategory: FaqCategory,
        title: string,
        subtitle: string,
        desc: string,
        icon: string,
        external_data: INotificationExternalData,
    ) {
        this.question = question;
        this.answer = answer;
        this.route_name = routeName;
        this.route_id = routeId;
        this.faqCategory = faqCategory;
        this.created_at = this.updated_at = new Date();
        this.title = title;
        this.subtitle = subtitle;
        this.desc = desc;
        this.icon = icon;
        this.external_data = external_data;
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    question: string;

    @Column()
    image: string;

    @Column()
    faq_category_id: number;

    @Column()
    answer: string;

    @Column()
    route_name: string;

    @Column()
    route_id: string;

    @Column()
    title: string;

    @Column()
    subtitle: string;

    @Column()
    desc: string;

    @Column()
    icon: string;

    @Column('simple-json')
    external_data: INotificationExternalData;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => FaqCategory, (faqCategory) => faqCategory.faqs)
    @JoinColumn({
        name: 'faq_category_id',
        referencedColumnName: 'id',
    })
    faqCategory: FaqCategory;
}
