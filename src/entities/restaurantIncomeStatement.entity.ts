import { Entity, PrimaryGeneratedColumn, Column, Unique, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('restaurant_income_statement')
// @Unique('restaurant_income_statement_pk', ['restaurant', 'interval_type', 'to_date', 'from_date'])
export class RestaurantIncomeStatement {
    @PrimaryGeneratedColumn({ type: 'bigint' })
    id: string;

    @Column({ type: 'int', unsigned: true, default: 0 })
    total_order: number;

    @Column({ type: 'double', default: 0 })
    total_order_trends: number;

    @Column({ type: 'int', unsigned: true, default: 0 })
    total_canceled_count: number;

    @Column({ type: 'double', default: 0 })
    total_canceled_count_trends: number;

    @Column({ type: 'int', unsigned: true, default: 0 })
    total_delivered_order: number;

    @Column({ type: 'double', default: 0 })
    total_delivered_order_trends: number;

    @Column({ type: 'varchar', length: 30, charset: 'utf8mb4', collation: 'utf8mb4_unicode_ci' })
    interval_type: string;

    @Column({ type: 'double', default: 0 })
    gross_amount: number;

    @Column({ type: 'double', default: 0 })
    gross_amount_trends: number;

    @Column({ type: 'double', default: 0 })
    net_amount: number;

    @Column({ type: 'double', default: 0 })
    net_amount_trends: number;

    @Column({ type: 'double', default: 0 })
    trade_discount: number;

    @Column({ type: 'double', default: 0 })
    trade_discount_trends: number;

    @Column({ type: 'double', default: 0 })
    promo_discount: number;

    @Column({ type: 'double', default: 0 })
    promo_discount_trends: number;

    @Column({ type: 'date' })
    from_date: Date;

    @Column({ type: 'date' })
    to_date: Date;

    @Column({ type: 'int', default: 0 })
    total_canceled_order: number;

    @Column({ type: 'double', default: 0 })
    total_discount: number;

    // @Column({ type: 'double', default: 0 })
    // net_amount_after_tax: number;

    // @Column({ type: 'double', default: 0 })
    // net_amount_after_tax_trends: number;

    @Column({ type: 'double', default: 0 })
    order_restaurant_tax: number;

    @Column({ type: 'double', default: 0 })
    order_restaurant_tax_trends: number;

    @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updated_at: Date;

    @Column({ type: 'double', default: 0 })
    total_canceled_order_trends: number;

    @Column({ type: 'double', default: 0 })
    total_discount_trends: number;
}
