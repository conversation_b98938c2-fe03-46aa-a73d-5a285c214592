import { decimalColumnTransformer } from 'src/common/typorm/decimalColumnTransformer';
import {
    Column,
    CreateDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { TaxReportingDriverPeriod } from './taxReportingDriverPeriod.entity';

export enum EDriverTaxFinalizationStatus {
    completed = 'completed', // when driver has finalized the tax for the year and paid the tax to the government
    pending = 'pending', // when driver has not finalized the tax for the year
    tax_gov_pending = 'tax_gov_pending', // when driver has finalized the tax for the year, but the tax has not been paid to the government
    in_progress = 'in_progress', // when having event and not completed yet
    // reviewed = 'reviewed',
    // disputed = 'disputed', // when driver disputes the tax finalization report and requests a review
    // canceled = 'canceled',
}

export const DriverTaxFinalizationPendingStatusGroup = [
    EDriverTaxFinalizationStatus.pending,
    EDriverTaxFinalizationStatus.tax_gov_pending,
];

@Entity('driver_tax_finalization')
export class DriverTaxFinalization {
    constructor(partial: Partial<DriverTaxFinalization>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        nullable: false,
    })
    period_id: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
        comment: 'Total income of driver for the year',
    })
    total_income: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
    })
    total_tax: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
        comment: 'Tax amount withheld from driver, which is the tax amount that driver has paid to the government',
    })
    tax_withheld: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
        comment: 'Tax amount refunded to driver',
    })
    tax_refund: number;

    @Column({
        type: 'decimal',
        precision: 16,
        scale: 3,
        nullable: false,
        transformer: decimalColumnTransformer,
        comment: 'Tax amount paid to the government',
    })
    tax_gov: number;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    status: EDriverTaxFinalizationStatus;

    @Column({
        type: 'varchar',
        length: 500,
        nullable: true,
    })
    note: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => Driver, (driver) => driver.id)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver?: Driver;

    @ManyToOne(() => TaxReportingDriverPeriod, (period) => period.id)
    @JoinColumn({
        name: 'period_id',
        referencedColumnName: 'id',
    })
    period?: TaxReportingDriverPeriod;

    @Column({
        select: false,
        insert: false,
        update: false,
    })
    tax_debt: number;
}
