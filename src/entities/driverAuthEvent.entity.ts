import { Column, <PERSON>tity, <PERSON>inColumn, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Driver } from './driver.entity';

@Entity('driver_auth_events')
export class DriverAuthEvent {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    device_id: string;

    @Column()
    driver_id: number;

    @Column()
    os: string;

    @Column()
    device_type: string;

    @Column()
    device_idiom: string;

    @Column()
    ip: string;

    @Column()
    action: string;

    @Column()
    status: string;

    @Column()
    failure_reason: string;

    @Column()
    note: string;

    @Column()
    created_at: Date;

    @ManyToOne(() => Driver)
    @JoinColumn({
        name: 'driver_id',
        referencedColumnName: 'id',
    })
    driver: Driver;
}
