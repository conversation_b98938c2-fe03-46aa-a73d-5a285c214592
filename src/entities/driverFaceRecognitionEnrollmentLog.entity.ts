import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Driver } from './driver.entity';
import { DriverFaceRecognition } from './driverFaceRecognition.entity';
import { DriverFaceRecognitionEnrollment } from './driverFaceRecognitionEnrollment.entity';
import { FaceRecognitionEnrollmentType } from './faceRecognitionEnrollmentType.entity';

@Entity('driver_face_recognition_enrollment_logs')
export class DriverFaceRecognitionEnrollmentLog {
    constructor(partial: Partial<DriverFaceRecognitionEnrollmentLog>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn({
        type: 'int',
        unsigned: true,
    })
    id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    enrollment_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    driver_id: number;

    @Column({
        type: 'int',
        unsigned: true,
        nullable: false,
    })
    face_recognition_id: number;

    @Column({
        type: 'tinyint',
    })
    is_matching: TinyInt;

    @Column({
        type: 'tinyint',
    })
    is_verified: TinyInt;

    @Column({
        type: 'tinyint',
        unsigned: true,
        nullable: false,
    })
    match_confidence: number;

    @Column({
        type: 'tinyint',
        unsigned: true,
        nullable: false,
    })
    verify_confidence: number;

    @Column({
        type: 'varchar',
        length: 700,
    })
    input_face_url_right: string;

    @Column({
        type: 'varchar',
        length: 700,
    })
    input_face_url_middle: string;

    @Column({
        type: 'varchar',
        length: 700,
    })
    input_face_url_left: string;

    @Column({
        type: 'varchar',
        length: 700,
    })
    input_video_url: string;

    @Column({
        type: 'varchar',
        length: 1535,
    })
    response_json: string;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => Driver)
    @JoinColumn({ name: 'driver_id', referencedColumnName: 'id' })
    driver: Driver;

    @ManyToOne(() => FaceRecognitionEnrollmentType)
    @JoinColumn({ name: 'face_recognition_id', referencedColumnName: 'id' })
    type: FaceRecognitionEnrollmentType;

    @ManyToOne(() => DriverFaceRecognitionEnrollment)
    @JoinColumn({ name: 'enrollment_id', referencedColumnName: 'id' })
    enrollment: DriverFaceRecognitionEnrollment;

    @ManyToOne(() => DriverFaceRecognition)
    @JoinColumn({ name: 'face_recognition_id', referencedColumnName: 'id' })
    faceRecognition: DriverFaceRecognition;
}

export enum ESideFace {
    SIDE_RIGHT = 'SIDE_RIGHT',
    SIDE_LEFT = 'SIDE_LEFT',
    SIDE_MIDDLE = 'SIDE_MIDDLE',
    SIDE_UP_TURN = 'SIDE_UP_TURN',
    UNKNOWN = 'UNKNOWN',
}
