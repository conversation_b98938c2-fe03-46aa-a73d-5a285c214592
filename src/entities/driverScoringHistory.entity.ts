import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON>n,
    <PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>inTable,
    ManyToMany,
    ManyToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';
import { Driver } from './driver.entity';
import { DriverScore } from './driverScore.entity';
import { ScoringRule } from './driverScoringRule.entity';
import { EPointType, EScoringSourceType, EScoringHistoryStatus } from './types/EScoringRule.enum';

@Entity('driver_scoring_histories')
export class ScoringHistory {
    constructor(partial: Partial<ScoringHistory>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    source_id: number;

    @Column()
    driver_id: number;

    @Column()
    rule_id: number;

    @Column({ length: 255 })
    rule_name: string;

    //loại để biết cộng điểm hay trừ điểm
    @Column({
        type: 'enum',
        enum: EPointType,
        nullable: true,
        comment: 'Source type for this scoring rule',
    })
    points_type: EPointType;

    @Column({
        type: 'enum',
        enum: EScoringSourceType,
        nullable: true,
        comment: 'Source type for this scoring rule',
    })
    source_type: EScoringSourceType;

    @Column({ type: 'int' })
    points_awarded: number;

    @Column({ type: 'json', nullable: true })
    points_before: Record<string, number>;

    @Column({ type: 'json', nullable: true })
    points_after: Record<string, number>;

    @Column({
        type: 'text',
        nullable: true,
        comment: 'text description of calculation details',
    })
    calculation_details: string;

    @Column({ type: 'text', nullable: true })
    notes: string; // Additional notes for manual scoring

    // Status management fields
    @Column({
        type: 'enum',
        enum: EScoringHistoryStatus,
        default: EScoringHistoryStatus.SUCCESS,
        comment: 'Status of the scoring history record',
    })
    status: EScoringHistoryStatus;

    // Refund related fields
    @Column({ nullable: true })
    refunded_at: Date;

    @Column({ nullable: true })
    refunded_by: number;

    @Column({ type: 'text', nullable: true })
    refund_reason: string;

    @CreateDateColumn()
    created_at: Date;

    @Column({ nullable: true })
    processed_by: number;

    @ManyToOne(() => ScoringRule, {
        onDelete: 'SET NULL',
    })
    @JoinColumn({ name: 'rule_id' })
    rule: ScoringRule;

    @ManyToOne(() => Driver, {
        onDelete: 'SET NULL',
    })
    @JoinColumn({ name: 'driver_id' })
    driver: Driver;

    

    @ManyToMany(() => DriverScore, (driverScore) => driverScore.scoring_histories)
    @JoinTable({
        name: 'driver_scoring_history_driver_scores',
        joinColumn: { name: 'scoring_history_id', referencedColumnName: 'id' },
        inverseJoinColumn: {
            name: 'driver_score_id',
            referencedColumnName: 'id',
        },
    })
    driver_scores: DriverScore[];
}
