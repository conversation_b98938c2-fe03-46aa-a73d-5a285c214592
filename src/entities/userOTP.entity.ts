import { Column, CreateDate<PERSON>olumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum ESmsProvider {
    ABENLA = 'ABENLA',
}

export enum ESmsType {
    FORGOT_PASSWORD = 'FORGOT_PASSWORD',
    REGISTER = 'REGISTER',
}

@Entity('sms_codes')
export class UserSmsCode {
    constructor(partial: Partial<UserSmsCode>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    otp_code: string;

    @Column()
    phone: string;

    @Column()
    exp_time: string;

    @Column()
    province_id: string;

    @Column()
    provider: ESmsProvider;

    @Column()
    abl_res_code: string;

    @Column()
    status: number;

    @Column()
    otp_used: number;

    @Column()
    sms_type: ESmsType;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}

export enum EUserStatisticInterval {
    WEEK = 'WEEK',
}
