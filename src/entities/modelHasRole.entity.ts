import { ModelRolesProvinces } from './modelRolesProvinces.entity';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, PrimaryColumn } from 'typeorm';
import { Role } from './role.entity';
import { User } from './user.entity';

@Entity('model_has_roles')
export class ModelHasRole {
    @Column()
    model_id: number;

    @Column({
        type: 'varchar',
        nullable: false,
        default: () => 'App\\Models\\User',
    })
    model_type: string = ModelRoleType.USER;

    @Column()
    role_id: number;

    @PrimaryColumn()
    id: string;

    @ManyToOne(() => User, (user) => user.modelHasRoles)
    @JoinColumn({
        name: 'model_id',
        referencedColumnName: 'id',
    })
    user: User;

    @ManyToOne(() => Role, (role) => role.modelHasRoles)
    @JoinColumn({
        name: 'role_id',
        referencedColumnName: 'id',
    })
    role: Role;

    @OneToMany(() => ModelRolesProvinces, (modelRolesProvinces) => modelRolesProvinces.modelRole)
    @JoinColumn({
        name: 'id',
        referencedColumnName: 'model_role_id',
    })
    modelRolesProvinces: ModelRolesProvinces[];

    generateId() {
        this.id = `${this.model_id}_${this.role_id}`;
    }
}

export enum ModelRoleType {
    USER = 'App\\Models\\User',
}
