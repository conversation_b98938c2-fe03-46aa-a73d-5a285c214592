import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON><PERSON>C<PERSON><PERSON>n, OneToOne } from 'typeorm';
import { RestaurantReview } from './restaurantReview.entity';
import { ReviewTag } from './reviewTag.entity';

@Entity('restaurant_user_review_tags')
export class RestaurantUserReviewTags {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    review_id: number;

    @Column()
    tag_id: number;

    @ManyToOne(() => RestaurantReview, (review) => review.tags)
    @JoinColumn({
        name: 'review_id',
        referencedColumnName: 'id',
    })
    review: RestaurantReview;

    @OneToOne(() => ReviewTag, (tag) => tag.restaurantUserReviewTags)
    @JoinColumn({
        name: 'tag_id',
        referencedColumnName: 'id',
    })
    tag: ReviewTag;
}
