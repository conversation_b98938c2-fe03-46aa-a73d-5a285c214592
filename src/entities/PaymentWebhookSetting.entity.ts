import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON>n,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { TrackingBankProvider } from './TrackingBankProvider.entity';

@Entity('payment_webhook_settings')
export class PaymentWebhookSetting {
    constructor(partial: Partial<PaymentWebhookSetting>) {
        Object.assign(this, partial);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'json', nullable: true })
    headers: Record<string, string>;

    @Column({ type: 'json', nullable: true })
    auth_config: IWebhookAuthConfig;

    @Column({ type: 'json', nullable: true })
    payload_template: Record<string, any>;

    @Column({ default: 'POST' })
    http_method: string;

    @Column({ default: 30 })
    timeout_seconds: number;

    @Column({ default: 3 })
    max_retry_attempts: number;

    @Column({ default: true })
    is_active: boolean;

    @Column({ default: false })
    is_verified: boolean;

    @Column({ type: 'json', nullable: true })
    trigger_events: string[];

    @Column({ type: 'json', nullable: true })
    filter_conditions: IWebhookFilterConditions;



    @Column({ nullable: true })
    tracking_bank_provider_id: number;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @ManyToOne(() => TrackingBankProvider, (provider) => provider.id, { nullable: true })
    @JoinColumn({ name: 'tracking_bank_provider_id', referencedColumnName: 'id' })
    tracking_bank_provider: TrackingBankProvider;
}

export interface IWebhookAuthConfig {
    type: 'none' | 'basic' | 'bearer' | 'api_key';
    username?: string;
    password?: string;
    token?: string;
    api_key?: string;
    api_key_header?: string;
}

export interface IWebhookFilterConditions {
    min_amount?: number;
    max_amount?: number;
    account_numbers?: string[];
    transaction_types?: string[];
    bank_codes?: string[];
}

export enum EWebhookTriggerEvent {
    PAYMENT_RECEIVED = 'payment_received',
    PAYMENT_SENT = 'payment_sent',
    BALANCE_UPDATED = 'balance_updated',
    TRANSACTION_VERIFIED = 'transaction_verified',
    TRANSACTION_FAILED = 'transaction_failed',
}
