import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { Order } from './order.entity';
import { EDiscountAppliesOn, Promotion } from './promotion.entity';
import { TinyInt } from 'src/common/constants';

@Entity('order_promotions')
export class OrderPromotion {
    constructor(parital: Partial<OrderPromotion>) {
        Object.assign(this, parital);
    }

    @PrimaryGeneratedColumn()
    id: number;

    @Column('simple-json')
    promotion: Promotion;

    @Column({ type: 'double', width: 10, scale: 3, default: 0 })
    value: number;

    @Column({ type: 'varchar', length: 255, default: null })
    promotion_code: string;

    @Column({ type: 'double', width: 10, scale: 3, default: 0 })
    vill_fee: number;

    @Column({ type: 'double', width: 10, scale: 3, default: 0 })
    merchant_fee: number;

    @Column({ type: 'int', width: 10, nullable: false, unsigned: true })
    order_id: number;

    @Column({ type: 'int', width: 10, nullable: false, unsigned: true })
    promotion_id: number;

    @Column({ type: 'int', width: 10, nullable: false, unsigned: true })
    promotion_id_v2: number;

    @Column('simple-json')
    promotion_v2: IPromotionV2;

    @ManyToOne(() => Order)
    @JoinColumn({
        name: 'order_id',
        referencedColumnName: 'id',
    })
    order?: Order;

    get merchantSubTotalDiscount(): number {
        if (
            this.promotion_id_v2 &&
            this.promotion_v2 &&
            this.promotion_v2.type_id === PromotionTypeIdMap.spend_x_get_sub_total_discount
        ) {
            return this.merchant_fee;
        }
        if (this.promotion_id && this.promotion && this.promotion.discount_applies_on === EDiscountAppliesOn.BILL) {
            return this.merchant_fee;
        }
        return 0;
    }
}

export interface IPromotionV2 {
    title: string;
    sub_title: string;
    code: string; // unique code like 'GET50OFF'
    display_code: string; // code to display to user like 'GET50', because code can be like 'GET50OFF'
    icon: string;
    thumbnail: string;
    description: string;
    active_from: string;
    active_to: string;
    is_active: TinyInt;
    level: ELevel;
    type: EPromotionType;
    used: number;
    creator_id: number;
    type_id: number;
    // role: EActivityLogRole;
    // condition?: PromotionCondition;
    // contribution?: PromotionContribution;
    // restaurants?: RestaurantPromotion[];
    // discount: PromotionDiscount;
}

export enum ELevel {
    item = 'item', // get x for discount, buy x get discount
    transaction = 'transaction', // like buy x get discount, spend x get discount
}

export enum EPromotionType {
    spend_x_get_delivery_discount = 'spend_x_get_delivery_discount', // spend x get delivery discount. example in vietnamese: mua 100k giảm 10% phí giao hàng
    spend_x_get_sub_total_discount = 'spend_x_get_sub_total_discount', // spend x get sub total discount. example in vietnamese: mua 100k giảm 10% tổng hóa đơn
    buy_x_get_y = 'buy_x_get_y', // buy x in y get x. example in vietnamese: mua 2 bánh mì trong 1 hóa đơn 100k tặng 1 cocacola
    spend_x_get_y = 'spend_x_get_y', // spend x get y. example in vietnamese: mua 100k tặng 1 cocacola
    bike_move_x_get_delivery_discount = 'bike_move_x_get_delivery_discount', // move x get delivery discount. example in vietnamese: di chuyển 10km giảm 10% phí giao hàng xe máy
    express_move_x_get_delivery_discount = 'express_move_x_get_delivery_discount', // another name: fast_move_x_get_delivery_discount. example in vietnamese: di chuyển 10km giảm 10% phí giao hàng nhanh cho giao hàng nhanh
    car_move_x_get_delivery_discount = 'car_move_x_get_delivery_discount', // move x get delivery discount. example in vietnamese: di chuyển 10km giảm 10% phí giao hàng ô tô
}

export const PromotionTypeIdMap: {
    [key in EPromotionType]: number;
} = {
    [EPromotionType.spend_x_get_delivery_discount]: 1,
    [EPromotionType.spend_x_get_sub_total_discount]: 2,
    [EPromotionType.buy_x_get_y]: 3,
    [EPromotionType.spend_x_get_y]: 4,
    [EPromotionType.bike_move_x_get_delivery_discount]: 5,
    [EPromotionType.express_move_x_get_delivery_discount]: 6,
    [EPromotionType.car_move_x_get_delivery_discount]: 7,
};
