import {
    Column,
    CreateDate<PERSON><PERSON>umn,
    DeleteDateColumn,
    Entity,
    JoinColumn,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import {
    ERestaurantOperatingStatus,
    ERestaurantTradeDiscountType,
    RestaurantTradeDiscountPeriodType,
} from './restaurant.entity';
import { User } from './user.entity';

@Entity('global_restaurants')
export class GlobalRestaurant {
    constructor(partial: Partial<GlobalRestaurant>) {
        Object.assign(this, partial);
    }
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    image: string;

    @Column()
    background: string;

    @Column()
    address: string;

    @Column()
    latitude: string;

    @Column()
    longitude: string;

    @Column()
    phone: string;

    @Column()
    mobile: string;

    @Column()
    cooperating: boolean;

    @Column()
    province_id: number;

    @Column()
    sub_province_id: number;

    @Column()
    restaurant_id: number;

    @Column()
    seller_id: number;

    @Column()
    is_trial: boolean;

    @Column()
    trade_discount_period_type: RestaurantTradeDiscountPeriodType;

    @Column()
    trade_discount_type: ERestaurantTradeDiscountType;

    @Column({
        type: 'double',
        default: 0,
        nullable: false,
    })
    trade_discount: number;

    @Column({
        type: 'varchar',
        length: 30,
    })
    operating_status: ERestaurantOperatingStatus;

    @Column({
        type: 'timestamp',
    })
    reopen_time: Date;

    @Column({
        type: 'varchar',
        length: 30,
    })
    code: string;

    @Column()
    thumbnails: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;

    @DeleteDateColumn()
    deleted_at: Date;

    // @ManyToOne(() => User)
    // @JoinColumn({
    //     name: 'seller_id',
    //     referencedColumnName: 'id',
    // })
    // seller: User;
}
