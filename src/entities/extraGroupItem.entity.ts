import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { ExtraGroup } from './extraGroup.entity';

@Entity('extra_group_item')
export class ExtraGroupItem {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @ManyToOne(() => ExtraGroup, (extraGroup) => extraGroup.extraGroupItems)
    @JoinColumn({
        name: 'extra_group_id',
        referencedColumnName: 'id',
    })
    extraGroup?: ExtraGroup;
}
