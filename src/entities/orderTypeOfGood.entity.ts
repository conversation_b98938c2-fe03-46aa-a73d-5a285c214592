import { ApiProperty } from '@nestjs/swagger';
import { TinyInt } from 'src/common/constants';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('order_types_of_goods')
export class OrderTypeOfGood {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'varchar', length: 30, nullable: false })
    name: string;

    @Column({ type: 'text' })
    icon: string;

    @Column({ type: 'tinyint', default: TinyInt.YES, nullable: false })
    active: TinyInt;

    @UpdateDateColumn()
    updated_at: Date;

    @CreateDateColumn()
    created_at: Date;
}
