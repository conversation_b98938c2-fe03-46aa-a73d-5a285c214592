import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { ShipperRanking } from './shipperRankings.entity';

@Entity('job_rank_benefits')
export class JobRankBenefit {
    @PrimaryColumn()
    rank_id: number;

    @Column({ type: 'int', default: 0 })
    max_rejected_jobs: number;

    @Column({ type: 'int', default: 0 })
    first_job_waiting_seconds: number;

    @Column({ type: 'text', nullable: true, default: true })
    description: string;

    @OneToOne(() => ShipperRanking)
    @JoinColumn({
        name: 'rank_id',
        referencedColumnName: 'id',
    })
    rank?: ShipperRanking;
}
