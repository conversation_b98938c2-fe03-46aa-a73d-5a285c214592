import { Injectable, Logger } from '@nestjs/common';
import * as Mail from 'nodemailer/lib/mailer';
import { createTransport } from 'nodemailer';
import * as hbs from 'nodemailer-express-handlebars';
import { ConfigService } from '@nestjs/config';
import { Attachment } from 'nodemailer/lib/mailer';
import { Restaurant } from 'src/entities/restaurant.entity';

@Injectable()
export class MailService {
    private nodemailerTransport: Mail;
    private logger = new Logger(MailService.name);
    constructor(private configService: ConfigService) {
        this.nodemailerTransport = createTransport({
            host: this.configService.get('emailHost'),
            port: 587,
            secure: false,
            auth: {
                user: this.configService.get('emailUser'),
                pass: this.configService.get('emailPassword'),
            },
        });
        this.setupTemplates();
    }

    private setupTemplates() {
        this.nodemailerTransport.use(
            'compile',
            hbs({
                viewEngine: {
                    extname: '.hbs',
                    layoutsDir: 'src/mail/templates',
                    defaultLayout: 'template',
                },
                viewPath: 'src/mail/templates',
                extName: '.hbs',
            }),
        );
    }

    async sendMail(to: string, subject: string, template: string, context: any, attachments: Attachment[]) {
        const options = {
            from: this.configService.get('emailUser'),
            to,
            subject,
            template,
            context,
            attachments,
        };
        try {
            this.logger.log(`Sending mail to: ${JSON.stringify(options.to)}`);
            return await this.nodemailerTransport.sendMail(options);
        } catch (e) {
            this.logger.error(`[sendMail] | message: ${e.message} | stack: ${e.stack}`);
        }
    }

    sendMerchantContractFile(to: string, file: Buffer, restaurant?: Restaurant) {
        const attachment = { content: file, filename: 'hopDongVillShip.pdf', encoding: 'base64' };
        const context = { restaurant_name: '', title: '', restaurant_address: '', phone: '' };
        if (restaurant) {
            context.restaurant_address = 'Địa chỉ cửa hàng: ' + restaurant.address;
            context.phone = 'Số điện thoại: ' + restaurant.phone;
        }
        context.restaurant_name = restaurant && restaurant.name ? restaurant.name : 'Anh/Chị';
        context.title = `Chúng tôi xin xác nhận quá trình ký kết hợp đồng hợp tác giữa "${context.restaurant_name}" và VillShip đã được hoàn thành.`;
        this.sendMail(
            to,
            '[VILLSHIP MERCHANT] XÁC NHẬN HỢP ĐỒNG TRỞ THÀNH ĐỐI TÁC NHÀ HÀNG CÙNG VILLSHIP',
            'template',
            context,
            [attachment],
        );
    }
}
