/**
 * Interface for 9Pay Transaction IPN (Instant Payment Notification)
 * Received when 9Pay notifies about transaction status
 */
export interface INPayTransactionIPN {
    /**
     * Transaction code
     * @example "8MZ1BV6ROMPO"
     */
    code: string;

    /**
     * Transaction title
     * @example "Thanh toán tại đối tác ABCD"
     */
    title: string;

    /**
     * Amount before fees
     * @example 200000
     */
    actual_amount: number;

    /**
     * Final amount to be paid (after deducting fees or promotions)
     * @example 200000
     */
    amount: number;

    /**
     * Transaction fee
     * @example 0
     */
    fee: number;

    /**
     * Transaction status:
     * 1 → Initialized
     * 2 → Processing
     * 3 → Success
     * 4 → Failed
     * 5 → Cancelled
     * @example 3
     */
    status: ENPayTransactionIPNStatus;

    /**
     * Transaction type:
     * 1 → QR Payment
     * 2 → Top-up
     * 3 → Withdraw to linked bank
     * 4 → Wallet to wallet transfer
     * 5 → Payment via transfer (Wallet)
     * 6 → ATM card payment
     * 7 → Wallet payment
     * 8 → International card payment
     * 9 → Payment via transfer (Gateway)
     * 10 → Payment via Napas link
     * 11 → Payment via direct link
     * 12 → Withdrawal (Wallet to Bank)
     * 13 → Wallet to business wallet transfer
     * 14 → Business wallet to wallet transfer
     * @example 7
     */
    type: ENPayTransactionType;

    /**
     * Creation time
     * @example "2024-08-28 15:36:26"
     */
    created_at: string;

    /**
     * Completion time (optional)
     * @example "2024-08-28 15:36:32"
     */
    completed_at?: string;

    /**
     * User's phone number who performed the transaction
     * @example "*********"
     */
    user_phone: string;

    /**
     * Merchant key linked to the user
     * @example "abcd"
     */
    merchant_key: string;

    /**
     * Security signature
     * @example "G/zJWBLHEr+bJcEUmbqMhqc0oYrEvuiy0ln8yqlFxag2o5TTIgrD0ZUJKKnXZQ7Iv952EWbQqZDvn2IuXGxdlU/7sgKAQox6WRazdogMoLw5GSLoLLrRINJTXThqbgrSUNpHSpxOPH97Ex/Jpvv5I8+8gI/KOEnrBax1MTwY/SouFY/iGjuM4om8vzgaHKIPM0fi71XlzqZgDbQvhqz4GyZI/b1e4nZEolRt1Jau07/zX1BXgaZ/xMvwsPwTFOXHLuk70PMJ2f/X80tGdrRnYG7TcN3VjSsMpluQ5nzkSGSUzuiLkR8cdMctloSQ4SPl30hteW/ciHr+yHozr5psBA=="
     */
    signature: string;
}

/**
 * Enum for 9Pay Transaction Status
 * Transaction status:
 * 1 → Initialized
 * 2 → Processing
 * 3 → Success
 * 4 → Failed
 * 5 → Cancelled
 * @example 3
 */
export enum ENPayTransactionIPNStatus {
    INITIALIZED = 1,
    PROCESSING = 2,
    SUCCESS = 3,
    FAILURE = 4,
    CANCEL = 5,
}

/**
 * Enum for 9Pay Transaction Type
 */
export enum ENPayTransactionType {
    /**
     * Payment made via QR code scanning
     */
    QR_PAYMENT = 1,

    /**
     * Adding funds to the wallet
     */
    TOP_UP = 2,

    /**
     * Withdrawing funds to a linked bank account
     */
    WITHDRAW_TO_LINKED_BANK = 3,

    /**
     * Transfer between two 9Pay wallets
     */
    WALLET_TO_WALLET_TRANSFER = 4,

    /**
     * Payment made via bank transfer to the wallet
     */
    PAYMENT_VIA_TRANSFER_WALLET = 5,

    /**
     * Payment made using an ATM card
     */
    ATM_CARD_PAYMENT = 6,

    /**
     * Payment made directly from the 9Pay wallet
     */
    WALLET_PAYMENT = 7,

    /**
     * Payment made using an international credit/debit card
     */
    INTERNATIONAL_CARD_PAYMENT = 8,

    /**
     * Payment made via bank transfer to the payment gateway
     */
    PAYMENT_VIA_TRANSFER_GATEWAY = 9,

    /**
     * Payment made via Napas link
     */
    PAYMENT_VIA_NAPAS_LINK = 10,

    /**
     * Payment made via direct link
     */
    PAYMENT_VIA_DIRECT_LINK = 11,

    /**
     * Withdrawing funds from wallet to a bank account
     */
    WITHDRAWAL_WALLET_TO_BANK = 12,

    /**
     * Transfer from a personal wallet to a business wallet
     */
    WALLET_TO_BUSINESS_WALLET_TRANSFER = 13,

    /**
     * Transfer from a business wallet to a personal wallet
     */
    BUSINESS_WALLET_TO_WALLET_TRANSFER = 14,
}
