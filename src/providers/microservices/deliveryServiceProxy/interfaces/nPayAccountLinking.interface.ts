
export interface INPayAccountLinking {
    merchant_id: number;
    phone: string;
    frozen_amount: number;
    status: string;
    created_at: Date;
    updated_at: Date;
    balance_detail?: IBalanceDetailResponse;
}

export interface IFrozenMerchant {
    merchant_key: string;
    merchant_name: string;
    balance_frozen: number;
}

export interface IBalanceDetailResponse {
    balance: number;
    total_balance: number;
    balance_frozen: number;
    frozen_merchants?: IFrozenMerchant[];
    signature: string;
}

