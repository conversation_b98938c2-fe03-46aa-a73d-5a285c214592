export enum Merchant9PayCmd {
    INITIATE_LINKING_ACCOUNT = '9_pay.initiate_linking_account',
    GET_9_PAY_WALLET_BALANCE_DETAIL = '9_pay.get_wallet_balance_detail',
    LINKING_ACCOUNT_IPN = '9_pay.account_linking_ipn',
    TRANSACTION_IPN = '9_pay.transaction_ipn',
    FREEZE_MERCHANT_9PAY_WALLET = '9_pay.freeze_merchant_9pay_wallet',
    DEFROST_MERCHANT_9PAY_WALLET = '9_pay.defrost_merchant_9pay_wallet',
    SYNC_9_PAY_WALLET_BALANCE_STATUS = '9_pay.sync_wallet_balance_status_by_merchant_id',
    GET_MANY_WITH_PAGINATION = '9_pay.get_many_with_pagination',
    GET_9_PAY_WALLET_BALANCE_DETAIL_BY_RESTAURANT_ID = '9_pay.get_wallet_balance_detail_by_restaurant_id',
}
