import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { LocationV2Cmd } from '../cmd/location-v2.cmd';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class LocationV2CommandService {
    constructor(@Inject('DELIVERY_SERVICE') private readonly deliveryServiceClient: ClientProxy) {}

    private sendCommand(cmd: string, data: any) {
        return this.deliveryServiceClient.send({ cmd }, data);
    }

    findLocationV2ById(id: number) {
        return lastValueFrom(this.sendCommand(LocationV2Cmd.findLocationV2ById, { id }));
    }

    getLocationV2Provinces() {
        return lastValueFrom(this.sendCommand(LocationV2Cmd.getLocationV2Provinces, {}));
    }

    getLocationV2WardsByProvinceCode(provinceCode: string) {
        return lastValueFrom(this.sendCommand(LocationV2Cmd.getLocationV2WardsByProvinceCode, { provinceCode }));
    }
}
