import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Merchant9PayCmd } from '../cmd/merchant-npay.cmd';
import { GetMany9PayAccountLinkingWithPaginationDto } from 'src/models/merchant/dto/get-9pay-account-linking.dto';
import { firstValueFrom } from 'rxjs';
import { INPayAccountLinking } from '../interfaces/nPayAccountLinking.interface';
@Injectable()
export class Merchant9PayCommandService {
    constructor(@Inject('DELIVERY_SERVICE') private readonly deliveryServiceClient: ClientProxy) {}

    private sendCommand(cmd: string, data: any) {
        return this.deliveryServiceClient.send({ cmd }, data);
    }

    getDetail(merchantId: number) {
        return this.sendCommand(Merchant9PayCmd.GET_9_PAY_WALLET_BALANCE_DETAIL, { merchant_id: merchantId });
    }

    freezeWalletByMerchantId(merchantId: number) {
        return this.sendCommand(Merchant9PayCmd.FREEZE_MERCHANT_9PAY_WALLET, { merchant_id: merchantId });
    }

    defrostWalletByMerchantId(merchantId: number) {
        return this.sendCommand(Merchant9PayCmd.DEFROST_MERCHANT_9PAY_WALLET, { merchant_id: merchantId });
    }

    syncNPayWalletBalanceStatusByMerchantId(merchantId: number) {
        return this.sendCommand(Merchant9PayCmd.SYNC_9_PAY_WALLET_BALANCE_STATUS, { merchant_id: merchantId });
    }

    getManyWithPagination(data: GetMany9PayAccountLinkingWithPaginationDto) {
        return this.sendCommand(Merchant9PayCmd.GET_MANY_WITH_PAGINATION, data);
    }

    async getDetailByRestaurantId(restaurantId: number, provinceId: number): Promise<INPayAccountLinking> {
        const result = await firstValueFrom(
            this.sendCommand(Merchant9PayCmd.GET_9_PAY_WALLET_BALANCE_DETAIL_BY_RESTAURANT_ID, {
                restaurant_id: restaurantId,
                province_id: provinceId,
            })
        );
        // console.log('getDetailByRestaurantId result: ', result);
        return result;
    }
}
