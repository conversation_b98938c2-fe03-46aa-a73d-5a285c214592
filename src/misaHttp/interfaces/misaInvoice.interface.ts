export interface MisaAuthResponseDto {
    success: boolean;
    errorCode: string | null;
    descriptionErrorCode: string | null;
    errors: string[];
    data: string;
    customData?: string;
}

export interface MisaOriginalInvoiceDetailDto {
    ItemType: number;
    LineNumber: number;
    SortOrder: number;
    ItemCode: string;
    ItemName: string;
    UnitName: string;
    Quantity: number;
    UnitPrice: number;
    AmountOC: number;
    Amount: number;
    DiscountRate: number;
    DiscountAmountOC: number;
    DiscountAmount: number;
    AmountWithoutVATOC: number;
    AmountWithoutVAT: number;
    VATRateName: string;
    VATAmountOC: number;
    VATAmount: number;
}

export interface MisaTaxRateInfoDto {
    VATRateName: string;
    AmountWithoutVATOC: number;
    VATAmountOC: number;
}

export interface MisaFeeInfoDto {
    FeeName: string;
    FeeAmount: number;
    FeeAmountOC: number;
}

export interface MisaClockInfoDto {
    ClockOrder: number;
    ClockCode: string;
    ClockSeri: string;
    LastIndex: number;
    FirstIndex: number;
    Coefficient: number;
    ClockCodeOld?: string;
    ClockSeriOld?: string;
    LastIndexOld?: number;
    FirstIndexOld?: number;
    CoefficientOld?: number;
    ClockStatus: number;
    RefuelerNo?: string;
    QualityControlNo?: string;
    StartMeter?: number;
    EndMeter?: number;
    ActualTemperature?: number;
    ActualDensity?: number;
    ObservedGallon?: number;
    ObservedLiters?: number;
}

export interface MisaOptionUserDefinedDto {
    MainCurrency?: string;
    AmountDecimalDigits?: string;
    AmountOCDecimalDigits?: string;
    UnitPriceOCDecimalDigits?: string;
    UnitPriceDecimalDigits?: string;
    QuantityDecimalDigits?: string;
    CoefficientDecimalDigits?: string;
    ExchangRateDecimalDigits?: string;
}

export interface MisaAioInvoiceDataDto {
    RefID: string;
    InvSeries: string;
    InvDate: string;
    InvoiceName: string;
    CurrencyCode: string;
    ExchangeRate: number;
    PaymentMethodName?: string;
    BuyerLegalName: string;
    BuyerTaxCode?: string;
    BuyerIDNumber?: string;
    BuyerAddress?: string;
    BuyerCode?: string;
    BuyerPhoneNumber?: string;
    BuyerEmail?: string;
    BuyerFullName?: string;
    IsTaxReduction43?: boolean;
    BuyerBankAccount?: string;
    BuyerBankName?: string;
    SellerLegalName?: string;
    SellerTaxCode?: string;
    SellerAddress?: string;
    SellerPhoneNumber?: string;
    SellerEmail?: string;
    SellerBankAccount?: string;
    SellerBankName?: string;
    SellerFax?: string;
    SellerWebsite?: string;
    TotalSaleAmountOC: number;
    TotalSaleAmount: number;
    TotalAmountWithoutVATOC: number;
    TotalAmountWithoutVAT: number;
    TotalDiscountAmountOC: number;
    TotalDiscountAmount: number;
    TotalVATAmountOC: number;
    TotalVATAmount: number;
    TotalAmountOC: number;
    TotalAmount: number;
    TotalAmountInWords?: string;
    IsSendEmail?: boolean;
    ReceiverEmail?: string;
    OriginalInvoiceDetail: MisaOriginalInvoiceDetailDto[];
    TaxRateInfo: MisaTaxRateInfoDto[];
    OptionUserDefined: MisaOptionUserDefinedDto;
}

export interface MisaAioInvoiceRequestDto {
    SignType: number;
    InvoiceData: MisaAioInvoiceDataDto[];
    PublishInvoiceData: any;
}

export interface MisaInvoiceStatusRequestDto {
    RefIDs: string[];
}

export interface MisaInvoiceStatusDataDto {
    RefID: string;
    InvDate: string;
    InvSeries: string;
    InvTempl: string;
    BuyerName: string | null;
    BuyerTaxCode: string | null;
    BuyerCode: string | null;
    BuyerFullName: string | null;
    OrganizationUnitID: string | null;
    TransactionID: string;
    PublishStatus: number;
    EInvoiceStatus: number;
    ReferenceType: number;
    InvoiceTemplate: string | null;
    InvoiceCode: string;
    MessageCode: string | null;
    SourceType: string;
    SendTaxStatus: number;
    PublishedTime: string;
    IsSentEmail: boolean;
    IsDelete: boolean;
    DeletedDate: string | null;
    DeletedReason: string | null;
    ReceivedStatus: number;
    OrgTransactionID: string | null;
    OrgInvoiceStatus: number;
    RefInvoiceStatus: number;
    SummaryStatus: string | null;
    notificationInfos: any | null;
    InvNo: string;
    InvoiceNote: string | null;
    GrantCodeStatus: number;
}

export interface MisaInvoiceStatusResponseDto {
    success: boolean;
    errorCode: string | null;
    errors: string[];
    data: MisaInvoiceStatusDataDto[];
    customData: any;
}

export interface MisaTemplateResponseDto {
    success: boolean;
    errorCode: string | null;
    errors: string[];
    data: string;
    customData: any | null;
}

export interface MisaPublishHsmResponseDto {
    success: boolean;
    errorCode: string | null;
    descriptionErrorCode: string | null;
    createInvoiceResult: null;
    publishInvoiceResult: string;
}

export interface MisaDownloadInvoiceRequestDto {
    invoiceCodes: string[];
    invoiceWithCode?: boolean;
    invoiceCalcu?: boolean;
    downloadDataType?: 'pdf' | 'xml';
}

export interface MisaInvoiceDataPublished {
    TransactionID: string;
    Data: string;
}

export interface MisaDownloadInvoiceResponseDto {
    success: boolean;
    errorCode: string | null;
    descriptionErrorCode: string | null;
    errors: string[];
    data: string;
    customData: any | null;
}
