import { DynamicModule, Module } from '@nestjs/common';
import { HttpModule, HttpService } from '@nestjs/axios';
import { IMisaHttpOption } from './interfaces/misaHttpOption.interface';
import { MisaHttpService } from './misaHttp.service';
import { MISA_HTTP, MISA_HTTP_OPTIONS } from './misaHttp.constant';
import MisaHttpOptions from './misaHttp.type';
import { MattermostService } from './mattermost.service';

@Module({})
export class MisaHttpModule {
    static async forRootAsync(options: MisaHttpOptions): Promise<DynamicModule> {
        return {
            module: MisaHttpModule,
            imports: [
                HttpModule.register({
                    timeout: 30000,
                    maxRedirects: 5,
                }),
                ...(options.imports || []),
            ],
            providers: [
                {
                    provide: MISA_HTTP_OPTIONS,
                    useFactory: options.useFactory,
                    inject: options.inject,
                },
                {
                    provide: MISA_HTTP,
                    inject: [HttpService, MISA_HTTP_OPTIONS],
                    useFactory: (httpService: HttpService, option: IMisaHttpOption) => {
                        console.log('init misa http service ==> ', option);
                        return new MisaHttpService(httpService, option);
                    },
                },
                MattermostService,
            ],
            exports: [MISA_HTTP, MattermostService],
        };
    }
}
