import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { firstValueFrom } from 'rxjs';
import {
    MisaAuthResponseDto,
    MisaInvoiceStatusRequestDto,
    MisaInvoiceStatusResponseDto,
    MisaTemplateResponseDto,
    MisaPublishHsmResponseDto,
    MisaAioInvoiceRequestDto,
    MisaAioInvoiceDataDto,
    MisaDownloadInvoiceResponseDto,
    MisaInvoiceDataPublished,
} from './interfaces/misaInvoice.interface';
import {
    MisaAuthenticationException,
    MisaValidationException,
    MisaServiceException,
    MisaNetworkException,
    MisaConfigurationException,
} from './exceptions/misaExceptions.interface';
import { IMisaHttpOption } from './interfaces/misaHttpOption.interface';

@Injectable()
export class MisaHttpService {
    private readonly logger = new Logger(MisaHttpService.name);
    private readonly config: IMisaHttpOption;
    private readonly baseUrl: string;
    private authToken: string | null = null;
    private tokenExpiryTime: Date | null = null;

    constructor(private readonly httpService: HttpService, private readonly option: IMisaHttpOption) {
        this.config = this.option;
        if (!this.config) {
            throw new MisaConfigurationException('MISA configuration not found');
        }

        this.baseUrl = this.config.baseUrl;

        this.logger.log(`MISA HTTP Service initialized with baseUrl: ${this.baseUrl}`);
    }

    private getEndpointPath(path: string): string {
        return path;
    }

    private mapSellerInformation(invoiceData: MisaAioInvoiceDataDto): MisaAioInvoiceDataDto {
        return {
            ...invoiceData,
            SellerLegalName: this.config.companyName || invoiceData.SellerLegalName,
            SellerTaxCode: this.config.companyTaxCode || invoiceData.SellerTaxCode,
            SellerAddress: this.config.companyAddress || invoiceData.SellerAddress,
            SellerPhoneNumber: this.config.companyPhone || invoiceData.SellerPhoneNumber,
            SellerEmail: this.config.companyEmail || invoiceData.SellerEmail,
            SellerBankAccount: this.config.companyBankAccount || invoiceData.SellerBankAccount,
            SellerBankName: this.config.companyBankName || invoiceData.SellerBankName,
            SellerFax: this.config.companyFax || invoiceData.SellerFax,
            SellerWebsite: this.config.companyWebsite || invoiceData.SellerWebsite,
        };
    }

    private async getValidToken(): Promise<string> {
        if (this.authToken && this.tokenExpiryTime && new Date() < this.tokenExpiryTime) {
            return this.authToken;
        }

        const authResponse = await this.authenticate();

        return authResponse.data;
    }

    async authenticate(): Promise<MisaAuthResponseDto> {
        try {
            const requestData = {
                appid: this.config.appId,
                taxcode: this.config.companyTaxCode,
                username: this.config.username,
                password: this.config.password,
            };

            const response: AxiosResponse<MisaAuthResponseDto> = await firstValueFrom(
                this.httpService.post(`${this.baseUrl}/auth/token`, requestData),
            );

            if (!response.data.success) {
                throw new MisaAuthenticationException(
                    `Authentication failed: ${response.data.errorCode || 'Unknown error'}`,
                );
            }

            this.authToken = response.data.data;
            this.tokenExpiryTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            console.log('authResponse: ', response.data);
            return response.data;
        } catch (error) {
            this.handleError(error, 'authenticate');
        }
    }

    async publishInvoiceHsm(requestData: MisaAioInvoiceRequestDto, token?: string): Promise<MisaPublishHsmResponseDto> {
        try {
            const authToken = token || (await this.getValidToken());

            const mappedInvoiceData = requestData.InvoiceData.map((invoiceData) =>
                this.mapSellerInformation(invoiceData),
            );

            const mappedRequestData: MisaAioInvoiceRequestDto = {
                ...requestData,
                InvoiceData: mappedInvoiceData,
            };

            this.logger.log(`mappedRequestData: ${JSON.stringify(mappedRequestData)}`);

            this.logger.log(
                `Publishing ${
                    mappedRequestData.InvoiceData.length
                } HSM invoices with RefIDs: ${mappedRequestData.InvoiceData.map((d) => d.RefID).join(', ')}`,
            );

            const endpointPath = this.getEndpointPath('/invoice');
            let response: AxiosResponse<MisaPublishHsmResponseDto>;
            let lastError: any;

            for (let attempt = 1; attempt <= 3; attempt++) {
                try {
                    response = await firstValueFrom(
                        this.httpService.post(`${this.baseUrl}${endpointPath}`, mappedRequestData, {
                            headers: {
                                Authorization: `Bearer ${authToken}`,
                                'Content-Type': 'application/json',
                                CompanyTaxCode: this.config.companyTaxCode,
                            },
                        }),
                    );
                    break;
                } catch (error) {
                    lastError = error;
                    this.logger.warn(`Attempt ${attempt} failed for publishInvoiceHsm: ${error.message}`);

                    if (attempt === 3) {
                        throw lastError;
                    }

                    // Wait before retry (exponential backoff)
                    await new Promise((resolve) => setTimeout(resolve, attempt * 1000));
                }
            }

            if (!response.data.success) {
                throw new MisaServiceException(`HSM invoice publishing failed: ${response.data.errorCode}`);
            }

            this.logger.log(`HSM invoices published successfully`);

            return response.data;
        } catch (error) {
            this.handleError(error, 'publishInvoiceHsm');
        }
    }

    async getInvoiceStatus(
        requestData: MisaInvoiceStatusRequestDto,
        token?: string,
    ): Promise<MisaInvoiceStatusResponseDto> {
        try {
            const authToken = token || (await this.getValidToken());

            this.logger.log(`Getting invoice status for RefIDs: ${requestData.RefIDs.join(', ')}`);

            const endpointPath = this.getEndpointPath('/invoice/status');
            const queryParams = new URLSearchParams({
                inputType: '2',
                invoiceWithCode: 'true',
                invoiceCalcu: 'true',
            });

            const response = await firstValueFrom(
                this.httpService.post(`${this.baseUrl}${endpointPath}?${queryParams.toString()}`, requestData.RefIDs, {
                    headers: {
                        Authorization: `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                        CompanyTaxCode: this.config.companyTaxCode,
                    },
                }),
            );

            if (!response.data.success) {
                throw new MisaServiceException(`Invoice status fetching failed: ${response.data.errorCode}`);
            }

            const result: MisaInvoiceStatusResponseDto = {
                ...response.data,
                data: JSON.parse(response.data.data || '[]'),
            };

            return result;
        } catch (error) {
            this.handleError(error, 'getInvoiceStatus');
        }
    }

    async getTemplates(token?: string): Promise<MisaTemplateResponseDto> {
        try {
            const authToken = token || (await this.getValidToken());

            this.logger.log('Getting MISA templates');

            const endpointPath = this.getEndpointPath('/invoice/templates?invoiceWithCode=true');
            const response: AxiosResponse<MisaTemplateResponseDto> = await firstValueFrom(
                this.httpService.get(`${this.baseUrl}${endpointPath}`, {
                    headers: {
                        Authorization: `Bearer ${authToken}`,
                        CompanyTaxCode: this.config.companyTaxCode,
                    },
                }),
            );

            return response.data;
        } catch (error) {
            this.handleError(error, 'getTemplates');
        }
    }

    async downloadInvoice(invoiceCodes: string[], token?: string): Promise<Buffer[]> {
        try {
            const authToken = token || (await this.getValidToken());

            this.logger.log(`Downloading invoices: ${invoiceCodes.join(', ')}`);

            const endpointPath = this.getEndpointPath('/invoice/download');
            const queryParams = new URLSearchParams({
                invoiceWithCode: 'true',
                invoiceCalcu: 'true',
                downloadDataType: 'pdf',
            });

            let response: AxiosResponse<MisaDownloadInvoiceResponseDto>;
            let lastError: Error;

            for (let attempt = 1; attempt <= 3; attempt++) {
                try {
                    response = await firstValueFrom(
                        this.httpService.post(
                            `${this.baseUrl}${endpointPath}?${queryParams.toString()}`,
                            invoiceCodes,
                            {
                                headers: {
                                    Authorization: `Bearer ${authToken}`,
                                    'Content-Type': 'application/json',
                                    CompanyTaxCode: this.config.companyTaxCode,
                                },
                            },
                        ),
                    );

                    if (!response.data.success) {
                        throw new MisaServiceException(`Invoice download failed: ${response.data.errorCode}`);
                    }

                    break; // Success, exit retry loop
                } catch (error) {
                    lastError = error;
                    this.logger.warn(`Invoice download attempt ${attempt} failed: ${error.message}`);

                    if (attempt === 5) {
                        throw lastError;
                    }

                    // Wait before retrying (exponential backoff)
                    await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                }
            }

            const invoiceDataPublished: MisaInvoiceDataPublished[] = JSON.parse(response.data.data);
            const pdfBuffers: Buffer[] = [];

            for (const invoiceData of invoiceDataPublished) {
                const pdfBuffer = Buffer.from(invoiceData.Data, 'base64');
                pdfBuffers.push(pdfBuffer);
                this.logger.log(`Downloaded invoice with TransactionID: ${invoiceData.TransactionID}`);
            }

            this.logger.log(`Successfully downloaded ${pdfBuffers.length} invoice(s)`);

            return pdfBuffers;
        } catch (error) {
            this.handleError(error, 'downloadInvoice');
        }
    }

    // async downloadInvoiceByRefIds(refIds: string[], token?: string): Promise<Buffer[]> {
    //     try {
    //         const authToken = token || (await this.getValidToken());

    //         this.logger.log(`Getting invoice status for RefIDs to extract invoice codes: ${refIds.join(', ')}`);

    //         const statusResponse = await this.getInvoiceStatus({ RefIDs: refIds }, authToken);

    //         if (!statusResponse.Success || !statusResponse.Data || statusResponse.Data.length === 0) {
    //             throw new MisaServiceException('No invoice data found for the provided RefIDs');
    //         }

    //         const invoiceCodes: string[] = [];
    //         for (const invoiceData of statusResponse.Data) {
    //             if (invoiceData.InvoiceCode) {
    //                 invoiceCodes.push(invoiceData.InvoiceCode);
    //             } else if (invoiceData.InvoiceNumber) {
    //                 invoiceCodes.push(invoiceData.InvoiceNumber);
    //             }
    //         }

    //         if (invoiceCodes.length === 0) {
    //             throw new MisaServiceException('No invoice codes found in the status response');
    //         }

    //         this.logger.log(`Extracted invoice codes: ${invoiceCodes.join(', ')}`);

    //         return await this.downloadInvoice(invoiceCodes, authToken);
    //     } catch (error) {
    //         this.handleError(error, 'downloadInvoiceByRefIds');
    //     }
    // }

    // async downloadSingleInvoice(invoiceCode: string, token?: string): Promise<Buffer> {
    //     try {
    //         const buffers = await this.downloadInvoice([invoiceCode], token);

    //         if (buffers.length === 0) {
    //             throw new MisaServiceException(`No invoice data found for invoice code: ${invoiceCode}`);
    //         }

    //         return buffers[0];
    //     } catch (error) {
    //         this.handleError(error, 'downloadSingleInvoice');
    //     }
    // }

    // async downloadSingleInvoiceByRefId(refId: string, token?: string): Promise<Buffer> {
    //     try {
    //         const buffers = await this.downloadInvoiceByRefIds([refId], token);

    //         if (buffers.length === 0) {
    //             throw new MisaServiceException(`No invoice data found for RefID: ${refId}`);
    //         }

    //         return buffers[0];
    //     } catch (error) {
    //         this.handleError(error, 'downloadSingleInvoiceByRefId');
    //     }
    // }

    private handleError(error: any, context: string): never {
        this.logger.error(`MISA Error in ${context}: ${error.message}`, error.stack);

        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            throw new MisaNetworkException(`Network error: ${error.message}`);
        }

        if (error.response?.status === 401) {
            this.authToken = null;
            this.tokenExpiryTime = null;
            throw new MisaAuthenticationException('Authentication failed - token invalid or expired');
        }

        if (
            error instanceof MisaAuthenticationException ||
            error instanceof MisaValidationException ||
            error instanceof MisaServiceException ||
            error instanceof MisaNetworkException
        ) {
            throw error;
        }

        throw new MisaServiceException(error.message || 'Unknown MISA error');
    }
}
