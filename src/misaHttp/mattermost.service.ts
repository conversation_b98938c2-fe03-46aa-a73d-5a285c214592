import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

const MATTERMOST_URL = 'https://workspace.villvietnam.vn/api/v4/posts';
const MATTERMOST_TOKEN = 'jzjzqftwxbgs7y6igxpd7wmbey';
const MATTERMOST_CHANNEL_ID = 'guj1hyfow3ng8cuwibpo1tu19o';

@Injectable()
export class MattermostService {
    private readonly logger = new Logger(MattermostService.name);
    constructor(private readonly httpService: HttpService, private readonly configService: ConfigService) {}

    async sendMessage(message: string): Promise<any> {
        try {
            const mattermostToken = this.configService.get('MATTERMOST_TOKEN') || MATTERMOST_TOKEN;
            const mattermostChannelId = this.configService.get('MATTERMOST_CHANNEL_ID') || MATTERMOST_CHANNEL_ID;
            const mattermostUrl = this.configService.get('MATTERMOST_URL') || MATTERMOST_URL;

            const requestData = {
                channel_id: mattermostChannelId,
                message: message,
            };

            const headers = {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${mattermostToken}`,
            };

            const response = await firstValueFrom(this.httpService.post(mattermostUrl, requestData, { headers }));
            return response.data;
        } catch (error) {
            this.logger.error(`Failed to send Mattermost message: ${error.message}`);
        }
    }
}
