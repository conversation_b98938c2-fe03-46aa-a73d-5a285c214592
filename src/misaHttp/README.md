# MISA HTTP Library

A reusable NestJS library for integrating with MISA invoice API. This library provides a clean HTTP service layer for MISA operations with proper error handling and configuration management.

## Features

- **Authentication**: Automatic token management with refresh
- **Invoice Publishing**: HSM invoice publishing with seller information mapping
- **Invoice Status**: Check invoice status by RefIDs
- **Templates**: Retrieve MISA invoice templates
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Configuration**: Flexible configuration through NestJS config system

## Installation

The library is already included in this project. To use it in your module:

```typescript
import { MisaHttpModule } from '../misaHttp/misaHttp.module';
import { IMisaHttpOption } from '../misaHttp/interfaces/misaHttpOption.interface';
```

## Usage

### 1. Import the Module

```typescript
@Module({
  imports: [
    MisaHttpModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService): IMisaHttpOption => ({
        baseUrl: configService.get('misa.baseUrl'),
        appId: configService.get('misa.appId'),
        companyTaxCode: configService.get('misa.companyTaxCode'),
        username: configService.get('misa.username'),
        password: configService.get('misa.password'),
        companyName: configService.get('misa.companyName'),
        companyAddress: configService.get('misa.companyAddress'),
        companyPhone: configService.get('misa.companyPhone'),
        companyEmail: configService.get('misa.companyEmail'),
        companyBankAccount: configService.get('misa.companyBankAccount'),
        companyBankName: configService.get('misa.companyBankName'),
        companyFax: configService.get('misa.companyFax'),
        companyWebsite: configService.get('misa.companyWebsite'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class YourModule {}
```

### 2. Inject the Service

```typescript
import { MISA_HTTP } from '../misaHttp/misaHttp.constant';
import { MisaHttpService } from '../misaHttp/misaHttp.service';

@Injectable()
export class YourService {
  constructor(
    @Inject(MISA_HTTP) private misaHttpService: MisaHttpService,
  ) {}

  async authenticateWithMisa() {
    return await this.misaHttpService.authenticate();
  }

  async publishInvoice(invoiceData: MisaAioInvoiceRequestDto) {
    return await this.misaHttpService.publishInvoiceHsm(invoiceData);
  }

  async getInvoiceStatus(refIds: string[]) {
    return await this.misaHttpService.getInvoiceStatus({ RefIDs: refIds });
  }

  async getTemplates() {
    return await this.misaHttpService.getTemplates();
  }

  async downloadInvoice(invoiceCodes: string[]) {
    // Returns Buffer[] - array of PDF buffers
    return await this.misaHttpService.downloadInvoice({
      invoiceCodes,
      invoiceWithCode: true,
      invoiceCalcu: true,
      downloadDataType: 'pdf'
    });
  }

  async downloadSingleInvoice(invoiceCode: string) {
    // Returns Buffer - single PDF buffer
    return await this.misaHttpService.downloadSingleInvoice(invoiceCode);
  }

  async downloadInvoiceByRefIds(refIds: string[]) {
    // Returns Buffer[] - array of PDF buffers
    return await this.misaHttpService.downloadInvoiceByRefIds(refIds);
  }

  async downloadSingleInvoiceByRefId(refId: string) {
    // Returns Buffer - single PDF buffer
    return await this.misaHttpService.downloadSingleInvoiceByRefId(refId);
  }
}
```

## Configuration

The library requires the following configuration options:

```typescript
interface IMisaHttpOption {
  baseUrl: string;           // MISA API base URL
  appId: string;            // MISA application ID
  companyTaxCode: string;   // Company tax code
  username: string;         // MISA username
  password: string;         // MISA password
  companyName: string;      // Company name for invoices
  companyAddress: string;   // Company address
  companyPhone: string;     // Company phone
  companyEmail: string;     // Company email
  companyBankAccount: string; // Bank account
  companyBankName: string;  // Bank name
  companyFax?: string;      // Optional fax
  companyWebsite?: string;  // Optional website
  timeout?: number;         // HTTP timeout (default: 30000)
  maxRedirects?: number;    // Max redirects (default: 5)
}
```

## Available Methods

### Authentication
- `authenticate()`: Authenticates with MISA and returns a token

### Invoice Operations
- `publishInvoiceHsm(invoiceData)`: Publishes HSM invoices
- `getInvoiceStatus(refIds)`: Gets the status of invoices by RefIDs
- `getTemplates()`: Gets available invoice templates
- `downloadInvoice(requestData)`: Downloads multiple invoice PDFs as array of Buffers using invoice codes
- `downloadInvoiceByRefIds(refIds)`: Downloads multiple invoice PDFs using RefIDs (gets status first to extract invoice codes)
- `downloadSingleInvoice(invoiceCode)`: Downloads a single invoice PDF as Buffer using invoice code
- `downloadSingleInvoiceByRefId(refId)`: Downloads a single invoice PDF as Buffer using RefID

### Download Invoice Parameters

```typescript
interface MisaDownloadInvoiceRequestDto {
  invoiceCodes: string[];           // Array of invoice codes to download
  invoiceWithCode?: boolean;        // Include invoice code (default: true)
  invoiceCalcu?: boolean;          // Include calculations (default: true)
  downloadDataType?: 'pdf' | 'xml'; // Download format (default: 'pdf')
}
```

### Response Structure

The MISA download API returns invoice data in base64 format:

```typescript
interface MisaInvoiceDataPublished {
  TransactionID: string;  // Invoice lookup code
  Data: string;          // Invoice data in base64 format
}
```

The `downloadInvoice` method returns an array of `Buffer` objects (one for each invoice), while `downloadSingleInvoice` returns a single `Buffer`. These can be:
- Saved to file system
- Uploaded to S3
- Sent as HTTP response
- Processed further

## Error Handling

The library provides custom exceptions:

- `MisaAuthenticationException`: Authentication failures
- `MisaValidationException`: Validation errors
- `MisaServiceException`: MISA API errors
- `MisaNetworkException`: Network connectivity issues
- `MisaConfigurationException`: Configuration errors

## Architecture

This library follows the same architectural patterns as the winston-loki library:

- **Dynamic Module**: Uses `forRootAsync()` for flexible configuration
- **Dependency Injection**: Proper DI with constants and tokens
- **Service Layer**: Clean separation of HTTP operations
- **Interface Definitions**: TypeScript interfaces for all DTOs
- **Error Handling**: Comprehensive error handling with custom exceptions

## Example

See the existing `misa-invoice.module.ts` for a complete implementation example.
