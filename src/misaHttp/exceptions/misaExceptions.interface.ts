export class MisaAuthenticationException extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'MisaAuthenticationException';
    }
}

export class MisaValidationException extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'MisaValidationException';
    }
}

export class MisaServiceException extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'MisaServiceException';
    }
}

export class MisaNetworkException extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'MisaNetworkException';
    }
}

export class MisaConfigurationException extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'MisaConfigurationException';
    }
}
