import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
export const RequestUser = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest() as Request;
    return request['user'];
});

export interface IRequestUser {
    id: number;
    name: string;
    avatar: string;
    email: string;
    phone: string;
    created_at: string;
    updated_at: string;
    roles: IRole[];
    provinceIds: string[];
    iat: number;
    exp: number;
}

interface IRole {
    id: number;
    name: string;
}
