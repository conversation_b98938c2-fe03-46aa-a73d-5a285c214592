import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import { PROVINCE_HEADER } from '../constants';
export const HeaderProvince = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest() as Request;
    let provinceId = request.headers[PROVINCE_HEADER] as string;
    if (!provinceId) {
        provinceId = request.headers['x-province'] as string;
    }
    return provinceId;
});
