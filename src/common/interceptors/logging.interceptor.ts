import { CallH<PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor, SetMetadata } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';

// Metadata key for skipping logging interceptor
const SKIP_LOGGING_KEY = 'skipLogging';

// Decorator to skip logging interceptor
export const SkipLoggingInterceptor = () => SetMetadata(SKIP_LOGGING_KEY, true);

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
    constructor(private reflector: Reflector) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        // Check if the handler is marked to skip logging interceptor
        const skipLogging = this.reflector.getAllAndOverride<boolean>(SKIP_LOGGING_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        if (skipLogging) {
            // Return original response without wrapping
            return next.handle();
        }

        return next.handle().pipe(
            map((data) => ({
                success: true,
                data: data ? data : null,
                message: 'success',
            })),
        );
    }
}
