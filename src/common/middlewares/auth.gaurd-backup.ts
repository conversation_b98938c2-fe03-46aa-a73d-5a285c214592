import { ERole } from './../../entities/role.entity';
import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import * as cacheManager from 'cache-manager';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import * as _ from 'lodash';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { PROVINCE_HEADER } from '../constants';
import { PERMISSION_KEY, ROLE_PERMISSIONS } from './permissions.decorator';
import { ROLES_KEY } from './roles.decorator';

@Injectable()
export default class AuthGuardBK implements CanActivate {
    private memoryCache: cacheManager.Cache;
    constructor(private reflector: Reflector) {
        this.memoryCache = cacheManager.caching({ store: 'memory', max: 1000, ttl: 86400 });
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest() as Request;
        const provinceId = request.get(PROVINCE_HEADER);
        const header = request.header('Authorization');
        if (!header) {
            throw new HttpException('Authorization: Bearer <token> header missing', HttpStatus.UNAUTHORIZED);
        }

        const parts = header.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            throw new HttpException('Authorization: Bearer <token> header invalid', HttpStatus.UNAUTHORIZED);
        }

        const token = parts[1];
        let user = null;
        try {
            user = jwt.verify(token, process.env.JWT_SECRET) as any;
        } catch (e) {
            throw new HttpException(e.message, HttpStatus.UNAUTHORIZED);
        }
        if (_.isEmpty(user)) return false;

        const { permissions, roles } = user;
        let permissionAccept = false;
        let roleAccept = false;
        const rolesAllowed = this.reflector.get<ERole[]>(ROLES_KEY, context.getHandler());
        if (!_.isEmpty(rolesAllowed)) {
            roles?.forEach(({ name }) => {
                if (rolesAllowed.includes(name)) {
                    roleAccept = true;
                }
            });
            if (!roleAccept) {
                throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);
            }
        } else roleAccept = true;

        const requirePermissions = this.reflector.getAllAndOverride<PermissionsAccessAction[]>(PERMISSION_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!_.isEmpty(requirePermissions)) {
            let isActive = false;
            if (roles) {
                requirePermissions.forEach((p) => {
                    for (let i = 0; i < user.roles.length && !isActive; i++) {
                        isActive = ROLE_PERMISSIONS[provinceId][user.roles[i].name].some((r) => {
                            return r == p;
                        });
                    }
                });
            } else {
                isActive = requirePermissions.some((p) => permissions.some((r) => r == p));
            }
            if (isActive) {
                permissionAccept = true;
            } else {
                throw new HttpException('FORBIDDEN', HttpStatus.FORBIDDEN);
            }
        } else {
            permissionAccept = true;
            // return true;
        }

        if (roleAccept && permissionAccept) {
            if (user.provinceIds && user.provinceIds.includes(Number(provinceId))) {
                request['user'] = user;
                return true;
            }

            return false;
        } else {
            return false;
        }
    }
}
