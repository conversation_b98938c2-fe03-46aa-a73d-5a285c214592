import { ERole } from './../../entities/role.entity';
import {
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
    Injectable,
    ForbiddenException,
    Logger,
    UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import * as cacheManager from 'cache-manager';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import * as _ from 'lodash';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { PROVINCE_HEADER } from '../constants';
import { PERMISSION_KEY, ROLE_PERMISSIONS, ROLE_PERMISSIONS_EMPLOYEE } from './permissions.decorator';
import { ROLES_KEY } from './roles.decorator';

export class UserJwt {
    id: number;
    name: string;
    email: string;
    avatar: string;
    phone: string;
    roles: {
        id: number;
        name: ERole;
    }[];
    permissions: PermissionsAccessAction[];
    province_ids: number[];
    iat: number;
    exp: number;
}

@Injectable()
export default class AuthGuard implements CanActivate {
    private logger = new Logger(AuthGuard.name);

    private memoryCache: cacheManager.Cache;
    constructor(private reflector: Reflector) {
        this.memoryCache = cacheManager.caching({ store: 'memory', max: 1000, ttl: 86400 });
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest() as Request;
        const provinceId = request.get(PROVINCE_HEADER);
        const header = request.header('Authorization');

        if (!header) {
            throw new UnauthorizedException('Authorization: Bearer <token> header missing');
        }
        const parts = header.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            throw new UnauthorizedException('Authorization: Bearer <token> header invalid');
        }

        const token = parts[1];
        let user: UserJwt = null;
        try {
            user = jwt.verify(token, process.env.JWT_SECRET) as UserJwt;
        } catch (e) {
            this.logger.error(`[jwt verify] error: ${e.message} | stack: ${e.stack}`);
            throw new UnauthorizedException(e.message);
        }
        if (_.isEmpty(user)) return false;
        const { roles = [], province_ids = [] } = user;

        if (roles.length === 0) {
            throw new ForbiddenException('Tài khoản của bạn chưa được cập nhập Role!');
        }

        let roleValid = false;
        // requireRoles get from decorator RolesAllowed
        // check user has role in requireRoles
        const requireRoles = this.reflector.get<ERole[]>(ROLES_KEY, context.getHandler());
        if (requireRoles && requireRoles.length > 0) {
            for (let i = 0; i < roles.length; i++) {
                if (requireRoles.includes(roles[i].name)) {
                    roleValid = true;
                    break;
                }
            }
            if (!roleValid) {
                throw new ForbiddenException('Role của bạn không được sử dụng tính năng này!');
            }
        } else {
            roleValid = true;
        }

        if (!roleValid) {
            throw new ForbiddenException('Role của bạn không được sử dụng tính năng này!');
        }

        let permissionValid = false;
        // requirePermissions get from decorator RequirePermissions
        // check user has permission in requirePermissions
        const requirePermissions = this.reflector.getAllAndOverride<PermissionsAccessAction[]>(PERMISSION_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (requirePermissions && requirePermissions.length > 0) {
            const roleIds = roles.map((r) => r.id);
            const permissionsInRoles = [];
            for (let i = 0; i < roleIds.length; i++) {
                const roleId = roleIds[i];
                const permissions = ROLE_PERMISSIONS_EMPLOYEE[roleId];
                permissionsInRoles.push(...permissions);
            }
            // remove duplicate permissions in roles
            const perInRoles = permissionsInRoles.filter((value, index, self) => self.indexOf(value) === index);
            permissionValid = perInRoles.some((p) => requirePermissions.includes(p));
        } else {
            permissionValid = true;
        }
        if (!permissionValid) {
            throw new ForbiddenException(
                `Tài khoản không có quyền ${requirePermissions.map((p) => PermissionsAccessAction[p]).join(', ')}`,
            );
        }

        const provinceValid = province_ids.includes(Number(provinceId));
        if (!provinceValid) {
            throw new ForbiddenException(`Tài khoản của bạn không được sử dụng tại tỉnh này! ${provinceId} ${user?.id}`);
        }

        request['user'] = user;
        return true;
    }
}
