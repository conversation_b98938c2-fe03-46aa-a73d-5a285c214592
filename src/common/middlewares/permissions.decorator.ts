import { SetMetadata } from '@nestjs/common';
import { PermissionsAccessAction } from 'src/entities/permission.entity';

export const PERMISSION_KEY = 'PERMISSION';
export const RequirePermissions = (...permissions: PermissionsAccessAction[]) =>
    SetMetadata(PERMISSION_KEY, permissions);

// eslint-disable-next-line no-var
export var ROLE_PERMISSIONS: { [provinceId: string]: { [roleName: string]: string[] } } = {};

// ROLE_PERMISSIONS_V2[1] = ['1', '2', '3'];
export var ROLE_PERMISSIONS_EMPLOYEE: { [roleId: number]: string[] } = {};
