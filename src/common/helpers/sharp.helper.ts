import { Logger } from '@nestjs/common';

import * as Jimp from 'jimp';
import * as JPEG from 'jpeg-js';
import * as _ from 'lodash';
import * as sharp from 'sharp';

Jimp.decoders['image/jpeg'] = (data: Buffer) => JPEG.decode(data, { maxMemoryUsageInMB: 1024 });

const DEFAULT_MAX_WIDTH = 600;

export async function resizeImage(buffer: Buffer, _mine: string, maxWidth = DEFAULT_MAX_WIDTH): Promise<Buffer> {
    return new Promise(async (resolve, reject) => {
        if (!buffer && !_.isBuffer(buffer)) {
            reject(`Image empty`);
        }

        let imageWidth = 0;

        try {
            imageWidth = await sharp(buffer)
                .metadata()
                .then((val) => val.width);
        } catch (error) {
            Logger.error(`sharp error resizing image | message: ${error.message} | stack: ${error.stack}`);
        }

        // Jimp.read(buffer)
        //     .then((img) => {
        //         const width = img.getWidth();
        if (imageWidth > maxWidth) {
            sharp(buffer)
                .resize(maxWidth)
                .withMetadata()
                .toBuffer()
                .then(async (value: Buffer) => {
                    resolve(value);
                })
                .catch((err) => {
                    Logger.error(`sharp error resizing image | message: ${err.message} | stack: ${err.stack}`);
                    reject(new Error(`sharp error resizing image`));
                });
            // return img.resize(maxWidth, Jimp.AUTO).quality(100).getBufferAsync(mine);
        } else {
            resolve(buffer);
            // return img.getBufferAsync(mine);
        }
    });
}
