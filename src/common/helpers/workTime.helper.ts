import { WorkTime } from 'src/entities/workTime.entity';
import * as moment from 'moment';
import { RestaurantBusinessHours } from 'src/entities/restaurantBusinessHour.entity';

export function isOpen(workTimes: WorkTime[], currentTime: string) {
    const isOpen = workTimes.some(({ open_time, close_time }) => {
        const openTime = moment(open_time, 'HH:mm');
        const closeTime = moment(close_time, 'HH:mm');
        const currentTimeMoment = moment(currentTime, 'HH:mm');
        return currentTimeMoment.isBetween(openTime, closeTime);
    });
    return isOpen ? 1 : 0;
}

export function isOpenByBusinessHours(
    businessHours: RestaurantBusinessHours[],
    isoWeekDay: number,
    currentHourTime: string,
) {
    const businessHour = businessHours.find(({ day }) => day === isoWeekDay);
    if (businessHour) {
        const workTimes = businessHour.workTimes;
        if (workTimes && workTimes.length > 0) {
            return isOpen(workTimes, currentHourTime);
        } else {
            return 0;
        }
    } else {
        return 0;
    }
}
