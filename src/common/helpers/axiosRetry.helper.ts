import { Logger } from '@nestjs/common';
import axios, {
    AxiosError,
    AxiosInstance,
    AxiosRequestConfig,
    AxiosResponse,
} from 'axios';

const axiosRetryLogger = new Logger(axiosRetry.name);
export async function axiosRetry<T = unknown>(
    axiosInstance: AxiosInstance,
    axiosRequestConfig: AxiosRequestConfig,
    axiosRetryCodes: string[],
    maxRetryCount: number = 4,
    backoffMs: number = 1000,
    context: string = 'axiosRetry'
): Promise<AxiosResponse<T>> {
    let retryCount = 0;
    let canRetry = false;
    // let httpResponse: AxiosResponse<T> = null;
    do {
        const now = new Date();
        try {
            const httpResponse = await axiosInstance(axiosRequestConfig);
            const endTime = new Date();
            // responseData = response?.data;
            canRetry = false;
            axiosRetryLogger.log(
                `[request] [${context}] response: ${
                    httpResponse?.data
                        ? JSON.stringify(httpResponse?.data)
                        : httpResponse?.data
                } | input_args: ${JSON.stringify({
                    url: httpResponse.config.url,
                    method: httpResponse.config.method,
                    data: httpResponse.config.data,
                    headers: httpResponse.config.headers,
                    params: httpResponse.config.params,
                    status: httpResponse.status,
                    time: endTime.getTime() - now.getTime(),
                    startTime: now,
                    endTime,
                    retryCount,
                    canRetry,
                })}`
            );
            return httpResponse;
        } catch (error) {
            const axiosErrorInfo: Pick<AxiosError, 'code' | 'status'> = {};
            if (axios.isAxiosError(error)) {
                axiosErrorInfo.code = error.code;
                axiosErrorInfo.status = error.status;
            }
            const endTime = new Date();
            retryCount++;
            canRetry =
                axiosRetryCodes.includes(axiosErrorInfo.code) &&
                retryCount <= maxRetryCount;
            axiosRetryLogger.error(
                `[request] [${context}] error message: ${
                    error.message
                } | stack: ${error.stack} | input_args: ${JSON.stringify({
                    axiosErrorInfo,
                    url: error.config?.url,
                    method: error.config?.method,
                    data: error.config?.data,
                    headers: error.config?.headers,
                    params: error.config?.params,
                    status: error.response?.status,
                    time: endTime.getTime() - now.getTime(),
                    startTime: now,
                    endTime,
                    retryCount,
                    canRetry,
                })}`
            );

            if (!canRetry) {
                // If the error is not retryable, throw it
                throw error;
            }
        }
        if (canRetry) {
            await new Promise((resolve) => setTimeout(resolve, backoffMs));
        }
    } while (canRetry);

    // return httpResponse;
}
