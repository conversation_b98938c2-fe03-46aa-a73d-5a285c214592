import { Request } from 'express';
export function parseHttpReqToLokiParams(httpReq: Request, executionTime: number, context?: string) {
    const ip = httpReq.headers['x-real-ip'] || httpReq.ip || 'unknown';
    return {
        labels: {
            context: context,
            method: httpReq.method,
            statusCode: httpReq.res?.statusCode,
        },
        body: httpReq.body,
        query: httpReq.query,
        params: httpReq.params,
        path: httpReq.path,
        ip,
        headers: {
            'x-province': httpReq.headers['x-province'],
            'x-sub-province': httpReq.headers['x-sub-province'],
            'user-agent': httpReq.headers['user-agent'],
            'x-app-version': httpReq.headers['x-app-version'],
            'sec-ch-ua-mobile': httpReq.headers['sec-ch-ua-mobile'],
            'sec-ch-ua-platform': httpReq.headers['sec-ch-ua-platform'],
        },
        executionTime,
    };
}
