import * as _ from 'lodash';
import { ShipperRanking } from 'src/entities/shipperRankings.entity';

interface ICalcRankCondition {
    totalSuccessOrders: number;
    rating: number;
}
export function calcRankByConditions({ totalSuccessOrders, rating }: ICalcRankCondition, rankList: ShipperRanking[]) {
    rankList = _.sortBy(rankList, (r) => r.level);
    return (
        _.findLast(rankList, ({ conditions }) => {
            if (conditions) {
                const { min_total_successful_orders, min_rating } = conditions;
                if (min_total_successful_orders <= totalSuccessOrders && min_rating <= rating) return true;
                return false;
            } else {
                return false;
            }
        }) || rankList[0]
    );
}
