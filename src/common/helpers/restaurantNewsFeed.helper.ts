import { NewsFeedQueries } from '../constants/newsfeed.constant';

export function findQueryParamByKey(key: string) {
    return NewsFeedQueries.find((queryParam) => queryParam.key === key);
}

export function validateQueryParams(queryParams: Record<string, any>) {
    const queryParamsKeys = Object.keys(queryParams);
    const isValid = queryParamsKeys.every((key) => {
        const queryParam = findQueryParamByKey(key);
        if (!queryParam) {
            return false;
        }
        const queryValue = queryParams[key];
        const { dataType, inValues } = queryParam;
        if (typeof queryValue !== dataType) {
            return false;
        }
        if (inValues && inValues.findIndex((inValue) => inValue.value === queryValue) === -1) {
            return false;
        }
        return true;
    });
    return isValid;
}

export function transformQueryParamsToApi(queryParams: Record<string, any>, domain: string, endpoint: string) {
    let api = `${domain}/${endpoint}?`;
    const queryParamsKeys = Object.keys(queryParams);
    queryParamsKeys.forEach((key, index) => {
        const value = queryParams[key];
        const transform = findQueryParamByKey(key)?.transform;
        if (transform) {
            api += `${key}=${transform(value)}`;
        } else {
            api += `${key}=${value}`;
        }
        if (index < queryParamsKeys.length - 1) {
            api += '&';
        }
    });
    return api;
}

export function validateAndTransformQueryParamsToApi(
    queryParams: Record<string, any>,
    domain: string,
    endpoint: string,
): string {
    const isValid = validateQueryParams(queryParams);
    if (!isValid) {
        throw new Error('Invalid query params');
    }
    return transformQueryParamsToApi(queryParams, domain, endpoint);
}
