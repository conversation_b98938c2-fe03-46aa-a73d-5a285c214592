import { EScreenCode } from 'src/models/notification/notification.dto';

export enum EPnsType {
    PNS_REDIRECT_TO_RESTAURANT = 'PNS_REDIRECT_TO_RESTAURANT',
    PNS_REDIRECT_TO_COLLECTION = 'PNS_REDIRECT_TO_COLLECTION',
    PNS_REDIRECT_TO_NOTIFICATION = 'PNS_REDIRECT_TO_NOTIFICATION',
    PNS_SCREEN_CODE = 'PNS_SCREEN_CODE',
    PNS_DETAIL_MESSAGE = 'PNS_DETAIL_MESSAGE',
}

export interface IPayloadPnsTypeScreenCode {
    screen_code: string;
    province_id: number;
}

export interface IPayloadPnsTypeDetailMessage {
    id: string;
    message: string;
    user_id: number;
    room_id: string;
}

export class OneSignalNotificationDataBuilder {
    private pnsType: EPnsType;
    private title: string;
    private message: string;
    private payload: Record<string, unknown | any> | IPayloadPnsTypeScreenCode | IPayloadPnsTypeDetailMessage = {};
    private screenCode?: EScreenCode;
    private messageDetail?: IPayloadPnsTypeDetailMessage;
    private provinceId?: number;
    constructor(pnsType: EPnsType, title: string, message: string) {
        this.pnsType = pnsType;
        this.title = title;
        this.message = message;
    }

    private buildPayloadScreenCode(): IPayloadPnsTypeScreenCode | null | Record<string, any> {
        if (this.screenCode && this.provinceId) {
            return {
                screen_code: this.screenCode,
                province_id: this.provinceId,
            };
        }
        return {};
    }

    private buildPayloadDetailMessage(): IPayloadPnsTypeDetailMessage | null | Record<string, unknown> {
        if (this.messageDetail) {
            return this.messageDetail;
        }
        return {};
    }

    private buildPayload(): void {
        switch (this.pnsType) {
            case EPnsType.PNS_SCREEN_CODE:
                this.payload = this.buildPayloadScreenCode();
                break;
            case EPnsType.PNS_DETAIL_MESSAGE:
                this.payload = this.buildPayloadDetailMessage();
                break;
            default:
                this.payload = {};
                break;
        }
    }

    setScreenCode(screenCode: EScreenCode): OneSignalNotificationDataBuilder {
        this.screenCode = screenCode;
        this.buildPayload();
        return this;
    }

    setMessageDetail(messageDetail: IPayloadPnsTypeDetailMessage): OneSignalNotificationDataBuilder {
        this.messageDetail = messageDetail;
        this.buildPayload();
        return this;
    }

    setProvinceId(provinceId: number): OneSignalNotificationDataBuilder {
        this.provinceId = provinceId;
        this.buildPayload();
        return this;
    }

    build(): {
        pns_type: string;
        title: string;
        message: string;
        payload: string;
    } {
        return {
            pns_type: this.pnsType,
            title: this.title,
            message: this.message,
            payload: JSON.stringify(this.payload),
        };
    }
}
