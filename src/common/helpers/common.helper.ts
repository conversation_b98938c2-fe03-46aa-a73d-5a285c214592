import { BadRequestException } from '@nestjs/common';
import * as _ from 'lodash';
import { EUploadType } from 'src/entities/publicFile.entity';
import { v4 as uuid } from 'uuid';
import { TinyInt } from '../constants';

export function parseSearchToCondition(search, searchFields, searchJoin, condition: Record<string, any>) {
    let where;
    if (searchJoin === 'AND') {
        where = condition;
    }
    if (search) {
        const parsedSearch = _.keys(search).map((searchKey: string) => {
            if (!searchFields[searchKey]) {
                throw new BadRequestException(`${searchKey} requires searchFields`);
            }
            const parts = _.split(searchKey, '.');
            const operation = this.parseOperation(searchFields[searchKey], search[searchKey]);
            if (parts.length === 2) {
                return {
                    [parts[0]]: {
                        [parts[1]]: operation,
                    },
                };
            } else {
                return {
                    [searchKey]: operation,
                };
            }
        });

        if (searchJoin === 'OR') {
            where = parsedSearch.map((s) => ({
                ...condition,
                ...s,
            }));
        } else {
            _.assign(where, ...parsedSearch);
        }
    }
    return where;
}

export function createImageUrlDependingOnProvince(
    id: number,
    fileType: string,
    provinceId: string,
    cate?: string,
): string {
    let url = '';

    switch (provinceId) {
        case '1':
            url += 'baoloc';
            break;
        case '2':
            url += 'vungtau';
            break;
        case '3':
            url += 'dalat';
            break;
        case '4':
            url += 'ductrong';
            break;
        default:
            url += 'file';
            break;
    }
    if (provinceId) url += '/';
    switch (cate) {
        case EUploadType.FOOD:
            url += 'foods';
            break;
        case EUploadType.CATEGORY:
            url += 'collections';
            break;
        case EUploadType.AD_BANNER:
            url += 'banners';
            break;
        case EUploadType.CATEGORY:
            url += 'categories';
            break;
        case EUploadType.AVATAR:
            url += 'avatars';
            break;
        case EUploadType.RESTAURANT:
            url += 'restaurants';
            break;
        case EUploadType.EXTRA:
            url += 'extras';
            break;
        case EUploadType.FAQ:
            url += 'faqs';
            break;
        case EUploadType.FCMImages:
            url += 'fcmimages';
            break;
        case EUploadType.FRAME:
            url += 'frames';
            break;
        default:
            url += 'images';
    }
    url += '/';
    if (id) url += id;
    url += `${uuid()}.${fileType}`;
    return url;
}

export function isValidHttpUrl(str: string) {
    let url;

    try {
        url = new URL(str);
    } catch (_) {
        return false;
    }

    return url.protocol === 'http:' || url.protocol === 'https:';
}

export function removeUnicode(str: string) {
    if (_.isNil(str)) return str;

    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
    str = str.replace(/Đ/g, 'D');
    return str;
}

export function parseBooleanToTinyInt(value: boolean) {
    return value ? TinyInt.YES : TinyInt.NO;
}

export function removeQueryParams(url: string) {
    return url.split('?')[0];
}

export function applyCounterToSuffix(counter: number, subfixLength: number): string {
    const counterString = counter.toString();
    const counterLength = counterString.length;
    let subfix = '';
    if (counterLength > subfixLength) {
        subfixLength = subfixLength + 1;
    }
    if (subfixLength < 3) {
        subfixLength = 4;
    }
    for (let i = 0; i < subfixLength - counterLength; i++) {
        subfix += '0';
    }
    return subfix + counterString;
}

export function generateCode(prefix: string, counter: number, subfixLength: number, postfix?: string): string {
    let code = prefix + applyCounterToSuffix(counter, subfixLength);
    if (postfix) {
        code = code + postfix;
    }
    return code;
}

export function generateContractCode(province_name?: string): string {
    let code = 'MBL';
    let names = province_name ? province_name.split(' ') : [];
    names = names.map(([v]) => v);
    code = names.length > 0 ? 'M' + names.join('') : 'MBL';
    return code;
}

export const legalData = [
    {
        id: 1,
        role: 'Giám đốc kinh doanh',
        name: 'Đỗ Phú Thái',
        signature:
            'https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/images/ed850403-4091-42f0-a65f-86c0dcbbf1a0.png',
        address: '199 Nguyễn Công Trứ, phường 1, tp Bảo Lộc, Việt Nam',
    },
    {
        id: 2,
        role: 'Giám đốc kinh doanh',
        name: 'Đỗ Phú Thái',
        signature:
            'https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/images/ed850403-4091-42f0-a65f-86c0dcbbf1a0.png',
        address: '199 Nguyễn Công Trứ, phường 1, tp Bảo Lộc, Việt Nam',
    },
    {
        id: 3,
        role: 'Giám đốc kinh doanh',
        name: 'Đỗ Phú Thái',
        signature:
            'https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/images/ed850403-4091-42f0-a65f-86c0dcbbf1a0.png',
        address: '199 Nguyễn Công Trứ, phường 1, tp Bảo Lộc, Việt Nam',
    },
];

export function removeEmoji(text: string) {
    return text.replace(
        /([\u2700-\u27BF]|[\uE000-\uF8FF]|[\uD83C][\uDC00-\uDFFF]|[\uD83D][\uDC00-\uDFFF]|[\uFE00-\uFE0F]|\u24C2|[\uD83E][\uDD00-\uDDFF])/g,
        '',
    );
}
