import { IOrderTradeDiscountInfo, Order } from 'src/entities/order.entity';
import { TinyInt } from '../constants';
import { ERestaurantTradeDiscountType } from 'src/entities/restaurant.entity';
import * as _ from 'lodash';

export function calPercentTradeDiscount(total: number, tradeDiscount: number, roundTradeDiscount = 0): number {
    let result = 0;
    if (!roundTradeDiscount) {
        result = _.round(total * (tradeDiscount / 100), 3);
    } else {
        result = _.round(total * (tradeDiscount / 100));
    }
    return result;
}

export function calcMonthTradeDiscountByOrders(orders: Order[], roundTradeDiscount: TinyInt): number {
    const aggOrders = {
        percentType: {},
        priceType: {},
    };
    let totalTradeDiscount = 0;
    for (const item of orders) {
        if (
            item.restaurant_trade_discount_info.trade_discount_type ===
            ERestaurantTradeDiscountType.TRADE_DISCOUNT_TYPE_PRICE
        ) {
            if (!aggOrders.priceType[item.restaurant_trade_discount_info.trade_discount]) {
                aggOrders.priceType[item.restaurant_trade_discount_info.trade_discount] = [];
            }
            aggOrders.priceType[item.restaurant_trade_discount_info.trade_discount].push(item);
        }
        if (
            item.restaurant_trade_discount_info.trade_discount_type ===
            ERestaurantTradeDiscountType.TRADE_DISCOUNT_TYPE_PERCENT
        ) {
            if (!aggOrders.percentType[item.restaurant_trade_discount_info.trade_discount]) {
                aggOrders.percentType[item.restaurant_trade_discount_info.trade_discount] = [];
            }
            aggOrders.percentType[item.restaurant_trade_discount_info.trade_discount].push(item);
        }
    }

    Object.keys(aggOrders.percentType).forEach((key) => {
        const orders: Order[] = aggOrders.percentType[key];
        // console.log({ key });
        const priceToCalcTradeDiscount = _.sumBy(orders, (order) => {
            const price = order.note_price ? +order.note_price : order.sub_total_price * 1000;
            return price - _.sumBy(order.promotions, (promotion) => promotion.merchantSubTotalDiscount) * 1000;
        });
        const tradeDiscount = calPercentTradeDiscount(priceToCalcTradeDiscount / 1000, +key, roundTradeDiscount) * 1000;
        totalTradeDiscount += tradeDiscount;
    });
    for (const key of Object.keys(aggOrders.priceType)) {
        const orders: Order[] = aggOrders.priceType[key];
        let foodQuantity = 0;
        orders.forEach((order) => {
            foodQuantity += _.sumBy(order.foodOrders, 'quantity');
        });

        totalTradeDiscount += +key * foodQuantity * 1000;
    }
    return totalTradeDiscount;
}

export function calcMonthTradeDiscount(
    totalSubTotalPrice: number, // unit KVND
    totalFoodQuantity: number,
    tradeDiscountInfo: IOrderTradeDiscountInfo,
    roundTradeDiscount: TinyInt,
) {
    if (tradeDiscountInfo.trade_discount_type === ERestaurantTradeDiscountType.TRADE_DISCOUNT_TYPE_PRICE) {
        return tradeDiscountInfo.trade_discount * totalFoodQuantity;
    }
    return calPercentTradeDiscount(totalSubTotalPrice, tradeDiscountInfo.trade_discount, roundTradeDiscount);
}
