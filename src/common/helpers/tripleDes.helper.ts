import * as crypto from 'crypto';
export default {
    tripledes_encrypt: function (mytext: string, key: string) {
        return tripledes_encrypt({
            alg: 'des-ede3', //3des-ecb
            autoPad: false,
            key,
            plaintext: mytext,
            iv: null,
        });
    },
    tripledes_decrypt: function (encrypted: string, key: string) {

        return tripledes_decrypt({
            alg: 'des-ede3', //3des-ecb
            autoPad: false,
            key,
            ciph: encrypted,
            iv: null,
        });
    },
    hash: function (mytext: string) {
        return hash({
            key: process.env.ENCODE_KEY,
            plaintext: mytext,
        });
    },
};
function tripledes_encrypt(param: any) {
    const key = Buffer.from(param.key);
    const iv = Buffer.alloc(param.iv ? param.iv : 0);
    const plaintext = param.plaintext;
    const alg = param.alg;

    //encrypt
    const cipher = crypto.createCipheriv(alg, key, iv);
    //cipher.setAutoPadding(autoPad);  //default true
    let ciph = cipher.update(plaintext, 'utf8', 'hex');
    ciph += cipher.final('hex');
    return ciph;
}

function tripledes_decrypt(param: any) {
    const key = Buffer.from(param.key);
    const iv = Buffer.alloc(param.iv ? param.iv : 0);
    const alg = param.alg;

    //decrypt
    const decipher = crypto.createDecipheriv(alg, key, iv);
    //cipher.setAutoPadding(autoPad);
    let txt = decipher.update(param.ciph, 'hex', 'utf8');
    txt += decipher.final('utf8');
    return txt;
}

function hash(param: any) {
    const hash = crypto.createHash('sha256').update(param.plaintext, 'utf8').digest('hex');
    return hash;
}
