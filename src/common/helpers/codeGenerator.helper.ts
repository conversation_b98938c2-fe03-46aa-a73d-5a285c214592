const placeholder = '#';

function size(fn: { (x: any): boolean; (arg0: any): unknown }, pattern: string): number {
    if (fn) {
        return Array.from(pattern).filter((x) => fn(x)).length;
    } else {
        pattern.length;
    }
}

function radomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function randomElement(pattern: string): any {
    return pattern[radomInt(0, pattern.length - 1)];
}

enum Charset {
    NUMBERS = 'numbers',
    ALPHABETIC = 'alphabetic',
    ALPHANUMERIC = 'alphanumeric',
}
export const charsetOpts = {
    [Charset.NUMBERS]: '0123456789',
    [Charset.ALPHABETIC]: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
    [Charset.ALPHANUMERIC]: '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
};

interface IConfig {
    count: number;
    length: number;
    charset: (typeof charsetOpts)[Charset];
    prefix: string;
    postfix: string;
    pattern: string;
}
function generateConfig(config: Partial<IConfig>): IConfig {
    const count = config.count ? config.count : 1;
    const length = config.length ? config.length : 8;
    const charset = config.charset ? config.charset : Charset.ALPHANUMERIC;
    const prefix = config.prefix ? config.prefix : '';
    const postfix = config.postfix ? config.postfix : '';
    const pattern = config.pattern ? config.pattern : placeholder.repeat(config.length ? config.length : 8);
    return {
        count,
        length,
        charset,
        prefix,
        postfix,
        pattern,
    };
}

function isFeasible({ charset, pattern, count }: IConfig) {
    return charset.length ** size((x) => x === placeholder, pattern) >= count;
}

export function createOne(config: Partial<IConfig>) {
    const validatedConfig = generateConfig(config);
    const { charset, count, pattern, prefix, postfix } = validatedConfig;
    // if (!isFeasible(validatedConfig)) {
    //     throw new Error('Not possible to generate requested number of codes.');
    // }
    let code = '';
    for (const p of pattern) {
        const c = p === placeholder ? randomElement(charset) : p;
        code += c;
    }
    return `${prefix}${code}${postfix}`;
}
