export function calculateTax(
    amount: number,
    taxPercentage: number,
): {
    grossAmount: number;
    taxAmount: number;
    netAmount: number;
    taxPercentage: number;
} {
    const result = {
        grossAmount: amount,
        taxAmount: 0,
        netAmount: amount,
        taxPercentage,
    };

    if (taxPercentage > 0) {
        result.taxAmount = Math.round(amount * (taxPercentage / 100));
        result.netAmount = amount - result.taxAmount;
    }

    return result;
}