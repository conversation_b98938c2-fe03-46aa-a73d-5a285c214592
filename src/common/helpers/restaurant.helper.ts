import axios from 'axios';

const DomainUriPrefix = 'https://onelink.vill.vn';
const AndroidPackageName = 's1.vn.villship';
const IosBundleId = 's1.vn.villship';
const IosAppStoreId = '1506703946';
const generateDynamicLinkUri =
    'https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyASEG73ze6q1KOFmNn-lCA8ehz38pR8C-w';

export async function generateFirebaseDynamicLink(
    restaurantId: number,
    {
        socialTitle,
        socialDescription,
        socialImageLink,
    }: { socialTitle: string; socialDescription: string; socialImageLink: string },
    provinceId?: string,
) {
    let link = `${DomainUriPrefix}/details?id=${restaurantId}`;
    if (provinceId) {
        link = `${DomainUriPrefix}/details?id=${restaurantId}&province_id=${provinceId}`;
    }
    const payload = {
        dynamicLinkInfo: {
            domainUriPrefix: DomainUriPrefix,
            link,
            androidInfo: {
                androidPackageName: AndroidPackageName,
            },
            iosInfo: {
                iosBundleId: IosBundleId,
                iosAppStoreId: IosAppStoreId,
            },
            socialMetaTagInfo: {
                socialTitle: socialTitle,
                socialDescription: socialDescription,
                socialImageLink: socialImageLink,
            },
            desktopInfo: {
                desktopFallbackLink: 'https://vill.vn/download',
            },
        },
    };
    try {
        const response: { shortLink: string; previewLink: string } = (await axios.post(generateDynamicLinkUri, payload))
            .data;
        return response;
    } catch (error) {
        this.logger.error(`[generateFirebaseDynamicLink] error: ${error.message} | stack: ${error.stack}`);
        return null;
    }
}
