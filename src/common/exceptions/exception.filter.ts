import {
    ArgumentsHost,
    Catch,
    ExceptionFilter,
    ForbiddenException,
    HttpException,
    HttpStatus,
    Logger,
    UnauthorizedException,
} from '@nestjs/common';
import { HttpArgumentsHost } from '@nestjs/common/interfaces/features/arguments-host.interface';
import { Request, Response } from 'express';
import { QueryFailedError } from 'typeorm';
import { parseHttpReqToLokiParams } from '../helpers/httpRequestLog.helper';
import responseHelper from '../helpers/response.helper';

@Catch()
export class AllExceptionFilter implements ExceptionFilter {
    private logger = new Logger(AllExceptionFilter.name);
    private static handleResponse(response: Response, exception: HttpException | QueryFailedError | Error): void {
        let statusCode = HttpStatus.OK;
        let message = exception.message;
        if (exception instanceof UnauthorizedException || exception instanceof ForbiddenException) {
            statusCode = exception.getStatus();
        }
        if (exception instanceof QueryFailedError) message = 'Đã có lỗi xảy ra';
        response.status(statusCode).json(responseHelper.failed(message));
    }

    catch(exception: HttpException | Error, host: ArgumentsHost): void {
        const ctx: HttpArgumentsHost = host.switchToHttp();
        const response: Response = ctx.getResponse();

        // Response to client
        AllExceptionFilter.handleResponse(response, exception);

        const errorMsg = exception instanceof Object ? exception.message : exception;
        this.logger.error(`error message: ${errorMsg} | stack: ${exception?.stack}`);

        // Handling error message and logging
        if (exception instanceof HttpException || exception instanceof QueryFailedError || exception instanceof Error) {
            this.handleMessage(exception, ctx);
        }
    }

    private handleMessage(exception: HttpException | QueryFailedError | Error, ctx: HttpArgumentsHost): void {
        let message: any = 'Đã có lỗi xảy ra';
        let stack: string = '';
        if (exception instanceof HttpException) {
            message = JSON.stringify(exception.getResponse());
            stack = exception.stack.toString();
        } else if (exception instanceof QueryFailedError) {
            message = `${exception.message} - ${exception.query} - ${exception.parameters}`;
        } else if (exception instanceof Error) {
            message = exception.stack.toString();
        }
        const req = ctx.getRequest<Request>();
        this.logger.error({
            ...parseHttpReqToLokiParams(req, 0, 'http'),
            message,
            stack,
        });
    }
}
