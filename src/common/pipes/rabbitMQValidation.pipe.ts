import { ArgumentMetadata, Injectable, PipeTransform, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import * as _ from 'lodash';

@Injectable()
export class RabbitMQValidation implements PipeTransform<any> {
    async transform(value: any, { metatype }: ArgumentMetadata) {
        if (!metatype || !this.toValidate(metatype)) {
            return value;
        }

        const object = plainToClass(metatype, value);

        const errors = await validate(object);
        if (errors.length > 0) {
            Logger.error(`bad request error : ${errors} | data: ${JSON.stringify(value)}`, 'RabbitMQValidation');
            let errorMsg = 'Validation failed';
            if (_.isObject(errors[0].constraints)) {
                errorMsg = _.values(errors[0].constraints)[0];
            } else if (errors[0].children && _.isObject(errors[0].children[0])) {
                errorMsg = _.values(errors[0].children[0].constraints)[0];
            }
        }
        return object;
    }

    private toValidate(metatype: any): boolean {
        const types: any[] = [String, Boolean, Number, Array, Object];
        return !types.includes(metatype);
    }
}
