import { Optional } from '@nestjs/common';
import { Transform, TransformFnParams } from 'class-transformer';
import {
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Min,
    Validate,
    ValidationArguments,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Moment } from 'moment';

export enum SortedByEnum {
    DESC = 'DESC',
    ASC = 'ASC',
}

@ValidatorConstraint()
export class DateStringValidator implements ValidatorConstraintInterface {
    validate(date: Moment, validationArguments: ValidationArguments) {
        return date.isValid();
    }
}

export class BaseQueryFilterDto {
    @IsOptional()
    @IsString()
    orderBy = 'id';

    @Transform(({ value }) => (!_.isEmpty(value) ? _.upperCase(value) : 'DESC'), { toClassOnly: true })
    @IsEnum(SortedByEnum)
    sortedBy: SortedByEnum = SortedByEnum.DESC;

    @IsNotEmpty()
    @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    @IsNumber()
    limit = 20;

    @Optional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    page = 0;

    @Optional()
    // @IsString()
    name: any;

    @IsOptional()
    @Transform(({ value }) => moment(value, 'YYYY-MM-DD'))
    @Validate(DateStringValidator, {
        message: 'from_date is invalid - YYYY-MM-DD',
    })
    from_date: Moment;

    @IsOptional()
    @Transform(({ value }) => moment(value, 'YYYY-MM-DD').set({ hour: 23, minute: 59, second: 59, millisecond: 59 }))
    @Validate(DateStringValidator, {
        message: 'to_date is invalid - YYYY-MM-DD',
    })
    to_date: Moment = moment();
}

export class BaseQueryFilterV2Dto {
    @IsOptional()
    @IsString()
    orderBy = 'id';

    @Transform(({ value }) => (!_.isEmpty(value) ? _.upperCase(value) : 'DESC'), { toClassOnly: true })
    @IsEnum(SortedByEnum)
    sortedBy: SortedByEnum = SortedByEnum.DESC;

    @IsNotEmpty()
    @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    @IsNumber()
    limit = 20;

    @Optional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    page = 0;

    @IsOptional()
    @IsDateString()
    from_date?: string;

    @IsOptional()
    @IsDateString()
    to_date?: string;
}

export class BasePagedListDto {
    @IsOptional()
    @IsString()
    orderBy = 'id';

    @Transform(({ value }) => (!_.isEmpty(value) ? _.upperCase(value) : 'DESC'), { toClassOnly: true })
    @IsEnum(SortedByEnum)
    sortedBy: SortedByEnum = SortedByEnum.DESC;

    @IsNotEmpty()
    @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    @IsNumber()
    limit = 20;

    @Optional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    page = 1;
}

export class BaseTimeQueryFilterDto {
    @IsOptional()
    @Transform(({ value }) => moment(value, 'YYYY-MM-DD'))
    @Validate(DateStringValidator, {
        message: 'from_date is invalid - YYYY-MM-DD',
    })
    from_date: Moment;

    @IsOptional()
    @Transform(({ value }) => moment(value, 'YYYY-MM-DD').set({ hour: 23, minute: 59, second: 59, millisecond: 59 }))
    @Validate(DateStringValidator, {
        message: 'to_date is invalid - YYYY-MM-DD',
    })
    to_date: Moment = moment();
}

export class BaseRPCQueryFilterDto {
    sortedBy?: SortedByEnum = SortedByEnum.DESC;
    orderBy?: string = 'id';
    limit?: number = 20;
    page?: number = 0;
    province_id?: string = '1';
    id?: number;
}

export class BaseRPCQPayloadDto {
    @IsOptional()
    @IsString()
    sortedBy?: SortedByEnum = SortedByEnum.DESC;

    @IsOptional()
    @IsString()
    orderBy?: string = 'id';

    @IsOptional()
    @IsNumber()
    limit?: number = 20;

    @IsOptional()
    @IsNumber()
    page?: number = 0;

    @IsOptional()
    @IsNumber()
    provinceId?: number;

    @IsOptional()
    @IsNumber()
    id?: number;
}

export enum IntBooleanStr {
    YES = '1',
    NO = '0',
}

export enum Condition {
    EQUAL = '=',
    NOT_EQUAL = '!=',
    GREATER_THAN = '>',
    LESS_THAN = '<',
    GREATER_THAN_OR_EQUAL = '>=',
    LESS_THAN_OR_EQUAL = '<=',
    IN = 'IN',
    NOT_IN = 'NOT IN',
    LIKE = 'LIKE',
    NOT_LIKE = 'NOT LIKE',
    BETWEEN = 'BETWEEN',
    NOT_BETWEEN = 'NOT BETWEEN',
    IS_NULL = 'IS NULL',
    IS_NOT_NULL = 'IS NOT NULL',
}
