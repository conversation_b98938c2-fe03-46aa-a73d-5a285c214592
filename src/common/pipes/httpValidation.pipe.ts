import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import * as _ from 'lodash';

@Injectable()
export class HttpValidationPipe implements PipeTransform<any> {
    async transform(value: any, { metatype }: ArgumentMetadata) {
        if (!metatype || !this.toValidate(metatype)) {
            return value;
        }

        const object = plainToClass(metatype, value);

        const errors = await validate(object);
        if (errors.length > 0) {
            console.log('bad request error : ', JSON.stringify(errors));
            let errorMsg = 'Validation failed';

            if (_.isObject(errors[0].constraints)) {
                errorMsg = _.values(errors[0].constraints)[0];
            } else if (errors[0].children && _.isObject(errors[0].children[0])) {
                errorMsg = _.values(errors[0].children[0].constraints)[0];
            }

            throw new BadRequestException(errorMsg);
        }

        return object;
    }

    private toValidate(metatype: any): boolean {
        const types: any[] = [String, <PERSON>olean, Number, Array, Object];
        return !types.includes(metatype);
    }
}
