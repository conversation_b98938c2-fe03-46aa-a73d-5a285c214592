import * as _ from 'lodash';

export const currencyFormatDE = function (num: number, end: string = 'đ'): string {
    if (!num) {
        return `0 ${end}`;
    }
    let numOfDecimal = 0;
    let partial = _.toNumber(num).toString().split('.');
    let value = _.toNumber(partial[0])
        .toString()
        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');

    if (partial.length === 2) {
        value += '.' + partial[1];
    }
    let displayVnd = value + ` ${end}`;
    if (displayVnd) {
        displayVnd = displayVnd.replace(/,/g, ',');
    }
    return displayVnd;
};