/**
 * Utility class for handling calculation details as plain text
 * Simplified to support only text-based descriptions
 */
export class CalculationDetailsUtil {

    /**
     * Create calculation details from plain text
     */
    static fromText(text: string): string {
        return text || 'No calculation details provided';
    }

    /**
     * Create a simple text calculation details
     */
    static createSimpleText(
        ruleName: string,
        calculationText: string,
        result: number
    ): string {
        return `Rule: ${ruleName} | Calculation: ${calculationText} | Result: ${result} points`;
    }

    /**
     * Create calculation text for fixed points
     */
    static createFixedPointsText(ruleName: string, points: number): string {
        return `Rule: ${ruleName} | Fixed points awarded: ${points}`;
    }

    /**
     * Create calculation text for tiered calculation
     */
    static createTieredText(
        ruleName: string,
        fieldName: string,
        fieldValue: number,
        tierDescription: string,
        points: number
    ): string {
        return `Rule: ${ruleName} | Tiered calculation on ${fieldName}(${fieldValue}): ${tierDescription} = ${points} points`;
    }

    /**
     * Create calculation text for conditional rules
     */
    static createConditionalText(
        ruleName: string,
        conditions: string[],
        calculation: string,
        points: number
    ): string {
        const conditionsText = conditions.length > 0 ? conditions.join(' AND ') : 'All conditions met';
        return `Rule: ${ruleName} | Conditions: ${conditionsText} | Calculation: ${calculation} | Points: ${points}`;
    }

    /**
     * Create error text for failed calculations
     */
    static createErrorText(ruleName: string, errorMessage: string): string {
        return `Rule: ${ruleName} | Error: ${errorMessage} | Points: 0`;
    }

    /**
     * Validate calculation details input (simplified)
     */
    static validate(details: any): { isValid: boolean; error?: string } {
        if (details === null || details === undefined) {
            return { isValid: false, error: 'Calculation details is required' };
        }

        // Convert to string if not already
        if (typeof details !== 'string') {
            return { isValid: true }; // Will be converted to string
        }

        // Check if string is not empty
        if (details.trim().length === 0) {
            return { isValid: false, error: 'Calculation details cannot be empty' };
        }

        return { isValid: true };
    }

    /**
     * Sanitize text input
     */
    static sanitize(text: string): string {
        if (!text || typeof text !== 'string') {
            return 'No calculation details available';
        }

        // Remove potentially harmful characters and limit length
        return text
            .replace(/[<>]/g, '') // Remove HTML-like brackets
            .substring(0, 1000) // Limit to 1000 characters
            .trim();
    }

    /**
     * Format text for display
     */
    static formatForDisplay(text: string): string {
        if (!text || typeof text !== 'string') {
            return 'No calculation details available';
        }

        return text.trim();
    }
} 