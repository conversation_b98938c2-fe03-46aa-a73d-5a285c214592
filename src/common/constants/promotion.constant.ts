export enum EDiscountType {
    percentage = 'percentage',
    fixed_amount = 'fixed_amount',
}

export enum EProviderType {
    vill = 'vill',
    merchant = 'merchant',
    vill_and_merchant = 'vill_and_merchant',
}

export enum EPromotionType {
    spend_x_get_delivery_discount = 'spend_x_get_delivery_discount', // spend x get delivery discount. example in vietnamese: mua 100k giảm 10% phí giao hàng
    spend_x_get_sub_total_discount = 'spend_x_get_sub_total_discount', // spend x get sub total discount. example in vietnamese: mua 100k giảm 10% tổng hóa đơn
    buy_x_get_y = 'buy_x_get_y', // buy x in y get x. example in vietnamese: mua 2 bánh mì trong 1 hóa đơn 100k tặng 1 cocacola
    spend_x_get_y = 'spend_x_get_y', // spend x get y. example in vietnamese: mua 100k tặng 1 cocacola
    // buy_x_get_discount = 'buy_x_get_discount', // buy x get discount. example in vietnamese: mua 1 bánh mì giảm 50%
    // buy_x_get_y_discount = 'buy_x_get_y_discount', // buy x get y discount. example in vietnamese: mua 1 bánh mì tặng 1 cocacola giảm 50%
}