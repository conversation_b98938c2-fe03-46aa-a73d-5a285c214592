import { TinyInt } from '.';

export interface IQueryInValue {
    name: string;
    value: any;
}

export interface IQuery {
    name: string;
    key: string;
    required: boolean;
    dataType: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array';
    inValues?: Array<IQueryInValue>;
    transform?: (value: any) => any;
}

const TinyIntValues: Array<IQueryInValue> = [
    {
        name: 'Có',
        value: TinyInt.YES,
    },
    {
        name: 'không',
        value: TinyInt.NO,
    },
];

const BooleanValues: Array<IQueryInValue> = [
    {
        name: 'Có',
        value: true,
    },
    {
        name: 'không',
        value: false,
    },
];

export const RestaurantFilterSearchQueries: IQuery[] = [
    {
        name: 'Từ khoá tìm kiếm',
        key: 'search',
        dataType: 'string',
        required: false,
    },
    {
        name: 'Vị trí latitude',
        key: 'myLat',
        dataType: 'string',
        required: false,
    },
    {
        name: '<PERSON>ại của danh mục cụ thể (id)',
        key: 'categoryId',
        dataType: 'number',
        required: false,
    },
    {
        name: 'Vị trí longitude',
        key: 'myLon',
        dataType: 'string',
        required: false,
    },
    {
        name: 'Mở cửa buổi sáng',
        key: 'isMorning',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Mở cửa buổi trưa',
        key: 'isNoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Mở cửa buổi tối',
        key: 'isAfternoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Mở cửa xế chiều',
        key: 'isMidAfternoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Có khuyễn mãi',
        key: 'hasPromotion',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Có khuyễn mãi có giảm giá cụ thể',
        key: 'promotionId',
        required: false,
        dataType: 'number',
    },
    {
        name: 'Chọn nhiều mã khuyễn mãi có giảm giá cụ thể',
        key: 'promotionIds',
        required: false,
        dataType: 'array',
    },
    {
        name: 'Khuyến mãi có thể áp dụng với khuyến mãi khác',
        key: 'can_have_conjunction',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Được lên top (on top)',
        key: 'onTop',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Là quán đối tác',
        key: 'star',
        dataType: 'boolean',
        required: false,
        inValues: BooleanValues,
        transform: (value: boolean) => (value ? TinyInt.YES : TinyInt.NO),
    },
    {
        name: 'Top trending',
        key: 'topTrending',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Quán có món độc',
        key: 'isSpecial',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
    },
    {
        name: 'Thuộc loại dịch vụ có ID',
        key: 'collectionId',
        dataType: 'string',
        required: false,
    },
    {
        name: 'Chỉ lấy quán đang mở cửa',
        key: 'isOpen',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
    },
    {
        name: 'Random danh sách quán',
        key: 'randomDocs',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
    },
    {
        name: 'lấy danh sách nhà hàng trong bán kính (đơn vị: km)',
        key: 'maxDistance',
        dataType: 'number',
        required: false,
    },
    {
        name: 'Lấy danh sách quán gần đây mới tạo gần đây theo ngày (nhập ngày)',
        key: 'daysFromCurrentDate',
        dataType: 'number',
        required: false,
    },
    {
        name: 'Ưu tiên quán có quảng cáo newsfeed',
        key: 'newsfeedAds',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
    },
    {
        name: 'Thuộc thị trường con',
        key: 'subProvinceId',
        required: false,
        dataType: 'number',
    },
    {
        name: 'Thuộc news feed',
        key: 'newsFeedId',
        required: false,
        dataType: 'number',
    },
    {
        name: 'Sắp xếp danh sách cửa hàng theo',
        key: 'orderBy',
        dataType: 'string',
        required: false,
        inValues: [
            { name: 'Theo kết quả đúng nhất (điểm số tìm kiếm chính xác)', value: 'score' },
            { name: 'Theo bán kính', value: 'geo_distance' },
            { name: 'Theo id của cửa hàng', value: 'id' },
            { name: 'Theo ngày cập nhật của cửa hàng', value: 'updated_at' },
            { name: 'Theo ngày ngày tạo của cửa hàng', value: 'created_at' },
            { name: 'Theo rating của cửa hàng', value: 'rating' },
        ],
    },
    {
        name: 'Sắp xếp danh sách kiểu',
        key: 'sortedBy',
        dataType: 'string',
        required: false,
        inValues: [
            { name: 'Từ nhỏ đến lớn', value: 'ASC' },
            { name: 'Từ lớn đến nhỏ', value: 'DESC' },
        ],
    },
    {
        name: 'Số lượng lấy',
        key: 'limit',
        required: false,
        dataType: 'number',
    },
    {
        name: 'Trang',
        key: 'page',
        required: false,
        dataType: 'number',
    },
];
