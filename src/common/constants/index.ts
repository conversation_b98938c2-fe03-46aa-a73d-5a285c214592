// export const ConnectionNames = {
//     DEVA_NEW: 'default',
//     VILLSHIP_LAMDONG: 'VILLSHIP_LAMDONG',
//     VILLSHIP_DALAT: 'VILLSHIP_DALAT',
//     VILLSHIP_DUCTRONG: 'VILLSHIP_DUCTRONG',
//     VILLSHIP_DONGXOAI: 'VILLSHIP_DONGXOAI',
//     VILLSHIP_TAYNINH: 'VILLSHIP_TAYNINH',
//     VILLSHIP_GIANGHIA: 'VILLSHIP_GIANGHIA',
// };

export const TOKEN_NAME = 'api_token';

// export const EXCEPTION_PROVINCE_ID = 2;
// export const VUNGTAU_PROVINCE_ID = '2';

// export const DEFAULT_PROVINCE_ID = '1';

export const REDIS_TOKEN_EXPIRES_IN = 60 * 60 * 24 * 30;

export const PROVINCE_HEADER = 'X-Province';

export enum Role {
    client = 'client',
    driver = 'driver',
    manager = 'manager',
    admin = 'admin',
    adminTeam = 'TEAM - ADMIN',
    assignOrder = 'Nhân viên gán đơn',
    bussiness = 'Nhân viên kinh doanh',
    content = 'Nhân Viên Content',
    shipperManager = 'Quản Lý Shipper',
    marketAnalysis = 'Phân Tích Thị Trường',
    'TEAM - GDTINH' = 'TEAM - GDTINH',
    'TEAM - LEAD CITY' = 'TEAM - LEAD CITY',
    'TEAM - ADMIN' = 'TEAM - ADMIN',
    'TEAM - MARKETING' = 'TEAM - MARKETING',
    'TEAM - MERCHANT' = 'TEAM - MERCHANT',
    'TEAM - PTTT' = 'TEAM - PTTT',
    'TEAM - QLSP' = 'TEAM - QLSP',
    'TEAM - FINANCE' = 'TEAM - FINANCE',
    'TEAM - SALE' = 'TEAM - SALE',
    'TEAM - CSKH' = 'TEAM - CSKH',
    'TEAM - QLLEVEL2' = 'TEAM - QLLEVEL2',
    adsTeam = 'VILLADS TEAM',
}

export enum S3Folder {
    avatar = 'avatars',
}

export enum OrderStatusEnum {
    REPARING = 1,
    REPARED = 2,
    COMMING_TO_RESTAURANT = 3,
    SHIPPING = 4,
    ARRIVED = 5,
    CANCELED = 6,
    ASSIGNED = 7,
}

export enum MediaCollectionName {
    IMAGE = 'image',
    AVATAR = 'avatar',
    APP_LOGO = 'app_logo',
    DEFAULT = 'default',
}

export enum CacheTypeEnum {
    GET_BANNERS_CACHE_KEY = 'GET_BANNERS_CACHE',
}

export enum ModelType {
    ADS_BANNER = 'App\\Models\\AdsBanner',
    CATEGORY = 'App\\Models\\Category',
    EXTRA = 'App\\Models\\Extra',
    FAQ = 'App\\Models\\Faq',
    FOOD = 'App\\Models\\Food',
    GALLERY = 'App\\Models\\Gallery',
    MEDIA = 'App\\Models\\Media',
    RESTAURANT = 'App\\Models\\Restaurant',
    UPLOAD = 'App\\Models\\Upload',
    USER = 'App\\Models\\User',
}

export enum EMineType {
    'image/jpeg' = 'image/jpeg',
    'video/mp4' = 'video/mp4',
    'image/png' = 'image/png',
    'image/svg+xml' = 'image/svg+xml',
    'application/pdf' = 'application/pdf',
    'text/csv' = 'text/csv',
}

export enum TinyInt {
    YES = 1,
    NO = 0,
    TRUE = 1,
    FALSE = 0,
}

export const K_DONG_TO_VND_RATE = 1000;