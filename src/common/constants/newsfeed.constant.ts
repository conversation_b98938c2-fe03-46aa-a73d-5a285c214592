import { TinyInt } from '.';

export interface INewsFeedQueryInValue {
    name: string;
    value: any;
}

export interface INewsFeedQuery {
    name: string;
    key: string;
    required: boolean;
    dataType: 'string' | 'number' | 'boolean' | 'date' | 'object';
    inValues?: Array<INewsFeedQueryInValue>;
    type?: string;
    transform?: (value: any) => any;
}

const TinyIntValues: Array<INewsFeedQueryInValue> = [
    {
        name: 'Có',
        value: TinyInt.YES,
    },
    {
        name: 'không',
        value: TinyInt.NO,
    },
];

const BooleanValues: Array<INewsFeedQueryInValue> = [
    {
        name: 'Có',
        value: true,
    },
    {
        name: 'không',
        value: false,
    },
];

export interface IPriceRangeValue {
    gte: number;
    lte: number;
}

export const NewsFeedQueries: INewsFeedQuery[] = [
    {
        name: 'Mở cửa buổi sáng',
        key: 'isMorning',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Mở cửa buổi trưa',
        key: 'isNoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Mở cửa buổi tối',
        key: 'isAfternoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Mở cửa xế chiều',
        key: 'isMidAfternoon',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Có khuyễn mãi',
        key: 'hasPromotion',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Có khuyễn mãi có giảm giá cụ thể',
        key: 'promotionId',
        required: false,
        dataType: 'number',
        type: 'RESTAURANT',
    },
    {
        name: 'Khuyến mãi có thể áp dụng với khuyến mãi khác',
        key: 'can_have_conjunction',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Được lên top (on top)',
        key: 'onTop',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Là quán đối tác',
        key: 'star',
        dataType: 'boolean',
        required: false,
        inValues: BooleanValues,
        transform: (value: boolean) => (value ? TinyInt.YES : TinyInt.NO),
        type: 'RESTAURANT',
    },
    {
        name: 'Top trending',
        key: 'topTrending',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Quán có món độc',
        key: 'isSpecial',
        dataType: 'number',
        required: false,
        inValues: TinyIntValues,
        type: 'RESTAURANT',
    },
    {
        name: 'Thuộc loại dịch vụ có ID',
        key: 'collectionId',
        dataType: 'string',
        required: false,
        type: 'RESTAURANT',
    },
    // {
    //     name: 'Nhà hàng có ID cụ thể',
    //     key: 'restaurantIds',
    //     dataType: 'string',
    //     required: false,
    //     transform: (value: string) =>
    //         value
    //             .split(',')
    //             .map((id) => `restaurantIds[]=${id}`)
    //             .join('&'),
    // },
    {
        name: 'Chỉ lấy quán đang mở cửa',
        key: 'isOpen',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
        type: 'RESTAURANT',
    },
    {
        name: 'Random danh sách quán',
        key: 'randomDocs',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
        type: 'RESTAURANT',
    },
    {
        name: 'Lấy danh sách quán cụ thể',
        key: 'restaurantIds',
        dataType: 'object',
        required: false,
        transform: (value: any) => {
            if (value && Array.isArray(value)) {
                return value.join(',');
            }
            return '';
        },
        type: 'RESTAURANT',
    },
    {
        name: 'lấy danh sách nhà hàng trong bán kính (đơn vị: km)',
        key: 'maxDistance',
        dataType: 'number',
        required: false,
        type: 'RESTAURANT',
    },
    {
        name: 'Lấy danh sách quán gần đây mới tạo gần đây theo ngày (nhập ngày)',
        key: 'daysFromCurrentDate',
        dataType: 'number',
        required: false,
        type: 'RESTAURANT',
    },
    {
        name: 'Ưu tiên quán có quảng cáo newsfeed',
        key: 'newsfeedAds',
        dataType: 'number',
        required: false,
        inValues: [{ name: 'Có', value: TinyInt.YES }],
        type: 'RESTAURANT',
    },
    {
        name: 'Thuộc thị trường con',
        key: 'subProvinceId',
        required: false,
        dataType: 'number',
        type: 'RESTAURANT',
    },
    {
        name: 'Thuộc news feed',
        key: 'newsFeedId',
        required: false,
        dataType: 'number',
        type: 'RESTAURANT',
    },
    {
        name: 'Sắp xếp danh sách cửa hàng theo',
        key: 'orderBy',
        dataType: 'string',
        required: false,
        inValues: [
            { name: 'Theo kết quả đúng nhất (điểm số tìm kiếm chính xác)', value: 'score' },
            { name: 'Theo bán kính', value: 'geo_distance' },
            { name: 'Theo id của cửa hàng', value: 'id' },
            { name: 'Theo ngày cập nhật của cửa hàng', value: 'updated_at' },
            { name: 'Theo ngày ngày tạo của cửa hàng', value: 'created_at' },
            { name: 'Theo rating của cửa hàng', value: 'rating' },
        ],
        type: 'RESTAURANT',
    },
    {
        name: 'Sắp xếp danh sách kiểu',
        key: 'sortedBy',
        dataType: 'string',
        required: false,
        inValues: [
            { name: 'Từ nhỏ đến lớn', value: 'ASC' },
            { name: 'Từ lớn đến nhỏ', value: 'DESC' },
        ],
        type: 'RESTAURANT',
    },
    {
        name: 'Số lượng lấy',
        key: 'limit',
        required: false,
        dataType: 'number',
        type: 'RESTAURANT',
    },
    {
        name: 'Trang',
        key: 'page',
        required: false,
        dataType: 'number',
        type: 'RESTAURANT',
    },
    {
        name: 'Khoảng Giá món',
        key: 'price_range',
        required: false,
        dataType: 'object',
        transform: (value: IPriceRangeValue) => {
            if (value) {
                return value.gte + ',' + value.lte;
            }
            return '';
        },
        type: 'FOOD',
    },
    {
        name: 'Newsfeed cho món ăn',
        key: 'is_food_newsfeed',
        required: false,
        dataType: 'number',
        type: 'FOOD',
    },
    {
        name: 'Món gợi ý',
        key: 'isHotFood',
        required: false,
        dataType: 'number',
        inValues: [{ name: 'Có', value: TinyInt.YES }],
        type: 'FOOD',
    },
    {
        name: 'Giới hạn số lượng đặt món',
        key: 'isFoodOrderQuantityLimited',
        required: false,
        dataType: 'number',
        inValues: [{ name: 'Có', value: TinyInt.YES }],
        type: 'FOOD',
    },
    {
        name: 'Lấy danh sách newsfeed',
        key: 'newsfeedIds',
        dataType: 'object',
        required: false,
        transform: (value: any) => {
            if (value && Array.isArray(value)) {
                return value.join(',');
            }
            return '';
        },
        type: 'NEWSFEED',
    },
    {
        name: 'Lấy danh sách quán theo thương hiệu cụ thể',
        key: 'branchIds',
        dataType: 'object',
        required: false,
        transform: (value: any) => {
            if (value && Array.isArray(value)) {
                return value.join(',');
            }
            return '';
        },
        type: 'RESTAURANT',
    },
    {
        name: 'Lấy danh sách món cụ thể',
        key: 'foodIds',
        dataType: 'object',
        required: false,
        transform: (value: any) => {
            if (value && Array.isArray(value)) {
                return value.join(',');
            }
            return '';
        },
        type: 'FOOD',
    },
    {
        name: 'Food hit name',
        key: 'food_hit_name',
        required: false,
        dataType: 'string',
        type: 'FOOD',
    },
];
