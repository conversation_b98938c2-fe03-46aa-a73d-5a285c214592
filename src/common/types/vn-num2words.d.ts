/**
 * Type declarations for vn-num2words module
 * Vietnamese number to words converter
 */
declare module 'vn-num2words' {
  /**
   * Converts a number to Vietnamese words
   * @param input - The number to convert (can be number or string)
   * @returns The Vietnamese words representation of the number
   * 
   * @example
   * ```typescript
   * import VNnum2words from 'vn-num2words';
   * 
   * VNnum2words(1000); // "một nghìn"
   * VNnum2words('1234567899876543210101'); // for large numbers
   * ```
   */
  function VNnum2words(input: number | string): string;
  
  export = VNnum2words;
}
