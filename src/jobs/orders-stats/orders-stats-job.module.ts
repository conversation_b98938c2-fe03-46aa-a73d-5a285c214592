import { Modu<PERSON> } from '@nestjs/common';
import { OrdersStatsTask } from './orders-stats-job.task';
import { OrdersStatisticProcessor } from './orders-stats-job.processor';
import { BullModule } from '@nestjs/bull';
import { JobQueue } from '..';
import { OrderStatsService } from 'src/models/order-stats/order-stats.service';
import { OrdersStatsCronProcessor, OrdersStatsCronTask } from './orders-stats-job.cron';
import { OrderStatsServiceV2 } from 'src/models/order-stats/order-stats-v2.service';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            name: JobQueue.ORDER_STATS_JOB_QUEUE.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: 100,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
        BullModule.registerQueueAsync({
            name: JobQueue.CRON_JOB.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: true,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
    ],
    providers: [
        OrdersStatisticProcessor,
        OrdersStatsTask,
        OrderStatsService,
        OrderStatsServiceV2,
        OrdersStatsCronTask,
        OrdersStatsCronProcessor,
        OrderStatsServiceV2,
    ],
    exports: [OrdersStatsTask],
})
export class OrdersStatsJobModule {}
