import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { OrdersStatsTask } from './orders-stats-job.task';
import { OrderStatsService } from 'src/models/order-stats/order-stats.service';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class OrdersStatsCronTask {
    private logger = new Logger(OrdersStatsCronTask.name);

    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async orderStatistic() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_ORDER_STATS);
            this.logger.log(`[orderStatistic] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_ORDER_STATS) {
                        this.logger.log(`[orderStatistic] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_ORDER_STATS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_4AM,
                        tz: VietNamTimeZone,
                        key: 'CRON_ORDER_STATS',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[orderStatistic] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    @Timeout(5000)
    async createSheetForOrderFailureStatistic() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_ORDER_CANCELLED_STATS);
            this.logger.log(`[createSheetForOrderFailureStatistic] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_ORDER_CANCELLED_STATS) {
                        this.logger.log(`[orderStatistic] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_ORDER_CANCELLED_STATS,
                {},
                {
                    removeOnComplete: false,
                    removeOnFail: false,
                    attempts: 3,
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_4AM,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_ORDER_CANCELLED_STATS,
                    },
                },
            );
        } catch (error) {
            this.logger.error(
                `[createSheetForOrderFailureStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @Timeout(5000)
    async createSheetForOrderSuccessStatistic() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_ORDER_COMPLETE_STATS);
            this.logger.log(`[createSheetForOrderSuccessStatistic] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_ORDER_COMPLETE_STATS) {
                        this.logger.log(`[orderStatistic] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_ORDER_COMPLETE_STATS,
                {},
                {
                    attempts: 3,
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_4AM,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_ORDER_COMPLETE_STATS,
                    },
                },
            );
        } catch (error) {
            this.logger.error(
                `[createSheetForOrderSuccessStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @Timeout(5000)
    async orderStatsV2() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_ORDER_STATS_V2);
            this.logger.log(`[orderStatsV2] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_ORDER_STATS_V2) {
                        this.logger.log(`[orderStatsV2] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_ORDER_STATS_V2,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_4AM,
                        tz: VietNamTimeZone,
                        key: 'CRON_ORDER_STATS_V2',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[orderStatsV2] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class OrdersStatsCronProcessor {
    private logger = new Logger(OrdersStatsCronProcessor.name);

    constructor(
        private readonly ordersStatisticTask: OrdersStatsTask,
        private readonly orderStatsService: OrderStatsService,
    ) {}

    @Process({
        name: CRON_JOB.PROCESS.CRON_ORDER_STATS,
        concurrency: 1,
    })
    async cronOrderStatistic() {
        try {
            this.logger.log(`[cronOrderStatistic]: ${new Date().toISOString()} `);
            await this.ordersStatisticTask.orderStatistic();
        } catch (error) {
            this.logger.error(`[cronOrderStatistic] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_ORDER_STATS_V2,
        concurrency: 1,
    })
    async cronOrderStatsV2() {
        try {
            this.logger.log(`[cronOrderStatsV2]: ${new Date().toISOString()} `);
            await this.ordersStatisticTask.orderStats();
        } catch (error) {
            this.logger.error(`[cronOrderStatsV2] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_ORDER_COMPLETE_STATS,
        concurrency: 1,
    })
    async createSheetForOrderSuccessStatistic() {
        try {
            this.logger.log(`[createSheetForOrderSuccessStatistic]`);
            await this.orderStatsService.createSheetForOrderSuccessStatistic();
        } catch (error) {
            this.logger.error(
                `[createSheetForOrderSuccessStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_ORDER_CANCELLED_STATS,
        concurrency: 1,
    })
    async createSheetForOrderFailureStatistic() {
        try {
            this.logger.log(`[createSheetForOrderFailureStatistic]`);
            await this.orderStatsService.createSheetForOrderFailureStatistic();
        } catch (error) {
            this.logger.error(
                `[createSheetForOrderFailureStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }
}
