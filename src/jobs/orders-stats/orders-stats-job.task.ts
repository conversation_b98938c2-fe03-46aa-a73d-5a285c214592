import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { Queue } from 'bull';
import { JobQueue } from '..';
import * as moment from 'moment';
import { Province } from 'src/entities/province.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { OrderType } from 'src/entities/order.entity';
import { EOrderStatisticsConstantType, OrderStatisticsConstant } from 'src/entities/order-statistics-constant.entity';

const { ORDER_STATS_JOB_QUEUE } = JobQueue;

@Injectable()
export class OrdersStatsTask {
    private logger = new Logger(OrdersStatsTask.name);

    constructor(
        @InjectQueue(ORDER_STATS_JOB_QUEUE.PROCESSOR)
        private readonly ordersStatisticQueue: Queue,
    ) {}

    async reGenerateForOrderSuccessStatistic(milestone: string) {
        if (!milestone) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_COMPLETE_STATS_REGENERATE,
                'REGENERATE_' + milestone,
            );

            const currJob = await this.ordersStatisticQueue.getJob(jobId);
            if (currJob) {
                const jobStatus = await currJob.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await currJob.remove();
                } else {
                    return;
                }
            }

            await this.ordersStatisticQueue.add(
                ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_COMPLETE_STATS_REGENERATE,
                milestone,
                {
                    removeOnComplete: 100,
                    attempts: 3,
                    jobId,
                },
            );
        } catch (error) {
            this.logger.error(
                `[reGenerateForOrderSuccessStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    async reGenerateForOrderFailureStatistic(milestone: string) {
        if (!milestone) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_CANCELLED_STATS_REGENERATE,
                'REGENERATE_' + milestone,
            );

            const currJob = await this.ordersStatisticQueue.getJob(jobId);
            if (currJob) {
                const jobStatus = await currJob.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await currJob.remove();
                } else {
                    return;
                }
            }

            await this.ordersStatisticQueue.add(
                ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_CANCELLED_STATS_REGENERATE,
                milestone,
                {
                    removeOnComplete: 100,
                    attempts: 3,
                    jobId,
                },
            );
        } catch (error) {
            this.logger.error(
                `[reGenerateForOrderFailureStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    // replace cron here
    async orderStatistic() {
        const activeProvinces = await DatabaseService.getRepositoryByDefaultConnection(Province).find({
            where: {
                is_active: 1,
            },
        });

        try {
            for (let i = 0; i < activeProvinces.length; i++) {
                const { id: provinceId, parent_id: parentProvinceId, name } = activeProvinces[i];
                const jobId = generateJobIdByProcessName(
                    ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE,
                    `${provinceId}_${parentProvinceId}`,
                );

                const currJob = await this.ordersStatisticQueue.getJob(jobId);
                if (currJob) {
                    const jobStatus = await currJob.getState();
                    if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                        await currJob.remove();
                    } else {
                        return;
                    }
                }

                await this.ordersStatisticQueue.add(
                    ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE,
                    {
                        provinceId,
                        parentProvinceId,
                        provinceName: name,
                        dateOfStats: moment().utc().add(7, 'hours').subtract(1, 'days').format('YYYY-MM-DD'),
                    },
                    {
                        removeOnComplete: 100,
                        attempts: 3,
                        jobId,
                        /* repeat: {
                            cron: '0 4 * * *',
                            tz: 'Asia/Ho_Chi_Minh',
                            key: 'ORDER_STATS_BY_PROVINCE',
                        }, have query before job so not repeat here */
                    },
                );
            }
        } catch (error) {
            this.logger.error(`[orderStatistic] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    async reGenerateForOrderStatistic(dateOfStats: string) {
        const activeProvinces = await DatabaseService.getRepositoryByDefaultConnection(Province).find({
            where: {
                is_active: 1,
            },
        });

        try {
            for (let i = 0; i < activeProvinces.length; i++) {
                const { id: provinceId, parent_id: parentProvinceId, name } = activeProvinces[i];
                const jobId = generateJobIdByProcessName(
                    ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE,
                    `${provinceId}_${parentProvinceId}`,
                );

                const currJob = await this.ordersStatisticQueue.getJob(jobId);
                if (currJob) {
                    const jobStatus = await currJob.getState();
                    if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                        await currJob.remove();
                    } else {
                        return;
                    }
                }

                await this.ordersStatisticQueue.add(
                    ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE,
                    {
                        provinceId,
                        parentProvinceId,
                        provinceName: name,
                        dateOfStats,
                    },
                    {
                        removeOnComplete: 100,
                        attempts: 3,
                        jobId,
                    },
                );
            }
        } catch (error) {
            this.logger.error(`[reGenerateForOrderStatistic] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    async orderStats(dateOfStats: string = moment().utc().add(7, 'hours').subtract(1, 'days').format('YYYY-MM-DD')) {
        try {
            const activeProvinces = await DatabaseService.getRepositoryByDefaultConnection(Province).find({
                where: {
                    is_active: 1,
                },
                select: ['id', 'parent_id', 'name'],
            });
            const tenantGroups = await DatabaseService.getRepositoryByProvinceId(OrderStatisticsConstant, null).find({
                where: { type: EOrderStatisticsConstantType.TENANT_GROUPS },
            });
            const groups = {};
            if (tenantGroups && tenantGroups.length > 0) {
                tenantGroups.forEach((group) => {
                    groups[group.province_id] = group.value ? group.value.split(',') : [];
                });
            }
            const groupKeys = Object.keys(groups);
            const groupIds = Object.values(groups)
                .flat()
                .map((id) => Number(id));
            const provinceNotHaveGroup = activeProvinces.filter((province) => !groupIds.includes(province.id));
            const provinceHaveGroup = [];
            groupKeys.forEach((key) => {
                const provinceIds = groups[key].map((id) => Number(id));
                const provinces = activeProvinces.filter((province) => provinceIds.includes(province.id));
                const mapProvinces = provinces.map((province) => province.id);
                provinceHaveGroup.push({
                    id: key,
                    name: provinces.map((province) => province.name).join(','),
                    provinceIds: mapProvinces,
                });
            });
            const orderTypes = [OrderType.VILLCAR, 'VILLMOTO'];

            for (let i = 0; i < provinceNotHaveGroup.length; i++) {
                for (let j = 0; j < orderTypes.length; j++) {
                    const { id: provinceId, parent_id: parentProvinceId, name } = provinceNotHaveGroup[i];
                    const orderType = orderTypes[j];
                    const jobId = generateJobIdByProcessName(
                        ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE_V2,
                        `${provinceId}_${orderType}_${parentProvinceId}`,
                    );

                    const currJob = await this.ordersStatisticQueue.getJob(jobId);
                    if (currJob) {
                        const jobStatus = await currJob.getState();
                        if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                            await currJob.remove();
                        } else {
                            return;
                        }
                    }

                    await this.ordersStatisticQueue.add(
                        ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE_V2,
                        {
                            provinceId,
                            parentProvinceId,
                            provinceName: name,
                            dateOfStats,
                            orderType,
                        },
                        {
                            removeOnComplete: 100,
                            attempts: 3,
                            jobId,
                        },
                    );
                }
            }

            for (let i = 0; i < provinceHaveGroup.length; i++) {
                for (let j = 0; j < orderTypes.length; j++) {
                    const { id: provinceId, provinceIds, name } = provinceHaveGroup[i];
                    const orderType = orderTypes[j];
                    const jobId = generateJobIdByProcessName(
                        ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_GROUP_PROVINCES,
                        `${provinceId}_${orderType}_${provinceIds.join('_')}`,
                    );

                    const currJob = await this.ordersStatisticQueue.getJob(jobId);
                    if (currJob) {
                        const jobStatus = await currJob.getState();
                        if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                            await currJob.remove();
                        } else {
                            return;
                        }
                    }

                    await this.ordersStatisticQueue.add(
                        ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_GROUP_PROVINCES,
                        {
                            provinceId,
                            provinceName: name,
                            dateOfStats,
                            orderType,
                            provinceIds,
                        },
                        {
                            removeOnComplete: 100,
                            attempts: 3,
                            jobId,
                        },
                    );
                }
            }
        } catch (error) {
            this.logger.error(`[orderStatistic] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    async updateOrdersStats(dateOfStats: string) {
        if (!dateOfStats) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_REGENERATE,
                'REGENERATE_' + dateOfStats,
            );

            const currJob = await this.ordersStatisticQueue.getJob(jobId);
            if (currJob) {
                const jobStatus = await currJob.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await currJob.remove();
                } else {
                    return;
                }
            }

            await this.ordersStatisticQueue.add(ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_REGENERATE, dateOfStats, {
                removeOnComplete: 100,
                attempts: 3,
                jobId,
            });
        } catch (error) {
            this.logger.error(`[updateOrdersStats] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }
}
