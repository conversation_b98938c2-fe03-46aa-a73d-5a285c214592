import { OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';
import { OrderStatsService } from 'src/models/order-stats/order-stats.service';
import { OrderStatsServiceV2 } from 'src/models/order-stats/order-stats-v2.service';
import { OrdersStatsTask } from './orders-stats-job.task';
const { ORDER_STATS_JOB_QUEUE } = JobQueue;

@Processor(ORDER_STATS_JOB_QUEUE.PROCESSOR)
export class OrdersStatisticProcessor {
    private logger = new Logger(OrdersStatisticProcessor.name);

    constructor(
        private readonly orderStatsService: OrderStatsService,
        private readonly ordersStatisticTask: OrdersStatsTask,
        private readonly orderStatsServiceV2: OrderStatsServiceV2,
    ) {}

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE,
        concurrency: 1,
    })
    async orderStatistic(
        job: Job<{
            provinceId: string;
            parentProvinceId: string;
            provinceName: string;
            dateOfStats: string;
        }>,
    ) {
        try {
            const { provinceId, parentProvinceId, provinceName, dateOfStats } = job.data;
            this.logger.log(`[orderStatistic]: data: ${JSON.stringify(job.data)}`);
            await this.orderStatsService.orderStatsDailyByProvinceId(
                provinceId,
                parentProvinceId,
                provinceName,
                dateOfStats,
            );
        } catch (error) {
            this.logger.error(`[orderStatistic] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_CANCELLED_STATS_REGENERATE,
        concurrency: 1,
    })
    async reGenerateForOrderFailureStatistic(job: Job<string>) {
        try {
            this.logger.log(`[reGenerateForOrderFailureStatistic]: ${job.data} `);
            await this.orderStatsService.createSheetForOrderFailureStatistic(job.data);
        } catch (error) {
            this.logger.error(
                `[reGenerateForOrderFailureStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_COMPLETE_STATS_REGENERATE,
        concurrency: 1,
    })
    async reGenerateForOrderSuccessStatistic(job: Job<string>) {
        try {
            this.logger.log(`[reGenerateForOrderSuccessStatistic]: ${job.data} `);
            await this.orderStatsService.createSheetForOrderSuccessStatistic(job.data);
        } catch (error) {
            this.logger.error(
                `[reGenerateForOrderSuccessStatistic] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_PROVINCE_V2,
        concurrency: 1,
    })
    async orderStats(
        job: Job<{
            provinceId: string;
            parentProvinceId: string;
            provinceName: string;
            dateOfStats: string;
            orderType: 'VILLMOTO' | 'VILLCAR';
        }>,
    ) {
        try {
            const { orderType, provinceId, parentProvinceId, provinceName, dateOfStats } = job.data;
            this.logger.log(`[orderStats]: data: ${JSON.stringify(job.data)}`);
            await this.orderStatsServiceV2.orderStatsByProvinceId(
                provinceId,
                parentProvinceId,
                provinceName,
                dateOfStats,
                orderType,
            );
        } catch (error) {
            this.logger.error(`[orderStats] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_BY_GROUP_PROVINCES,
        concurrency: 1,
    })
    async orderStatsByGroup(
        job: Job<{
            provinceId: string;
            provinceName: string;
            dateOfStats: string;
            orderType: 'VILLMOTO' | 'VILLCAR';
            provinceIds: string[];
        }>,
    ) {
        try {
            const { orderType, provinceId, provinceName, dateOfStats, provinceIds } = job.data;
            this.logger.log(`[orderStatsByGroup]: data: ${JSON.stringify(job.data)}`);
            await this.orderStatsServiceV2.orderStatsByGroupProvinceIds(
                provinceId,
                provinceName,
                dateOfStats,
                orderType,
                provinceIds,
            );
        } catch (error) {
            this.logger.error(`[orderStatsByGroup] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    @Process({
        name: ORDER_STATS_JOB_QUEUE.PROCESS.ORDER_STATS_REGENERATE,
        concurrency: 1,
    })
    async updateOrdersStats(job: Job<string>) {
        try {
            this.logger.log(`[updateOrdersStats]: ${job.data} `);
            await this.ordersStatisticTask.orderStats(job.data);
        } catch (error) {
            this.logger.error(`[updateOrdersStats] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    @OnQueueFailed()
    handleQueueFailed(job: Job, err: Error) {
        this.logger.log(`[ FAILED ] JobId: ${job.id} | job name: ${job.name} | err: ${err ? err.message : null}`);
    }
}
