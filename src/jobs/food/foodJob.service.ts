import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { CronExpression } from '@nestjs/schedule';
import { CronRepeatOptions, Queue } from 'bull';
import * as moment from 'moment';
import * as jobHelpers from 'src/common/helpers/job.helpter';
import { LoggerService } from 'src/common/logger/logger.service';
import {
    EFoodSalesLimitPeriod,
    FoodSalesLimitManagement,
    IFoodSalesLimitManagementMetadata,
} from 'src/entities/foodSalesLimitManagement.entity';
import { FoodSalesLimitManagementService } from 'src/models/food/services/foodSaleLimitManagement.service';
import { JobQueue, VietNamTimeZone, VietNamTimeZoneNum } from '..';
import { IFoodJobResetSalesLimitData } from './foodJob.interface';

@Injectable()
export class FoodJobService {
    private logger = new LoggerService(FoodJobService.name);
    constructor(
        @InjectQueue(JobQueue.FOOD_QUEUE.PROCESSOR) private foodQueue: Queue,
        private foodSalesLimitService: FoodSalesLimitManagementService,
    ) {}

    async setCronJobResetSalesLimit(foodSalesLimit: FoodSalesLimitManagement, provinceId: string): Promise<void> {
        if (!foodSalesLimit) return;
        try {
            const {
                food_id,
                reset_time,
                reset_day_of_weeks,
                reset_period,
                // has_sales_limit,
                has_reset_sales_limit,
                recurring_sales_limit,
                metadata,
            } = foodSalesLimit;
            const jobId = jobHelpers.generateJobIdByProcessName(
                JobQueue.FOOD_QUEUE.PROCESS.FOOD_RESET_SALES_LIMIT,
                food_id,
                provinceId,
            );
            if (metadata) {
                if (metadata.repeatJobKey) {
                    await this.foodQueue
                        .removeRepeatableByKey(metadata.repeatJobKey)
                        .then((value) => {
                            this.logger.log(`removeRepeatableByKey: ${value} success`);
                        })
                        .catch((error) => {
                            this.logger.error(
                                `[setCronJobResetSalesLimit] removeRepeatableByKey | message: ${error.message} | stack: ${error.stack}`,
                            );
                        });
                }
                if (metadata.nextRepeatJobId) {
                    const jobExisting = await this.foodQueue.getJob(metadata.nextRepeatJobId);
                    if (jobExisting) {
                        await jobExisting
                            .remove()
                            .then((value) => {
                                this.logger.log(`remove jobId: ${value} success`);
                            })
                            .catch((error) => {
                                this.logger.error(
                                    `[setCronJobResetSalesLimit] remove | message: ${error.message} | stack: ${error.stack}`,
                                );
                            });
                    }
                }
            }
            if (has_reset_sales_limit) {
                const jobData: IFoodJobResetSalesLimitData = {
                    foodId: food_id,
                    provinceId,
                    has_reset_sales_limit,
                    recurring_sales_limit,
                    reset_period,
                    reset_time,
                    reset_day_of_weeks,
                };
                let repeat: CronRepeatOptions = null;
                if (reset_period == EFoodSalesLimitPeriod.HOURLY) {
                    repeat = {
                        cron: CronExpression.EVERY_HOUR,
                        tz: VietNamTimeZone,
                    };
                } else if (reset_period == EFoodSalesLimitPeriod.DAILY) {
                    if (reset_time) {
                        const resetTimeMoment = moment(reset_time, 'HH:mm:ss');
                        const hour = resetTimeMoment.get('hour');
                        const minute = resetTimeMoment.get('minute');
                        repeat = {
                            cron: `${minute} ${hour} * * *`,
                            tz: VietNamTimeZone,
                        };
                    }
                } else if (reset_period == EFoodSalesLimitPeriod.WEEKLY) {
                    if (reset_time && reset_day_of_weeks.length > 0) {
                        const resetTimeMoment = moment(reset_time, 'HH:mm:ss');
                        const hour = resetTimeMoment.get('hour');
                        const minute = resetTimeMoment.get('minute');
                        const dayOfWeeks = reset_day_of_weeks.join(',');
                        repeat = {
                            cron: `${minute} ${hour} * * ${dayOfWeeks}`,
                            tz: VietNamTimeZone,
                        };
                    }
                }
                if (repeat) {
                    const repeatableJob = await this.foodQueue.add(
                        JobQueue.FOOD_QUEUE.PROCESS.FOOD_RESET_SALES_LIMIT,
                        jobData,
                        {
                            jobId,
                            repeat: {
                                key: `food_reset_sales_limit_${food_id}_${provinceId}`,
                                ...repeat,
                            },
                        },
                    );
                    if (repeatableJob) {
                        const metadata: IFoodSalesLimitManagementMetadata = {
                            nextRepeatJobId: repeatableJob.opts.jobId as string,
                            repeatJobKey: repeatableJob.opts.repeat['key'],
                        };
                        await this.foodSalesLimitService.updateCronJobResetSalesLimit(food_id, metadata, provinceId);
                    }
                }
            }
        } catch (error) {
            this.logger.error(
                `[setCronJobResetSalesLimit] | message: ${error.message} | stack: ${error.stack}`,
                `setCronJobResetSalesLimit - foodId: ${foodSalesLimit.food_id} - provinceId: ${provinceId}`,
            );
        }
    }

    async setSoldOutUntil(foodId: number, provinceId: string, soldOutUntil: string) {
        const jobId = jobHelpers.generateJobIdByProcessName(
            JobQueue.FOOD_QUEUE.PROCESS.FOOD_SOLD_OUT_UNTIL,
            foodId,
            provinceId,
        );

        const currJob = await this.foodQueue.getJob(jobId);
        if (currJob) {
            await currJob.remove();
        }

        if (!soldOutUntil) {
            this.logger.error(
                `[removeSoldOutUntil] | soldOutUntil: ${soldOutUntil} | foodId: ${foodId} | provinceId: ${provinceId}`,
            );
            return;
        }

        const soldOutUntilMoment = moment(soldOutUntil, 'YYYY-MM-DD HH:mm:ss').utc(true);
        const currentTime = moment().utc().add(VietNamTimeZoneNum, 'hours');
        const delayTime = soldOutUntilMoment.valueOf() - currentTime.valueOf();

        if (delayTime < 0) {
            this.logger.error(
                `[setSoldOutUntil] | delayTime: ${delayTime} | soldOutUntil: ${soldOutUntil} | foodId: ${foodId} | provinceId: ${provinceId}`,
            );
            return;
        }

        await this.foodQueue.add(
            JobQueue.FOOD_QUEUE.PROCESS.FOOD_SOLD_OUT_UNTIL,
            {
                foodId,
                provinceId,
                soldOutUntil,
            },
            {
                jobId,
                delay: delayTime,
                attempts: 3,
                removeOnComplete: 1000,
            },
        );
    }
}
