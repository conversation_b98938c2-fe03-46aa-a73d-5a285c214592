import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EFoodEventNames } from 'src/events/constant';
import { FoodSalesLimitUpdatedDto } from 'src/events/dto';
import { FoodJobService } from './foodJob.service';

@Injectable()
export class FoodSalesLimitListener {
    constructor(private foodJobService: FoodJobService) {}
    @OnEvent(EFoodEventNames.FOOD_SALES_LIMIT_UPDATED, { async: true })
    handleFoodSalesLimitUpdatedEvent(eventData: FoodSalesLimitUpdatedDto) {
        console.log('handleFoodSalesLimitUpdatedEvent', eventData);
        if (!eventData) return;

        const { data, provinceId } = eventData;
        if (!data || !provinceId) return;

        this.foodJobService.setCronJobResetSalesLimit(data, provinceId);
    }
}
