import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { LoggerService } from 'src/common/logger/logger.service';
import { FoodService } from 'src/models/food/services/food.service';
import { JobQueue } from '..';
import { IFoodJobSoldOutUntilData } from './foodJob.interface';

@Processor(JobQueue.FOOD_QUEUE.PROCESSOR)
export class FoodJobProcessor {
    private logger = new LoggerService(FoodJobProcessor.name);

    constructor(private readonly foodService: FoodService) {}

    @Process(JobQueue.FOOD_QUEUE.PROCESS.FOOD_SOLD_OUT_UNTIL)
    async execSoldOutUntil(job: Job<IFoodJobSoldOutUntilData>) {
        try {
            if (job && job.data) {
                const jobData = job.data;
                const { foodId, provinceId, soldOutUntil } = jobData;
                this.logger.log(
                    `[execSoldOutUntil] | foodId: ${foodId} | provinceId: ${provinceId} | soldOutUntil: ${soldOutUntil}`,
                );
                await this.foodService.activateFood(foodId, provinceId);
            }
        } catch (error) {
            this.logger.error(`[execSoldOutUntil] | message: ${error.message} | stack: ${error.stack}`);
        }
    }
}
