import { TinyInt } from 'src/common/constants';
import { EFoodSalesLimitPeriod } from 'src/entities/foodSalesLimitManagement.entity';

export interface IFoodJobResetSalesLimitData {
    foodId: number;
    provinceId: string;
    has_reset_sales_limit: TinyInt;
    recurring_sales_limit: number;
    reset_period: EFoodSalesLimitPeriod;
    reset_time: string;
    reset_day_of_weeks: number[];
}

export interface IFoodJobSoldOutUntilData {
    foodId: number;
    provinceId: string;
    soldOutUntil: string;
}
