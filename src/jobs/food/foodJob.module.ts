import { Module } from '@nestjs/common';
import { UserActivityService } from 'src/models/adminUserActivity/userActivity.service';
import { ChatGptService } from 'src/models/chat-gpt/chat-gpt.service';
import { DriverService } from 'src/models/driver/services/driver.service';
import { EmployeeService } from 'src/models/employee/employee.service';
import { FoodEventPublisherService } from 'src/models/food/publishers/foodEvent.publisher';
import { FoodService } from 'src/models/food/services/food.service';
import { FoodQueueService } from 'src/models/food/services/foodQueue.service';
import { FoodSalesLimitManagementService } from 'src/models/food/services/foodSaleLimitManagement.service';
import { RecurringFoodHistoryService } from 'src/models/food/services/recurringFoodHistory.service';
import { LocationService } from 'src/models/location/location.service';
import { ProvinceService } from 'src/models/province/province.service';
import { ResizeImageService } from 'src/models/resizeImage/resizeImage.service';
import { RestaurantPaymentMethodsService } from 'src/models/restaurant-payment-methods/restaurant-payment-methods.service';
import { RestaurantPublisher } from 'src/models/restaurant/publishers/restaurant.publisher';
import { RestaurantService } from 'src/models/restaurant/services/restaurant.service';
import { RestaurantBusinessHourService } from 'src/models/restaurant/services/restaurantBusinessHour.service';
import { RestaurantLocationService } from 'src/models/restaurant/services/restaurantLocation.service';
import { RestaurantSearchService } from 'src/models/restaurant/services/restaurantSearch.service';
import { RestaurantCounterService } from 'src/models/restaurantCounter/restaurantCounter.service';
import { RestaurantReviewSummaryService } from 'src/models/restaurantReview/services/restaurantReviewSummary.service';
import { RestaurantUpdateHistoryService } from 'src/models/restaurantUpdateHistory/restaurantUpdateHistory.service';
import { UserEventPublisherService } from 'src/models/user/publishers/userEvent.publisher';
import { CentralizedUserService } from 'src/models/user/services/centralizedUser.service';
import { UserService } from 'src/models/user/services/user.service';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { FirebaseAdmin } from 'src/providers/firebase/firebase.service';
import { RabbitMQEventModule } from 'src/rabbitMQ/rabbitMQ.module';
import { FoodSalesLimitListener } from './food.listener';
import { FoodJobProcessor } from './foodJob.processor';
import { FoodJobService } from './foodJob.service';
@Module({
    imports: [
        // BullModule.registerQueueAsync({
        //     name: JobQueue.FOOD_QUEUE.PROCESSOR,
        //     useFactory: () => ({
        //         defaultJobOptions: {
        //             removeOnComplete: 100,
        //             attempts: 2,
        //             backoff: 10000,
        //         },
        //     }),
        // }),
        RabbitMQEventModule,
    ],
    providers: [
        FoodJobService,
        FoodJobProcessor,
        FoodSalesLimitManagementService,
        FoodSalesLimitListener,
        FoodService,
        AwsS3Service,
        RestaurantSearchService,
        FirebaseAdmin,
        RestaurantService,
        ResizeImageService,
        FoodQueueService,
        RecurringFoodHistoryService,
        UserActivityService,
        FoodEventPublisherService,
        ChatGptService,
        ProvinceService,
        RestaurantReviewSummaryService,
        RestaurantBusinessHourService,
        RestaurantUpdateHistoryService,
        RestaurantLocationService,
        LocationService,
        RestaurantCounterService,
        RestaurantPublisher,
        RestaurantPaymentMethodsService,
        CentralizedUserService,
        EmployeeService,
        UserEventPublisherService,
        UserService,
        DriverService,
    ],
})
export class FoodJobModule {}
