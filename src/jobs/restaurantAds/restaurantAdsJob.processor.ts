import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { RestaurantAd } from 'src/entities/restaurantAd.entity';
import { RestaurantAdService } from 'src/models/restaurantAd/services/restaurantAd.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { JobQueue } from '..';
import { CheckExpiredAdsDto } from './restaurantAds.dto';
import { ERestaurantAdStatus } from 'src/models/restaurantAd/dto/restaurantAd.dto';

const { RESTAURANT_ADS_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_ADS_QUEUE;
@Processor(RESTAURANT_ADS_QUEUE.PROCESSOR)
export class RestaurantAdsProcessor {
    private logger = new Logger(RestaurantAdsProcessor.name);

    constructor(private restaurantAdService: RestaurantAdService) { }

    @Process(PROCESS.CHECK_EXPIRED_ADS)
    async checkAndUpdateExpiredAds(job: Job<CheckExpiredAdsDto>) {
        const provinceId = job.data?.provinceId;
        if (!provinceId) {
            return;
        }
        const expiredAds = await DatabaseService.getRepositoryByProvinceId(RestaurantAd, provinceId)
            .createQueryBuilder()
            .where({ status: ERestaurantAdStatus.done })
            .andWhere('active_to <= CURRENT_TIMESTAMP')
            .getMany();
        for (const ad of expiredAds) {
            try {
                await this.restaurantAdService.updateRestaurantAd(
                    ad.id,
                    {
                        status: ERestaurantAdStatus.expired,
                        is_active: false,
                    },
                    null,
                    provinceId,
                );
            } catch (error) {
                this.logger.error(`[checkAndUpdateExpiredAds] | message: ${error.message} | stack: ${error.stack}`);
            }
        }
        return;
    }
}
