import { Module } from '@nestjs/common';
import { JobQueue } from '..';
import { BullModule } from '@nestjs/bull';
import { RestaurantAdsJobTask } from './restaurantAdsJob.task';
import { RestaurantAdsProcessor } from './restaurantAdsJob.processor';
import { RestaurantAdModule } from 'src/models/restaurantAd/restaurantAd.module';
import { RestaurantAdsJobCron, RestaurantAdsJobCronProcessor } from './restaurant-ads-job.cron';

@Module({
    imports: [
        BullModule.registerQueue({
            name: JobQueue.RESTAURANT_ADS_QUEUE.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            },
        }),
        RestaurantAdModule,
    ],
    providers: [RestaurantAdsJobTask, RestaurantAdsProcessor, RestaurantAdsJobCronProcessor, RestaurantAdsJobCron],
    exports: [RestaurantAdsJobTask],
})
export class RestaurantAdJobModule {}
