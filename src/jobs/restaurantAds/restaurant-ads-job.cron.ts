import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { RestaurantAdsJobTask } from './restaurantAdsJob.task';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class RestaurantAdsJobCron {
    private logger = new Logger(RestaurantAdsJobCron.name);

    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async checkRestaurantAdActiveTimeAndUpdateStatus() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                CRON_JOB.PROCESS.CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS,
            );
            this.logger.log(`[checkRestaurantAdActiveTimeAndUpdateStatus] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS) {
                        this.logger.log(`[checkRestaurantAdActiveTimeAndUpdateStatus] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: 'CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[checkRestaurantAdActiveTimeAndUpdateStatus] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class RestaurantAdsJobCronProcessor {
    private logger = new Logger(RestaurantAdsJobCronProcessor.name);

    constructor(private restaurantAdsJobTask: RestaurantAdsJobTask) {}

    @Process(CRON_JOB.PROCESS.CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS)
    async checkRestaurantAdActiveTimeAndUpdateStatus(job: Job) {
        try {
            await this.restaurantAdsJobTask.checkRestaurantAdActiveTimeAndUpdateStatus();
        } catch (error) {
            this.logger.error(
                `[checkRestaurantAdActiveTimeAndUpdateStatus] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}
