import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Queue } from 'bull';
import { DatabaseService } from 'src/providers/database/database.service';

import { ECronjobs, JobQueue, VietNamTimeZone } from '..';
const { RESTAURANT_ADS_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_ADS_QUEUE;

@Injectable()
export class RestaurantAdsJobTask {
    private logger = new Logger(RestaurantAdsJobTask.name);

    constructor(@InjectQueue(RESTAURANT_ADS_QUEUE.PROCESSOR) private restaurantAdsQueue: Queue) {}

    /* Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
        name: ECronjobs.CheckExpiredAds,
        timeZone: VietNamTimeZone,
    }) */
    async checkRestaurantAdActiveTimeAndUpdateStatus() {
        const provinceIds = DatabaseService.getAllProvinceIds();
        for (const provinceId of provinceIds) {
            await this.restaurantAdsQueue.add(PROCESS.CHECK_EXPIRED_ADS, { provinceId }, { removeOnComplete: 100 });
        }
    }
}
