import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { JobQueue } from '..';
import { RestaurantRevenueJobCron, RestaurantRevenueJobCronProcessor } from './restaurant-revenue-job.cron';
import { RestaurantRevenueTask } from 'src/models/restaurant/restaurantRevenue.task';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            name: JobQueue.RESTAURANT_QUEUE.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: 100,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
    ],
    providers: [RestaurantRevenueJobCron, RestaurantRevenueJobCronProcessor, RestaurantRevenueTask],
})
export class RestaurantRevenueJobModule {}
