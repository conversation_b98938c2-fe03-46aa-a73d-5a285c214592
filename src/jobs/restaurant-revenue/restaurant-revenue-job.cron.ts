import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { RestaurantRevenueTask } from 'src/models/restaurant/restaurantRevenue.task';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class RestaurantRevenueJobCron {
    private logger = new Logger(RestaurantRevenueJobCron.name);

    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async dailySummaryRestaurantRevenue() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_DAILY_SUMMARY_RESTAURANT_REVENUE);
            this.logger.log(`[dailySummaryRestaurantRevenue] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_DAILY_SUMMARY_RESTAURANT_REVENUE) {
                        this.logger.log(`[dailySummaryRestaurantRevenue] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_DAILY_SUMMARY_RESTAURANT_REVENUE,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_7AM,
                        tz: VietNamTimeZone,
                        key: 'CRON_DAILY_SUMMARY_RESTAURANT_REVENUE',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[dailySummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async weekSummaryRestaurantRevenue() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE);
            this.logger.log(`[weekSummaryRestaurantRevenue] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE) {
                        this.logger.log(`[weekSummaryRestaurantRevenue] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_WEEK,
                        tz: VietNamTimeZone,
                        key: 'CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[weekSummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async monthSummaryRestaurantRevenue() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE);
            this.logger.log(`[monthSummaryRestaurantRevenue] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE) {
                        this.logger.log(`[monthSummaryRestaurantRevenue] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: 'CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[monthSummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class RestaurantRevenueJobCronProcessor {
    private logger = new Logger(RestaurantRevenueJobCronProcessor.name);

    constructor(private readonly restaurantRevenueTask: RestaurantRevenueTask) {}

    @Process(CRON_JOB.PROCESS.CRON_DAILY_SUMMARY_RESTAURANT_REVENUE)
    async dailySummaryRestaurantRevenue(job: Job) {
        try {
            await this.restaurantRevenueTask.dailySummaryRestaurantRevenue();
        } catch (error) {
            this.logger.error(`[dailySummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process(CRON_JOB.PROCESS.CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE)
    async weekSummaryRestaurantRevenue(job: Job) {
        try {
            await this.restaurantRevenueTask.weekSummaryRestaurantRevenue();
        } catch (error) {
            this.logger.error(`[weekSummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process(CRON_JOB.PROCESS.CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE)
    async monthSummaryRestaurantRevenue(job: Job) {
        try {
            await this.restaurantRevenueTask.monthSummaryRestaurantRevenue();
        } catch (error) {
            this.logger.error(`[monthSummaryRestaurantRevenue] | message: ${error.message} | stack: ${error.stack}`);
        }
    }
}
