import { Module } from '@nestjs/common';
import { OrderModule } from 'src/models/order/order.module';
import { Order<PERSON>obCron, OrderJobCronProcessor } from './order-job.cron';
import { OrderProcessor } from './orderJob.processor';

@Module({
    imports: [
        OrderModule,
        // BullModule.registerQueueAsync({
        //     name: JobQueue.ORDER_QUEUE.PROCESSOR,
        //     useFactory: () => ({
        //         defaultJobOptions: {
        //             removeOnComplete: 100,
        //             attempts: 2,
        //             backoff: 10000,
        //         },
        //     }),
        // }),
    ],
    providers: [OrderProcessor, OrderJobCron, OrderJobCronProcessor],
})
export class OrderJobModule {}
