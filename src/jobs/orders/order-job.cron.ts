import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { OrderTask } from 'src/models/order/order.task';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class OrderJobCron {
    private logger = new Logger(OrderJobCron.name);
    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) { }

    @Timeout(5000)
    async fetchZaloPayOrderStatusAndUpdateToAllDBConnections() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                CRON_JOB.PROCESS.CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
            );

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (
                        job &&
                        job.name === CRON_JOB.PROCESS.CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS
                    ) {
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_10_MINUTES,
                        tz: VietNamTimeZone,
                        key: 'CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[fetchZaloPayOrderStatusAndUpdateToAllDBConnections] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }

    @Timeout(5000)
    async fetchMomoOrderStatusAndUpdateToAllDBConnections() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                CRON_JOB.PROCESS.CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
            );

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (
                        job &&
                        job.name === CRON_JOB.PROCESS.CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS
                    ) {
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_10_MINUTES,
                        tz: VietNamTimeZone,
                        key: 'CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[fetchMomoOrderStatusAndUpdateToAllDBConnections] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }



    /* @Timeout(5000)
    async autoDeleteExpiredCarts() {
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_AUTO_DELETE_EXPIRED_CARTS);
            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_AUTO_DELETE_EXPIRED_CARTS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_30_MINUTES,
                        tz: VietNamTimeZone,
                        key: 'CRON_AUTO_DELETE_EXPIRED_CARTS',
                    },
                },
            );
        } catch (error) {
            this.logger.error('[autoDeleteExpiredCarts]', error);
        }
    } */
}

@Processor(CRON_JOB.PROCESSOR)
export class OrderJobCronProcessor {
    private logger = new Logger(OrderJobCronProcessor.name);
    constructor(private readonly orderTask: OrderTask) { }

    @Process({
        name: CRON_JOB.PROCESS.CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
        concurrency: 1,
    })
    async fetchZaloPayOrderStatusAndUpdateToAllDBConnections() {
        try {
            this.logger.log(
                `[fetchZaloPayOrderStatusAndUpdateToAllDBConnections] Fetch ZaloPay order status and update to all DB connections ...`,
            );
            await this.orderTask.FetchZaloPayOrderStatusAndUpdateToAllDBConnections();
        } catch (error) {
            this.logger.error(
                `[fetchZaloPayOrderStatusAndUpdateToAllDBConnections] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS,
        concurrency: 1,
    })
    async fetchMomoOrderStatusAndUpdateToAllDBConnections() {
        try {
            this.logger.log(
                `[fetchMomoOrderStatusAndUpdateToAllDBConnections] Fetch Momo order status and update to all DB connections ...`,
            );
            await this.orderTask.FetchMomoOrderStatusAndUpdateToAllDBConnections();
        } catch (error) {
            this.logger.error(
                `[fetchMomoOrderStatusAndUpdateToAllDBConnections] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }



    /* @Process({
        name: CRON_JOB.PROCESS.CRON_AUTO_DELETE_EXPIRED_CARTS,
        concurrency: 1,
    })
    async autoDeleteExpiredCarts() {
        try {
            this.logger.log(`[autoDeleteExpiredCarts] Auto delete expired carts ...`);
            await this.orderTask.autoDeleteExpiredCarts();
        } catch (error) {
            this.logger.error('[autoDeleteExpiredCarts]', error);
        }
    } */
}
