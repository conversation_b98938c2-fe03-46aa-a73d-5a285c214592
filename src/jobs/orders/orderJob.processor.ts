import { OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { isObject } from 'class-validator';
import { EOrderStatusId } from 'src/entities/orderStatus.entity';
import { OrderService } from 'src/models/order/services/order.service';
import { OrderNotificationService } from 'src/models/order/services/orderNotification.service';
import { JobQueue } from '..';
import { EOrderJobCancelWaitingOnlinePayment, EOrderJobRequestCancelingConfirmation } from './orderJob.interface';
const { ORDER_QUEUE } = JobQueue;
const { PROCESS } = ORDER_QUEUE;

@Processor(ORDER_QUEUE.PROCESSOR)
export class OrderProcessor {
    private logger = new Logger(OrderProcessor.name);

    constructor(private orderService: OrderService, private orderNotificationService: OrderNotificationService) {}

    @Process(PROCESS.REQUEST_CANCELING_CONFIRMATION)
    async requestCancelingConfirmation(job: Job<EOrderJobRequestCancelingConfirmation>) {
        const { id, provinceId, userDeviceToken } = job.data;
        const order = await this.orderService.getOrderById(id, [], provinceId);
        if (order && order.order_status_id < EOrderStatusId.ARRIVED && !order.driver_id) {
            await this.orderNotificationService.pushNotiToClientRequestCancelingConfirmation(
                userDeviceToken,
                order.code,
                order.id,
                provinceId,
            );
            await job.progress(100);
        } else {
            await job.progress(100);
        }
        return;
    }

    @Process(PROCESS.CANCEL_ORDER_WAITING_ONLINE_PAYMENT)
    async cancelOrdersOnlinePaymentLongTime(job: Job<EOrderJobCancelWaitingOnlinePayment>) {
        try {
            const { id, provinceId } = job.data;
            await this.orderService.cancelOrderWaitingOnlinePayment(id, provinceId);
            await job.progress(1000);
            return;
        } catch (error) {
            this.logger.error(
                `[cancelOrdersOnlinePaymentLongTime] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    @OnQueueCompleted()
    handleQueueCompleted(job: Job, result: any) {
        this.logger.log(job);
        this.logger.log(
            `[ SUCCESS ] JobId: ${job.id} | job name: ${job.name} | result: ${
                result && isObject(result) ? JSON.stringify(result) : null
            }`,
        );
    }

    @OnQueueFailed()
    handleQueueFailed(job: Job, err: Error) {
        this.logger.log(`[ FAILED ] JobId: ${job.id} | job name: ${job.name} | err: ${err ? err.message : null}`);
    }
}
