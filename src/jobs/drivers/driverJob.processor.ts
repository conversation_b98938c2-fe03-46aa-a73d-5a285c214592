import { OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { isObject } from 'lodash';
import * as moment from 'moment';
import { TinyInt } from 'src/common/constants';
import { createRequestByProvince } from 'src/common/helpers/request.helper';
import { calcRankByConditions } from 'src/common/helpers/shipperRank.helper';
import { Driver } from 'src/entities/driver.entity';
import { DriverRankHistory } from 'src/entities/driverRankHistory.entity';
import { Order } from 'src/entities/order.entity';
import { EOrderStatusId } from 'src/entities/orderStatus.entity';
import { ShipperReview } from 'src/entities/shipperReview.entity';
import { WeekShipperTimeKeeping } from 'src/entities/weekShipperTimeKeeping.entity';
import { UpdateDriverJobSettingDto } from 'src/models/driver/dto';
import { DriverJobSettingService } from 'src/models/driver/services/driverJobSetting.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { MoreThanOrEqual } from 'typeorm';
import { JobQueue, VietNamTimeZoneNum } from '..';
import {
    DriverCanReceivePendingOrderDto,
    IDriverAggregateOrder,
    IDriverAggregateRating,
    IDriverOrderDoneJobData,
    IDriverRankHistoryJobData,
    IDriverReviewJobData,
    IEnableDriverCanReceiveJob,
    IWeekTimeKeepingCreateJobData,
} from './driverJob.interface';
import { DriverJobSetting } from 'src/entities/driverJobSetting.entity';
import { DriverJobStatsService } from 'src/models/driver/services/driverJobStats.service';
const { DRIVER_QUEUE } = JobQueue;

@Processor(DRIVER_QUEUE.PROCESSOR)
export class DriverJobProcessor {
    private logger = new Logger(DriverJobProcessor.name);
    constructor(
        private readonly driverJobSettingService: DriverJobSettingService,
        private readonly driverJobStatsService: DriverJobStatsService,
    ) {}

    @Process({
        name: DRIVER_QUEUE.PROCESS.AGGREGATE_ORDER_AFTER_ORDER_DONE,
        concurrency: 5,
    })
    async orderDone(job: Job<IDriverOrderDoneJobData>) {
        const { provinceId, driverId, orderId } = job.data;

        const order = await DatabaseService.getRepositoryByProvinceId(Order, provinceId).findOne({
            where: {
                id: orderId,
                order_status_id: MoreThanOrEqual(EOrderStatusId.ARRIVED),
            },
        });
        if (!order) return;

        if (
            moment().utcOffset(VietNamTimeZoneNum).isoWeek() ==
            moment(order.order_date).utcOffset(VietNamTimeZoneNum).isoWeek()
        ) {
            const builder = DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
                .createQueryBuilder()
                .update()
                .where(`user_id = ${driverId}`);
            if (order.order_status_id == EOrderStatusId.ARRIVED) {
                builder.set({
                    week_total_successful_orders: () => 'week_total_successful_orders + 1',
                    total_orders: () => 'total_orders + 1',
                });
            } else {
                builder.set({
                    week_total_canceled_orders: () => 'week_total_canceled_orders + 1',
                });
            }

            return await builder.execute();
        }
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.AGGREGATE_WEEK_RATING_FOR_NEW_REVIEW,
        concurrency: 1,
    })
    async aggregateWeekRatingForNewReview(job: Job<IDriverReviewJobData>) {
        const { provinceId, reviewId } = job.data;

        const review = await DatabaseService.getRepositoryByProvinceId(ShipperReview, provinceId).findOne({
            where: { id: reviewId },
            relations: ['order'],
        });
        await job.progress(30);

        if (!review || (review && review.order && !moment(review.order.order_date).isSame(moment(), 'isoWeek'))) {
            await job.progress(100);
            return null;
        } else {
            await job.progress(100);
            return await DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
                .createQueryBuilder()
                .update()
                .set({
                    week_rating: () =>
                        `((week_rating * week_total_reviews) + ${review.rate}) / (week_total_reviews + 1)`,
                    week_total_reviews: () => 'week_total_reviews + 1',
                })
                .where('user_id = :driverId', { driverId: review.shipper_id })
                .execute();
        }
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.AGGREGATE_CURRENT_WEEK_RATING,
        concurrency: 1,
    })
    async aggregateWeekRating(job: Job<IDriverAggregateRating>) {
        const { provinceId, driverId, fromDate, toDate } = job.data;
        const { total_reviews, rating } = await this.aggregateRating(driverId, fromDate, toDate, provinceId);
        await job.progress(80);

        await job.log(`[${provinceId}] driverId #${driverId} total_reviews: ${total_reviews} rating: ${rating}`);

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
            .createQueryBuilder()
            .update()
            .set({
                week_rating: rating,
                week_total_reviews: total_reviews,
            })
            .where('user_id = :userId', { userId: driverId })
            .execute();
        await job.progress(100);
        return updateResult;
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.AGGREGATE_PREV_WEEK_RATING,
        concurrency: 1,
    })
    async aggregatePrevWeekRating(job: Job<IDriverAggregateRating>) {
        const { provinceId, driverId, fromDate, toDate } = job.data;
        const { total_reviews, rating } = await this.aggregateRating(driverId, fromDate, toDate, provinceId);
        await job.progress(80);

        await job.log(`[${provinceId}] driverId #${driverId} total_reviews: ${total_reviews} rating: ${rating}`);

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
            .createQueryBuilder()
            .update()
            .set({
                rating: rating,
            })
            .where('user_id = :userId', { userId: driverId })
            .execute();
        await job.progress(100);
        return updateResult;
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.AGGREGATE_WEEK_ORDERS,
        concurrency: 1,
    })
    async aggregateWeekOrders(job: Job<IDriverAggregateOrder>) {
        const { provinceId, driverId, fromDate, toDate } = job.data;
        const { total_canceled_orders, total_successful_orders, total_orders } = await this.aggregateOrders(
            driverId,
            fromDate,
            toDate,
            provinceId,
        );
        await job.progress(80);

        await job.log(
            `[${provinceId}] driverId #${driverId} total_orders: ${total_orders} total_successful_orders: ${total_successful_orders} total_canceled_orders: ${total_canceled_orders}`,
        );

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
            .createQueryBuilder()
            .update()
            .set({
                week_total_canceled_orders: total_canceled_orders,
                week_total_successful_orders: total_successful_orders,
            })
            .where('user_id = :userId', { userId: driverId })
            .execute();
        await job.progress(100);
        return updateResult;
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.ADD_RANK_HISTORIES,
        concurrency: 1,
    })
    async driverRankHistories(job: Job<IDriverRankHistoryJobData>) {
        try {
            const { provinceId, driverId, month, year, fromDate, toDate, userId, rating, rankList } = job.data;

            const { total_successful_orders, total_canceled_orders } = await this.countWeekTotalOrdersOfDriver(
                userId,
                provinceId,
            );

            const rank = calcRankByConditions(
                {
                    rating: rating,
                    totalSuccessOrders: +total_successful_orders,
                },
                rankList,
            );
            const totalReviews = await this.countWeekTotalReviewOfDriver(driverId, provinceId);
            let rankHistory = new DriverRankHistory({
                driver_id: driverId,
                month,
                year,
                from_date: fromDate,
                to_date: toDate,
                rating,
                total_canceled_orders,
                total_successful_orders,
                total_reviews: totalReviews,
            });
            rankHistory.setRank(rank);

            rankHistory = await DatabaseService.getRepositoryByProvinceId(DriverRankHistory, provinceId).save(
                rankHistory,
            );

            return await DatabaseService.getRepositoryByProvinceId(Driver, provinceId)
                .createQueryBuilder()
                .update()
                .set({
                    week_rating: 5,
                    week_total_reviews: 0,
                    week_total_canceled_orders: 0,
                    week_total_successful_orders: 0,
                    ranking: rank,
                    rating,
                })
                .where('id = :driverId', { driverId })
                .execute();
        } catch (error) {
            this.logger.error(`[driverRankHistories] | message: ${error.message} | stack: ${error.stack}`);
            await job.moveToFailed(error);
        }
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.CREATE_WEEK_TIME_KEEPING,
        concurrency: 1,
    })
    async createNewTimeKeepingRecords(job: Job<IWeekTimeKeepingCreateJobData>) {
        try {
            const { provinceId, driverIds, isoWeek, isoWeekYear, fromDate, toDate } = job.data;

            const timekeepings = driverIds.map(
                (id) =>
                    new WeekShipperTimeKeeping({
                        driver_id: id,
                        week: isoWeek,
                        year: isoWeekYear,
                        from_date: fromDate,
                        to_date: toDate,
                        actual_received_bonus_percentage: 0,
                    }),
            );
            return await DatabaseService.getRepositoryByProvinceId(WeekShipperTimeKeeping, provinceId).save(
                timekeepings,
            );
        } catch (error) {
            this.logger.error(`[createNewTimeKeepingRecords] | message: ${error.message} | stack: ${error.stack}`);
            await job.moveToFailed(error);
        }
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.ENABLE_CAN_RECEIVE_JOB,
        concurrency: 1,
    })
    async enableDriverCanReceiveJob(job: Job<IEnableDriverCanReceiveJob>) {
        try {
            const { provinceId, driverId } = job.data;
            await DatabaseService.getRepositoryByProvinceId(DriverJobSetting, provinceId)
                .createQueryBuilder('jobSetting')
                .update()
                .set({
                    can_receiving_job: TinyInt.YES,
                    waiting_time: null,
                    message: null,
                })
                .where('driver_id = :driverId', { driverId })
                .execute();
            const req = createRequestByProvince(provinceId);
            await this.driverJobStatsService.updateByDriverId(req, driverId, { priority_mode_rejected_jobs_count: 0 });
            this.logger.log(
                `[enableDriverCanReceiveJob] Enable driver can receive job | provinceId: ${provinceId}, driverId: ${driverId}`,
            );

            await this.driverJobSettingService.updateCacheByDriverId(provinceId, driverId);
            return await DatabaseService.getRepositoryByProvinceId(DriverJobSetting, provinceId).findOne({
                where: { driver_id: driverId },
            });
        } catch (error) {
            this.logger.error(`[enableDriverCanReceiveJob] | message: ${error.message} | stack: ${error.stack}`);
            await job.moveToFailed(error);
        }
    }

    @Process({
        name: DRIVER_QUEUE.PROCESS.ENABLE_CAN_RECEIVE_PENDING_ORDER,
        concurrency: 1,
    })
    async execCronjobToEnableDriverCanReceivePendingOrder(job: Job<DriverCanReceivePendingOrderDto>) {
        try {
            const { provinceId, driverId } = job.data;
            await DatabaseService.getRepositoryByProvinceId(DriverJobSetting, provinceId)
                .createQueryBuilder('jobSetting')
                .update()
                .set({
                    in_discipline: TinyInt.NO,
                    waiting_time_in_discipline: null,
                    message: null,
                })
                .where('driver_id = :driverId', { driverId })
                .execute();

            this.logger.log(
                `[execCronjobToEnableDriverCanReceivePendingOrder] Enable driver can receive job | provinceId: ${provinceId}, driverId: ${driverId} }`,
            );
            await this.driverJobSettingService.updateCacheByDriverId(provinceId, driverId);
            return await DatabaseService.getRepositoryByProvinceId(DriverJobSetting, provinceId).findOne({
                where: { driver_id: driverId },
            });
        } catch (error) {
            this.logger.error(
                `[execCronjobToEnableDriverCanReceivePendingOrder] | message: ${error.message} | stack: ${error.stack}`,
            );
            await job.moveToFailed(error);
        }
    }

    async aggregateRating(driverId: number, fromDate: string, toDate: string, provinceId: string) {
        return DatabaseService.getConnectionByProvinceId(provinceId)
            .createQueryBuilder(ShipperReview, 'shipperReviews')
            .select('COUNT(*)', 'total_reviews')
            .addSelect('AVG(shipperReviews.rate)', 'rating')
            .where('shipperReviews.shipper_id = :shipperId', { shipperId: driverId })
            .andWhere((qb) => {
                const subQuery = qb
                    .subQuery()
                    .select('order.id')
                    .from(Order, 'order')
                    .where(`order.order_date >= '${fromDate}'`)
                    .andWhere(`order.order_date <= '${toDate}'`)
                    .andWhere(`order.order_status_id = ${EOrderStatusId.ARRIVED}`)
                    .andWhere(`order.driver_id = ${driverId}`)
                    .getQuery();
                return 'shipperReviews.order_id IN ( ' + subQuery + ' )';
            })
            .execute()
            .then(([{ total_reviews, rating }]) => {
                return {
                    total_reviews: +total_reviews || 0,
                    rating: +rating || 5,
                };
            });
    }

    async aggregateOrders(driverId: number, fromDate: string, toDate: string, provinceId: string) {
        const [{ total_successful_orders, total_canceled_orders }] = await DatabaseService.getRepositoryByProvinceId(
            Driver,
            provinceId,
        )
            .createQueryBuilder('driver')
            .select((qb) => {
                qb.from(Order, 'order')
                    .select('COUNT(*)', 'total_successful_orders')
                    .where(`order.order_date BETWEEN '${fromDate}' AND '${toDate}'`)
                    .andWhere(`order.order_status_id = ${EOrderStatusId.ARRIVED}`)
                    .andWhere(`order.driver_id = ${driverId}`);
                return qb;
            }, 'total_successful_orders')
            .select((qb) => {
                qb.from(Order, 'order')
                    .select('COUNT(*)', 'total_canceled_orders')
                    .where(`order.order_date BETWEEN '${fromDate}' AND '${toDate}'`)
                    .andWhere(`order.order_status_id = ${EOrderStatusId.CANCELED}`)
                    .andWhere(`order.driver_id = ${driverId}`);
                return qb;
            }, 'total_canceled_orders')
            .where('driver.user_id = :driverId', { driverId })
            .execute();

        const result = {
            total_successful_orders: total_successful_orders == null ? 0 : +total_successful_orders,
            total_canceled_orders: total_canceled_orders == null ? 0 : +total_canceled_orders,
        };

        return {
            ...result,
            total_orders: result.total_canceled_orders + result.total_successful_orders,
        };
    }

    @OnQueueCompleted()
    handleQueueCompleted(job: Job, result: any) {
        this.logger.log(
            `[ SUCCESS ] JobId: ${job.id} | job name: ${job.name} | result: ${
                result && isObject(result) ? JSON.stringify(result) : null
            }`,
        );
    }

    @OnQueueFailed()
    handleQueueFailed(job: Job, err: Error) {
        this.logger.log(`[ FAILED ] JobId: ${job.id} | job name: ${job.name} | err: ${err ? err.message : null}`);
    }

    async countWeekTotalOrdersOfDriver(
        driverId: number,
        provinceId: string,
    ): Promise<{ total_successful_orders: number; total_canceled_orders: number }> {
        const lastWeek = moment().subtract(1, 'w').add(7, 'hour').format('YYYY-MM-DD');
        const curr = moment().add(7, 'hour').format('YYYY-MM-DD');
        const data = await DatabaseService.getRepositoryByProvinceId(Order, provinceId)
            .createQueryBuilder()
            .select('COUNT(IF(order_status_id = 5, 1, null))', 'total_successful_orders')
            .addSelect('COUNT(IF(order_status_id = 6, 1, null))', 'total_canceled_orders')
            .where({ driver_id: driverId })
            .andWhere(`order_date BETWEEN '${lastWeek}' AND '${curr}'`)
            .execute();
        return (
            data[0] || {
                total_successful_orders: 0,
                total_canceled_orders: 0,
            }
        );
    }

    async countWeekTotalReviewOfDriver(driverId: number, provinceId: string) {
        const lastWeek = moment().subtract(1, 'w').add(7, 'hour').format('YYYY-MM-DD');
        const curr = moment().add(7, 'hour').format('YYYY-MM-DD');
        const data = await DatabaseService.getRepositoryByProvinceId(Order, provinceId)
            .createQueryBuilder('order')
            .where({ driver_id: driverId })
            .innerJoin('order.shipperReview', 'shipperReview')
            .andWhere(`order.order_date BETWEEN '${lastWeek}' AND '${curr}'`)
            .getCount();
        return data;
    }
}
