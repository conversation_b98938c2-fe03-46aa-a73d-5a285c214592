import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { Job, Queue } from 'bull';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { ShipperTimeKeepingTask } from 'src/models/shipperTimeKeeping/shipperTimeKeeping.task';
import { JobQueue, VietNamTimeZone } from '..';
import { DriverJobTask } from './driverJob.task';
import { DriverDisciplinaryActionService } from 'src/models/driverDiscipline/services/driverDisciplinaryAction.service';
const { CRON_JOB } = JobQueue;

@Injectable()
export class DriverJobCron {
    private logger = new Logger(DriverJobCron.name);
    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR) private cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async analysisOrdersForShipperDaily() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_SHIPPER_TIME_KEEPING);
            this.logger.log(`[analysisOrdersForShipperDaily] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_SHIPPER_TIME_KEEPING) {
                        this.logger.log(`[analysisOrdersForShipperDaily] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_SHIPPER_TIME_KEEPING,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_7AM,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_SHIPPER_TIME_KEEPING,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[analysisOrdersForShipperDaily] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async addRankHistories() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_ADD_RANK_HISTORIES);
            this.logger.log(`[addRankHistories] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_ADD_RANK_HISTORIES) {
                        this.logger.log(`[addRankHistories] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_ADD_RANK_HISTORIES,
                {},
                {
                    jobId,
                    repeat: {
                        cron: '0 0 * * 1',
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_ADD_RANK_HISTORIES,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[addRankHistories] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async createWeekTimeKeepingRecords() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_CREATE_WEEK_TIME_KEEPING_RECORDS);
            this.logger.log(`[createWeekTimeKeepingRecords] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CREATE_WEEK_TIME_KEEPING_RECORDS) {
                        this.logger.log(`[createWeekTimeKeepingRecords] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CREATE_WEEK_TIME_KEEPING_RECORDS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: '0 0 * * 1',
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_CREATE_WEEK_TIME_KEEPING_RECORDS,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[createWeekTimeKeepingRecords] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    // vi: Tính lương thưởng hàng tuần net theo số ngày làm việc trong tuần.
    // example: 5 ngày làm việc -> 100%, 4 ngày làm việc -> 80%, 3 ngày làm việc -> 60%, 2 ngày làm việc -> 40%, 1 ngày làm việc -> 20%, 0 ngày làm việc
    @Timeout(5000)
    async initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS);
            this.logger.log(`[initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS) {
                        this.logger.log(
                            `[initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob] Remove job ${job.name}`,
                        );
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: '0 0 * * 2',
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob] message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class DriverJobCronProcessor {
    private logger = new Logger(DriverJobCronProcessor.name);
    constructor(
        private readonly shipperTimeKeepingTask: ShipperTimeKeepingTask,
        private readonly driverJobTask: DriverJobTask,
        private readonly driverDisciplinaryActionService: DriverDisciplinaryActionService,
    ) {}

    @Process({
        name: CRON_JOB.PROCESS.CRON_SHIPPER_TIME_KEEPING,
        concurrency: 1,
    })
    async analysisOrdersForShipperDaily(job: Job) {
        try {
            await this.shipperTimeKeepingTask.analysisOrdersForShipperDaily();
        } catch (error) {
            this.logger.error(`[analysisOrdersForShipperDaily] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_ADD_RANK_HISTORIES,
        concurrency: 1,
    })
    async addRankHistories(job: Job) {
        try {
            await this.driverJobTask.addRankHistories();
        } catch (error) {
            this.logger.error(`[addRankHistories] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_CREATE_WEEK_TIME_KEEPING_RECORDS,
        concurrency: 1,
    })
    async createWeekTimeKeepingRecords(job: Job) {
        try {
            await this.driverJobTask.createWeekTimeKeepingRecords();
        } catch (error) {
            this.logger.error(`[createWeekTimeKeepingRecords] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS,
        concurrency: 1,
    })
    async initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob(job: Job) {
        try {
            await this.driverDisciplinaryActionService.calculateIfDriversWorkedEnoughDaysInWeek();
        } catch (error) {
            this.logger.error(
                `[initCalculateNetWeeklyBonusByWorkDaysOfWeekRepeatJob] message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}
