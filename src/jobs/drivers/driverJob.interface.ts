import { ShipperRanking } from 'src/entities/shipperRankings.entity';

export interface IDriverOrderDoneJobData {
    provinceId: string;
    driverId: number;
    orderId: number;
    rankId: number;
}

export interface IDriverRankHistoryJobData {
    provinceId: string;
    driverId: number;
    userId: number;
    month: number;
    year: number;
    fromDate: string;
    toDate: string;
    rankList: ShipperRanking[];
    // rank: ShipperRanking;
    rating: number;
    // totalSuccessfullOrders: number;
    // totalCanceledOrders: number;
    // totalReviews: number;
    weekYear: number;
}

export interface IWeekTimeKeepingCreateJobData {
    provinceId: string;
    driverIds: number[];
    isoWeek: number;
    isoWeekYear: number;
    fromDate: string;
    toDate: string;
}

export interface IDriverReviewJobData {
    provinceId: string;
    reviewId: number;
}

export interface IDriverAggregateOrder {
    provinceId: string;
    driverId: number;
    fromDate: string;
    toDate: string;
}

export interface IDriverAggregateRating {
    provinceId: string;
    driverId: number;
    fromDate: string;
    toDate: string;
}

export interface IEnableDriverCanReceiveJob {
    provinceId: string;
    driverId: number;
}

export class DriverCanReceivePendingOrderDto {
    driverId: number;
    provinceId: string;
}
