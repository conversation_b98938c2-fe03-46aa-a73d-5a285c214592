import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { DriverService } from 'src/models/driver/services/driver.service';
import { DriverJobSettingService } from 'src/models/driver/services/driverJobSetting.service';
import { JobQueue } from '..';
import { DriverJobController } from './driverJob.controller';
import { DriverJobProcessor } from './driverJob.processor';
import { DriverJobTask } from './driverJob.task';
import { RabbitMQEventModule } from 'src/rabbitMQ/rabbitMQ.module';
import { DriverDisciplineModule } from 'src/models/driverDiscipline/driverDiscipline.module';
import { DriverModule } from 'src/models/driver/driver.module';
import { UserActivityModule } from 'src/models/adminUserActivity/userActivity.module';
import { ShipperTimeKeepingModule } from 'src/models/shipperTimeKeeping/shipperTimeKeeping.module';
import { Driver<PERSON>ob<PERSON>ron, DriverJobCronProcessor } from './driverJob.cron';

@Module({
    controllers: [DriverJobController],
    imports: [
        BullModule.registerQueue({
            name: JobQueue.DRIVER_QUEUE.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 10000,
            },
        }),
        RabbitMQEventModule,
        DriverModule,
        UserActivityModule,
        ShipperTimeKeepingModule,
        DriverDisciplineModule,
    ],
    providers: [
        DriverJobProcessor,
        DriverJobTask,
        DriverJobSettingService,
        DriverService,
        DriverJobCron,
        DriverJobCronProcessor,
    ],
})
export class DriverJobModule {}
