import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, Timeout } from '@nestjs/schedule';
import { Queue } from 'bull';
import * as moment from 'moment';
import { Driver } from 'src/entities/driver.entity';
import { ShipperRanking } from 'src/entities/shipperRankings.entity';
import { DatabaseService } from 'src/providers/database/database.service';

import { ECronjobs, JobQueue, VietNamTimeZone, VietNamTimeZoneNum } from '..';
import {
    IDriverAggregateRating,
    IDriverRankHistoryJobData,
    IWeekTimeKeepingCreateJobData,
} from './driverJob.interface';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';

const { DRIVER_QUEUE } = JobQueue;
const { PROCESS } = DRIVER_QUEUE;

@Injectable()
export class DriverJobTask {
    private logger = new Logger(DriverJobTask.name);
    constructor(@InjectQueue(DRIVER_QUEUE.PROCESSOR) private driverQueue: Queue) {}

    /* Cron('0 0 * * 1', {
        name: ECronjobs.AddShipperRankHistories,
        timeZone: VietNamTimeZone,
    }) */
    async addRankHistories() {
        const driverRepositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Driver);

        const lastWeek = moment().utcOffset(VietNamTimeZoneNum).subtract(1, 'w');
        const month = lastWeek.month();
        const year = lastWeek.year();
        const isoWeekYear = lastWeek.isoWeekYear();
        const fromDate = lastWeek.startOf('isoWeek').format('YYYY-MM-DD');
        const toDate = lastWeek.endOf('isoWeek').format('YYYY-MM-DD');

        for (let index = 0; index < driverRepositories.length; index++) {
            const { repository: repo, name: provinceId } = driverRepositories[index];

            const rankList = await DatabaseService.getRepositoryByProvinceId(ShipperRanking, provinceId).find({
                where: {
                    is_active: true,
                },
            });

            const limit = 100;
            let count = 0;
            const total = await repo
                .createQueryBuilder('driver')
                .where('driver.is_active = 1')
                .andWhere('driver.user_id IS NOT NULL')
                .getCount();

            while (count < total) {
                const drivers = await repo
                    .createQueryBuilder('driver')
                    .where('driver.is_active = 1')
                    .andWhere('driver.user_id IS NOT NULL')
                    .offset(count)
                    .limit(limit)
                    .getMany();

                const bulkJobData = await Promise.all(
                    drivers.map(async ({ id, week_rating, user_id }) => {
                        const data: IDriverRankHistoryJobData = {
                            provinceId,
                            driverId: id,
                            userId: user_id,
                            month,
                            year,
                            fromDate,
                            toDate,
                            rating: week_rating,
                            rankList,
                            weekYear: isoWeekYear,
                        };
                        return {
                            name: PROCESS.ADD_RANK_HISTORIES,
                            data,
                        };
                    }),
                )

                this.driverQueue.addBulk(bulkJobData);

                count += drivers.length;
            }
        }
    }

    /* Cron('0 0 * * 1', {
        name: ECronjobs.CreateNewWeekShipperTimeKeeping,
        timeZone: VietNamTimeZone,
    }) */
    async createWeekTimeKeepingRecords() {
        const driverRepositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Driver);

        const currentTime = moment().utcOffset(VietNamTimeZoneNum);
        const isoWeek = currentTime.isoWeek();
        const isoWeekYear = currentTime.isoWeekYear();
        const fromDate = currentTime.startOf('isoWeek').format('YYYY-MM-DD');
        const toDate = currentTime.endOf('isoWeek').format('YYYY-MM-DD');

        for (let index = 0; index < driverRepositories.length; index++) {
            const { repository: repo, name: provinceId } = driverRepositories[index];
            // const repo = driverRepositories[index];
            // const provinceId = repo.manager.connection.name;

            const limit = 100;
            let count = 0;
            const total = await repo
                .createQueryBuilder('driver')
                .where('driver.is_active = 1')
                .andWhere('driver.user_id IS NOT NULL')
                .getCount();

            while (count < total) {
                const drivers = await repo
                    .createQueryBuilder('driver')
                    .where('driver.is_active = 1')
                    .andWhere('driver.user_id IS NOT NULL')
                    .offset(count)
                    .limit(limit)
                    .getMany();

                const driverIds = drivers.map(({ id }) => id);
                await this.driverQueue.add(PROCESS.CREATE_WEEK_TIME_KEEPING, {
                    provinceId,
                    driverIds,
                    isoWeekYear,
                    isoWeek,
                    fromDate,
                    toDate,
                } as IWeekTimeKeepingCreateJobData);
                count += drivers.length;
            }
        }
    }

    // @Timeout(5000)
    async aggregateWeekRating() {
        const driverRepositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Driver);

        const currentTime = moment().utcOffset(VietNamTimeZoneNum);
        const fromDate = currentTime.clone().startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');
        const toDate = currentTime.clone().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');

        for (let index = 0; index < driverRepositories.length; index++) {
            const { repository: repo, name: provinceId } = driverRepositories[index];
            // const repo = driverRepositories[index];
            // const provinceId = repo.manager.connection.name;

            const limit = 100;
            let count = 0;
            const total = await repo
                .createQueryBuilder('driver')
                .where('driver.is_active = 1')
                .andWhere('driver.user_id IS NOT NULL')
                .getCount();

            while (count < total) {
                const drivers = await repo
                    .createQueryBuilder('driver')
                    .where('driver.is_active = 1')
                    .andWhere('driver.user_id IS NOT NULL')
                    .offset(count)
                    .limit(limit)
                    .getMany();

                drivers.forEach(({ user_id }) => {
                    this.driverQueue.add(PROCESS.AGGREGATE_CURRENT_WEEK_RATING, {
                        provinceId,
                        driverId: user_id,
                        fromDate,
                        toDate,
                    });
                });

                count += drivers.length;
            }
        }
    }

    // @Timeout(5000)
    async aggregateWeekOrders() {
        const driverRepositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Driver);

        const currentTime = moment().utcOffset(VietNamTimeZoneNum);
        const fromDate = currentTime.clone().startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');
        const toDate = currentTime.clone().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');

        for (let index = 0; index < driverRepositories.length; index++) {
            const { repository: repo, name: provinceId } = driverRepositories[index];
            // const repo = driverRepositories[index];
            // const provinceId = repo.manager.connection.name;

            const limit = 100;
            let count = 0;
            const total = await repo
                .createQueryBuilder('driver')
                .where('driver.is_active = 1')
                .andWhere('driver.user_id IS NOT NULL')
                .getCount();

            while (count < total) {
                const drivers = await repo
                    .createQueryBuilder('driver')
                    .where('driver.is_active = 1')
                    .andWhere('driver.user_id IS NOT NULL')
                    .offset(count)
                    .limit(limit)
                    .getMany();

                drivers.forEach(({ user_id }) => {
                    this.driverQueue.add(PROCESS.AGGREGATE_WEEK_ORDERS, {
                        provinceId,
                        driverId: user_id,
                        fromDate,
                        toDate,
                    });
                });

                count += drivers.length;
            }
        }
    }

    // @Timeout(5000)
    async aggregatePrevWeekRating() {
        const driverRepositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Driver);

        const currentTime = moment().utcOffset(VietNamTimeZoneNum).subtract(1, 'week');
        const fromDate = currentTime.clone().startOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');
        const toDate = currentTime.clone().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss');

        for (let index = 0; index < driverRepositories.length; index++) {
            const { repository: repo, name: provinceId } = driverRepositories[index];
            // const repo = driverRepositories[index];
            // const provinceId = repo.manager.connection.name;

            const limit = 100;
            let count = 0;
            const total = await repo
                .createQueryBuilder('driver')
                .where('driver.is_active = 1')
                .andWhere('driver.user_id IS NOT NULL')
                .getCount();

            while (count < total) {
                const drivers = await repo
                    .createQueryBuilder('driver')
                    .where('driver.is_active = 1')
                    .andWhere('driver.user_id IS NOT NULL')
                    .offset(count)
                    .limit(limit)
                    .getMany();

                drivers.forEach(({ user_id }) => {
                    this.driverQueue.add(PROCESS.AGGREGATE_PREV_WEEK_RATING, {
                        provinceId,
                        driverId: user_id,
                        fromDate,
                        toDate,
                    } as IDriverAggregateRating);
                });

                count += drivers.length;
            }
        }
    }
}
