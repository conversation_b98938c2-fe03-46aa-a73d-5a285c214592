import { InjectQueue } from '@nestjs/bull';
import { Body, Controller, Post, Get, UseGuards } from '@nestjs/common';
import { Queue } from 'bull';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { JobQueue } from '..';
import { IDriverOrderDoneJobData, IDriverReviewJobData } from './driverJob.interface';
import { DriverOrderDoneDto } from './dto';
import * as jobHelpers from 'src/common/helpers/job.helpter';
import { DriverJobTask } from './driverJob.task';
import { DriverJobProcessor } from './driverJob.processor';
const { DRIVER_QUEUE } = JobQueue;
const { PROCESS } = DRIVER_QUEUE;
import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('jobs/drivers')
@UseGuards(AuthGuard)
export class DriverJobController {
    constructor(
        @InjectQueue(DRIVER_QUEUE.PROCESSOR) private driverQueue: Queue,
        private driverJobTask: DriverJobTask,
        private driverJobProcessor: DriverJobProcessor,
    ) {}

    @Post('order-done')
    async orderDone(@Body() { driverId, orderId, provinceId }: DriverOrderDoneDto) {
        console.log('order done');
        const jobId = jobHelpers.generateJobIdByProcessName(
            PROCESS.AGGREGATE_ORDER_AFTER_ORDER_DONE,
            provinceId,
            driverId,
            orderId,
        );
        await this.driverQueue.add(
            PROCESS.AGGREGATE_ORDER_AFTER_ORDER_DONE,
            {
                provinceId,
                driverId,
                orderId,
            } as IDriverOrderDoneJobData,
            {
                jobId,
                removeOnComplete: 100,
                attempts: 2,
            },
        );
        return;
    }

    @Post('order-reviews')
    async aggregateReview(@Body() { reviewId, provinceId }: IDriverReviewJobData) {
        const jobId = jobHelpers.generateJobIdByProcessName(
            PROCESS.AGGREGATE_WEEK_RATING_FOR_NEW_REVIEW,
            provinceId,
            reviewId,
        );
        await this.driverQueue.add(
            PROCESS.AGGREGATE_WEEK_RATING_FOR_NEW_REVIEW,
            {
                provinceId,
                reviewId,
            } as IDriverReviewJobData,
            {
                jobId,
                removeOnComplete: 100,
                attempts: 2,
            },
        );
        return;
    }

    @Get('aggregateWeekOrders')
    async aggregateWeekOrders() {
        return this.driverJobTask.aggregateWeekOrders();
    }

    @Get('aggregateWeekRating')
    async aggregateWeekRating() {
        return this.driverJobTask.aggregateWeekRating();
    }

    @Get('aggregatePrevWeekRating')
    async aggregatePrevWeekRating() {
        return this.driverJobTask.aggregatePrevWeekRating();
    }

    @Get('test')
    test() {
        return this.driverJobProcessor.countWeekTotalOrdersOfDriver(29055, '1');
    }
}
