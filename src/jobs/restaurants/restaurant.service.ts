import { Restaurant } from './../../entities/restaurant.entity';
import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
@Injectable()
export class RestaurantService {
    async getById(restaurantId: number, provinceId: string) {
        return await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurantId },
        });
    }
}
