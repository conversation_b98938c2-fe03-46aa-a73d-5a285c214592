import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import * as moment from 'moment';

import { Restaurant } from 'src/entities/restaurant.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { JobQueue } from '..';
import {
    IreIndexRestaurantToEls,
    IRestaurantReopen,
    ISummaryRestaurantTotalOrders,
    IUpdateRestaurantAdsIsActive,
} from './restaurantJob.interface';
import * as jobHelpers from 'src/common/helpers/job.helpter';
import { RestaurantService } from './restaurant.service';
import { ProvinceService } from 'src/models/province/province.service';
import { RestaurantSearchService } from 'src/models/restaurant/services/restaurantSearch.service';
const { RESTAURANT_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_QUEUE;

@Injectable()
export class RestaurantJobService {
    private logger = new Logger(RestaurantJobService.name);
    constructor(
        @InjectQueue(RESTAURANT_QUEUE.PROCESSOR) private restaurantQueue: Queue,
        private restaurantService: RestaurantService,
        private provinceService: ProvinceService,
        private restaurantSearchService: RestaurantSearchService,
    ) {}

    async addJobToReopenRestaurant(restaurantId: number, provinceId: string) {
        try {
            const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
                where: { id: restaurantId },
            });
            if (!restaurant) return;
            const { reopen_time, id } = restaurant;
            const jobId = jobHelpers.generateJobIdByProcessName(PROCESS.REOPEN_TIME, provinceId, id);
            const currJob = await this.restaurantQueue.getJob(jobId);
            if (currJob) await currJob.remove();
            if (reopen_time) {
                const momentDate = moment(reopen_time);
                await this.restaurantQueue.add(
                    PROCESS.REOPEN_TIME,
                    {
                        provinceId,
                        restaurantId,
                    } as IRestaurantReopen,
                    {
                        jobId,
                        delay: momentDate.valueOf() - moment().valueOf(),
                        attempts: 2,
                    },
                );
            }
            return;
        } catch (error) {
            this.logger.error(`[addJobToReopenRestaurant] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    async addJobToSummaryRestaurantTotalOrders(restaurantId: number, provinceId: string) {
        try {
            const jobId = jobHelpers.generateJobIdByProcessName(
                PROCESS.SUMMARY_RESTAURANT_TOTAL_ORDERS,
                provinceId,
                restaurantId,
            );
            const currJob = await this.restaurantQueue.getJob(jobId);
            if (currJob) return currJob;
            return await this.restaurantQueue.add(
                PROCESS.SUMMARY_RESTAURANT_TOTAL_ORDERS,
                {
                    provinceId,
                    restaurantId,
                } as ISummaryRestaurantTotalOrders,
                {
                    jobId,
                    attempts: 2,
                },
            );
        } catch (error) {
            this.logger.error(
                `[addJobToSummaryRestaurantTotalOrders] | message: ${error.message} | stack: ${error.stack}`,
            );
            return;
        }
    }

    async reIndexRestaurantToEls(restaurantId: number, provinceId: string) {
        try {
            const jobId = jobHelpers.generateJobIdByProcessName(
                PROCESS.RE_INDEX_RESTAURANT_TO_ELS,
                provinceId,
                restaurantId,
            );
            const jobExist = await this.restaurantQueue.getJob(jobId);
            if (jobExist) {
                const jobStatus = await jobExist.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await jobExist.remove();
                } else {
                    return jobExist;
                }
            }
            const job = await this.restaurantQueue.add(
                PROCESS.RE_INDEX_RESTAURANT_TO_ELS,
                {
                    restaurantId,
                    provinceId,
                } as IreIndexRestaurantToEls,
                {
                    jobId,
                },
            );
            this.logger.log(`Job added ${JSON.stringify(job)}`);
            return job;
        } catch (error) {
            this.logger.error(`[reIndexRestaurantToEls] | message: ${error.message} | stack: ${error.stack}`);
            return null;
        }
    }

    async reIndexRestaurantToElsWithDelay(restaurantId: number, provinceId: string, delayMs: number = 1000) {
        try {
            const jobId =
                jobHelpers.generateJobIdByProcessName(PROCESS.RE_INDEX_RESTAURANT_TO_ELS, provinceId, restaurantId) +
                '_delayed';

            const jobExist = await this.restaurantQueue.getJob(jobId);
            if (jobExist) {
                const jobStatus = await jobExist.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await jobExist.remove();
                } else {
                    return jobExist;
                }
            }

            const job = await this.restaurantQueue.add(
                PROCESS.RE_INDEX_RESTAURANT_TO_ELS,
                {
                    restaurantId,
                    provinceId,
                } as IreIndexRestaurantToEls,
                {
                    jobId,
                    delay: delayMs, // Add delay to prevent race condition
                },
            );

            this.logger.log(`Delayed reindex job added with ${delayMs}ms delay: ${JSON.stringify(job)}`);
            return job;
        } catch (error) {
            this.logger.error(`[reIndexRestaurantToElsWithDelay] | message: ${error.message} | stack: ${error.stack}`);
            return null;
        }
    }

    async reIndexRestaurantsToEls(restaurantIds: number[], provinceId: string) {
        for (let i = 0; i < restaurantIds.length; i++) {
            const element = restaurantIds[i];
            await this.reIndexRestaurantToEls(element, provinceId);
        }
    }

    async removeReopenTimeJob(restaurantId: number, provinceId: string) {
        try {
            const jobId = jobHelpers.generateJobIdByProcessName(PROCESS.REOPEN_TIME, provinceId, restaurantId);
            await this.restaurantQueue.removeJobs(jobId);
        } catch (error) {
            this.logger.error(`[removeReopenTimeJob] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    async updateRestaurantPromotionsToEls(restaurantId: number, provinceId: string) {
        try {
            const province = await this.provinceService.getProvinceById(provinceId);
            if (!province) {
                this.logger.error(`updateRestaurantPromotionsToEls Province not found ${provinceId}`);
                return;
            }
            provinceId = (province.parent_id || province.id).toString();
            const restaurant = await this.restaurantService.getById(restaurantId, provinceId.toString());
            if (!restaurant) {
                this.logger.error(
                    `updateRestaurantPromotionsToEls Restaurant not found ${restaurantId} in province ${provinceId}`,
                );
                return;
            }
            const subProvinceId = restaurant.province_id.toString();
            const jobId = jobHelpers.generateJobIdByProcessName(
                PROCESS.UPDATE_RESTAURANT_PROMOTIONS_TO_ELS,
                provinceId,
                restaurantId,
            );
            const jobExist = await this.restaurantQueue.getJob(jobId);
            if (jobExist) {
                const jobStatus = await jobExist.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await jobExist.remove();
                } else {
                    return jobExist;
                }
            }
            const job = await this.restaurantQueue.add(
                PROCESS.UPDATE_RESTAURANT_PROMOTIONS_TO_ELS,
                {
                    restaurantId,
                    subProvinceId,
                    provinceId,
                } as IreIndexRestaurantToEls,
                {
                    jobId,
                },
            );
            this.logger.log(`Job added ${JSON.stringify(job)}`);
            return job;
        } catch (error) {
            this.logger.error(`[reIndexRestaurantToEls] | message: ${error.message} | stack: ${error.stack}`);
            return null;
        }
    }

    async updateRestaurantAdsIsActive(
        provinceId: string,
        restaurantId: number,
        isActive: boolean,
        itemId: number,
        scheduledTime: Date,
    ) {
        try {
            const now = moment();
            const scheduledMoment = moment(scheduledTime);
            const delay = Math.max(0, scheduledMoment.valueOf() - now.valueOf());

            // Generate unique job ID that includes itemId to avoid conflicts
            const jobId = `ads_${isActive ? 'activate' : 'deactivate'}_${provinceId}_${restaurantId}_${itemId}`;

            this.logger.log(
                `[updateRestaurantAdsIsActive] Scheduling ${
                    isActive ? 'activation' : 'deactivation'
                } for item ${itemId} with delay ${delay}ms`,
            );

            // Remove existing job atomically
            await this.removeJobAtomically(jobId);

            const job = await this.restaurantQueue.add(
                PROCESS.UPDATE_RESTAURANT_ADS_IS_ACTIVE,
                {
                    restaurantId,
                    provinceId,
                    isActive,
                    itemId,
                } as IUpdateRestaurantAdsIsActive,
                {
                    jobId,
                    delay,
                    attempts: 3, // Increased retry attempts
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                    removeOnComplete: 10, // Keep last 10 completed jobs for debugging
                    removeOnFail: 5, // Keep last 5 failed jobs for debugging
                },
            );

            this.logger.log(
                `[updateRestaurantAdsIsActive] Successfully scheduled job ${jobId} for ${new Date(scheduledTime).toISOString()}`,
            );
            return job;
        } catch (error) {
            this.logger.error(`[updateRestaurantAdsIsActive] | message: ${error.message} | stack: ${error.stack}`);
            return null;
        }
    }

    /**
     * Remove job atomically to avoid race conditions
     * @param jobId - Job ID to remove
     */
    private async removeJobAtomically(jobId: string): Promise<void> {
        try {
            const job = await this.restaurantQueue.getJob(jobId);
            if (job) {
                const state = await job.getState();
                this.logger.log(`[removeJobAtomically] Found existing job ${jobId} with state: ${state}`);

                // Only remove jobs that are not currently being processed
                if (['waiting', 'delayed', 'paused'].includes(state)) {
                    await job.remove();
                    this.logger.log(`[removeJobAtomically] Successfully removed job ${jobId}`);
                } else if (['active'].includes(state)) {
                    this.logger.warn(`[removeJobAtomically] Job ${jobId} is currently active, cannot remove`);
                } else {
                    // For completed, failed, stuck jobs - try to remove but don't fail if it doesn't exist
                    try {
                        await job.remove();
                        this.logger.log(`[removeJobAtomically] Cleaned up ${state} job ${jobId}`);
                    } catch (removeError) {
                        this.logger.warn(
                            `[removeJobAtomically] Could not remove ${state} job ${jobId}: ${removeError.message}`,
                        );
                    }
                }
            }
        } catch (error) {
            // Job might not exist or might have been processed already
            this.logger.warn(`[removeJobAtomically] Could not find or remove job ${jobId}: ${error.message}`);
        }
    }

    /**
     * Cancel all existing jobs for a specific ads item
     * @param provinceId - Province ID
     * @param restaurantId - Restaurant ID
     * @param itemId - Campaign item ID
     */
    async cancelAdsJobsForItem(provinceId: string, restaurantId: number, itemId: number): Promise<void> {
        try {
            const activateJobId = `ads_activate_${provinceId}_${restaurantId}_${itemId}`;
            const deactivateJobId = `ads_deactivate_${provinceId}_${restaurantId}_${itemId}`;

            this.logger.log(
                `[cancelAdsJobsForItem] Cancelling jobs for item ${itemId}: ${activateJobId}, ${deactivateJobId}`,
            );

            await Promise.all([this.removeJobAtomically(activateJobId), this.removeJobAtomically(deactivateJobId)]);

            this.logger.log(`[cancelAdsJobsForItem] Successfully cancelled jobs for item ${itemId}`);
        } catch (error) {
            this.logger.error(`[cancelAdsJobsForItem] Error cancelling jobs for item ${itemId}: ${error.message}`);
        }
    }

    /**
     * Get job status for a specific ads item
     * @param provinceId - Province ID
     * @param restaurantId - Restaurant ID
     * @param itemId - Campaign item ID
     * @returns Job status information
     */
    async getAdsJobStatus(
        provinceId: string,
        restaurantId: number,
        itemId: number,
    ): Promise<{
        activateJob?: { id: string; state: string; delay?: number };
        deactivateJob?: { id: string; state: string; delay?: number };
    }> {
        try {
            const activateJobId = `ads_activate_${provinceId}_${restaurantId}_${itemId}`;
            const deactivateJobId = `ads_deactivate_${provinceId}_${restaurantId}_${itemId}`;

            const [activateJob, deactivateJob] = await Promise.all([
                this.restaurantQueue.getJob(activateJobId),
                this.restaurantQueue.getJob(deactivateJobId),
            ]);

            const result: any = {};

            if (activateJob) {
                const state = await activateJob.getState();
                result.activateJob = {
                    id: activateJobId,
                    state,
                    delay: activateJob.opts?.delay,
                };
            }

            if (deactivateJob) {
                const state = await deactivateJob.getState();
                result.deactivateJob = {
                    id: deactivateJobId,
                    state,
                    delay: deactivateJob.opts?.delay,
                };
            }

            return result;
        } catch (error) {
            this.logger.error(`[getAdsJobStatus] Error getting job status for item ${itemId}: ${error.message}`);
            return {};
        }
    }
}
