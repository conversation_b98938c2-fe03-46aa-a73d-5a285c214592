import { BullModule } from '@nestjs/bull';
import { Module, forwardRef } from '@nestjs/common';
import { RestaurantSearchService } from 'src/models/restaurant/services/restaurantSearch.service';
import { JobQueue } from '..';
import { RestaurantJobController } from './restaurantJob.controller';
import { RestaurantProcessor } from './restaurantJob.processor';
import { RestaurantJobService } from './restaurantJob.service';
import { RestaurantJobTask } from './restaurantJob.task';
import { RabbitMQEventModule } from 'src/rabbitMQ/rabbitMQ.module';
import { RestaurantTask } from 'src/models/restaurant/restaurant.task';
import { RestaurantJobCron, RestaurantJobCronProcessor } from './restaurant-job.cron';
import { PromotionV2Module } from 'src/models/promotionV2/promotionV2.module';
import { ProvinceModule } from 'src/models/province/province.module';
import { RestaurantService } from './restaurant.service';
import { RestaurantBusinessHourService } from 'src/models/restaurant/services/restaurantBusinessHour.service';
import { AdsCampaignsService } from 'src/models/restaurantAd/services/adsCampaigns.service';
import { AdCategoryService } from 'src/models/restaurantAd/services/adCategory.service';
// import { RestaurantAdModule } from 'src/models/restaurantAd/restaurantAd.module';
import { AdsFoodCategoryService } from 'src/models/restaurantAd/services/adsFoodCategory.service';
import { AdSellersService } from 'src/models/restaurantAd/services/adsSellers.service';
import { AdsCampaignLogsService } from 'src/models/restaurantAd/services/adsCampaignLogs.service';
import { AdPaymentsService } from 'src/models/restaurantAd/services/adsPayments.service';
import { AdsNewsfeedService } from 'src/models/restaurantAd/services/adsNewsfeed.service';
import { FoodCategoryService } from 'src/models/foodCategory/foodCategory.service';
import { CentralizedUserService } from 'src/models/user/services/centralizedUser.service';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { CategoryRestaurantSetService } from 'src/models/categoryRestaurantSet/categoryRestaurantSet.service';
import { UserEventPublisherService } from 'src/models/user/publishers/userEvent.publisher';
import { UserService } from 'src/models/user/services/user.service';
import { DriverService } from 'src/models/driver/services/driver.service';
import { UserActivityService } from 'src/models/adminUserActivity/userActivity.service';
import { EmployeeService } from 'src/models/employee/employee.service';
import { RestaurantAdFoodCategoryService } from 'src/models/restaurantAd/services/restaurantAdFoodCategory.service';
import { AdsSearchPageService } from 'src/models/restaurantAd/services/adsSearchPage.service';
import { AdsTaxService } from 'src/models/order/services/adsTax.service';
import { AdsContractInfoService } from 'src/models/restaurantAd/services/adsContractInfo.service';
import { KeywordService } from 'src/models/restaurantAd/services/adsKeyWord.service';
import { AdsContractPublisher } from 'src/models/restaurantAd/publishers/createAdsContract.publisher';
import { AdsRequestInfoService } from 'src/models/restaurantAd/services/adsRequestInfo.service';
import { OrderModule } from 'src/models/order/order.module';
import { AdsBannerService } from 'src/models/restaurantAd/services/adsBanner.service';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            name: JobQueue.RESTAURANT_QUEUE.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: 100,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
        RabbitMQEventModule,
        PromotionV2Module,
        ProvinceModule,
        forwardRef(() => OrderModule),
        // RestaurantAdModule,
    ],
    controllers: [RestaurantJobController],
    providers: [
        RestaurantProcessor,
        RestaurantJobTask,
        RestaurantJobService,
        RestaurantSearchService,
        RestaurantBusinessHourService,
        RestaurantTask,
        RestaurantJobCron,
        RestaurantJobCronProcessor,
        RestaurantService,
        AdsCampaignsService,
        AdCategoryService,
        AdsFoodCategoryService,
        RestaurantAdFoodCategoryService,
        AdSellersService,
        AdsCampaignLogsService,
        AdPaymentsService,
        AdsNewsfeedService,
        AdsSearchPageService,
        FoodCategoryService,
        CentralizedUserService,
        AwsS3Service,
        CategoryRestaurantSetService,
        UserEventPublisherService,
        UserService,
        DriverService,
        UserActivityService,
        EmployeeService,
        AdsTaxService,
        AdsContractInfoService,
        AdsContractPublisher,
        KeywordService,
        AdsRequestInfoService,
        AdsBannerService,
    ],
    exports: [RestaurantJobService],
})
export class RestaurantJobModule {}
