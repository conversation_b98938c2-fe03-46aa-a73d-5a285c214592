import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { Job, Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { RestaurantTask } from 'src/models/restaurant/restaurant.task';
import { RestaurantJobTask } from './restaurantJob.task';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class RestaurantJobCron {
    private logger = new Logger(RestaurantJobCron.name);

    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async resetFoodSalesTomorrowInElsDB() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_RESET_FOOD_SALES_TOMORROW);
            this.logger.log(`[resetFoodSalesTomorrowInElsDB] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_RESET_FOOD_SALES_TOMORROW) {
                        this.logger.log(`[resetFoodSalesTomorrowInElsDB] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_RESET_FOOD_SALES_TOMORROW,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: 'CRON_RESET_FOOD_SALES_TOMORROW',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[resetFoodSalesTomorrowInElsDB] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async createRestaurantRevenueDaily() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_DAILY);
            this.logger.log(`[createRestaurantRevenueDaily] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_DAILY) {
                        this.logger.log(`[createRestaurantRevenueDaily] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_DAILY,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: 'CRON_CREATE_RESTAURANT_REVENUE_DAILY',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[createRestaurantRevenueDaily] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async createRestaurantRevenueWeekly() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_WEEKLY);
            this.logger.log(`[createRestaurantRevenueWeekly] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_WEEKLY) {
                        this.logger.log(`[createRestaurantRevenueWeekly] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_WEEKLY,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_WEEK,
                        tz: VietNamTimeZone,
                        key: 'CRON_CREATE_RESTAURANT_REVENUE_WEEKLY',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[createRestaurantRevenueWeekly] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async createRestaurantRevenueMonth() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_MONTH);
            this.logger.log(`[createRestaurantRevenueMonth] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_MONTH) {
                        this.logger.log(`[createRestaurantRevenueMonth] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_MONTH,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: 'CRON_CREATE_RESTAURANT_REVENUE_MONTH',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[createRestaurantRevenueMonth] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async findRestaurantHasFrameExpiredAndRemoveFrame() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(
                CRON_JOB.PROCESS.CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME,
            );
            this.logger.log(`[findRestaurantHasFrameExpiredAndRemoveFrame] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME) {
                        this.logger.log(`[findRestaurantHasFrameExpiredAndRemoveFrame] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_1AM,
                        tz: VietNamTimeZone,
                        key: 'CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[findRestaurantHasFrameExpiredAndRemoveFrame] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class RestaurantJobCronProcessor {
    private logger = new Logger(RestaurantJobCronProcessor.name);

    constructor(
        private readonly restaurantTask: RestaurantTask,
        private readonly restaurantJobTask: RestaurantJobTask,
    ) {}

    @Process({
        name: CRON_JOB.PROCESS.CRON_RESET_FOOD_SALES_TOMORROW,
        concurrency: 1,
    })
    async resetFoodSalesTomorrowInElsDB(job: Job) {
        try {
            this.logger.log(`[resetFoodSalesTomorrowInElsDB] Reset food sales tomorrow in ElasticSearch ...`);
            await this.restaurantTask.resetFoodSalesTomorrowInElsDB();
        } catch (error) {
            this.logger.error(`[resetFoodSalesTomorrowInElsDB] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_DAILY,
        concurrency: 1,
    })
    async createRestaurantRevenueDaily(job: Job) {
        try {
            this.logger.log(`[createRestaurantRevenueDaily] Create restaurant revenue daily ...`);
            await this.restaurantJobTask.createRestaurantRevenueDaily();
        } catch (error) {
            this.logger.error('[createRestaurantRevenueDaily]', error);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_WEEKLY,
        concurrency: 1,
    })
    async createRestaurantRevenueWeekly(job: Job) {
        try {
            this.logger.log(`[createRestaurantRevenueWeekly] Create restaurant revenue weekly ...`);
            await this.restaurantJobTask.createRestaurantRevenueWeekly();
        } catch (error) {
            this.logger.error(`[createRestaurantRevenueWeekly] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_CREATE_RESTAURANT_REVENUE_MONTH,
        concurrency: 1,
    })
    async createRestaurantRevenueMonth(job: Job) {
        try {
            this.logger.log(`[createRestaurantRevenueMonth] Create restaurant revenue month ...`);
            await this.restaurantJobTask.createRestaurantRevenueMonth();
        } catch (error) {
            this.logger.error(`[createRestaurantRevenueMonth] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME,
        concurrency: 1,
    })
    async findRestaurantHasFrameExpiredAndRemoveFrame(job: Job) {
        try {
            this.logger.log(
                `[findRestaurantHasFrameExpiredAndRemoveFrame] Find restaurant has frame expired and remove frame ...`,
            );
            await this.restaurantJobTask.findRestaurantHasFrameExpiredAndRemoveFrame();
        } catch (error) {
            this.logger.error(
                `[findRestaurantHasFrameExpiredAndRemoveFrame] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}
