import { InjectQueue } from '@nestjs/bull';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { Queue } from 'bull';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RestaurantOrderDoneDto, RestaurantReopenDto } from './dto';
import { IRestaurantOrderOneJobData } from './restaurantJob.interface';
import { RestaurantJobService } from './restaurantJob.service';
import { JobQueue } from '..';

const { RESTAURANT_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_QUEUE;
import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('jobs/restaurants')
@UseGuards(AuthGuard)
export class RestaurantJobController {
    constructor(
        @InjectQueue(RESTAURANT_QUEUE.PROCESSOR) private restaurantQueue: Queue,
        private restaurantJobService: RestaurantJobService,
    ) {}

    @Post('reopen')
    async reopen(@Body() { restaurantId, provinceId }: RestaurantReopenDto) {
        return await this.restaurantJobService.addJobToReopenRestaurant(restaurantId, provinceId);
    }

    @Post('order-done')
    async restaurantOrderDone(@Body() { orderId, provinceId, restaurantId }: RestaurantOrderDoneDto) {
        const jobId = [provinceId, restaurantId, orderId].join('_');
        await this.restaurantQueue.add(
            PROCESS.AGGREGATE_REVENUE_AFTER_ORDER_DONE,
            {
                provinceId,
                restaurantId,
                orderId,
            } as IRestaurantOrderOneJobData,
            {
                jobId,
            },
        );
        return;
    }
}
