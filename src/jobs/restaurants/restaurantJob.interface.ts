import { ERestaurantRevenueInterval } from 'src/entities/restaurantRevenue.entity';

export interface IRestaurantOrderOneJobData {
    provinceId: string;
    restaurantId: number;
    orderId: number;
}

export interface IRestaurantRevenuePerDayJobData {
    provinceId: string;
    restaurantId: number;
    date: string;
}

export interface IRestaurantRevenuePerWeekJobData {
    provinceId: string;
    restaurantId: number;
    fromDate: string;
    toDate: string;
}

export interface IRestaurantRevenuePerMonthJobData {
    provinceId: string;
    restaurantId: number;
    fromDate: string;
    toDate: string;
}

export interface ICreateRestaurantRevenueJobData {
    provinceId: string;
    restaurantIds: number[];
    interval: ERestaurantRevenueInterval;
    fromDate: string;
    toDate: string;
}

export interface IRestaurantReopen {
    provinceId: string;
    restaurantId: number;
}

export interface IreIndexRestaurantToEls {
    provinceId: string;
    restaurantId: number;
    subProvinceId: string;
    jobId?: string;
}

export interface IUpdateRestaurantTotalOrders {
    provinceId: string;
    restaurantId: number;
}
export interface IUpdateRestaurantReviewsSummary {
    provinceId: string;
    restaurantId: number;
    reviewSumaryId: number;
}

export interface ISummaryRestaurantTotalOrders {
    provinceId: string;
    restaurantId: number;
}

export interface IIdentifyRestaurantsOpeningStatus {
    provinceId: string;
    isoWeekDay: number;
    currentHourTime: string;
    identifyOpeningStatus: boolean;
}

export interface IUpdateRestaurantAdsIsActive {
    provinceId: string;
    restaurantId: number;
    itemId: number;
    isActive: boolean;
    jobId?: string;
}
