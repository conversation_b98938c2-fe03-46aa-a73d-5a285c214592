import { InjectQueue, OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { isObject } from 'class-validator';
import * as moment from 'moment';
import { Order } from 'src/entities/order.entity';
import { EOrderStatusId } from 'src/entities/orderStatus.entity';
import { ERestaurantOperatingStatus, Restaurant } from 'src/entities/restaurant.entity';
import { ERestaurantRevenueInterval, RestaurantRevenue } from 'src/entities/restaurantRevenue.entity';
import { RestaurantReview } from 'src/entities/restaurantReview.entity';
import { RestaurantReviewSumary } from 'src/entities/restaurantReviewSumary.entity';
import { EventService } from 'src/events/event.service';
import { RestaurantSearchService } from 'src/models/restaurant/services/restaurantSearch.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { JobQueue, VietNamTimeZoneNum } from '..';
import {
    ICreateRestaurantRevenueJobData,
    IreIndexRestaurantToEls,
    IRestaurantOrderOneJobData,
    IRestaurantReopen,
    IRestaurantRevenuePerDayJobData,
    IRestaurantRevenuePerMonthJobData,
    IRestaurantRevenuePerWeekJobData,
    ISummaryRestaurantTotalOrders,
    IUpdateRestaurantAdsIsActive,
} from './restaurantJob.interface';
import { RestaurantJobService } from './restaurantJob.service';
import { AdsCampaignsService } from 'src/models/restaurantAd/services/adsCampaigns.service';
const { RESTAURANT_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_QUEUE;
@Processor(RESTAURANT_QUEUE.PROCESSOR)
export class RestaurantProcessor {
    private logger = new Logger(RestaurantProcessor.name);

    constructor(
        @InjectQueue(RESTAURANT_QUEUE.PROCESSOR) private restaurantQueue: Queue,
        private restaurantSearchService: RestaurantSearchService,
        private restaurantAdsService: AdsCampaignsService,
        private readonly restaurantJobService: RestaurantJobService,
        private eventEmitter: EventService,
    ) {}

    @Process(PROCESS.AGGREGATE_REVENUE_AFTER_ORDER_DONE)
    async orderOne(job: Job<IRestaurantOrderOneJobData>) {
        const { provinceId, restaurantId, orderId } = job.data;

        const order = await DatabaseService.getRepositoryByProvinceId(Order, provinceId).findOne({
            where: { id: orderId },
        });

        if (order && [EOrderStatusId.ARRIVED, EOrderStatusId.CANCELED].includes(order.order_status_id)) {
            const currentDateStr = moment().utcOffset(VietNamTimeZoneNum).format('YYYY-MM-DD');
            if (moment(order.order_date).format('YYYY-MM-DD') == currentDateStr) {
                let restaurantRevenue = await DatabaseService.getRepositoryByProvinceId(
                    RestaurantRevenue,
                    provinceId,
                ).findOne({
                    where: {
                        restaurant_id: order.restaurant_id,
                        interval: ERestaurantRevenueInterval.DATE,
                        from_date: currentDateStr,
                        to_date: currentDateStr,
                    },
                });
                if (!restaurantRevenue) {
                    restaurantRevenue = await this.createNewRecord(
                        restaurantId,
                        ERestaurantRevenueInterval.DATE,
                        currentDateStr,
                        currentDateStr,
                        provinceId,
                    );
                }

                if (order.order_status_id == EOrderStatusId.ARRIVED) {
                    await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId)
                        .createQueryBuilder()
                        .update()
                        .set({
                            total_orders: () => 'total_orders + 1',
                            total_successful_orders: () => 'total_successful_orders + 1',
                            revenue: () => `revenue + ${order.sub_total_price}`,
                            trade_discount: () => `trade_discount + ${order.trade_discount}`,
                        })
                        .where('id = :id', { id: restaurantRevenue.id })
                        .execute();
                    // await this.restaurantService.plusOneTotalOrders(restaurantId, provinceId);
                    await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                        .createQueryBuilder()
                        .update()
                        .set({ total_orders: () => 'total_orders + 1' })
                        .where('id = :id', { id: restaurantId })
                        .execute();
                    await this.restaurantJobService.reIndexRestaurantToEls(restaurantId, provinceId);
                } else {
                    await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId)
                        .createQueryBuilder()
                        .update()
                        .set({
                            total_orders: () => 'total_orders + 1',
                            total_canceled_orders: () => 'total_canceled_orders + 1',
                        })
                        .where('id = :id', { id: restaurantRevenue.id })
                        .execute();
                }

                this.addQueueJobsToCalcRevenueByWeekAndMonth(restaurantId, currentDateStr, provinceId);
            } else {
                this.restaurantQueue.add('restaurant:revenuePerDay', {
                    provinceId,
                    restaurantId,
                    date: currentDateStr,
                } as IRestaurantRevenuePerDayJobData);
            }
        }
    }

    @Process(PROCESS.AGGREGATE_REVENUE_PER_DAY)
    async calcRevenuePerDay(job: Job<IRestaurantRevenuePerDayJobData>) {
        const { provinceId, date, restaurantId } = job.data;

        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) return;

        const momentDate = moment(date, 'YYYY-MM-DD');
        const fromDate = momentDate.clone().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
            toDate = momentDate.clone().endOf('date').format('YYYY-MM-DD HH:mm:ss');
        const { total_canceled_orders, total_orders, total_successful_orders, sub_total_price, trade_discount } =
            await DatabaseService.getRepositoryByProvinceId(Order, provinceId)
                .createQueryBuilder('order')
                .select('COUNT(*)', 'total_orders')
                .addSelect(
                    `COUNT(DISTINCT CASE WHEN order_status_id = ${EOrderStatusId.CANCELED} THEN id END)`,
                    'total_canceled_orders',
                )
                .addSelect(
                    `COUNT(DISTINCT CASE WHEN order_status_id = ${EOrderStatusId.ARRIVED} THEN id END)`,
                    'total_successful_orders',
                )
                .addSelect(
                    `SUM(IF(order.order_status_id = ${EOrderStatusId.ARRIVED}, order.sub_total_price, 0))`,
                    'sub_total_price',
                )
                .addSelect(
                    `SUM(IF(order.order_status_id = ${EOrderStatusId.ARRIVED}, order.trade_discount, 0))`,
                    'trade_discount',
                )
                .where('order.restaurant_id = :restaurantId', { restaurantId })
                .andWhere(`order.order_date BETWEEN '${fromDate}' AND '${toDate}'`)
                .execute()
                .then(
                    ([
                        {
                            total_orders,
                            total_canceled_orders,
                            total_successful_orders,
                            sub_total_price,
                            trade_discount,
                        },
                    ]) => {
                        return {
                            total_orders: +total_orders || 0,
                            total_canceled_orders: +total_canceled_orders || 0,
                            total_successful_orders: +total_successful_orders || 0,
                            sub_total_price: +sub_total_price || 0,
                            trade_discount: +trade_discount || 0,
                        };
                    },
                );
        let restaurantRevenue = await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).findOne({
            where: {
                interval: ERestaurantRevenueInterval.DATE,
                restaurant_id: restaurantId,
                from_date: date,
                to_date: date,
            },
        });
        if (!restaurant) {
            restaurantRevenue = new RestaurantRevenue({
                interval: ERestaurantRevenueInterval.DATE,
                restaurant_id: restaurantId,
                from_date: date,
                to_date: date,
                total_orders,
                total_canceled_orders,
                total_successful_orders,
                revenue: sub_total_price,
                trade_discount,
            });
        }

        restaurantRevenue = await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).save(
            restaurantRevenue,
        );
        this.addQueueJobsToCalcRevenueByWeekAndMonth(restaurantId, date, provinceId);
        return restaurantRevenue;
    }

    @Process(PROCESS.AGGREGATE_REVENUE_PER_WEEK)
    async calcRevenuePerWeek(job: Job<IRestaurantRevenuePerWeekJobData>) {
        const { provinceId, restaurantId, fromDate, toDate } = job.data;

        return await this.aggregateAndUpdateRevenue(
            restaurantId,
            fromDate,
            toDate,
            ERestaurantRevenueInterval.WEEK,
            provinceId,
        );
    }

    @Process(PROCESS.AGGREGATE_REVENUE_PER_MONTH)
    async calcRevenuePerMonth(job: Job<IRestaurantRevenuePerMonthJobData>) {
        const { provinceId, restaurantId, fromDate, toDate } = job.data;
        return await this.aggregateAndUpdateRevenue(
            restaurantId,
            fromDate,
            toDate,
            ERestaurantRevenueInterval.MONTH,
            provinceId,
        );
    }

    @Process(PROCESS.CREATE_REVENUE_RECORD)
    async createRevenueRecord(job: Job<ICreateRestaurantRevenueJobData>) {
        const { provinceId, interval, restaurantIds, fromDate, toDate } = job.data;
        return await this.createNewRecords(restaurantIds, interval, fromDate, toDate, provinceId);
    }

    @Process(PROCESS.REOPEN_TIME)
    async reopen(job: Job<IRestaurantReopen>) {
        const { provinceId, restaurantId } = job.data;

        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) return;

        // check restaurant is close and reopen time is valid to reopen
        if (
            restaurant.operating_status === ERestaurantOperatingStatus.TEMPT_CLOSE &&
            restaurant.reopen_time &&
            moment().isSameOrAfter(moment(restaurant.reopen_time))
        ) {
            const operatingStatus = ERestaurantOperatingStatus.OPEN;
            const updateResult = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder()
                .update()
                .set({
                    reopen_time: null,
                    operating_status: operatingStatus,
                })
                .where('id = :restaurantId', { restaurantId })
                .execute();
            await this.eventEmitter.restaurantUpdateReopenTime(restaurantId, Number(provinceId));
            this.restaurantSearchService.indexRestaurant(restaurantId, provinceId);
            // this.syncRestaurant(restaurantId, provinceId);
            return updateResult;
        } else {
            return;
        }
    }

    @Process({
        name: PROCESS.RE_INDEX_RESTAURANT_TO_ELS,
        concurrency: 1,
    })
    async reIndexRestaurantToEls(job: Job<IreIndexRestaurantToEls>) {
        const { provinceId, restaurantId } = job.data;
        this.logger.log(
            `ReIndexing restaurant ${restaurantId} to ElasticSearch ... stating ${new Date().toISOString()}`,
        );
        await this.restaurantSearchService.updateRestaurantDetailsById(restaurantId, provinceId);
        this.logger.log(
            `ReIndexing restaurant ${restaurantId} to ElasticSearch ... ending ${new Date().toISOString()}`,
        );
        await job.progress(100);
        return true;
    }

    @Process({
        name: PROCESS.UPDATE_RESTAURANT_PROMOTIONS_TO_ELS,
        concurrency: 1,
    })
    async updateRestaurantPromotionToEls(job: Job<IreIndexRestaurantToEls>) {
        const { provinceId, subProvinceId, restaurantId } = job.data;
        this.logger.log(
            `Updating restaurant ${restaurantId} promotion to ElasticSearch ... stating ${new Date().toISOString()}`,
        );
        await this.restaurantSearchService.updatePromotionsByRestaurantId(restaurantId, subProvinceId, provinceId);
        this.logger.log(
            `Updating restaurant ${restaurantId} promotion to ElasticSearch ... ending ${new Date().toISOString()}`,
        );
        await job.progress(100);
        return true;
    }

    @Process({
        name: PROCESS.SUMMARY_RESTAURANT_TOTAL_ORDERS,
        concurrency: 1,
    })
    async summaryRestaurantTotalOrders(job: Job<ISummaryRestaurantTotalOrders>) {
        const { provinceId, restaurantId } = job.data;
        const totalOrders = await DatabaseService.getRepositoryByProvinceId(Order, provinceId)
            .createQueryBuilder('order')
            .where('order.restaurant_id = :restaurantId', { restaurantId })
            .andWhere('order.order_status_id = :orderStatusId', { orderStatusId: EOrderStatusId.ARRIVED })
            .getCount();
        const result = await DatabaseService.getConnectionByProvinceId(provinceId).transaction(
            async (EntityManager) => {
                const updateResult = await EntityManager.getRepository(Restaurant)
                    .createQueryBuilder('restaurant')
                    .update()
                    .set({
                        total_orders: totalOrders,
                    })
                    .where('id = :restaurantId', { restaurantId })
                    .set({ total_orders: totalOrders })
                    .execute();
                if (updateResult.affected === 0) {
                    throw new Error('Cập nhật không thành công!');
                }
                let restuarantSummaryExisting = await EntityManager.getRepository(RestaurantReviewSumary).findOne({
                    where: {
                        restaurant_id: restaurantId,
                    },
                });
                if (!restuarantSummaryExisting) {
                    const totalReviews = await EntityManager.getRepository(RestaurantReview)
                        .createQueryBuilder('review')
                        .where('review.restaurant_id = :restaurantId', { restaurantId })
                        .getCount();
                    const restaurantSummary = new RestaurantReviewSumary({
                        restaurant_id: restaurantId,
                        total_reviews_with_content: totalReviews,
                        total_reviews_with_tag: 0,
                        total_reviews_with_image: 0,
                    });
                    restuarantSummaryExisting = await EntityManager.getRepository(RestaurantReviewSumary).save(
                        restaurantSummary,
                    );
                }

                return {
                    total_orders: totalOrders,
                    provinceId,
                    restaurantId,
                    restuarantSummaryExisting,
                };
            },
        );

        await this.restaurantSearchService.indexRestaurant(restaurantId, provinceId);
        await job.progress(100);
        return result;
    }

    // @Process({
    //     name: PROCESS.IDENTIFY_RESTAURANTS_AND_UPDATE_OPEN_STATUS,
    //     concurrency: 1,
    // })
    // async findAndUpdateRestaurantsStatusClosing(job: Job<IIdentifyRestaurantsOpeningStatus>) {
    //     const { provinceId, currentHourTime, isoWeekDay, identifyOpeningStatus } = job.data;
    //     if (identifyOpeningStatus) {
    //         return await this.restaurantBusinessHourService.findAndUpdateRestaurantsStatusOpening(
    //             isoWeekDay,
    //             currentHourTime,
    //             provinceId,
    //         );
    //     } else {
    //         return await this.restaurantBusinessHourService.findAndUpdateRestaurantsStatusClosing(
    //             isoWeekDay,
    //             currentHourTime,
    //             provinceId,
    //         );
    //     }
    // }

    @Process({
        name: PROCESS.UPDATE_RESTAURANT_ADS_IS_ACTIVE,
        concurrency: 1,
    })
    async updateRestaurantAdsIsActive(job: Job<IUpdateRestaurantAdsIsActive>) {
        const { provinceId, restaurantId, itemId, isActive } = job.data;
        this.logger.log(
            `Updating restaurant ${restaurantId} ads is active status to ${isActive} ... stating ${new Date().toISOString()}`,
        );
        await this.restaurantAdsService.updateIsActive(provinceId, restaurantId, itemId, isActive);
        this.logger.log(
            `Updating restaurant ${restaurantId} ads is active status to ${isActive} ... ending ${new Date().toISOString()}`,
        );
        await job.progress(100);
        return true;
    }

    @OnQueueCompleted()
    handleQueueCompleted(job: Job, result: any) {
        this.logger.log(
            `[ SUCCESS ] JobId: ${job.id} | job name: ${job.name} | result: ${
                result && isObject(result) ? JSON.stringify(result) : null
            }`,
        );
    }

    @OnQueueFailed()
    handleQueueFailed(job: Job, err: Error) {
        this.logger.log(`[ FAILED ] JobId: ${job.id} | job name: ${job.name} | err: ${err ? err.message : null}`);
    }

    @OnQueueActive()
    handleQueueActive(job: Job) {
        this.logger.log(`[ ACTIVE ] JobId: ${job.id} | job name: ${job.name}`);
    }

    /**
     * add queue jobs to calculate depending depending on daily restaurant revenue
     * @param restaurantId restaurant id
     * @param fromDate format YYYY-MM-DD +7
     * @param toDate format YYYY-MM-DD +7
     * @param provinceId province id
     * @returns any
     */
    async aggregateRestaurantRevenueByDateInterval(
        restaurantId: number,
        fromDate: string,
        toDate: string,
        provinceId: string,
    ) {
        const [{ total_orders, total_successful_orders, total_canceled_orders, revenue, trade_discount }] =
            await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId)
                .createQueryBuilder()
                .select('SUM(total_orders)', 'total_orders')
                .addSelect('SUM(total_successful_orders)', 'total_successful_orders')
                .addSelect('SUM(total_canceled_orders)', 'total_canceled_orders')
                .addSelect('SUM(revenue)', 'revenue')
                .addSelect('SUM(trade_discount)', 'trade_discount')
                .where('restaurant_id = :restaurantId', { restaurantId })
                .andWhere(`from_date >= '${fromDate}'`)
                .andWhere(`from_date <= '${toDate}'`)
                .andWhere(`\`interval\` = '${ERestaurantRevenueInterval.DATE}'`)
                .execute();
        return {
            total_orders: +total_orders || 0,
            total_successful_orders: +total_successful_orders || 0,
            total_canceled_orders: +total_canceled_orders || 0,
            revenue: +revenue || 0,
            trade_discount: +trade_discount || 0,
        };
    }

    /**
     * add queue jobs to calculate depending depending on daily restaurant revenue
     * @param restaurantId restaurant id
     * @param dateStr format YYYY-MM-DD +7
     * @param provinceId province id
     */
    async addQueueJobsToCalcRevenueByWeekAndMonth(restaurantId: number, dateStr: string, provinceId: string) {
        const momentDate = moment(dateStr, 'YYYY-MM-DD');
        this.restaurantQueue.add(PROCESS.AGGREGATE_REVENUE_PER_WEEK, {
            provinceId,
            restaurantId,
            fromDate: momentDate.startOf('isoWeek').format('YYYY-MM-DD'),
            toDate: momentDate.endOf('isoWeek').format('YYYY-MM-DD'),
        } as IRestaurantRevenuePerWeekJobData);

        this.restaurantQueue.add(PROCESS.AGGREGATE_REVENUE_PER_MONTH, {
            provinceId,
            restaurantId,
            fromDate: momentDate.startOf('month').format('YYYY-MM-DD'),
            toDate: momentDate.endOf('month').format('YYYY-MM-DD'),
        } as IRestaurantRevenuePerMonthJobData);
    }

    /**
     *
     * @param { number } restaurantId
     * @param { ERestaurantRevenueInterval } interval
     * @param { string } fromDate - format YYYY-MM-DD +7
     * @param { string } toDate - format YYYY-MM-DD +7
     * @param { string } provinceId province id
     * @returns RestaurantRevenue
     */
    createNewRecord(
        restaurantId: number,
        interval: ERestaurantRevenueInterval,
        fromDate: string,
        toDate: string,
        provinceId: string,
    ) {
        return DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).save(
            new RestaurantRevenue({
                restaurant_id: restaurantId,
                interval,
                from_date: fromDate,
                to_date: toDate,
            }),
        );
    }
    /**
     *
     * @param { Array<number> } restaurantIds
     * @param { ERestaurantRevenueInterval } interval
     * @param { string } fromDate - format YYYY-MM-DD +7
     * @param { string } toDate - format YYYY-MM-DD +7
     * @param { string } provinceId province id
     * @returns RestaurantRevenue
     */
    createNewRecords(
        restaurantIds: number[],
        interval: ERestaurantRevenueInterval,
        fromDate: string,
        toDate: string,
        provinceId: string,
    ) {
        return DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).save(
            restaurantIds.map(
                (id) =>
                    new RestaurantRevenue({
                        restaurant_id: id,
                        interval,
                        from_date: fromDate,
                        to_date: toDate,
                    }),
            ),
        );
    }

    /**
     *
     * @param { number } restaurantId
     * @param { ERestaurantRevenueInterval } interval
     * @param { string } fromDate - format YYYY-MM-DD +7
     * @param { string } toDate - format YYYY-MM-DD +7
     * @param { string } provinceId province id
     * @returns RestaurantRevenue
     */
    async aggregateAndUpdateRevenue(
        restaurantId: number,
        fromDate: string,
        toDate: string,
        interval: ERestaurantRevenueInterval,
        provinceId: string,
    ) {
        const { total_canceled_orders, total_orders, total_successful_orders, revenue, trade_discount } =
            await this.aggregateRestaurantRevenueByDateInterval(restaurantId, fromDate, toDate, provinceId);
        let restaurantRevenue = await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).findOne({
            where: {
                restaurant_id: restaurantId,
                from_date: fromDate,
                to_date: toDate,
                interval,
            },
        });
        if (!restaurantRevenue) {
            restaurantRevenue = new RestaurantRevenue({
                restaurant_id: restaurantId,
                total_canceled_orders,
                total_orders,
                total_successful_orders,
                revenue,
                trade_discount,
                from_date: fromDate,
                to_date: toDate,
                interval,
            });
        } else {
            restaurantRevenue.total_canceled_orders = total_canceled_orders;
            restaurantRevenue.total_orders = total_orders;
            restaurantRevenue.total_successful_orders = total_successful_orders;
            restaurantRevenue.revenue = revenue;
            restaurantRevenue.trade_discount = trade_discount;
        }

        restaurantRevenue = await DatabaseService.getRepositoryByProvinceId(RestaurantRevenue, provinceId).save(
            restaurantRevenue,
        );
        return restaurantRevenue;
    }

    /**
     *
     * @param { number } restaurant_id
     * @param { string } provinceId province id
     * @returns GlobalRestaurant
     */
    // async syncRestaurant(restaurant_id: number, provinceId: string) {
    //     const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
    //         where: { id: restaurant_id },
    //     });
    //     if (restaurant) {
    //         const updateGlobalData = new UpdateDataDto();
    //         Object.assign(updateGlobalData, restaurant);
    //         updateGlobalData.restaurant_id = restaurant_id;
    //         await this.globalRestaurantService.update(restaurant_id, provinceId, updateGlobalData);
    //     }
    // }
}

export interface IUpdateReviewsSummary {
    restaurantId: number;
    provinceId: string;
}
