import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, Timeout } from '@nestjs/schedule';
import { Queue } from 'bull';
import * as moment from 'moment';
import { ERestaurantOperatingStatus, Restaurant } from 'src/entities/restaurant.entity';
import { ERestaurantRevenueInterval } from 'src/entities/restaurantRevenue.entity';
import { EventService } from 'src/events/event.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { IsNull, Not } from 'typeorm';

import { ECronjobs, JobQueue, VietNamTimeZone, VietNamTimeZoneNum } from '..';
import { ICreateRestaurantRevenueJobData } from './restaurantJob.interface';
import { RestaurantJobService } from './restaurantJob.service';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { Province } from 'src/entities/province.entity';
const { RESTAURANT_QUEUE } = JobQueue;
const { PROCESS } = RESTAURANT_QUEUE;
@Injectable()
export class RestaurantJobTask {
    private logger = new Logger(RestaurantJobTask.name);

    constructor(
        @InjectQueue(RESTAURANT_QUEUE.PROCESSOR) private restaurantQueue: Queue,
        private restaurantJobService: RestaurantJobService,
        private eventEmitter: EventService,
    ) {}

    /* Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
        name: ECronjobs.CreateRestaurantRevenueDaily,
        timeZone: VietNamTimeZone,
    }) */
    async createRestaurantRevenueDaily() {
        const currentStr = moment().utcOffset(VietNamTimeZoneNum).format('YYYY-MM-DD');
        await this.createRestaurantRevenueRecords(currentStr, currentStr, ERestaurantRevenueInterval.DATE);
    }

    /* Cron(CronExpression.EVERY_WEEK, {
        name: ECronjobs.CreateRestaurantRevenueWeekly,
        timeZone: VietNamTimeZone,
    }) */
    async createRestaurantRevenueWeekly() {
        const current = moment().utcOffset(VietNamTimeZoneNum);
        const fromDate = current.startOf('isoWeek').format('YYYY-MM-DD'),
            toDate = current.endOf('isoWeek').format('YYYY-MM-DD');
        await this.createRestaurantRevenueRecords(fromDate, toDate, ERestaurantRevenueInterval.WEEK);
    }

    /* Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT, {
        name: ECronjobs.CreateRestaurantRevenueMonth,
        timeZone: VietNamTimeZone,
    }) */
    async createRestaurantRevenueMonth() {
        const current = moment().utcOffset(VietNamTimeZoneNum);
        const fromDate = current.startOf('month').format('YYYY-MM-DD'),
            toDate = current.endOf('month').format('YYYY-MM-DD');
        await this.createRestaurantRevenueRecords(fromDate, toDate, ERestaurantRevenueInterval.MONTH);
    }

    @Timeout(5000)
    async fetchRestaurantsTempClosedAndCreateCronJob() {
        // const restaurantRepos = DatabaseService.getRepositoriesAndNamesOfAllConnections(Restaurant);
        // const provinces = await DatabaseService.getRepositoryByDefaultConnection(Province).find({
        //     where: { parent_id: null, is_active: 1 },
        // });
        const provinceIds = DatabaseService.getAllProvinceIds();
        for (let i = 0; i < provinceIds.length; i++) {
            const provinceId = provinceIds[i];
            const repo = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceIds[i]);
            // const provinceId = repo.manager.connection.name;

            const restaurants = await repo.find({
                where: {
                    reopen_time: Not(IsNull()),
                },
            });

            for (let j = 0; j < restaurants.length; j++) {
                const { id, reopen_time, operating_status } = restaurants[j];
                const openTime = moment(reopen_time);
                if (openTime.isBefore(moment()) || operating_status == ERestaurantOperatingStatus.OPEN) {
                    await repo.update(id, {
                        operating_status: ERestaurantOperatingStatus.OPEN,
                        reopen_time: null,
                    });
                    this.eventEmitter.restaurantUpdateReopenTime(id, Number(provinceId));
                } else {
                    await this.restaurantJobService.addJobToReopenRestaurant(id, provinceId);
                }
            }
        }
    }

    async createRestaurantRevenueRecords(fromDate: string, toDate: string, interval: ERestaurantRevenueInterval) {
        const restaurantRepos = DatabaseService.getRepositoriesAndNamesOfAllConnections(Restaurant);
        for (let i = 0; i < restaurantRepos.length; i++) {
            const { name: provinceId, repository: repo } = restaurantRepos[i];
            // const repo = restaurantRepos[i];
            // const provinceId = repo.manager.connection.name;

            const total = await repo.createQueryBuilder().getCount();

            const limit = 100;
            let count = 0;
            while (count < total) {
                const restaurants = await repo.createQueryBuilder().offset(count).limit(limit).getMany();

                const restaurantIds = restaurants.map(({ id }) => id);

                await this.restaurantQueue.add(PROCESS.CREATE_REVENUE_RECORD, {
                    provinceId,
                    restaurantIds,
                    interval,
                    fromDate,
                    toDate,
                } as ICreateRestaurantRevenueJobData);

                count += restaurants.length;
            }
        }
    }

    // Cron(CronExpression.EVERY_5_MINUTES, {
    //     name: 'identify and update the status of restaurants that are opening',
    // })
    // async findAndUpdateRestaurantsStatusOpening() {
    //     const restaurantRepos = DatabaseService.getRepositoriesAndNamesOfAllConnections(Restaurant);
    //     const currentHourTime = moment().utcOffset(VietNamTimeZoneNum).format('HH:mm');
    //     const currentIsoWeekDay = moment().utcOffset(VietNamTimeZoneNum).isoWeekday();
    //     for (let i = 0; i < restaurantRepos.length; i++) {
    //         const { name: provinceId } = restaurantRepos[i];
    //         // const repo = restaurantRepos[i];
    //         // const provinceId = repo.manager.connection.name;
    //         this.restaurantJobService.addJobToIdentifyRestaurantsOpeningStatus(
    //             currentIsoWeekDay,
    //             currentHourTime,
    //             true,
    //             provinceId,
    //         );
    //     }
    // }

    // Cron(CronExpression.EVERY_5_MINUTES, {
    //     name: 'identify and update the status of restaurants that are closing',
    // })
    // async findAndUpdateRestaurantsStatusClosing() {
    //     const restaurantRepos = DatabaseService.getRepositoriesAndNamesOfAllConnections(Restaurant);
    //     const currentHourTime = moment().utcOffset(VietNamTimeZoneNum).format('HH:mm');
    //     const currentIsoWeekDay = moment().utcOffset(VietNamTimeZoneNum).isoWeekday();
    //     for (let i = 0; i < restaurantRepos.length; i++) {
    //         const { name: provinceId } = restaurantRepos[i];
    //         // const repo = restaurantRepos[i];
    //         // const provinceId = repo.manager.connection.name;
    //         this.restaurantJobService.addJobToIdentifyRestaurantsOpeningStatus(
    //             currentIsoWeekDay,
    //             currentHourTime,
    //             false,
    //             provinceId,
    //         );
    //     }
    // }

    // Cron(CronExpression.EVERY_DAY_AT_1AM)
    // async findAndRemoveFrameRestaurantsEmptyPromotions() {
    //     const provinceIds = DatabaseService.getAllProvinceIds();
    //     for (let i = 0; i < provinceIds.length; i++) {
    //         const provinceId = provinceIds[i];
    //         await this.eventEmitter.findAndRemoveFrameRestaurantsEmptyPromotionsEvent(provinceId);
    //     }
    // }

    // Cron(CronExpression.EVERY_DAY_AT_1AM)
    async findRestaurantHasFrameExpiredAndRemoveFrame() {
        const provinceIds = DatabaseService.getAllProvinceIds();
        for (let i = 0; i < provinceIds.length; i++) {
            const provinceId = provinceIds[i];
            await this.eventEmitter.findAndRemoveRestaurantsHasExpiredEvent(provinceId);
        }
    }
}
