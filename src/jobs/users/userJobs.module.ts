import { Module } from '@nestjs/common';
import { ProvinceService } from 'src/models/province/province.service';
import { UserListenerService } from './listeners/userCreated.listener';
import { UserMgapaySyncService } from './services/userMgapaySync.service';
import { UserRoleSyncService } from './services/userRoleSync.service';
import { UserSyncService } from './services/userSync.service';
import { UserProcessor } from './user.processor';
import { UserJobTask } from './userJobs.task';
import { UserStatisticService } from './services/userStatistic.service';
// import { UserOrdersRatingService } from './services/user-orders-rating.service';
import { UserJobCron, UserJobsCronProcessor } from './user-jobs.cron';
// import { UserRateProcessor } from './user-rate.processor';

@Module({
    imports: [],
    controllers: [],
    providers: [
        UserJobTask,
        // UserRateTask,
        ProvinceService,
        UserProcessor,
        // UserRateProcessor,
        UserSyncService,
        UserListenerService,
        UserRoleSyncService,
        UserMgapaySyncService,
        UserStatisticService,
        // UserOrdersRatingService,
        UserJobCron,
        UserJobsCronProcessor,
    ],
})
export class UserJobModule {}
