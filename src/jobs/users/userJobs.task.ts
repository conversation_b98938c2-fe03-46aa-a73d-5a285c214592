import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { User } from 'src/entities/user.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { ECronjobs, JobQueue, VietNamTimeZone } from '..';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class UserJobTask {
    private logger = new Logger(UserJobTask.name);
    constructor(@InjectQueue(JobQueue.USER_QUEUE.PROCESSOR) private queue: Queue) {}

    /* Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT, {
        name: ECronjobs.ResetUserMonthOrders,
        timeZone: VietNamTimeZone,
    }) */
    async resetMonthOrderInfo() {
        const userRepos = DatabaseService.getRepositoriesAndNamesOfAllConnections(User);
        for (let i = 0; i < userRepos.length; i++) {
            const { name: provinceId, repository: repo } = userRepos[i];

            const updateResult = await repo
                .createQueryBuilder()
                .update()
                .set({
                    total_monthly_villfood_orders: 0,
                })
                .where('total_monthly_villfood_orders > 0')
                .execute();

            this.logger.log(`[${provinceId}] ${updateResult}`);
        }
    }

    /*  ('0 0 * * 1', {
        name: ECronjobs.WeeklyUserStatistic,
        timeZone: VietNamTimeZone,
    }) */
    async userStatistic() {
        const allProvinceIds = DatabaseService.getAllProvinceIds();
        for (let i = 0; i < allProvinceIds.length; i++) {
            const provinceId = allProvinceIds[i];
            await this.queue.add(JobQueue.USER_QUEUE.PROCESS.USER_STATISTIC, { provinceId });
        }
    }
}
