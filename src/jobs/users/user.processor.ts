import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';
import { SyncUserJobDto } from './dto/syncUserJob.dto';
import { SyncUserToProvinceDto } from './dto/syncUserToProvince.dto';
import { UserStatisticJobDto } from './dto/userStatisticJob.dto';
import { UserStatisticService } from './services/userStatistic.service';
import { UserSyncService } from './services/userSync.service';
import { UserJobTask } from './userJobs.task';
const { USER_QUEUE } = JobQueue;

@Processor(USER_QUEUE.PROCESSOR)
export class UserProcessor {
    private logger = new Logger(UserProcessor.name);
    constructor(
        private readonly userSyncService: UserSyncService,
        private readonly userStatisticService: UserStatisticService,
        private readonly userJobTask: UserJobTask,
    ) {}

    @Process(JobQueue.USER_QUEUE.PROCESS.SYNC_NEW_DATA)
    async syncNewDataHandler(job: Job<SyncUserJobDto>) {
        this.logger.log(`Start sync user ${job.data.id} from province ${job.data.provinceId}`);
        await this.userSyncService.syncUserData(job.data.id, job.data.provinceId);
    }

    @Process(JobQueue.USER_QUEUE.PROCESS.SYNC_NEW_USER)
    async syncNewUserHandler(job: Job<SyncUserJobDto>) {
        this.logger.log(`Start sync user ${job.data.id} from province ${job.data.provinceId}`);
        await this.userSyncService.syncNewUser(job.data.id, job.data.provinceId);
    }

    @Process(JobQueue.USER_QUEUE.PROCESS.USER_STATISTIC)
    async userStatisticHandler(job: Job<UserStatisticJobDto>) {
        this.logger.log(`Start user statistic from province ${job.data.provinceId}`);
        await this.userStatisticService.userStatisticByProvinceId(job.data.provinceId);
    }

    @Process(JobQueue.USER_QUEUE.PROCESS.SYNC_TO_PROVINCE)
    async syncToProvinceHandler(job: Job<SyncUserToProvinceDto>) {
        try {
            this.logger.log(`Start sync user ${job.data.id} from province ${job.data.provinceId}`);
            await this.userSyncService.syncUserToProvinceByUserId(job.data.id, job.data.provinceId);
        } catch (error) {
            this.logger.error(`Error sync user ${job.data.id} from province ${job.data.provinceId} | ${error.message}`);
        }
    }
}
