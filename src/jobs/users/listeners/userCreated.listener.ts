import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Queue } from 'bull';
import { EUserEventNames } from 'src/events/constant';
import { UserCreatedEvent, UserUpdatedEvent } from 'src/events/dto';
import { JobQueue } from 'src/jobs';
import { SyncUserJobDto } from '../dto/syncUserJob.dto';
import { IGNORE_PROVINCE_IDS } from '../userJob.constant';

@Injectable()
export class UserListenerService {
    constructor(@InjectQueue(JobQueue.USER_QUEUE.PROCESSOR) private queue: Queue) {}

    // @OnEvent(EUserEventNames.USER_CREATED, { async: true })
    // async handleUserCreatedEvent({ user }: UserCreatedEvent) {
    //     if (user && !IGNORE_PROVINCE_IDS.includes(user.province_id)) {
    //         const jobData: SyncUserJobDto = {
    //             id: user.id,
    //             provinceId: user.province_id,
    //         };
    //         this.queue.add(JobQueue.USER_QUEUE.PROCESS.SYNC_NEW_USER, jobData, {
    //             removeOnComplete: 100,
    //             lifo: true, // last in first out
    //         });
    //     }
    // }

    // @OnEvent(EUserEventNames.USER_UPDATED, { async: true })
    // handleUserUpdatedEvent({ user, provinceId }: UserUpdatedEvent) {
    //     if (user && provinceId && !IGNORE_PROVINCE_IDS.includes(provinceId)) {
    //         const jobData: SyncUserJobDto = {
    //             id: user.id,
    //             provinceId,
    //         };
    //         this.queue.add(JobQueue.USER_QUEUE.PROCESS.SYNC_NEW_DATA, jobData, {
    //             removeOnComplete: 100,
    //             lifo: true, // last in first out
    //         });
    //     }
    // }
}
