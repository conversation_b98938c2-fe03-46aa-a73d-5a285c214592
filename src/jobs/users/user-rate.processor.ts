import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';
import { UserOrdersRatingService } from './services/user-orders-rating.service';
const { USER_RATE_QUEUE } = JobQueue;

@Processor(USER_RATE_QUEUE.PROCESSOR)
export class UserRateProcessor {
    private logger = new Logger(UserRateProcessor.name);
    constructor(private userOrdersRatingService: UserOrdersRatingService) {}

    @Process(JobQueue.USER_RATE_QUEUE.PROCESS.ORDERS_RATING_BY_PROVINCE)
    async userOrdersRatingByProvinceId(
        job: Job<{
            provinceId: number;
            parentProvinceId: number;
            dateOfStats: string;
        }>,
    ) {
        try {
            const { provinceId, parentProvinceId, dateOfStats } = job.data;
            this.logger.log(`[userOrdersRatingByProvinceId]: data: ${JSON.stringify(job.data)}`);
            await this.userOrdersRatingService.userOrdersRatingByProvinceId(provinceId, parentProvinceId, dateOfStats);
        } catch (error) {
            this.logger.error(`Error user orders rating from province ${job.data.provinceId} | ${error.message}`);
        }
    }

    @Process(JobQueue.USER_RATE_QUEUE.PROCESS.ORDERS_RATING)
    async userOrdersRatingByUserId(
        job: Job<{ dateOfStats: string; userId: number; provinceId: number; parentProvinceId: number }>,
    ) {
        try {
            const { userId, provinceId, parentProvinceId, dateOfStats } = job.data;
            this.logger.log(`[userOrdersRatingByUserId]: data: ${JSON.stringify(job.data)}`);
            this.userOrdersRatingService.userOrdersRatingByUserId(userId, provinceId, parentProvinceId, dateOfStats);
            return;
        } catch (error) {
            this.logger.error(`Error user orders rating from user ${job.data.userId} | ${error.message}`);
        }
    }
}
