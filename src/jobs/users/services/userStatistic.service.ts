import { Injectable, Logger } from '@nestjs/common';
import { EUserStatisticInterval, UserStatistic } from 'src/entities/userStatistic.entity';
import { DEFAULT_PROVINCE_ID, DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import * as moment from 'moment';
import { User } from 'src/entities/user.entity';
import { VietNamTimeZoneNum } from 'src/jobs';
@Injectable()
export class UserStatisticService {
    private logger = new Logger(UserStatisticService.name);

    async userStatisticByProvinceId(provinceId: number) {
        try {
            let totalUsers: number;
            if (provinceId == VUNGTAU_PROVINCE_ID) {
                totalUsers = await DatabaseService.getRepositoryByProvinceId(User, provinceId.toString()).count({
                    where: { province_id: provinceId },
                });
            } else {
                totalUsers = await DatabaseService.getRepositoryByProvinceId(
                    User,
                    DEFAULT_PROVINCE_ID.toString(),
                ).count({
                    where: { province_id: provinceId },
                });
            }

            const lastWeekMomentTZ7 = moment().utcOffset(VietNamTimeZoneNum).subtract(1, 'week');
            const week = lastWeekMomentTZ7.isoWeek();
            const year = lastWeekMomentTZ7.year();
            const startDate = lastWeekMomentTZ7.startOf('isoWeek').format('YYYY-MM-DD');
            const endDate = lastWeekMomentTZ7.endOf('isoWeek').format('YYYY-MM-DD');
            await DatabaseService.getRepositoryByDefaultConnection(UserStatistic).insert(
                new UserStatistic({
                    interval_type: EUserStatisticInterval.WEEK,
                    week,
                    from_date: startDate,
                    to_date: endDate,
                    total_users: totalUsers,
                    province_id: provinceId,
                    year,
                }),
            );
        } catch (error) {
            this.logger.error(`[userStatisticByProvinceId] | message: ${error.message} | stack: ${error.stack}`);
        }
    }
}
