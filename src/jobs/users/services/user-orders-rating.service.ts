import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import * as moment from 'moment';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { Order } from 'src/entities/order.entity';
import { UserOrdersRating } from 'src/entities/user-orders-rating.entity';
import { User } from 'src/entities/user.entity';
import { JobQueue } from 'src/jobs';
import { DEFAULT_PROVINCE_ID, DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { In, IsNull } from 'typeorm';

@Injectable()
export class UserOrdersRatingService {
    private logger = new Logger(UserOrdersRatingService.name);
    constructor(@InjectQueue(JobQueue.USER_RATE_QUEUE.PROCESSOR) private readonly queue: Queue) {}

    async userOrdersRatingByProvinceId(provinceId: number, parentProvinceId: number, dateOfStats: string) {
        try {
            let queryBuilder = DatabaseService.getRepositoryByProvinceId(Order, provinceId.toString());
            if (parentProvinceId) {
                queryBuilder = DatabaseService.getRepositoryByProvinceId(Order, parentProvinceId.toString());
            }
            const users = await queryBuilder
                .createQueryBuilder('order')
                .select('DISTINCT(order.user_id)', 'user_id')
                .where('order.order_date = :orderDate', {
                    orderDate: dateOfStats,
                    province_id: provinceId,
                })
                .getRawMany();

            const userIds = users.map((user) => user.user_id);

            for (let i = 0; i < userIds.length; i++) {
                const userId = userIds[i];
                const jobId = generateJobIdByProcessName(
                    JobQueue.USER_RATE_QUEUE.PROCESS.ORDERS_RATING,
                    userId.toString(),
                    provinceId.toString(),
                    dateOfStats,
                );

                const currJob = await this.queue.getJob(jobId);
                if (currJob) {
                    const jobStatus = await currJob.getState();
                    if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                        await currJob.remove();
                    } else {
                        return;
                    }
                }

                await this.queue.add(
                    JobQueue.USER_RATE_QUEUE.PROCESS.ORDERS_RATING,
                    {
                        userId,
                        provinceId,
                        parentProvinceId,
                        dateOfStats,
                    },
                    {
                        removeOnComplete: false,
                        attempts: 3,
                        jobId,
                    },
                );
            }
        } catch (error) {
            this.logger.error(`[userOrdersRatingByProvinceId] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    async userOrdersRatingByUserId(userId: number, provinceId: number, parentProvinceId: number, dateOfStats: string) {
        try {
            return await this.calculateUserOrdersRating(userId, provinceId, parentProvinceId, dateOfStats);
        } catch (error) {
            this.logger.error(`[userOrdersRatingByUserId] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    async calculateUserOrdersRating(userId: number, provinceId: number, parentProvinceId: number, dateOfStats: string) {
        try {
            let orderRepo = DatabaseService.getRepositoryByProvinceId(Order, provinceId.toString());
            if (parentProvinceId) {
                orderRepo = DatabaseService.getRepositoryByProvinceId(Order, parentProvinceId.toString());
            }

            const total_orders = await orderRepo.count({
                where: {
                    user_id: userId,
                    order_date: dateOfStats,
                    province_id: provinceId,
                },
            });
            if (total_orders === 0) {
                return;
            }
            const total_orders_completed = await orderRepo.count({
                where: {
                    user_id: userId,
                    order_date: dateOfStats,
                    order_status_id: 5,
                    province_id: provinceId,
                },
            });
            const total_orders_bombed = await orderRepo.count({
                where: {
                    user_id: userId,
                    order_date: dateOfStats,
                    order_status_id: 6,
                    cancellation_id: 12,
                    province_id: provinceId,
                },
            });
            const total_orders_cancelled = await orderRepo.count({
                where: {
                    user_id: userId,
                    order_date: dateOfStats,
                    order_status_id: 6,
                    cancellation_id: In([1, 2, 5]),
                    province_id: provinceId,
                },
            });

            const userOrdersRating = await DatabaseService.getRepositoryByProvinceId(
                UserOrdersRating,
                DEFAULT_PROVINCE_ID.toString(),
            ).findOne({
                where: {
                    user_id: userId,
                    province_id: provinceId,
                    date_of_stats: dateOfStats,
                },
            });

            if (userOrdersRating) {
                const temp = await DatabaseService.getRepositoryByProvinceId(
                    UserOrdersRating,
                    DEFAULT_PROVINCE_ID.toString(),
                ).update(
                    {
                        user_id: userId,
                        province_id: provinceId,
                        date_of_stats: dateOfStats,
                    },
                    {
                        total_orders,
                        total_orders_bombed,
                        total_orders_completed,
                        total_orders_cancelled,
                    },
                );
            } else {
                await DatabaseService.getRepositoryByProvinceId(
                    UserOrdersRating,
                    DEFAULT_PROVINCE_ID.toString(),
                ).insert({
                    user_id: userId,
                    province_id: provinceId,
                    total_orders,
                    total_orders_bombed,
                    total_orders_completed,
                    total_orders_cancelled,
                    date_of_stats: dateOfStats,
                });
            }

            const userOrdersRatingTotal = await DatabaseService.getRepositoryByProvinceId(
                UserOrdersRating,
                DEFAULT_PROVINCE_ID.toString(),
            ).findOne({
                where: {
                    user_id: userId,
                    province_id: provinceId,
                    date_of_stats: IsNull(),
                },
            });

            if (userOrdersRatingTotal) {
                const temp = await DatabaseService.getRepositoryByProvinceId(
                    UserOrdersRating,
                    DEFAULT_PROVINCE_ID.toString(),
                ).update(
                    {
                        user_id: userId,
                        province_id: provinceId,
                        date_of_stats: IsNull(),
                    },
                    {
                        total_orders: () => `total_orders + ${total_orders}`,
                        total_orders_bombed: () => `total_orders_bombed + ${total_orders_bombed}`,
                        total_orders_completed: () => `total_orders_completed + ${total_orders_completed}`,
                        total_orders_cancelled: () => `total_orders_cancelled + ${total_orders_cancelled}`,
                    },
                );
            } else {
                await DatabaseService.getRepositoryByProvinceId(
                    UserOrdersRating,
                    DEFAULT_PROVINCE_ID.toString(),
                ).insert({
                    user_id: userId,
                    province_id: provinceId,
                    total_orders,
                    total_orders_bombed,
                    total_orders_completed,
                    total_orders_cancelled,
                });
            }

            return;
        } catch (error) {
            this.logger.error(`[calculateUserOrdersRating] | message: ${error.message} | stack: ${error.stack}`);
        }
    }
}
