import { Injectable, Logger } from '@nestjs/common';
import { User } from 'src/entities/user.entity';
import { ProvinceService } from 'src/models/province/province.service';
import { DEFAULT_PROVINCE_ID, DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { EntityManager } from 'typeorm';
import { IGNORE_PROVINCE_IDS } from '../userJob.constant';
import { UserMgapaySyncService } from './userMgapaySync.service';
import { UserRoleSyncService } from './userRoleSync.service';

@Injectable()
export class UserSyncService {
    private logger = new Logger(UserSyncService.name);
    constructor(
        private provinceService: ProvinceService,
        private userRoleSyncService: UserRoleSyncService,
        private userMgaPaymentIcTokenSyncService: UserMgapaySyncService,
    ) {}

    private async findOneById(id: number, provinceId: string) {
        return await this.findOneByIdWithTransactionWrapper(
            DatabaseService.getConnectionByProvinceId(provinceId).manager,
            id,
        );
    }

    private async findOneByIdWithTransactionWrapper(entityManager: EntityManager, id: number) {
        return await entityManager
            .getRepository(User)
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.roles', 'roles')
            .leftJoinAndSelect('user.mgIcPaymentTokens', 'mgIcPaymentTokens')
            .where('user.id = :id', { id })
            .getOne();
    }

    private async updateByUserIdWithTransactionWrapper(entityManager: EntityManager, userId: number, user: User) {
        return await entityManager
            .getRepository(User)
            .createQueryBuilder()
            .update()
            .set({
                name: user.name,
                avatar: user.avatar,
                email: user.email,
                phone: user.phone,
                password: user.password,
                api_token: user.api_token,
                braintree_id: user.braintree_id,
                paypal_email: user.paypal_email,
                stripe_id: user.stripe_id,
                card_brand: user.card_brand,
                card_last_four: user.card_last_four,
                trial_ends_at: user.trial_ends_at,
                device_token: user.device_token,
                player_id_onesignal: user.player_id_onesignal,
                referral_code: user.referral_code,
                referrer_id: user.referrer_id,
                lat: user.lat,
                lng: user.lng,
                is_locked: user.is_locked,
                province_id: user.province_id,
                is_email_confirmed: user.is_email_confirmed,
                created_at: user.created_at,
                updated_at: user.updated_at,
            })
            .where('id = :userId', { userId })
            .execute();
    }

    private async syncUserToProvince(user: User, provinceId: string): Promise<User> {
        try {
            const userExisting = await this.findOneById(user.id, provinceId);
            if (!userExisting) {
                return await this.createOneToProvince(user, provinceId);
            } else {
                return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(
                    async (entityManager) => {
                        const updateResult = await this.updateByUserIdWithTransactionWrapper(
                            entityManager,
                            user.id,
                            user,
                        );
                        if (!updateResult.affected) {
                            throw new Error('Update user failed in transaction');
                        }

                        const updateRolesResult =
                            await this.userRoleSyncService.syncRolesToProvinceWithTransactionWrapper(
                                entityManager,
                                user.id,
                                user.roles,
                            );
                        this.logger.log(
                            `Sync roles result: ${updateRolesResult ? JSON.stringify(updateRolesResult) : null}`,
                        );
                        const syncMgapaymentIcTokenResult =
                            await this.userMgaPaymentIcTokenSyncService.asyncMgpayIcPaymentTokensWithTransactionWrapper(
                                entityManager,
                                user.id,
                                user.mgIcPaymentTokens,
                            );
                        this.logger.log(`Sync mgapayment ic token result: ${syncMgapaymentIcTokenResult}`);
                        return await this.findOneByIdWithTransactionWrapper(entityManager, user.id);
                    },
                );
            }
        } catch (error) {
            this.logger.error(`[syncUserToProvince] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    private async createOneToProvince(user: User, provinceId: string): Promise<User> {
        const newUser = new User(
            user.name,
            user.email,
            user.phone,
            user.password,
            user.province_id,
            user.is_locked,
            user.referral_code,
        );
        newUser.avatar = user.avatar;
        newUser.api_token = user.api_token;
        newUser.braintree_id = user.braintree_id;
        newUser.paypal_email = user.paypal_email;
        newUser.stripe_id = user.stripe_id;
        newUser.card_brand = user.card_brand;
        newUser.card_last_four = user.card_last_four;
        newUser.trial_ends_at = user.trial_ends_at;
        newUser.device_token = user.device_token;
        newUser.player_id_onesignal = user.player_id_onesignal;
        newUser.referral_code = user.referral_code;
        newUser.referrer_id = user.referrer_id;
        newUser.lat = user.lat;
        newUser.lng = user.lng;
        newUser.is_email_confirmed = user.is_email_confirmed;
        newUser.created_at = user.created_at;
        newUser.updated_at = user.updated_at;
        newUser.id = user.id;
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            const userCreated = await entityManager.getRepository(User).save(newUser);
            await this.userRoleSyncService.addRolesByUserIdWithTransactionWrapper(
                entityManager,
                user.roles,
                userCreated.id,
            );
            return await this.findOneByIdWithTransactionWrapper(entityManager, userCreated.id);
        });
    }

    private async filterProvinceIdsToSync(provinceId: number): Promise<number[]> {
        const mainProvinces = await this.provinceService.getMainProvinces();
        return mainProvinces
            .map((province) => province.id)
            .filter((id) => id !== provinceId && !IGNORE_PROVINCE_IDS.includes(id));
    }

    async syncUserData(userId: number, provinceId: number): Promise<void> {
        const provinceIdStr = provinceId.toString();
        const user = await this.findOneById(userId, provinceIdStr);
        const provinceIdsToSync = await this.filterProvinceIdsToSync(provinceId);
        for (let i = 0; i < provinceIdsToSync.length; i++) {
            const syncSuccess = await this.syncUserToProvince(user, provinceIdsToSync[i].toString());
            this.logger.log(
                `Sync user ${userId} to province ${provinceIdsToSync[i]} ${syncSuccess ? 'success' : 'failed'}`,
            );
        }
    }

    async syncUserToProvinceByUserId(userId: number, targetProvinceId: number): Promise<void> {
        if (targetProvinceId === DEFAULT_PROVINCE_ID || VUNGTAU_PROVINCE_ID === targetProvinceId) {
            this.logger.error(`Cannot sync user ${userId} to province ${targetProvinceId}`);
            return;
        }
        const user = await this.findOneById(userId, DEFAULT_PROVINCE_ID.toString());

        if (!user) {
            this.logger.error(`User ${userId} not found in default province`);
            return;
        }

        const syncSuccess = await this.syncUserToProvince(user, targetProvinceId.toString());
        this.logger.log(`Sync user ${userId} to province ${targetProvinceId} ${syncSuccess ? 'success' : 'failed'}`);
    }

    async syncNewUser(userId: number, provinceId: number) {
        const provinceIdStr = provinceId.toString();
        const user = await this.findOneById(userId, provinceIdStr);
        const provinceIdsToSync = await this.filterProvinceIdsToSync(provinceId);
        for (let i = 0; i < provinceIdsToSync.length; i++) {
            try {
                const syncSuccess = await this.createOneToProvince(user, provinceIdsToSync[i].toString());
                this.logger.log(
                    `Sync new user ${userId} to province ${provinceIdsToSync[i]} ${syncSuccess ? 'success' : 'failed'}`,
                );
            } catch (err) {
                this.logger.error(
                    `[syncNewUser] Sync new user ${userId} to province ${provinceIdsToSync[i]} failed | message: ${err.message} | stack: ${err.stack}`,
                );
            }
        }
    }
}
