import { Injectable, Logger } from '@nestjs/common';
import { ModelType } from 'src/common/constants';
import * as modelHasRoleHelper from 'src/common/helpers/modelHasRole.helper';
import { ModelHasRole } from 'src/entities/modelHasRole.entity';
import { Role } from 'src/entities/role.entity';
import { DeleteResult, EntityManager } from 'typeorm';

@Injectable()
export class UserRoleSyncService {
    private logger = new Logger(UserRoleSyncService.name);

    private findAllRolesWithTransactionWrapper(entityManager: EntityManager) {
        return entityManager.getRepository(Role).find();
    }

    private findRolesByNamesWithTransactionWrapper(entityManager: EntityManager, names: string[]) {
        return entityManager
            .getRepository(Role)
            .createQueryBuilder('role')
            .where('role.name IN (:...names)', { names })
            .getMany();
    }

    private async findRolesByUserIdWithTransactionWrapper(entityManager: EntityManager, userId: number) {
        return await entityManager
            .getRepository(Role)
            .createQueryBuilder('role')
            .innerJoin(
                'role.modelHasRoles',
                'modelHasRoles',
                'modelHasRoles.role_id = role.id AND modelHasRoles.model_id = :userId',
                {
                    userId,
                },
            )
            .getMany();
    }

    async addRolesByUserIdWithTransactionWrapper(
        entityManager: EntityManager,
        roles: Role[],
        userId: number,
    ): Promise<ModelHasRole[]> {
        const modelHasRole = roles.map((role) => {
            const modelHasRole = new ModelHasRole();
            modelHasRole.id = modelHasRoleHelper.generateModelId(userId, role.id);
            modelHasRole.model_id = userId;
            modelHasRole.role_id = role.id;
            modelHasRole.model_type = ModelType.USER;
            return modelHasRole;
        });
        return await entityManager.getRepository(ModelHasRole).save(modelHasRole);
    }

    private async removeRoleByUserIdWithTransactionWrapper(
        entityManager: EntityManager,
        role: Role,
        userId: number,
    ): Promise<DeleteResult> {
        return await entityManager
            .getRepository(ModelHasRole)
            .createQueryBuilder()
            .delete()
            .where('model_id = :userId', { userId })
            .andWhere('role_id = :roleId', { roleId: role.id })
            .execute();
    }

    private filterMissingRolesByName(roles: Role[], rolesExisting: Role[]): Role[] {
        return roles.filter((role) => !rolesExisting.some((roleExisting) => roleExisting.name === role.name));
    }

    async syncRolesToProvinceWithTransactionWrapper(
        entityManager: EntityManager,
        userId: number,
        rootRoles: Role[],
    ): Promise<Role[]> {
        const rolesExisting = await this.findRolesByUserIdWithTransactionWrapper(entityManager, userId);

        const missingRoles = this.filterMissingRolesByName(rootRoles, rolesExisting);
        const removedRoles = this.filterMissingRolesByName(rolesExisting, rootRoles);

        if (missingRoles && missingRoles.length > 0) {
            const addRoles = await this.findRolesByNamesWithTransactionWrapper(
                entityManager,
                missingRoles.map((role) => role.name),
            );
            const modelHasRoles = await this.addRolesByUserIdWithTransactionWrapper(entityManager, addRoles, userId);
            if (modelHasRoles && modelHasRoles.length > 0) {
                this.logger.log(`Added ${modelHasRoles.length} roles to user ${userId}`);
            } else {
                this.logger.log(`No roles added to user ${userId}`);
            }
        }
        if (removedRoles && removedRoles.length > 0) {
            for (const role of removedRoles) {
                const deleteResult = await this.removeRoleByUserIdWithTransactionWrapper(entityManager, role, userId);
                if (deleteResult && deleteResult.affected > 0) {
                    this.logger.log(`Removed role ${role.name} from user ${userId}`);
                } else {
                    this.logger.log(`No role ${role.name} removed from user ${userId}`);
                }
            }
        }
        return await this.findRolesByUserIdWithTransactionWrapper(entityManager, userId);
    }
}
