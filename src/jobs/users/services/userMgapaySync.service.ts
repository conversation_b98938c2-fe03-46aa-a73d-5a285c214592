import { Injectable, Logger } from '@nestjs/common';
import { MgpayIcPaymentToken } from 'src/entities/mgpayIcPaymentToken.entity';
import { EntityManager, InsertResult } from 'typeorm';

@Injectable()
export class UserMgapaySyncService {
    private logger = new Logger(UserMgapaySyncService.name);
    private async findByUserIdWithTransactionWrapper(entityManager: EntityManager, userId: number) {
        return await entityManager
            .getRepository(MgpayIcPaymentToken)
            .createQueryBuilder()
            .where('user_id = :userId', { userId })
            .getMany();
    }

    private async deleteByUserIdAndCardNoWithTransactionWrapper(
        entityManager: EntityManager,
        userId: number,
        cardNo: string,
    ) {
        return await entityManager
            .getRepository(MgpayIcPaymentToken)
            .createQueryBuilder()
            .delete()
            .where('user_id = :userId', { userId })
            .andWhere('card_no = :cardNo', { cardNo })
            .execute();
    }

    private async updateTokenByIdWithTransactionWrapper(entityManager: EntityManager, id: number, token: string) {
        return await entityManager
            .getRepository(MgpayIcPaymentToken)
            .createQueryBuilder()
            .update()
            .set({
                pay_token: token,
            })
            .where('id = :id', { id })
            .execute();
    }

    private isSameMgpayIcPaymentToken(
        mgpayIcPaymentTokenExisting: MgpayIcPaymentToken,
        mgpayIcPaymentToken: MgpayIcPaymentToken,
    ): boolean {
        return (
            mgpayIcPaymentTokenExisting.pay_token === mgpayIcPaymentToken.pay_token &&
            mgpayIcPaymentTokenExisting.payment_method_id === mgpayIcPaymentToken.payment_method_id
        );
    }

    private async insertWithTransactionWrapper(
        entityManager: EntityManager,
        mgpayIcPaymentToken: MgpayIcPaymentToken,
    ): Promise<InsertResult> {
        return await entityManager
            .getRepository(MgpayIcPaymentToken)
            .createQueryBuilder()
            .insert()
            // .into(MgpayIcPaymentToken)
            .values({
                user_id: mgpayIcPaymentToken.user_id,
                card_no: mgpayIcPaymentToken.card_no,
                pay_token: mgpayIcPaymentToken.pay_token,
                payment_method_id: mgpayIcPaymentToken.payment_method_id,
            })
            .execute();
    }

    private async insertOrUpdateMgpayIcPaymentTokensWithTransactionWrapper(
        entityManager: EntityManager,
        userId: number,
        sourceMgpayIcPaymentTokens: MgpayIcPaymentToken[],
        targetMgpayIcPaymentTokens: MgpayIcPaymentToken[],
    ): Promise<boolean> {
        try {
            for (let i = 0; i < sourceMgpayIcPaymentTokens.length; i++) {
                const targetMgpayIcPaymentTokenMatched = targetMgpayIcPaymentTokens.find(
                    (mgpayIcPaymentToken) =>
                        mgpayIcPaymentToken.card_no === sourceMgpayIcPaymentTokens[i].card_no &&
                        mgpayIcPaymentToken.payment_method_id === sourceMgpayIcPaymentTokens[i].payment_method_id,
                );
                if (!targetMgpayIcPaymentTokenMatched) {
                    await this.insertWithTransactionWrapper(entityManager, sourceMgpayIcPaymentTokens[i]);
                } else {
                    const isAlreadyUpdated = this.isSameMgpayIcPaymentToken(
                        sourceMgpayIcPaymentTokens[i],
                        targetMgpayIcPaymentTokenMatched,
                    );
                    if (!isAlreadyUpdated) {
                        await this.updateTokenByIdWithTransactionWrapper(
                            entityManager,
                            targetMgpayIcPaymentTokenMatched.user_id,
                            sourceMgpayIcPaymentTokens[i].pay_token,
                        );
                    }
                }
            }
            return true;
        } catch (error) {
            this.logger.error(
                `[insertOrUpdateMgpayIcPaymentTokensWithTransactionWrapper] | message: ${error.message} | stack: ${error.stack}`,
            );
            return false;
        }
    }

    private async deleteMgpayIcPaymentTokensWithTransactionWrapper(
        entityManager: EntityManager,
        userId: number,
        sourceMgpayIcPaymentTokens: MgpayIcPaymentToken[],
        targetMgpayIcPaymentTokens: MgpayIcPaymentToken[],
    ): Promise<boolean> {
        try {
            for (let i = 0; i < targetMgpayIcPaymentTokens.length; i++) {
                const mgpayIcPaymentTokenMatched = sourceMgpayIcPaymentTokens.find(
                    (mgpayIcPaymentToken) => mgpayIcPaymentToken.card_no === targetMgpayIcPaymentTokens[i].card_no,
                );
                if (!mgpayIcPaymentTokenMatched) {
                    await this.deleteByUserIdAndCardNoWithTransactionWrapper(
                        entityManager,
                        userId,
                        targetMgpayIcPaymentTokens[i].card_no,
                    );
                }
            }
            return true;
        } catch (error) {
            this.logger.error(
                `[deleteMgpayIcPaymentTokensWithTransactionWrapper] | message: ${error.message} | stack: ${error.stack}`,
            );
            return false;
        }
    }

    async asyncMgpayIcPaymentTokensWithTransactionWrapper(
        entityManager: EntityManager,
        userId: number,
        mgpayIcPaymentTokens: MgpayIcPaymentToken[],
    ): Promise<boolean> {
        try {
            const mgpayIcPaymentTokensExisting = await this.findByUserIdWithTransactionWrapper(entityManager, userId);
            const updateOrInsertResult = await this.insertOrUpdateMgpayIcPaymentTokensWithTransactionWrapper(
                entityManager,
                userId,
                mgpayIcPaymentTokens,
                mgpayIcPaymentTokensExisting,
            );
            if (!updateOrInsertResult) {
                return false;
            } else {
                return await this.deleteMgpayIcPaymentTokensWithTransactionWrapper(
                    entityManager,
                    userId,
                    mgpayIcPaymentTokensExisting,
                    mgpayIcPaymentTokensExisting,
                );
            }
        } catch (error) {
            this.logger.error(
                `[asyncMgpayIcPaymentTokensWithTransactionWrapper] | message: ${error.message} | stack: ${error.stack}`,
            );
            return false;
        }
    }
}
