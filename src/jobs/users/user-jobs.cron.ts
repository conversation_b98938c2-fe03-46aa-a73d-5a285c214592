import { Injectable, Logger } from '@nestjs/common';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { JobQueue, VietNamTimeZone } from '..';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { UserJobTask } from './userJobs.task';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UserJobCron {
    private readonly logger = new Logger(UserJobCron.name);

    constructor(
        @InjectQueue(JobQueue.CRON_JOB.PROCESSOR)
        private readonly cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async resetMonthOrderInfo() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(JobQueue.CRON_JOB.PROCESS.CRON_RESET_MONTH_ORDER_INFO);
            this.logger.log(`[resetMonthOrderInfo] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === JobQueue.CRON_JOB.PROCESS.CRON_RESET_MONTH_ORDER_INFO) {
                        this.logger.log(`[resetMonthOrderInfo] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(JobQueue.CRON_JOB.PROCESS.CRON_RESET_MONTH_ORDER_INFO, null, {
                jobId,
                repeat: {
                    cron: CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT,
                    tz: VietNamTimeZone,
                    key: 'CRON_RESET_MONTH_ORDER_INFO',
                },
                removeOnComplete: true,
                attempts: 5,
                backoff: 3000,
            });
        } catch (error) {
            this.logger.error(`[resetMonthOrderInfo] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async userStatistic() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(JobQueue.CRON_JOB.PROCESS.CRON_USER_STATISTIC);
            this.logger.log(`[userStatistic] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === JobQueue.CRON_JOB.PROCESS.CRON_USER_STATISTIC) {
                        this.logger.log(`[userStatistic] Remove job ${job.name}`);

                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                JobQueue.CRON_JOB.PROCESS.CRON_USER_STATISTIC,
                {},
                {
                    jobId,
                    repeat: {
                        cron: '0 0 * * 1',
                        tz: VietNamTimeZone,
                        key: 'CRON_USER_STATISTIC',
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`Error userStatistic | ${error.message}`);
        }
    }
}

@Processor(JobQueue.CRON_JOB.PROCESSOR)
export class UserJobsCronProcessor {
    private logger = new Logger(UserJobsCronProcessor.name);
    constructor(private readonly userJobTask: UserJobTask) {}

    @Process(JobQueue.CRON_JOB.PROCESS.CRON_RESET_MONTH_ORDER_INFO)
    async resetMonthOrderInfo(job: Job) {
        try {
            this.logger.log(`[CRON] Reset month order info`);
            await this.userJobTask.resetMonthOrderInfo();
        } catch (error) {
            this.logger.error(`Error reset month order info | ${error.message}`);
        }
    }

    @Process(JobQueue.CRON_JOB.PROCESS.CRON_USER_STATISTIC)
    async userStatistic(job: Job) {
        try {
            this.logger.log(`[CRON] User statistic`);
            await this.userJobTask.userStatistic();
        } catch (error) {
            this.logger.error(`Error user statistic | ${error.message} ${job.data.id}`);
        }
    }
}
