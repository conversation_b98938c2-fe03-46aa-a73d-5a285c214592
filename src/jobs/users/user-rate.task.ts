import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, Timeout } from '@nestjs/schedule';
import { DatabaseService } from 'src/providers/database/database.service';
import { ECronjobs, JobQueue, VietNamTimeZone } from '..';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Province } from 'src/entities/province.entity';
import * as moment from 'moment';

@Injectable()
export class UserRateTask {
    constructor(@InjectQueue(JobQueue.USER_RATE_QUEUE.PROCESSOR) private queue: Queue) {}

    async userOrdersRating() {
        const activeProvinces = await DatabaseService.getRepositoryByDefaultConnection(Province).find({
            where: {
                is_active: 1,
            },
        });

        for (let i = 0; i < activeProvinces.length; i++) {
            const { id: provinceId, parent_id: parentProvinceId } = activeProvinces[i];
            const dateOfStats = moment().utc().add(7, 'hours').subtract(1, 'days').format('YYYY-MM-DD');
            await this.queue.add(
                JobQueue.USER_RATE_QUEUE.PROCESS.ORDERS_RATING_BY_PROVINCE,
                {
                    provinceId,
                    parentProvinceId,
                    dateOfStats,
                },
                {
                    removeOnComplete: 100,
                },
            );
        }
    }
}
