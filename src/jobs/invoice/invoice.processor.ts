import { OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { isObject } from 'class-validator';
import { Invoice } from 'src/entities/invoice.entity';
import { OrderService } from 'src/models/order/services/order.service';
import { OrderInvoiceService } from 'src/models/order/services/orderInvoice.service';
import { JobQueue } from '..';
import { EInvoiceJobExportOrderToMisa } from './invoice.interface';

const { INVOICE_QUEUE } = JobQueue;
const { PROCESS } = INVOICE_QUEUE;

@Processor(INVOICE_QUEUE.PROCESSOR)
export class InvoiceProcessor {
    private logger = new Logger(InvoiceProcessor.name);

    constructor(private orderService: OrderService, private orderInvoiceService: OrderInvoiceService) {}

    @Process(PROCESS.EXPORT_ORDER_USER_INVOICE_TO_MISA)
    async exportOrderInvoiceToMisa(job: Job<EInvoiceJobExportOrderToMisa>) {
        try {
            const { order_id, provinceId } = job.data;

            this.logger.log(
                `[exportOrderInvoiceToMisa] Starting invoice export for order ${order_id} in province ${provinceId}`,
            );

            await job.progress(10);

            const order = await this.orderService.getOrderById(order_id, ['driverExpense'], provinceId);
            if (!order) {
                throw new Error(`Order with ID ${order_id} not found`);
            }

            await job.progress(30);

            const invoices = await this.orderInvoiceService.createDeliveryFeeInvoice(order, provinceId);

            await job.progress(80);

            if (invoices) {
                this.logger.log(
                    `[exportOrderInvoiceToMisa] Successfully exported invoice ${invoices
                        .map((invoice) => invoice.ref_id)
                        .join(', ')} for order ${order_id}`,
                );
                await job.progress(100);
                return {
                    success: true,
                    invoice_ids: invoices.map((invoice) => invoice.id),
                    ref_ids: invoices.map((invoice) => invoice.ref_id),
                    statuses: invoices.map((invoice) => invoice.status),
                    transaction_ids: invoices.map((invoice) => invoice.transaction_id),
                };
            } else {
                throw new Error(`Failed to create invoice for order ${order_id}`);
            }
        } catch (error) {
            this.logger.error(
                `[exportOrderInvoiceToMisa] | order_id: ${job.data.order_id} | message: ${error.message} | stack: ${error.stack}`,
            );
            throw error;
        }
    }

    @Process(PROCESS.EXPORT_ORDER_COMPANY_INVOICE_TO_MISA)
    async exportOrderCompanyInvoiceToMisa(job: Job<EInvoiceJobExportOrderToMisa>) {
        try {
            const { order_id, provinceId } = job.data;

            this.logger.log(
                `[exportOrderCompanyInvoiceToMisa] Starting invoice export for order ${order_id} in province ${provinceId}`,
            );

            await job.progress(10);

            const order = await this.orderService.getOrderById(order_id, ['driverExpense'], provinceId);
            if (!order) {
                throw new Error(`Order with ID ${order_id} not found`);
            }

            await job.progress(30);

            let invoices: Invoice[] = [];
            if (order.invoice_company_profile_id) {
                invoices = await this.orderInvoiceService.createCompanyInvoice(order, provinceId);
            } else {
                throw new Error(`Order ${order_id} does not have a company profile`);
            }
            await job.progress(80);

            if (invoices.length) {
                this.logger.log(
                    `[exportOrderCompanyInvoiceToMisa] Successfully exported invoice ${invoices
                        .map((invoice) => invoice.ref_id)
                        .join(', ')} for order ${order_id}`,
                );
                await job.progress(100);
                return {
                    success: true,
                    invoice_ids: invoices.map((invoice) => invoice.id),
                    ref_ids: invoices.map((invoice) => invoice.ref_id),
                    statuses: invoices.map((invoice) => invoice.status),
                    transaction_ids: invoices.map((invoice) => invoice.transaction_id),
                };
            } else {
                throw new Error(`Failed to create invoice for order ${order_id}`);
            }
        } catch (error) {
            this.logger.error(
                `[exportOrderInvoiceToMisa] | order_id: ${job.data.order_id} | message: ${error.message} | stack: ${error.stack}`,
            );
            throw error;
        }
    }

    @OnQueueCompleted()
    handleQueueCompleted(job: Job, result: any) {
        this.logger.log(job);
        this.logger.log(
            `[ SUCCESS ] JobId: ${job.id} | job name: ${job.name} | result: ${
                result && isObject(result) ? JSON.stringify(result) : null
            }`,
        );
    }

    @OnQueueFailed()
    handleQueueFailed(job: Job, err: Error) {
        this.logger.log(`[ FAILED ] JobId: ${job.id} | job name: ${job.name} | err: ${err ? err.message : null}`);
    }
}
