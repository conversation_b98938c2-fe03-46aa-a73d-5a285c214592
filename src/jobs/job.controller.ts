import { Controller, Get, UseGuards } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { CronJobDescriptions, ECronjobs } from '.';

import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('jobs')
@UseGuards(AuthGuard)
export class JobControllers {
    constructor(private schedulerRegistry: SchedulerRegistry) {}

    @Get()
    getCronJobs() {
        const list = [];
        this.schedulerRegistry.getCronJobs().forEach((cronjob, key) => {
            list.push({
                nextDate: cronjob.nextDate().toISOString(),
                name: CronJobDescriptions[key] || key,
                running: cronjob.running,
            });
        });

        return list;
    }
}
