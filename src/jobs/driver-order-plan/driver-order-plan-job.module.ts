import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JobQueue } from '..';
import { DriverOrderPlanJobCron } from './driver-order-plan-job.cron';
import { DriverOrderPlanJobProcessor } from './driver-order-plan-job.processor';
import { DriverOrderPlanModule } from 'src/models/driver-order-plan/driver-order-plan.module';

const { CRON_JOB } = JobQueue;

@Module({
    imports: [
        ConfigModule,
        BullModule.registerQueue({
            name: CRON_JOB.PROCESSOR,
        }),
        DriverOrderPlanModule,
    ],
    providers: [DriverOrderPlanJobCron, DriverOrderPlanJobProcessor],
    exports: [DriverOrderPlanJobCron, DriverOrderPlanJobProcessor],
})
export class DriverOrderPlanJobModule {}
