import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { Queue } from 'bull';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { JobQueue, VietNamTimeZone } from '..';

const { CRON_JOB } = JobQueue;

@Injectable()
export class DriverOrderPlanJobCron {
    private logger = new Logger(DriverOrderPlanJobCron.name);
    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR) private cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async initRecalculateCompletedOrdersJob() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_RECALCULATE_COMPLETED_ORDERS);
            this.logger.log(`[initRecalculateCompletedOrdersJob] Start job ${jobId}`);

            // Remove any existing delayed jobs with the same name
            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_RECALCULATE_COMPLETED_ORDERS) {
                        this.logger.log(`[initRecalculateCompletedOrdersJob] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_RECALCULATE_COMPLETED_ORDERS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_1AM,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_RECALCULATE_COMPLETED_ORDERS,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(
                `[initRecalculateCompletedOrdersJob] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}
