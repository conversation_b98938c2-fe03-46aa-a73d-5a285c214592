import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import * as moment from 'moment';
import { DatabaseService } from 'src/providers/database/database.service';
import { DriverDailyPlan } from 'src/entities/driver-daily-plan.entity';
import { JobQueue, VietNamTimeZoneNum } from '..';
import { DriverDailyPlanService } from 'src/models/driver-order-plan/services/driver-daily-plan.service';

const { CRON_JOB } = JobQueue;

@Processor(CRON_JOB.PROCESSOR)
export class DriverOrderPlanJobProcessor {
    private logger = new Logger(DriverOrderPlanJobProcessor.name);
    constructor(private readonly driverDailyPlanService: DriverDailyPlanService) {}

    @Process({
        name: CRON_JOB.PROCESS.CRON_RECALCULATE_COMPLETED_ORDERS,
        concurrency: 1,
    })
    async recalculateCompletedOrders(job: Job) {
        try {
            this.logger.log('[recalculateCompletedOrders] Starting job to recalculate completed orders for drivers');

            // Get yesterday's date in YYYY-MM-DD format
            const yesterday = moment().utcOffset(VietNamTimeZoneNum).subtract(1, 'day').format('YYYY-MM-DD');
            this.logger.log(`[recalculateCompletedOrders] Processing for date: ${yesterday}`);

            // Get all provinces
            const provinces = await DatabaseService.getAllProvinceIds();

            // Process each province
            for (const provinceId of provinces) {
                try {
                    this.logger.log(`[recalculateCompletedOrders] Processing province: ${provinceId}`);

                    // Get all daily plans for yesterday
                    const dailyPlans = await DatabaseService.getRepositoryByProvinceId(DriverDailyPlan, provinceId)
                        .createQueryBuilder('dailyPlan')
                        .where('dailyPlan.plan_date = :date', { date: yesterday })
                        .getMany();

                    this.logger.log(
                        `[recalculateCompletedOrders] Found ${dailyPlans.length} daily plans for province ${provinceId}`,
                    );

                    // Process each daily plan
                    for (const dailyPlan of dailyPlans) {
                        try {
                            this.logger.log(
                                `[recalculateCompletedOrders] Recalculating for daily plan ID: ${dailyPlan.id}`,
                            );
                            await this.driverDailyPlanService.recalculateCompletedOrders(dailyPlan.id, provinceId);
                        } catch (error) {
                            this.logger.error(
                                `[recalculateCompletedOrders] Error processing daily plan ID ${dailyPlan.id}: ${error.message}`,
                                error.stack,
                            );
                            // Continue with the next daily plan even if this one fails
                        }
                    }
                } catch (error) {
                    this.logger.error(
                        `[recalculateCompletedOrders] Error processing province ${provinceId}: ${error.message}`,
                        error.stack,
                    );
                    // Continue with the next province even if this one fails
                }
            }

            this.logger.log('[recalculateCompletedOrders] Completed job to recalculate completed orders for drivers');
        } catch (error) {
            this.logger.error(`[recalculateCompletedOrders] | message: ${error.message} | stack: ${error.stack}`);
            throw error;
        }
    }
}
