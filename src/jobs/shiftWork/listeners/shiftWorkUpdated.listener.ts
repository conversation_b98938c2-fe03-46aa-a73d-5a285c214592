import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EShiftWorkEventNames } from 'src/events/constant';
import { ShiftWorkUpdatedDto } from 'src/events/dto';
import { ShiftWorkJobService } from '../shiftWorkJob.service';

@Injectable()
export class ShiftWorkUpdatedListener {
    constructor(private shiftWorkJobService: ShiftWorkJobService) {}

    @OnEvent(EShiftWorkEventNames.SHIFT_WORK_UPDATED)
    async shiftWorkUpdatedHandler(body: ShiftWorkUpdatedDto) {
        await this.shiftWorkJobService.addJobToSendEndOfShiftEventByShiftWork(body.shiftWork, body.provinceId);
    }
}
