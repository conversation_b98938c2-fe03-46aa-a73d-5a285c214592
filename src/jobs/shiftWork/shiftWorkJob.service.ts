import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { ShiftWork } from 'src/entities/shiftWork.entity';
import * as moment from 'moment';
import * as jobHelpers from 'src/common/helpers/job.helpter';
import { JobQueue, VietNamTimeZoneNum } from '..';
import { shiftWorkEndJobDto } from './dto';

@Injectable()
export class ShiftWorkJobService {
    constructor(@InjectQueue(JobQueue.SHIFT_WORK_QUEUE.PROCESSOR) private shiftWorkQueue: Queue) {}

    async addJobToSendEndOfShiftEventByShiftWork(shiftWork: ShiftWork, provinceId: string) {
        const endOfShiftDto = new shiftWorkEndJobDto();
        endOfShiftDto.provinceId = provinceId;
        endOfShiftDto.shiftWork = shiftWork;
        const jobId = jobHelpers.generateJobIdByProcessName(
            JobQueue.SHIFT_WORK_QUEUE.PROCESS.SHIFT_WORK_END,
            provinceId,
            shiftWork.id,
        );
        this.shiftWorkQueue.getRepeatableJobs();
        const jobExisting = await this.shiftWorkQueue.getJob(jobId);
        if (jobExisting) {
            await jobExisting.remove();
        }
        if (shiftWork.end_time) {
            let delay = 0;
            const momentEndTime = moment(shiftWork.end_time, 'HH:mm:ss');
            const nextShiftEnd = moment()
                .utcOffset(VietNamTimeZoneNum)
                .set({
                    isoWeekday: shiftWork.day_of_week,
                    hour: momentEndTime.get('hour'),
                    minute: momentEndTime.get('minute'),
                    second: momentEndTime.get('second'),
                });
            const currentTime = new Date().valueOf();
            delay = nextShiftEnd.valueOf() - currentTime;
            if (delay < 0) {
                delay = nextShiftEnd.clone().add(1, 'week').valueOf() - currentTime;
            }
            const jobData: shiftWorkEndJobDto = {
                provinceId: provinceId,
                shiftWork: shiftWork,
            };
            const job = await this.shiftWorkQueue.add(JobQueue.SHIFT_WORK_QUEUE.PROCESS.SHIFT_WORK_END, jobData, {
                delay,
                jobId,
            });
            // this.logger.log(`create job done -> #${job.id} ${job.name} delay: ${delay / 60000} minutes`)
        }
    }

    async addJobToSendEndOfShiftEventByShiftWorks(shiftWorks: ShiftWork[], provinceId: string) {
        for (let i = 0; i < shiftWorks.length; i++) {
            const shiftWork = shiftWorks[i];
            await this.addJobToSendEndOfShiftEventByShiftWork(shiftWork, provinceId);
        }
    }
}
