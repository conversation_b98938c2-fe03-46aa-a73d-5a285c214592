import { OnQueueCompleted, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EventService } from 'src/events/event.service';
import { JobQueue } from '..';
import { shiftWorkEndJobDto } from './dto';
import { ShiftWorkJobService } from './shiftWorkJob.service';
const { SHIFT_WORK_QUEUE } = JobQueue;

@Processor(SHIFT_WORK_QUEUE.PROCESSOR)
export class ShiftWorkProcessor {
    private logger = new Logger(ShiftWorkProcessor.name);
    constructor(private eventEmitter: EventService, private shiftWorkJobService: ShiftWorkJobService) {}
    @Process(JobQueue.SHIFT_WORK_QUEUE.PROCESS.SHIFT_WORK_END)
    async endOfShiftHandler(job: Job<shiftWorkEndJobDto>) {
        const { data } = job;
        if (data) {
            this.logger.log(`End of shift -> ${JSON.stringify(data)}`);
            await this.eventEmitter.shiftWorkEndEvent(data.shiftWork, data.provinceId);
        }
    }

    @OnQueueCompleted({
        name: SHIFT_WORK_QUEUE.PROCESS.SHIFT_WORK_END,
    })
    onShiftEndJobCompleted(job: Job<shiftWorkEndJobDto>) {
        if (job.data) {
            this.shiftWorkJobService.addJobToSendEndOfShiftEventByShiftWork(job.data.shiftWork, job.data.provinceId);
        }
    }
}
