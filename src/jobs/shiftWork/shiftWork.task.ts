import { Injectable, Logger } from '@nestjs/common';
import { Timeout } from '@nestjs/schedule';
import { createRequestByProvince } from 'src/common/helpers/request.helper';
import { FetchShiftWorkDto } from 'src/models/shiftwork/dto/fetch-shift-work.dto';
import { ShiftWorkService } from 'src/models/shiftwork/shiftWork.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { ShiftWorkJobService } from './shiftWorkJob.service';

@Injectable()
export class ShiftWorkTask {
    private logger = new Logger(ShiftWorkTask.name);

    constructor(private shiftWorkService: ShiftWorkService, private shiftWorkJobService: ShiftWorkJobService) {}

    @Timeout(5000)
    async initEndOfShiftAtFirstTimeServiceStart() {
        this.logger.log(`initEndOfShiftAtFirstTimeServiceStart starting .....`);
        const provinceIds = DatabaseService.getAllProvinceIds();
        try {
            for (let i = 0; i < provinceIds.length; i++) {
                const provinceId = provinceIds[i];
                const req = createRequestByProvince(provinceId);
                const shiftWorks = await this.shiftWorkService.findAll(req, new FetchShiftWorkDto());
                await this.shiftWorkJobService.addJobToSendEndOfShiftEventByShiftWorks(shiftWorks, provinceId);
            }
        } catch (error) {
            this.logger.error(
                `[initEndOfShiftAtFirstTimeServiceStart] | message: ${error.message} | stack: ${error.stack}`,
            );
        }
    }
}
