import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { DriverService } from 'src/models/driver/services/driver.service';
import { DriverShiftWorkService } from 'src/models/driver/services/driverShiftWork.service';
import { ShiftWorkService } from 'src/models/shiftwork/shiftWork.service';
import { JobQueue } from '..';
import { ShiftWorkUpdatedListener } from './listeners/shiftWorkUpdated.listener';
import { ShiftWorkProcessor } from './shiftWork.process';
import { ShiftWorkTask } from './shiftWork.task';
import { ShiftWorkJobService } from './shiftWorkJob.service';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            name: JobQueue.SHIFT_WORK_QUEUE.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: 100,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
    ],
    controllers: [],
    providers: [
        ShiftWorkService,
        ShiftWorkProcessor,
        ShiftWorkJobService,
        ShiftWorkTask,
        ShiftWorkUpdatedListener,
        DriverShiftWorkService,
        ShiftWorkService,
        DriverService,
    ],
})
export class ShiftWorkJobModule {}
