import { Collection } from 'src/entities/collection.entity';
import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { v4 as uuid } from 'uuid';
import { JobQueue } from '..';
import { DatabaseService } from 'src/providers/database/database.service';
import { AppSetting, EAppSettingKey } from 'src/entities/appSetting.entity';
import { AppSettingJobService } from './appSettingJob.service';
import { Logger } from '@nestjs/common';
import { ETableName, ResizeImageService } from 'src/models/resizeImage/resizeImage.service';
import { Food } from 'src/entities/food.entity';
import { Restaurant } from 'src/entities/restaurant.entity';
import { RestaurantJobService } from '../restaurants/restaurantJob.service';
import { EUploadType, PublicFile } from 'src/entities/publicFile.entity';
import { ThumbnailsTemp } from 'src/entities/thumbnailsTemp.entity';
import { EStatus } from './appSettingJob.task';

const { APPSETTING_QUEUE } = JobQueue;
const { PROCESS } = APPSETTING_QUEUE;
@Processor(APPSETTING_QUEUE.PROCESSOR)
export class AppSettingJobProcessor {
    private logger = new Logger(AppSettingJobService.name);
    constructor(
        private appSettingJobService: AppSettingJobService,
        private readonly resizeImageService: ResizeImageService,
        private readonly restaurantJobService: RestaurantJobService,
    ) {}
    @Process(PROCESS.UPDATE_SHIPPING_FEE)
    async updateShippingFeeJob(job: Job) {
        const { jobNumber, provinceId, key } = job.data;
        const setting = JSON.parse(
            (await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId).findOne({ where: { key } })).value,
        );
        const updateData = { ...setting };
        if (key === EAppSettingKey.VILLFOOD_SETTING_FEES) {
            const {
                GT_3KM_DELIVERY_FEE_1,
                GT_3KM_DELIVERY_FEE_2,
                DEFAULT_1,
                DEFAULT_2,
                GTE_6KM_DELIVERY_FEE_1,
                GTE_6KM_DELIVERY_FEE_2,
            } = setting.FEE_AUTO_UPDATE_CONF;
            if (jobNumber == 1) {
                DEFAULT_1 && (updateData.DEFAULT_LT_3KM_DELIVERY_FEE = DEFAULT_1);
                GT_3KM_DELIVERY_FEE_1 && (updateData.GT_3KM_DELIVERY_FEE = GT_3KM_DELIVERY_FEE_1);
                GTE_6KM_DELIVERY_FEE_1 && (updateData.GTE_6KM_DELIVERY_FEE = GTE_6KM_DELIVERY_FEE_1);
            } else if (jobNumber == 2) {
                DEFAULT_2 && (updateData.DEFAULT_LT_3KM_DELIVERY_FEE = DEFAULT_2);
                GT_3KM_DELIVERY_FEE_2 && (updateData.GT_3KM_DELIVERY_FEE = GT_3KM_DELIVERY_FEE_2);
                GTE_6KM_DELIVERY_FEE_2 && (updateData.GTE_6KM_DELIVERY_FEE = GTE_6KM_DELIVERY_FEE_2);
            }
        } else if (key == EAppSettingKey.VILLEXPRESS_SETTING_FEES) {
            const { VAL_1, VAL_2, DEFAULT_1, DEFAULT_2 } = setting.FEE_AUTO_UPDATE_CONF;
            if (jobNumber == 1) {
                DEFAULT_1 && (updateData.DEFAULT_LT_3KM_DELIVERY_FEE = DEFAULT_1);
                VAL_1 && (updateData.GT_3KM_DELIVERY_FEE = VAL_1);
            } else if (jobNumber == 2) {
                DEFAULT_2 && (updateData.DEFAULT_LT_3KM_DELIVERY_FEE = DEFAULT_2);
                VAL_2 && (updateData.GT_3KM_DELIVERY_FEE = VAL_2);
            }
        } else if (key == EAppSettingKey.VILLBIKE_SETTING_FEES) {
            const { VAL_1, VAL_2, STANDARD_DEFAULT_1, PREMIUM_DEFAULT_1, STANDARD_DEFAULT_2, PREMIUM_DEFAULT_2 } =
                setting.FEE_AUTO_UPDATE_CONF;
            if (jobNumber == 1) {
                VAL_1.STANDARD && (updateData.GT_3KM_STANDARD_LEVEL_DELIVERY_FEE = VAL_1.STANDARD);
                VAL_1.PREMIUM && (updateData.GT_3KM_PREMIUM_LEVEL_DELIVERY_FEE = VAL_1.PREMIUM);
                STANDARD_DEFAULT_1 && (updateData.LT_3KM_STANDARD_LEVEL_DELIVERY_FEE = STANDARD_DEFAULT_1);
                PREMIUM_DEFAULT_1 && (updateData.LT_3KM_PREMIUM_LEVEL_DELIVERY_FEE = PREMIUM_DEFAULT_1);
            } else if (jobNumber == 2) {
                VAL_2.STANDARD && (updateData.GT_3KM_STANDARD_LEVEL_DELIVERY_FEE = VAL_2.STANDARD);
                VAL_2.PREMIUM && (updateData.GT_3KM_PREMIUM_LEVEL_DELIVERY_FEE = VAL_2.PREMIUM);
                STANDARD_DEFAULT_2 && (updateData.LT_3KM_STANDARD_LEVEL_DELIVERY_FEE = STANDARD_DEFAULT_2);
                PREMIUM_DEFAULT_2 && (updateData.LT_3KM_PREMIUM_LEVEL_DELIVERY_FEE = PREMIUM_DEFAULT_2);
            }
        }
        try {
            await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
                .createQueryBuilder()
                .update()
                .set({
                    value: JSON.stringify(updateData),
                })
                .where('key = :key', { key })
                .execute();
            return true;
        } catch (error) {
            this.logger.error(`[updateShippingFeeJob] message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process(PROCESS.CHECK_FEE_AUTO_UPDATE)
    async checkFeeAutoUpdate(job: Job) {
        const provinceId = job.data.provinceId;
        // VILLFOOD VILLBIKE, VILLEXPRESS SETTING FEE
        const key = [
            EAppSettingKey.VILLFOOD_SETTING_FEES,
            EAppSettingKey.VILLEXPRESS_SETTING_FEES,
            EAppSettingKey.VILLBIKE_SETTING_FEES,
        ];
        for (const keyName of key) {
            try {
                const settingTxt = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId).findOne({
                    where: { key: keyName },
                });
                if (settingTxt) {
                    const setting = JSON.parse(settingTxt.value);
                    await this.appSettingJobService.addJobToUpdateShippingFee(setting, keyName, provinceId);
                }
            } catch (error) {
                this.logger.error(`[checkFeeAutoUpdate] | message: ${error.message} | stack: ${error.stack}`);
                return null;
            }
        }
    }

    @Process(PROCESS.CHECK_COLLECTION_AUTO_ACTIVE)
    async checkCollectionAutoActive(job: Job) {
        try {
            const provinceId = job.data.provinceId;
            const collectionSettingTxt = await DatabaseService.getRepositoryByProvinceId(
                AppSetting,
                provinceId,
            ).findOne({
                where: { key: EAppSettingKey.COLLECTION_AUTO_ACTIVE_CONF },
            });
            let collectionSetting = null;
            if (collectionSettingTxt) {
                collectionSetting = JSON.parse(collectionSettingTxt.value);
                if (!collectionSetting) return;
                collectionSetting.forEach(
                    async (setting: {
                        collectionId: number;
                        autoActiveEnable: boolean;
                        activeTime: string;
                        unActiveTime: string;
                    }) => {
                        const { collectionId, autoActiveEnable, activeTime, unActiveTime } = setting;
                        await this.appSettingJobService.addCollectionActiveJob(
                            collectionId,
                            autoActiveEnable,
                            activeTime,
                            unActiveTime,
                            provinceId,
                        );
                    },
                );
            }
        } catch (error) {
            this.logger.error(`[checkCollectionAutoActive] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process(PROCESS.COLLECTION_AUTO_ACTIVE)
    async addCollectionAutoActiveJob(job: Job) {
        const { collectionId, val, provinceId } = job.data;
        try {
            await DatabaseService.getRepositoryByProvinceId(Collection, provinceId)
                .createQueryBuilder()
                .update()
                .set({
                    active: val,
                })
                .where('id = :id', { id: collectionId })
                .execute();
            return true;
        } catch (error) {
            this.logger.error(`[addCollectionAutoActiveJob] | message: ${error.message} | stack:${error.stack}`);
        }
    }

    @Process(PROCESS.TEMP_TABLE_GET_DATA)
    async pushDataToTempTableJob(job: Job) {
        // { image: image.image, image_id: image.id, table: tableName, status: 'pending' }
        const { provinceId, data, lastId } = job.data;
        const repository = DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId);
        try {
            await repository.save(data);
            return true;
        } catch (error) {
            if (error.code == 'ER_DUP_ENTRY' && lastId < data.image_id) {
                await repository.createQueryBuilder().delete().where('image = :image', { image: data.image }).execute();
                await repository.save(data);
            } else {
                this.logger.error(`[pushDataToTempTableJob] | message: ${error.message} | stack:${error.stack}`);
            }
        }
    }

    @Process(PROCESS.RESIZE_IMAGE)
    async addResizeImageJob(job: Job) {
        const { image, provinceId } = job.data;
        const imgName = uuid();
        const fileType = image.split('.').pop();
        const url = 'thumbnails/' + imgName + '.' + fileType;
        const { Location, Key } = await this.resizeImageService.imgUrlToResizedUrl(image, url).catch(async (error) => {
            await DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId)
                .createQueryBuilder()
                .update()
                .set({ status: EStatus.ERROR })
                .where('image=:image', { image })
                .execute();
            throw error;
        });
        if (Location) {
            await DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId)
                .createQueryBuilder()
                .update()
                .set({ thumbnail: Location, status: EStatus.READY, uuid: imgName, key: Key, type: EUploadType.IMAGE })
                .where('image=:image', { image })
                .execute();
        }
        return true;
    }

    @Process(PROCESS.SYNC_TO_MAIN_TABLE)
    async syncToMainTableJob(job: Job) {
        const { provinceId, data } = job.data;
        const { image, thumbnail, uuid, key, type, table } = data;
        const entity = table == ETableName.FOODS ? Food : Restaurant;
        const repo = DatabaseService.getRepositoryByProvinceId(entity, provinceId);
        await repo
            .createQueryBuilder()
            .update()
            .set({ thumbnails: thumbnail })
            .where('image = :image', { image })
            .execute();
        await DatabaseService.getRepositoryByProvinceId(PublicFile, provinceId).save({
            uuid,
            url: thumbnail,
            key,
            type,
        });
        if (table == ETableName.RESTAURANTS) {
            const restaurant = await repo.find({ where: { image } });
            restaurant.forEach((item) => {
                this.restaurantJobService.reIndexRestaurantToEls(item.id, provinceId);
            });
        }
        await DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId)
            .createQueryBuilder()
            .update()
            .set({ status: EStatus.DONE })
            .where('image = :image', { image })
            .execute();
    }

    @Process(PROCESS.RESET_DEFAULT_TAX)
    async resetDefaultTax(job: Job) {
        const { provinceId } = job.data;
        await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId).update(
            {
                key: EAppSettingKey.FEESHIP_RAINNY,
            },
            { value: (0).toString() },
        );
        console.log('reset default tax', provinceId);
    }
}
