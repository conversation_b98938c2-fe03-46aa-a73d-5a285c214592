import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { Job, Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { AppSettingJobTask } from './appSettingJob.task';
import { ConfigService } from '@nestjs/config';
const { CRON_JOB } = JobQueue;

@Injectable()
export class AppSettingJobCron {
    private logger = new Logger(AppSettingJobCron.name);
    constructor(
        @InjectQueue(CRON_JOB.PROCESSOR)
        private cronQueue: Queue,
        private readonly configService: ConfigService,
    ) {}

    @Timeout(5000)
    async resetDefaultTax() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_RESET_DEFAULT_TAX);
            this.logger.log(`[resetDefaultTax] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_RESET_DEFAULT_TAX) {
                        this.logger.log(`[resetDefaultTax] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_RESET_DEFAULT_TAX,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_RESET_DEFAULT_TAX,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[resetDefaultTax] message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Timeout(5000)
    async inactiveExpiredPromotions() {
        const RESET_REDIS_CRON = Boolean(this.configService.get('RESET_REDIS_CRON')) || false;
        if (!RESET_REDIS_CRON) {
            return;
        }
        try {
            const jobId = generateJobIdByProcessName(CRON_JOB.PROCESS.CRON_INACTIVE_EXPIRED_PROMOTIONS);
            this.logger.log(`[inactiveExpiredPromotions] Start job ${jobId}`);

            const delayedJobs = await this.cronQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === CRON_JOB.PROCESS.CRON_INACTIVE_EXPIRED_PROMOTIONS) {
                        this.logger.log(`[inactiveExpiredPromotions] Remove job ${job.name}`);
                        await job.remove();
                    }
                }
            }

            await this.cronQueue.add(
                CRON_JOB.PROCESS.CRON_INACTIVE_EXPIRED_PROMOTIONS,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_MIDNIGHT,
                        tz: VietNamTimeZone,
                        key: CRON_JOB.PROCESS.CRON_INACTIVE_EXPIRED_PROMOTIONS,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );
        } catch (error) {
            this.logger.error(`[inactiveExpiredPromotions] message: ${error.message} | stack: ${error.stack}`);
        }
    }
}

@Processor(CRON_JOB.PROCESSOR)
export class AppSettingJobCronProcessor {
    private logger = new Logger(AppSettingJobCronProcessor.name);
    constructor(private readonly appSettingJobTask: AppSettingJobTask) {}

    @Process({
        name: CRON_JOB.PROCESS.CRON_RESET_DEFAULT_TAX,
        concurrency: 1,
    })
    async resetDefaultTax(job: Job) {
        try {
            await this.appSettingJobTask.resetDefaultTax();
        } catch (error) {
            this.logger.error(`[resetDefaultTax] message: ${error.message} | stack: ${error.stack}`);
        }
    }

    @Process({
        name: CRON_JOB.PROCESS.CRON_INACTIVE_EXPIRED_PROMOTIONS,
        concurrency: 1,
    })
    async inactiveExpiredPromotions(job: Job) {
        try {
            await this.appSettingJobTask.inactiveExpiredPromotions();
        } catch (error) {
            this.logger.error(`[inactiveExpiredPromotions] message: ${error.message} | stack: ${error.stack}`);
        }
    }
}
