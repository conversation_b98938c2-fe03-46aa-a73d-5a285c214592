import { RestaurantJobModule } from './../restaurants/restaurantJob.module';
import { ResizeImageModule } from './../../models/resizeImage/resizeImage.module';
import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { JobQueue } from '..';
import { AppSettingJobService } from './appSettingJob.service';
import { AppSettingJobProcessor } from 'src/jobs/appSetting/appSettingJob.processor';
import { AppSettingJobTask } from './appSettingJob.task';
import { PromotionModule } from 'src/models/promotion/promotion.module';
import { AppSettingJobCron, AppSettingJobCronProcessor } from './appSettingJob.cron';

@Module({
    imports: [
        BullModule.registerQueue({
            name: JobQueue.APPSETTING_QUEUE.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            },
        }),
        ResizeImageModule,
        RestaurantJobModule,
        PromotionModule,
    ],
    exports: [AppSettingJobService, AppSettingJobProcessor, AppSettingJobTask],
    providers: [
        AppSettingJobService,
        AppSettingJobProcessor,
        AppSettingJobTask,
        AppSettingJobCronProcessor,
        AppSettingJobCron,
    ],
})
export class AppSettingJobModule {}
