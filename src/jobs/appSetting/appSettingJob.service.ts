import { EAppSettingKey, IVillExpressSettingFee, IVillfoodSettingFee } from 'src/entities/appSetting.entity';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { JobQueue, VietNamTimeZone } from '..';
import * as _ from 'lodash';

const { APPSETTING_QUEUE } = JobQueue;
const { PROCESS } = APPSETTING_QUEUE;

@Injectable()
export class AppSettingJobService {
    private logger = new Logger(AppSettingJobService.name);
    constructor(@InjectQueue(APPSETTING_QUEUE.PROCESSOR) private appSettingQueue: Queue) {}

    async checkFeeUpdateJobByProvinceId(provinceId: string) {
        await this.appSettingQueue.add(
            PROCESS.CHECK_FEE_AUTO_UPDATE,
            { provinceId },
            { removeOnComplete: 100, attempts: 5, backoff: 3000 },
        );
    }

    async checkCollectionAutoActive(provinceId: string) {
        await this.appSettingQueue.add(
            PROCESS.CHECK_COLLECTION_AUTO_ACTIVE,
            { provinceId },
            { removeOnComplete: 100, attempts: 5, backoff: 3000 },
        );
    }

    async addJobToUpdateShippingFee(
        setting: IVillfoodSettingFee | IVillExpressSettingFee,
        key: EAppSettingKey,
        provinceId: number,
    ) {
        try {
            const { TIME_1, TIME_2, IS_ENABLE } = setting.FEE_AUTO_UPDATE_CONF;

            const jobData1 = { jobNumber: 1, key, provinceId };
            const jobData2 = { jobNumber: 2, key, provinceId };

            const delayedJobs = await this.appSettingQueue.getDelayed();
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (
                        job &&
                        job.name === PROCESS.UPDATE_SHIPPING_FEE &&
                        (_.isEqual(job.data, jobData1) || _.isEqual(job.data, jobData2))
                    )
                        await job.remove();
                }
            }

            if (IS_ENABLE) {
                const cron1 = `${TIME_1.split(':')[1]} ${TIME_1.split(':')[0]} * * *`;
                const cron2 = `${TIME_2.split(':')[1]} ${TIME_2.split(':')[0]} * * *`;

                await this.appSettingQueue.add(
                    PROCESS.UPDATE_SHIPPING_FEE,
                    { jobNumber: 1, key, provinceId },
                    {
                        repeat: {
                            key: `${PROCESS.UPDATE_SHIPPING_FEE}_${key}_${provinceId}_1`,
                            cron: cron1,
                            tz: VietNamTimeZone,
                        },
                        jobId: `${PROCESS.UPDATE_SHIPPING_FEE}_${key}_${provinceId}_1`,
                        removeOnComplete: 100,
                        attempts: 5,
                        backoff: 3000,
                    },
                );
                await this.appSettingQueue.add(
                    PROCESS.UPDATE_SHIPPING_FEE,
                    { jobNumber: 2, key, provinceId },
                    {
                        repeat: {
                            key: `${PROCESS.UPDATE_SHIPPING_FEE}_${key}_${provinceId}_2`,
                            cron: cron2,
                            tz: VietNamTimeZone,
                        },
                        jobId: `${PROCESS.UPDATE_SHIPPING_FEE}_${key}_${provinceId}_2`,
                        removeOnComplete: 100,
                        attempts: 5,
                        backoff: 3000,
                    },
                );
            }

            return;
        } catch (error) {
            this.logger.error(`[addJobToUpdateShippingFee] | message: ${error.message} | stack: ${error.stack}`);
            return null;
        }
    }

    async addCollectionActiveJob(
        collectionId: number,
        enable: boolean,
        activeTime: string,
        unActiveTime: string,
        provinceId: string,
    ) {
        const activeData = { collectionId, val: 1, provinceId };
        const unActiveData = { collectionId, val: 0, provinceId };
        const delayedJobs = await this.appSettingQueue.getDelayed();
        if (delayedJobs && delayedJobs.length > 0) {
            for (const job of delayedJobs) {
                if (
                    job &&
                    job.name === PROCESS.COLLECTION_AUTO_ACTIVE &&
                    (_.isEqual(job.data, activeData) || _.isEqual(job.data, unActiveData))
                ) {
                    await job.remove();
                }
            }
        }

        if (enable) {
            const enableCron = `${activeTime.split(':')[1]} ${activeTime.split(':')[0]} * * *`;
            const unActiveCron = `${unActiveTime.split(':')[1]} ${unActiveTime.split(':')[0]} * * *`;
            await this.appSettingQueue.add(PROCESS.COLLECTION_AUTO_ACTIVE, activeData, {
                jobId: this.generateActiveCollectionJobId(collectionId, 1, provinceId),
                repeat: {
                    key: `${PROCESS.COLLECTION_AUTO_ACTIVE}_${collectionId}_1_${provinceId}`,
                    cron: enableCron,
                    tz: VietNamTimeZone,
                },
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            });
            await this.appSettingQueue.add(PROCESS.COLLECTION_AUTO_ACTIVE, unActiveData, {
                jobId: this.generateActiveCollectionJobId(collectionId, 0, provinceId),
                repeat: {
                    key: `${PROCESS.COLLECTION_AUTO_ACTIVE}_${collectionId}_0_${provinceId}`,
                    cron: unActiveCron,
                    tz: VietNamTimeZone,
                },
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            });
        }
    }

    private generateActiveCollectionJobId(collectionId: number, val: number, provinceId: string) {
        return `${PROCESS.COLLECTION_AUTO_ACTIVE}_${collectionId}_${val}_${provinceId}`;
    }
}
