import { Restaurant } from 'src/entities/restaurant.entity';
import { Food } from 'src/entities/food.entity';
import { EntityTarget } from 'typeorm';
import { ETableName } from 'src/models/resizeImage/resizeImage.service';
import { AppSetting, EAppSettingKey } from 'src/entities/appSetting.entity';
import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry, Timeout } from '@nestjs/schedule';
import { Queue } from 'bull';
import { DatabaseService } from 'src/providers/database/database.service';

import { ECronjobs, JobQueue, VietNamTimeZone } from '..';
import { CronJob } from 'cron';
import { ThumbnailsTemp } from 'src/entities/thumbnailsTemp.entity';
import { allProvinceIds } from 'src/providers/database/database.provider';
import { PromotionService } from 'src/models/promotion/services/promotion.service';
import { AppSettingJobService } from './appSettingJob.service';
import { TimeBasedDeliveryFee, VillFoodSettingFees } from 'src/models/appSetting/app-setting.dto';
const { APPSETTING_QUEUE } = JobQueue;
const { PROCESS } = APPSETTING_QUEUE;

export enum ECronJobName {
    PUSH_DATA_TO_TEMP_TABLE = 'PUSH_DATA_TO_TEMP_TABLE',
    ADD_RESIZE_JOB = 'ADD_RESIZE_JOB',
    SYNC_TO_MAIN_TABLE = 'SYNC_TO_MAIN_TABLE',
}

export enum EStatus {
    PENDING = 'pending',
    READY = 'ready',
    DONE = 'done',
    ERROR = 'error',
}
@Injectable()
export class AppSettingJobTask {
    private logger = new Logger(AppSettingJobTask.name);

    constructor(
        @InjectQueue(APPSETTING_QUEUE.PROCESSOR) private appSettingQueue: Queue,
        private readonly schedulerRegistry: SchedulerRegistry,
        private promotionService: PromotionService,
        private appSettingJobService: AppSettingJobService,
    ) {}

    @Timeout(5000)
    async checkJobOnServerStart() {
        const provinceIds = DatabaseService.getAllProvinceIds();
        for (const provinceId of provinceIds) {
            await this.appSettingJobService.checkFeeUpdateJobByProvinceId(provinceId);
        }
    }

    /* Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
        name: ECronjobs.ResetDefaultTaxSetting,
        timeZone: VietNamTimeZone,
    }) */
    async resetDefaultTax() {
        allProvinceIds.map(async (provinceId) => {
            await this.appSettingQueue.add(
                PROCESS.RESET_DEFAULT_TAX,
                { provinceId },
                { removeOnComplete: 100, attempts: 2, backoff: 1000 },
            );
        });
    }

    /* Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
        name: ECronjobs.InactiveExpiredPromotions,
        timeZone: VietNamTimeZone,
    }) */
    async inactiveExpiredPromotions() {
        const provinceIds = DatabaseService.getAllProvinceIds();
        provinceIds.forEach((provinceId) => {
            this.promotionService.inactiveExpiredPromotions(provinceId);
        });
    }

    //thumbnails resize job
    resizeImagePushDataToTempTable(tableName: ETableName, provinceId: string) {
        let entity: EntityTarget<Food | Restaurant>;
        if (tableName == ETableName.FOODS) entity = Food;
        else if (tableName == ETableName.RESTAURANTS) entity = Restaurant;
        let job = new CronJob(`0,10,20,30,40,50 * * * * *`, async () => {
            const tempTableRepo = DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId);
            const lastItem = await tempTableRepo
                .createQueryBuilder('tempTable')
                .where('tempTable.table = :tableName', { tableName })
                .orderBy('tempTable.image_id', 'DESC')
                .getOne();
            let lastId = lastItem ? lastItem.image_id : 0;
            const imageList = await DatabaseService.getRepositoryByProvinceId(entity, provinceId)
                .createQueryBuilder()
                .where("image LIKE 'https://%' ")
                .andWhere('thumbnails IS NULL')
                .andWhere('id > :lastId', { lastId })
                .limit(500)
                .orderBy('id', 'ASC')
                .getMany();
            for (let i = 0; i < imageList.length; i++) {
                const image = imageList[i];
                const jobId = `${PROCESS.TEMP_TABLE_GET_DATA}_${image.id}`;
                const activeJob = await this.appSettingQueue.getJob(jobId);
                activeJob?.remove();
                const data = { image: image.image, image_id: image.id, table: tableName, status: EStatus.PENDING };
                await this.appSettingQueue.add(
                    PROCESS.TEMP_TABLE_GET_DATA,
                    {
                        provinceId,
                        data,
                        lastId,
                    },
                    {
                        jobId: `${PROCESS.TEMP_TABLE_GET_DATA}_${tableName}_${image.id}`,
                    },
                );
                lastId = image.id;
            }
            if (imageList.length === 0) {
                this.addResizeJob(provinceId);
                job.stop();
            }
        });
        const cronJob = this.schedulerRegistry.doesExist(
            'cron',
            `${ECronJobName.PUSH_DATA_TO_TEMP_TABLE}_${provinceId}_${tableName}`,
        );
        if (cronJob) {
            job = this.schedulerRegistry.getCronJob(
                `${ECronJobName.PUSH_DATA_TO_TEMP_TABLE}_${provinceId}_${tableName}`,
            );
            job.start();
        } else {
            this.schedulerRegistry.addCronJob(
                `${ECronJobName.PUSH_DATA_TO_TEMP_TABLE}_${provinceId}_${tableName}`,
                job,
            );
            job.start();
        }
        console.log('job start');
    }

    addResizeJob(provinceId: string) {
        let job = new CronJob(`0,20,40 * * * * *`, async () => {
            console.log('addResizeJob');
            const tempTableRepo = DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId);
            const imageList = await tempTableRepo
                .createQueryBuilder()
                .where('status = :status', { status: EStatus.PENDING })
                .andWhere('thumbnail IS NULL')
                .limit(500)
                .getMany();
            console.log(imageList.length);
            if (imageList.length === 0) {
                this.syncToMainTable(provinceId);
                job.stop();
            }
            imageList.forEach(async (image) => {
                const jobId = `${PROCESS.RESIZE_IMAGE}_${image.id}`;
                const activeJob = await this.appSettingQueue.getJob(jobId);
                // activeJob && (await activeJob.remove());
                if (!activeJob) {
                    this.appSettingQueue.add(
                        PROCESS.RESIZE_IMAGE,
                        {
                            provinceId,
                            image: image.image,
                        },
                        {
                            jobId,
                        },
                    );
                }
            });
        });
        const cronJob = this.schedulerRegistry.doesExist('cron', ECronJobName.ADD_RESIZE_JOB);
        if (cronJob) {
            job = this.schedulerRegistry.getCronJob(ECronJobName.ADD_RESIZE_JOB);
            job.start();
        } else {
            this.schedulerRegistry.addCronJob(ECronJobName.ADD_RESIZE_JOB, job);
            job.start();
        }
        console.log('resize job start');
    }

    syncToMainTable(provinceId: string) {
        let table = ETableName.FOODS as string;
        let job = new CronJob(`0,10,20,30,40,50 * * * * *`, async () => {
            const tempTableRepo = DatabaseService.getRepositoryByProvinceId(ThumbnailsTemp, provinceId);
            const imageList = await tempTableRepo
                .createQueryBuilder()
                .where('status = :status', { status: EStatus.READY })
                .andWhere('thumbnail IS NOT NULL')
                .limit(300)
                .getMany();
            if (imageList.length === 0) {
                job.stop();
                console.log('table: ', table);
                if (table == ETableName.FOODS) {
                    table = ETableName.RESTAURANTS as string;
                    this.resizeImagePushDataToTempTable(ETableName.RESTAURANTS, provinceId);
                }
            } else {
                imageList.forEach(async (image) => {
                    const jobId = `${PROCESS.SYNC_TO_MAIN_TABLE}_${provinceId}_${image.id}`;
                    const activeJob = await this.appSettingQueue.getJob(jobId);
                    if (!activeJob) {
                        this.appSettingQueue.add(PROCESS.SYNC_TO_MAIN_TABLE, { provinceId, data: image }, { jobId });
                    }
                });
            }
        });
        const cronJob = this.schedulerRegistry.doesExist('cron', ECronJobName.SYNC_TO_MAIN_TABLE);
        if (cronJob) {
            job = this.schedulerRegistry.getCronJob(ECronJobName.SYNC_TO_MAIN_TABLE);
            job.start();
        } else {
            this.schedulerRegistry.addCronJob(ECronJobName.SYNC_TO_MAIN_TABLE, job);
            job.start();
        }
        console.log('sync to main start');
    }

    async updateJobAutoSetDeliveryFeeInTimeMode(
        provinceId: string,
        key: EAppSettingKey,
        oldTimesFee: TimeBasedDeliveryFee[] = [],
        newTimesFee: TimeBasedDeliveryFee[] = [],
        mode: 'DEFAULT_FEE' | 'TIMES_FEE',
    ) {
        const allowedKey = [
            EAppSettingKey.VILL_FOOD_SETTING_FEES_V2,
            EAppSettingKey.VILL_BIKE_SETTING_FEES_V2,
            EAppSettingKey.VILL_EXPRESS_SETTING_FEES_V2,
        ];

        return [];
    }
}
