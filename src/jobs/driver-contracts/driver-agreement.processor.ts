import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';

import { IGenerateDriverAgreement } from './interfaces';
import { DriverAgreementService } from 'src/models/driver-agreement/driver-agreement.service';
import { DriverAgreementTypeService } from 'src/models/driver-agreement-type/driver-agreement-type.service';

@Processor(JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESSOR)
export class DriverAgreementProcessor {
    private logger = new Logger(DriverAgreementProcessor.name);

    constructor(
        private readonly driverAgreementService: DriverAgreementService,
        private readonly driverAgreementTypeService: DriverAgreementTypeService,
    ) {}

    @Process({
        name: JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESS.GENERATE_DRIVER_AGREEMENT,
        concurrency: 1,
    })
    async generateContractForActiveDriver(job: Job<IGenerateDriverAgreement>) {
        const { provinceId, agreementTypeId, driverId } = job.data;

        if (!driverId) {
            this.logger.log(
                `Complete generating contract for driver in province ${provinceId} with agreement type ${agreementTypeId}`,
            );
            await this.driverAgreementTypeService.completeGenerateContractForActiveDriver(provinceId, agreementTypeId);
        } else {
            this.logger.log(
                `Start generating contract for driver ${driverId} in province ${provinceId} with agreement type ${agreementTypeId}`,
            );

            await this.driverAgreementService.generateContractForActiveDriver(provinceId, agreementTypeId, driverId);
        }

        return;
    }

    @Process({
        name: JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESS.GENERATE_DRIVER_AGREEMENT_FOR_NEW_DRIVER,
        concurrency: 1,
    })
    async generateContractForNewDriver(job: Job<IGenerateDriverAgreement>) {
        return await this.generateContractForActiveDriver(job);
    }
}
