import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import * as jobHelpers from 'src/common/helpers/job.helpter';
import { JobQueue } from '..';
import { IGenerateDriverAgreement, IGenerateDriverAgreementForActiveDriver } from './interfaces';
import { Driver } from 'src/entities/driver.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { DriverAgreement } from 'src/entities/driver-agreement.entity';
import { In } from 'typeorm';

@Injectable()
export class DriverAgreementJobService {
    private logger = new Logger(DriverAgreementJobService.name);
    constructor(
        @InjectQueue(JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESSOR)
        private readonly driverAgreementJobQueue: Queue,
    ) {}

    async generateContractForActiveDriver(provinceId: string, agreementTypeId: number) {
        const KEY = JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESS.GENERATE_DRIVER_AGREEMENT;
        try {
            const drivers = await DatabaseService.getConnectionByProvinceId(provinceId)
                .getRepository(Driver)
                .createQueryBuilder('driver')
                .select('driver.id')
                .where('driver.is_active = :isActive', { isActive: 1 })
                .getMany();

            for (const driver of drivers) {
                const jobId = jobHelpers.generateJobIdByProcessName(KEY, agreementTypeId, provinceId, driver.id);

                const currJob = await this.driverAgreementJobQueue.getJob(jobId);
                if (currJob) await currJob.remove();

                const payload: IGenerateDriverAgreement = {
                    provinceId,
                    agreementTypeId,
                    driverId: driver.id,
                };

                await this.driverAgreementJobQueue.add(KEY, payload, {
                    jobId,
                    attempts: 1,
                    removeOnComplete: 100,
                    removeOnFail: true,
                });
            }

            for (let i = 0; i < 3; i++) {
                const jobId = jobHelpers.generateJobIdByProcessName(
                    KEY,
                    agreementTypeId,
                    provinceId,
                    i,
                    new Date().getTime(),
                );

                await this.driverAgreementJobQueue.add(
                    KEY,
                    {
                        provinceId,
                        agreementTypeId,
                    },
                    {
                        jobId,
                        attempts: 1,
                    },
                );
            }

            return;
        } catch (error) {
            this.logger.error(`[generateContractForActiveDriver] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }

    async generateContractsForNewDriver(provinceId: string, agreementTypeId: number) {
        const KEY = JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESS.GENERATE_DRIVER_AGREEMENT_FOR_NEW_DRIVER;
        try {
            const drivers = await DatabaseService.getConnectionByProvinceId(provinceId)
                .getRepository(Driver)
                .createQueryBuilder('driver')
                .select('driver.id')
                .where('driver.is_active = :isActive', { isActive: 1 })
                .getMany();
            const driverIds = drivers.map((driver) => driver.id);

            const driverAgreement = await DatabaseService.getConnectionByProvinceId(provinceId)
                .getRepository(DriverAgreement)
                .find({
                    where: {
                        agreement_type_id: agreementTypeId,
                    },
                });
            const alreadyGeneratedDriverIds = driverAgreement.map((item) => item.driver_id);
            const notGeneratedDriverIds = driverIds.filter((id) => !alreadyGeneratedDriverIds.includes(id));

            for (const id of notGeneratedDriverIds) {
                const jobId = jobHelpers.generateJobIdByProcessName(KEY, agreementTypeId, provinceId, id);

                const currJob = await this.driverAgreementJobQueue.getJob(jobId);
                if (currJob) await currJob.remove();

                const payload: IGenerateDriverAgreement = {
                    provinceId,
                    agreementTypeId,
                    driverId: id,
                };

                await this.driverAgreementJobQueue.add(KEY, payload, {
                    jobId,
                    attempts: 3,
                    removeOnComplete: 100,
                    removeOnFail: true,
                });
            }

            for (let i = 0; i < 3; i++) {
                const jobId = jobHelpers.generateJobIdByProcessName(
                    KEY,
                    agreementTypeId,
                    provinceId,
                    i,
                    new Date().getTime(),
                );

                await this.driverAgreementJobQueue.add(
                    KEY,
                    {
                        provinceId,
                        agreementTypeId,
                    },
                    {
                        jobId,
                        attempts: 3,
                    },
                );
            }

            return;
        } catch (error) {
            this.logger.error(`[generateContractsForNewDriver] | message: ${error.message} | stack: ${error.stack}`);
            return;
        }
    }
}
