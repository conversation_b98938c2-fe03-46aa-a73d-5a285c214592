import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { JobQueue } from '..';
import { DriverAgreementProcessor } from './driver-agreement.processor';
import { DriverAgreementJobService } from './driver-agreement.service';
import { DriverAgreementService } from 'src/models/driver-agreement/driver-agreement.service';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { DriverAgreementTypeService } from 'src/models/driver-agreement-type/driver-agreement-type.service';
import { SignaturesService } from 'src/models/signatures/signatures.service';
import { DriverService } from 'src/models/driver/services/driver.service';

@Module({
    imports: [
        BullModule.registerQueueAsync({
            name: JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESSOR,
            useFactory: () => ({
                defaultJobOptions: {
                    removeOnComplete: 100,
                    attempts: 2,
                    backoff: 10000,
                },
            }),
        }),
    ],
    providers: [
        DriverAgreementProcessor,
        DriverAgreementJobService,
        DriverAgreementService,
        AwsS3Service,
        DriverAgreementTypeService,
        SignaturesService,
        DriverService,
    ],
    exports: [DriverAgreementJobService],
})
export class DriverAgreementJobModule {}
