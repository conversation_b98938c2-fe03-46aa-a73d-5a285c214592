import { createBullBoard } from "@bull-board/api";
import { BullAdapter } from "@bull-board/api/dist/src/queueAdapters/bull";
import { ExpressAdapter } from "@bull-board/express";
import { BullModule, getQueueToken } from "@nestjs/bull";
import {
  Global,
  Inject,
  MiddlewareConsumer,
  Module,
  NestModule,
} from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { ScheduleModule } from "@nestjs/schedule";
import { Queue } from "bull";
import * as basicAuth from "express-basic-auth";
import { JobQueue } from ".";
import { AppSettingJobModule } from "./appSetting/appSettingJob.module";
import { DriverAgreementJobModule } from "./driver-contracts/driver-agreement-job.module";
import { DriverJobModule } from "./drivers/driverJob.module";
import { EmployeeActivityJobModule } from "./employee-activity/employee-activity-job.module";
import { OrdersStatsJobModule } from "./orders-stats/orders-stats-job.module";
import { OrderJobModule } from "./orders/orderJob.module";
import { RestaurantRevenueJobModule } from "./restaurant-revenue/restaurant-revenue-job.module";
import { RestaurantAdJobModule } from "./restaurantAds/restaurantAdsJob.module";
import { RestaurantJobModule } from "./restaurants/restaurantJob.module";
import { ShiftWorkJobModule } from "./shiftWork/shiftWork.module";
import { UserJobModule } from "./users/userJobs.module";
import { DriverTaxReportJobModule } from "./driverTaxReport/driverTaxReport.module";
import { DriverOrderPlanJobModule } from "./driver-order-plan/driver-order-plan-job.module";
import { InvoiceJobModule } from "./invoice/invoice.module";

@Global()
@Module({
  imports: [
    ScheduleModule.forRoot(),
    DriverJobModule,
    RestaurantJobModule,
    UserJobModule,
    AppSettingJobModule,
    OrderJobModule,
    ShiftWorkJobModule,
    RestaurantAdJobModule,
    DriverAgreementJobModule,
    OrdersStatsJobModule,
    RestaurantRevenueJobModule,
    EmployeeActivityJobModule,
    DriverTaxReportJobModule,
    DriverOrderPlanJobModule,
    InvoiceJobModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        redis: {
          host: configService.get("jobTaskRedisHost"),
          port: configService.get("jobTaskRedisPort"),
          password: configService.get("jobTaskRedisPassword"),
          db: configService.get("jobTaskRedisDB"),
        },
      }),
    }),

    BullModule.registerQueue({ name: JobQueue.DRIVER_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.RESTAURANT_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.TRANSACTION_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.APPSETTING_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.ORDER_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.INVOICE_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.SHIFT_WORK_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.FOOD_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.USER_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.RESTAURANT_ADS_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.DISPATCH_QUEUE.PROCESSOR }),
    BullModule.registerQueue({
      name: JobQueue.BACKGROUND_DISPATCH_QUEUE.PROCESSOR,
    }),
    BullModule.registerQueue({ name: JobQueue.SOCIAL_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.BARIA_SOCIAL_QUEUE.PROCESSOR }),
    BullModule.registerQueue({
      name: JobQueue.RESTAURANT_INSIGHT_QUEUE.PROCESSOR,
    }),
    BullModule.registerQueue({
      name: JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESSOR,
    }),
    BullModule.registerQueue({
      name: JobQueue.ORDER_STATS_JOB_QUEUE.PROCESSOR,
    }),
    BullModule.registerQueue({
      name: JobQueue.ARCHIVE_EXECUTION_MGTM_QUEUE.PROCESSOR,
    }),
    BullModule.registerQueue({ name: JobQueue.ARCHIVE_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.USER_RATE_QUEUE.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.CRON_JOB.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.EMPLOYEE_ACTIVITY.PROCESSOR }),
    BullModule.registerQueue({ name: JobQueue.ROLE_QUEUE.PROCESSOR }),
    BullModule.registerQueue({
      name: JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESSOR,
    }),
  ],
  // controllers: [JobControllers],
  exports: [RestaurantJobModule, BullModule],
  providers: [],
})
export class JobModule implements NestModule {
  @Inject(getQueueToken(JobQueue.DRIVER_QUEUE.PROCESSOR))
  private readonly driverQueue: Queue;

  @Inject(getQueueToken(JobQueue.RESTAURANT_QUEUE.PROCESSOR))
  private readonly restaurantQueue: Queue;

  @Inject(getQueueToken(JobQueue.TRANSACTION_QUEUE.PROCESSOR))
  private readonly transactionQueue: Queue;

  @Inject(getQueueToken(JobQueue.APPSETTING_QUEUE.PROCESSOR))
  private readonly appSettingQueue: Queue;

  @Inject(getQueueToken(JobQueue.ORDER_QUEUE.PROCESSOR))
  private readonly orderQueue: Queue;

  @Inject(getQueueToken(JobQueue.INVOICE_QUEUE.PROCESSOR))
  private readonly invoiceQueue: Queue;

  @Inject(getQueueToken(JobQueue.SHIFT_WORK_QUEUE.PROCESSOR))
  private readonly shiftWorkQueue: Queue;

  @Inject(getQueueToken(JobQueue.FOOD_QUEUE.PROCESSOR))
  private readonly foodQueue: Queue;

  @Inject(getQueueToken(JobQueue.USER_QUEUE.PROCESSOR))
  private readonly userQueue: Queue;

  @Inject(getQueueToken(JobQueue.RESTAURANT_ADS_QUEUE.PROCESSOR))
  private readonly restaurantAdsQueue: Queue;

  @Inject(getQueueToken(JobQueue.DISPATCH_QUEUE.PROCESSOR))
  private readonly dispatcherQueue: Queue;

  @Inject(getQueueToken(JobQueue.BACKGROUND_DISPATCH_QUEUE.PROCESSOR))
  private readonly bgDispatcherQueue: Queue;

  @Inject(getQueueToken(JobQueue.SOCIAL_QUEUE.PROCESSOR))
  private readonly socialQueue: Queue;

  @Inject(getQueueToken(JobQueue.BARIA_SOCIAL_QUEUE.PROCESSOR))
  private readonly bariaSocialQueue: Queue;

  @Inject(getQueueToken(JobQueue.RESTAURANT_INSIGHT_QUEUE.PROCESSOR))
  private readonly restaurantInsightQueue: Queue;

  @Inject(getQueueToken(JobQueue.DRIVER_AGREEMENT_JOB_QUEUE.PROCESSOR))
  private readonly driverAgreementQueue: Queue;

  @Inject(getQueueToken(JobQueue.ORDER_STATS_JOB_QUEUE.PROCESSOR))
  private readonly orderStatisticsQueue: Queue;

  @Inject(getQueueToken(JobQueue.ARCHIVE_EXECUTION_MGTM_QUEUE.PROCESSOR))
  private readonly archiveMgtm: Queue;

  @Inject(getQueueToken(JobQueue.ARCHIVE_QUEUE.PROCESSOR))
  private readonly archiveQueue: Queue;

  @Inject(getQueueToken(JobQueue.USER_RATE_QUEUE.PROCESSOR))
  private readonly userRateQueue: Queue;

  @Inject(getQueueToken(JobQueue.CRON_JOB.PROCESSOR))
  private readonly cronQueue: Queue;

  @Inject(getQueueToken(JobQueue.EMPLOYEE_ACTIVITY.PROCESSOR))
  private readonly employeeActivityQueue: Queue;

  @Inject(getQueueToken(JobQueue.ROLE_QUEUE.PROCESSOR))
  private readonly roleQueue: Queue;

  @Inject(getQueueToken(JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESSOR))
  private readonly driverTaxReport: Queue;

  @Inject(getQueueToken(JobQueue.ROLE_QUEUE.PROCESSOR))
  configure(consumer: MiddlewareConsumer) {
    const serverAdapter = new ExpressAdapter();
    const {} = createBullBoard({
      queues: [
        new BullAdapter(this.driverQueue),
        new BullAdapter(this.restaurantQueue),
        new BullAdapter(this.transactionQueue),
        new BullAdapter(this.appSettingQueue),
        new BullAdapter(this.orderQueue),
        new BullAdapter(this.invoiceQueue),
        new BullAdapter(this.shiftWorkQueue),
        new BullAdapter(this.foodQueue),
        new BullAdapter(this.userQueue),
        new BullAdapter(this.restaurantAdsQueue),
        new BullAdapter(this.dispatcherQueue),
        new BullAdapter(this.socialQueue),
        new BullAdapter(this.bariaSocialQueue),
        new BullAdapter(this.restaurantInsightQueue),
        new BullAdapter(this.driverAgreementQueue),
        new BullAdapter(this.orderStatisticsQueue),
        new BullAdapter(this.archiveMgtm),
        new BullAdapter(this.archiveQueue),
        new BullAdapter(this.userRateQueue),
        new BullAdapter(this.cronQueue),
        new BullAdapter(this.employeeActivityQueue),
        new BullAdapter(this.roleQueue),
        new BullAdapter(this.bgDispatcherQueue),
        new BullAdapter(this.driverTaxReport),
      ],
      serverAdapter,
    });
    serverAdapter.setBasePath("/api/admin/queues");
    consumer
      .apply(
        basicAuth({
          users: {
            [process.env.BULL_BOARD_USERNAME]: process.env.BULL_BOARD_PASSWORD,
          },
          challenge: true,
          realm: process.env.BULL_BOARD_REALM,
        }),
        serverAdapter.getRouter()
      )
      .forRoutes("/admin/queues");
  }
}
