import { Modu<PERSON> } from '@nestjs/common';
import { JobQueue } from '..';
import { BullModule } from '@nestjs/bull';
import { EmployeeActivityProcessor } from './employee-activity-job.processor';
import { UserActivityService } from 'src/models/adminUserActivity/userActivity.service';
import { UserActivityModule } from 'src/models/adminUserActivity/userActivity.module';

@Module({
    imports: [
        BullModule.registerQueue({
            name: JobQueue.EMPLOYEE_ACTIVITY.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            },
        }),
        UserActivityModule,
    ],
    providers: [EmployeeActivityProcessor],
    exports: [EmployeeActivityProcessor],
})
export class EmployeeActivityJobModule {}
