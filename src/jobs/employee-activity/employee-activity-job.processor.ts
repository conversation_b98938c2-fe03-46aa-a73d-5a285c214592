import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';
import { UserActivityService } from 'src/models/adminUserActivity/userActivity.service';
import { EAdminEmployeeActivityAction, EAdminEmployeeActivityType } from 'src/entities/adminUserActivity.entity';

export class CreateEmployeeActivityDto {
    type: EAdminEmployeeActivityType;
    action: EAdminEmployeeActivityAction;
    old_data: Record<string, any>;
    new_data: Record<string, any>;
    provinceId: string;
    employee_id: number;
    user_id?: number;
}

@Processor(JobQueue.EMPLOYEE_ACTIVITY.PROCESSOR)
export class EmployeeActivityProcessor {
    private logger = new Logger(EmployeeActivityProcessor.name);

    constructor(private readonly userActivityService: UserActivityService) {}

    @Process(JobQueue.EMPLOYEE_ACTIVITY.PROCESS.CREATE)
    async createEmployeeActivity(job: Job<CreateEmployeeActivityDto>) {
        this.logger.log('[createEmployeeActivity] Start');
        const { type, action, old_data, new_data, provinceId, employee_id } = job.data;
        await this.userActivityService.createEmployeeActivity(
            type,
            action,
            old_data,
            new_data,
            provinceId,
            employee_id,
        );
    }
}
