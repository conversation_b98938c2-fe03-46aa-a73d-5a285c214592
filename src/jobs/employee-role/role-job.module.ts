import { Modu<PERSON> } from '@nestjs/common';
import { JobQueue } from '..';
import { BullModule } from '@nestjs/bull';
import { EmployeeRoleJobTaskService } from './role-job.task';
import { EmployeeRoleModule } from 'src/models/employee-role/employee-role.module';
import { EmployeeRoleJobProcessor } from './role-job.processor';

@Module({
    imports: [
        BullModule.registerQueue({
            name: JobQueue.ROLE_QUEUE.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
            },
        }),
        EmployeeRoleModule,
    ],
    providers: [EmployeeRoleJobTaskService, EmployeeRoleJobProcessor],
    exports: [EmployeeRoleJobTaskService],
})
export class EmployeeRoleJobModule {}
