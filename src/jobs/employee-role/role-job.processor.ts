import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { JobQueue } from '..';
import { EmployeeRoleJobTaskService } from './role-job.task';

export class SetPermissionsForAuthDto {
    roleId: number;
    permissions: string[];
}

@Processor(JobQueue.ROLE_QUEUE.PROCESSOR)
export class EmployeeRoleJobProcessor {
    private logger = new Logger(EmployeeRoleJobProcessor.name);

    constructor(private readonly roleJobTaskService: EmployeeRoleJobTaskService) {}

    @Process(JobQueue.ROLE_QUEUE.PROCESS.SET_PERMISSIONS_FOR_AUTH)
    async setPermissionsForAuth(job: Job<SetPermissionsForAuthDto>) {
        const { roleId, permissions } = job.data;
        this.logger.log(`Start setPermissionsForAuth with data: ${roleId} and ${permissions}`);
        if (!roleId || !permissions) {
            this.logger.error('[setPermissionsForAuth] Missing roleId or permissions');
            return;
        }
        await this.roleJobTaskService.setPermissionForAuth(roleId, permissions);
    }
}
