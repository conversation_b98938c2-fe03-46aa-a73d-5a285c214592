import { Injectable, Logger } from '@nestjs/common';
import { Timeout } from '@nestjs/schedule';
import { lastValueFrom } from 'rxjs';
import { ROLE_PERMISSIONS_EMPLOYEE } from 'src/common/middlewares/permissions.decorator';
import { EmployeeRoleService } from 'src/models/employee-role/employee-role.service';

@Injectable()
export class EmployeeRoleJobTaskService {
    private logger = new Logger(EmployeeRoleJobTaskService.name);

    constructor(private readonly employeeRoleService: EmployeeRoleService) {}

    async setPermissionForAuth(id: number, permissions: string[]) {
        ROLE_PERMISSIONS_EMPLOYEE[id] = permissions;
    }

    @Timeout(5000)
    async initPermissionsByRole() {
        try {
            this.logger.warn(`[initPermissionsByRole] start`);
            const rolesHavePermissions = await this.getRolesHavePermissions();

            for (let i = 0; i < rolesHavePermissions.length; i++) {
                const { id, permissions } = rolesHavePermissions[i];
                const permissionsName = permissions.map(({ name }) => name);
                await this.setPermissionForAuth(id, permissionsName);
            }
            this.logger.warn(`[initPermissionsByRole] done`);
        } catch (error) {
            this.logger.error(`[initPermissionsByRole] error: ${error.message}`);
        }
    }

    async getRolesHavePermissions() {
        const maxRetries = 10;
        let retries = 0;
        let rolesHavePermissions = null;

        while (retries < maxRetries) {
            try {
                const data = await lastValueFrom(await this.employeeRoleService.getAllRolesHavePermissions());
                if (data) {
                    rolesHavePermissions = data;
                    break;
                }
            } catch (error) {
                this.logger.error(
                    `[getRolesHavePermissions] Delivery service is not available ${retries}/${maxRetries}`,
                );
            }
            retries++;
            await new Promise((resolve) => setTimeout(resolve, 5000));
        }

        if (!rolesHavePermissions) {
            throw new Error('[getRolesHavePermissions] Delivery service is not available');
        }

        return rolesHavePermissions;
    }
}
