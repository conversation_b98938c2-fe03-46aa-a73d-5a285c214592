import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { CronExpression, Timeout } from '@nestjs/schedule';
import { Queue } from 'bull';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helpter';
import { JobQueue, VietNamTimeZone } from '..';

@Injectable()
export class DriverTaxReportJobStartUp {
    private readonly logger = new Logger(DriverTaxReportJobStartUp.name);
    constructor(@InjectQueue(JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESSOR) private readonly queue: Queue) {}

    async setAggregateDateDriverTaxReportJob(): Promise<void> {
        try {
            this.logger.log('[setAggregateDateDriverTaxReportJob] Start');
            // remove delayed job before add new job
            const delayedJobs = await this.queue.getDelayed();
            const jobName = JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESS.AGGREGATE_DATE_DRIVER_TAX_REPORT;
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === jobName) {
                        await job.remove();
                    }
                }
            }

            // add new job with cron expression
            const jobId = generateJobIdByProcessName(
                jobName,
            );
            const job = await this.queue.add(
                jobName,
                {},
                {
                    jobId,
                    repeat: {
                        cron: CronExpression.EVERY_DAY_AT_5AM,
                        // every 11:53 Am
                        // cron: '42 13 * * *', // 11:53 AM every day
                        tz: VietNamTimeZone,
                        key: jobName,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );

            this.logger.log(
                `[setAggregateDateDriverTaxReportJob] JobId: ${job.id} | Repeat: ${JSON.stringify(job.opts.repeat)}`,
            );
        } catch (error) {
            this.logger.error(`[setAggregateDateDriverTaxReportJob] Error: ${error.message} ${error.stack}`);
        }
    }

    async setAggregateMonthDriverTaxReportJob(): Promise<void> {
        try {
            this.logger.log('[setAggregateMonthDriverTaxReportJob] Start');
            // remove delayed job before add new job
            const delayedJobs = await this.queue.getDelayed();
            const jobName = JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESS.AGGREGATE_MONTH_DRIVER_TAX_REPORT;
            if (delayedJobs && delayedJobs.length > 0) {
                for (const job of delayedJobs) {
                    if (job && job.name === jobName) {
                        await job.remove();
                    }
                }
            }

            // add new job with cron expression
            const jobId = generateJobIdByProcessName(
                jobName,
            );
            const job = await this.queue.add(
                jobName,
                {},
                {
                    jobId,
                    repeat: {
                        cron: '30 3 3 * *', // 3:30 AM every 3rd day of the month
                        // cron: CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_NOON,
                        // cron: '43 13 * * *', // for testing
                        tz: VietNamTimeZone,
                        key: jobName,
                    },
                    removeOnComplete: true,
                    attempts: 5,
                    backoff: 3000,
                },
            );

            this.logger.log(
                `[setAggregateMonthDriverTaxReportJob] JobId: ${job.id} | Repeat: ${JSON.stringify(job.opts.repeat)}`,
            );
        } catch (error) {
            this.logger.error(`[setAggregateMonthDriverTaxReportJob] Error: ${error.message} ${error.stack}`);
        }
    }

    @Timeout(5000)
    async startUp(): Promise<void> {
        try {
            await this.setAggregateDateDriverTaxReportJob();
            await this.setAggregateMonthDriverTaxReportJob();
        } catch (error) {
            this.logger.error(`[startUp] Error: ${error.message} ${error.stack}`);
        }
    }
}
