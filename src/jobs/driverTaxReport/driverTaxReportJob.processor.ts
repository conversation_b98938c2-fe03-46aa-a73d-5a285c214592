import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import * as moment from 'moment';

import { DriverTaxReportService } from 'src/models/driverTax/services/driverTaxReport.service';
import { JobQueue, VietNamTimeZone } from '..';
import { DriverTaxReportAggregateOptionsDto } from 'src/models/driverTax/dto';

@Processor(JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESSOR)
export class DriverTaxReportJobProcessor {
    private readonly logger = new Logger(DriverTaxReportJobProcessor.name);
    constructor(private readonly driverTaxReportService: DriverTaxReportService) {}

    @Process(JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESS.AGGREGATE_DATE_DRIVER_TAX_REPORT)
    async aggregateDateDriverTaxReport() {
        try {
            this.logger.log('[aggregateDateDriverTaxReport] Start');
            const prevMoment = moment().subtract(1, 'day').utcOffset(VietNamTimeZone);
            const prevDate = prevMoment.format('YYYY-MM-DD');
            const options = new DriverTaxReportAggregateOptionsDto({
                include_driver_without_income: true,
                overwrite: false,
            });
            const result = await this.driverTaxReportService.aggregateByDateRangeAllProvinces(
                prevDate,
                prevDate,
                options,
            );
            this.logger.log(
                `[aggregateDateDriverTaxReport] End | inputArgs: ${JSON.stringify({
                    prevDate,
                    options,
                })} | Result: ${result ? JSON.stringify(result) : null}`,
            );
        } catch (error) {
            this.logger.error(`[aggregateDateDriverTaxReport] Error: ${error.message} ${error.stack}`);
        }
    }

    @Process(JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESS.AGGREGATE_MONTH_DRIVER_TAX_REPORT)
    async aggregateMonthDriverTaxReport() {
        try {
            this.logger.log('[aggregateMonthDriverTaxReport] Start');
            const prevMonthMoment = moment().subtract(1, 'month').utcOffset(VietNamTimeZone);
            const prevYearMonth = prevMonthMoment.format('YYYY-MM');
            const options = new DriverTaxReportAggregateOptionsDto({
                include_driver_without_income: true,
                overwrite: false,
            });
            const result = await this.driverTaxReportService.aggregateByMonthYearAllProvinces(prevYearMonth, options);
            this.logger.log(
                `[aggregateMonthDriverTaxReport] End | inputArgs: ${JSON.stringify({
                    prevMonthMoment,
                    options,
                })} | Result: ${result ? JSON.stringify(result) : null}`,
            );
        } catch (error) {
            this.logger.error(`[aggregateMonthDriverTaxReport] Error: ${error.message} ${error.stack}`);
        }
    }
}
