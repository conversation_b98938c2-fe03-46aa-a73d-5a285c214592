import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { JobQueue } from '..';
import { DriverTaxReportJobProcessor } from './driverTaxReportJob.processor';
import { DriverTaxModule } from 'src/models/driverTax/driverTax.module';
import { DriverTaxReportJobStartUp } from './driverTaxReportJob.startup';

@Module({
    imports: [BullModule.registerQueue({ name: JobQueue.DRIVER_TAX_REPORT_QUEUE.PROCESSOR }), DriverTaxModule],
    providers: [DriverTaxReportJobProcessor, DriverTaxReportJobStartUp],
    exports: [DriverTaxReportJobProcessor],
})
export class DriverTaxReportJobModule {}
