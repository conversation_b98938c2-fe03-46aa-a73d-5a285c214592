export const JobQueue = {
  DRIVER_QUEUE: {
    PROCESSOR: "driver",
    PROCESS: {
      AGGREGATE_ORDER_AFTER_ORDER_DONE: "driver:orderDone",
      AGGREGATE_WEEK_RATING_FOR_NEW_REVIEW: "driver:review",
      AGGREGATE_CURRENT_WEEK_RATING: "driver:aggregateCurrentWeekRating",
      AGGREGATE_PREV_WEEK_RATING: "driver:aggregatePrevWeekRating",
      AGGREGATE_WEEK_ORDERS: "driver:aggregateWeekOrders",
      ADD_RANK_HISTORIES: "driver:addRankHistories",
      CREATE_WEEK_TIME_KEEPING: "driver:createWeekTimeKeeping",
      ENABLE_CAN_RECEIVE_JOB: "driver:enableCanReceiveJob",
      ENABLE_CAN_RECEIVE_PENDING_ORDER: "driver:enableCanReceivePendingOrder",
    },
  },
  RESTAURANT_QUEUE: {
    PROCESSOR: "restaurant",
    PROCESS: {
      AGGREGATE_REVENUE_AFTER_ORDER_DONE: "restaurant:orderDone",
      AGGREGATE_REVENUE_PER_DAY: "restaurant:revenuePerDay",
      AGGREGATE_REVENUE_PER_WEEK: "restaurant:revenuePerWeek",
      AGGREGATE_REVENUE_PER_MONTH: "restaurant:revenuePerMonth",
      CREATE_REVENUE_RECORD: "restaurant:createRevenueRecord",
      REOPEN_TIME: "restaurant:reopen",
      RE_INDEX_RESTAURANT_TO_ELS: "restaurant:reindexToEls",
      UPDATE_RESTAURANT_PROMOTIONS_TO_ELS: "restaurant:updatePromotionsToEls",
      UPDATE_TOTAL_ORDERS: "restaurant:updateTotalOrders",
      UPDATE_REVIEWS_SUMARY: "restaurant:updateReviewSummary",
      SUMMARY_RESTAURANT_TOTAL_ORDERS: "restaurant:summaryTotalOrders",
      // IDENTIFY_RESTAURANTS_AND_UPDATE_OPEN_STATUS: 'restaurant:identifyAndUpdateOpenStatus',
      UPDATE_RESTAURANT_ADS_IS_ACTIVE: "restaurant:updateRestaurantAdsIsActive",
    },
  },
  TRANSACTION_QUEUE: {
    PROCESSOR: "transaction",
  },
  APPSETTING_QUEUE: {
    PROCESSOR: "appSetting",
    PROCESS: {
      UPDATE_SHIPPING_FEE: "appSetting:updateShippingFee",
      CHECK_FEE_AUTO_UPDATE: "appSetting:checkFeeAutoUpdate",
      CHECK_COLLECTION_AUTO_ACTIVE: "appSetting:checkCollectionAutoActive",
      COLLECTION_AUTO_ACTIVE: "appSetting:collectionAutoActive",
      RESIZE_IMAGE: "appSetting:ResizeImage",
      TEMP_TABLE_GET_DATA: "appSetting:tempTableGetData",
      SYNC_TO_MAIN_TABLE: "appSetting:syncToMainTable",
      RESET_DEFAULT_TAX: "appSetting:resetDefaultTax",
    },
  },
  ORDER_QUEUE: {
    PROCESSOR: "order",
    PROCESS: {
      CANCEL_ORDER_WAITING_ONLINE_PAYMENT:
        "order:cancelOrderWaitingOnlinePayment",
      REQUEST_CANCELING_CONFIRMATION: "order:RequestCancelingConfirmation",
    },
  },
  INVOICE_QUEUE: {
    PROCESSOR: "invoice",
    PROCESS: {
      EXPORT_ORDER_USER_INVOICE_TO_MISA: "invoice:exportOrderUserInvoiceToMisa",
      EXPORT_ORDER_COMPANY_INVOICE_TO_MISA:
        "invoice:exportOrderCompanyInvoiceToMisa",
    },
  },
  SHIFT_WORK_QUEUE: {
    PROCESSOR: "shiftWork",
    PROCESS: {
      SHIFT_WORK_END: "shiftWork:end",
    },
  },
  FOOD_QUEUE: {
    PROCESSOR: "food",
    PROCESS: {
      FOOD_RESET_SALES_LIMIT: "food:resetSalesLimit",
      FOOD_SOLD_OUT_UNTIL: "food:sold-out-until",
    },
  },
  USER_QUEUE: {
    PROCESSOR: "user",
    PROCESS: {
      SYNC_NEW_DATA: "user:syncNewData",
      SYNC_NEW_USER: "user:syncNewUser",
      USER_STATISTIC: "user:statistic",
      SYNC_TO_PROVINCE: "user:syncToProvince",
      ORDERS_RATING: "user:ordersRating",
      ORDERS_RATING_BY_PROVINCE: "user:ordersRatingByProvince",
      CRON_RESET_MONTH_ORDER_INFO: "user:cronResetMonthOrderInfo",
      CRON_USER_STATISTIC: "user:cronUserStatistic",
    },
  },
  USER_RATE_QUEUE: {
    PROCESSOR: "user-rate",
    PROCESS: {
      ORDERS_RATING: "user-rate:ordersRating",
      ORDERS_RATING_BY_PROVINCE: "user-rate:ordersRatingByProvince",
    },
  },
  RESTAURANT_ADS_QUEUE: {
    PROCESSOR: "restaurant_ads",
    PROCESS: {
      CHECK_EXPIRED_ADS: "restaurant_ad.check_expired_ads",
    },
  },
  CRON_JOB: {
    PROCESSOR: "cron",
    PROCESS: {
      CRON_ORDER_STATS: "order-stats-queue:cronOrderStats",
      CRON_ORDER_STATS_V2: "order-stats-queue:orderStatsByProvinceV2",
      CRON_ORDER_COMPLETE_STATS: "order-stats-queue:orderSuccessStats",
      CRON_ORDER_CANCELLED_STATS: "order-stats-queue:orderFailureStats",
      CRON_RESET_MONTH_ORDER_INFO: "user:cronResetMonthOrderInfo",
      CRON_USER_STATISTIC: "user:cronUserStatistic",
      CRON_SHIPPER_TIME_KEEPING: "driver:shipperTimeKeeping",
      CRON_RESET_FOOD_SALES_TOMORROW: "restaurant:resetFoodSalesTomorrow",
      CRON_RESET_DEFAULT_TAX_SETTING: "appSetting:resetDefaultTaxSetting",
      CRON_FETCH_ZALOPAY_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS:
        "order:fetchZaloPayOrderStatusAndUpdateToAllDBConnections",
      CRON_FETCH_MOMO_ORDER_STATUS_AND_UPDATE_TO_ALL_DB_CONNECTIONS:
        "order:fetchMomoOrderStatusAndUpdateToAllDBConnections",
      CRON_DAILY_SUMMARY_RESTAURANT_REVENUE:
        "restaurant:dailySummaryRestaurantRevenue",
      CRON_WEEKLY_SUMMARY_RESTAURANT_REVENUE:
        "restaurant:weeklySummaryRestaurantRevenue",
      CRON_MONTHLY_SUMMARY_RESTAURANT_REVENUE:
        "restaurant:monthlySummaryRestaurantRevenue",

      CRON_CREATE_RESTAURANT_REVENUE_DAILY:
        "restaurant:createRestaurantRevenueDaily",
      CRON_CREATE_RESTAURANT_REVENUE_WEEKLY:
        "restaurant:createRestaurantRevenueWeekly",
      CRON_CREATE_RESTAURANT_REVENUE_MONTH:
        "restaurant:createRestaurantRevenueMonth",
      CRON_FIND_RESTAURANT_HAS_FRAME_EXPIRED_AND_REMOVE_FRAME:
        "restaurant:findRestaurantHasFrameExpiredAndRemoveFrame",

      CRON_CHECK_RESTAURANT_ADS_ACTIVE_TIME_AND_UPDATE_STATUS:
        "restaurant_ads.checkRestaurantAdActiveTimeAndUpdateStatus",
      CRON_ADD_RANK_HISTORIES: "driver:addRankHistories",
      CRON_CREATE_WEEK_TIME_KEEPING_RECORDS:
        "driver:createWeekTimeKeepingRecords",
      CRON_CALCULATE_NET_WEEKLY_BONUS_BY_WORKING_DAYS:
        "driver:calculateNetWeeklyBonusByWorkingDays",

      CRON_RESET_DEFAULT_TAX: "appSetting:resetDefaultTax",
      CRON_INACTIVE_EXPIRED_PROMOTIONS: "appSetting:inactiveExpiredPromotions",
      CRON_RECALCULATE_COMPLETED_ORDERS: "driver:recalculateCompletedOrders",
    },
  },
  DISPATCH_QUEUE: {
    PROCESSOR: "dispatch",
  },
  BACKGROUND_DISPATCH_QUEUE: {
    PROCESSOR: "bg_dispatch",
  },
  SOCIAL_QUEUE: {
    PROCESSOR: "social",
  },
  BARIA_SOCIAL_QUEUE: {
    PROCESSOR: "baria_social",
  },
  RESTAURANT_INSIGHT_QUEUE: {
    PROCESSOR: "restaurantInsight",
    PROCESS: {
      AGGREGATE_INCOME_STATEMENT: "restaurantInsight:aggregateIncomeStatement",
      BATCH_JOB_PROCESS: "restaurantInsight:batchJobProcess",
      CRON_JOB_AGGREGATE_INCOME_STATEMENT_DAILY:
        "restaurantInsight:cronJobAggregateIncomeStatementDaily",
      CRON_JOB_AGGREGATE_INCOME_STATEMENT_WEEKLY:
        "restaurantInsight:cronJobAggregateIncomeStatementWeekly",
      CRON_JOB_AGGREGATE_INCOME_STATEMENT_MONTHLY:
        "restaurantInsight:cronJobAggregateIncomeStatementMonthly",
      CREATE_BATCH_JOB_TO_AGGREGATE_INCOME_BY_PROVINCE:
        "restaurantInsight:createBatchJobToAggregateIncomeByProvince",

      CREATE_BATCH_JOB_TO_AGGREGATE_ORDER_FOR_EXPORT_BY_PROVINCE:
        "restaurantInsight:createBatchJobToAggregateOrderForExportByProvince",

      CRON_JOB_AGGREGATE_ORDER_FOR_SHEET_STATEMENT_MONTHLY:
        "restaurantInsight:cronJobAggregateOrderForSheetStatementMonthly",
      CRON_JOB_AGGREGATE_ORDER_FOR_SHEET_STATEMENT_WEEKLY:
        "restaurantInsight:cronJobAggregateOrderForSheetStatementWeekly",
      CRON_JOB_AGGREGATE_ORDER_FOR_SHEET_STATEMENT_DAILY:
        "restaurantInsight:cronJobAggregateOrderForSheetStatementDaily",

      CRON_JOB_AGGREGATE_QUANTITY_SOLD_FOODS_WEEKLY:
        "restaurantInsight:cronJobAggregateQuantitySoldFoodsWeekly",

      CREATE_BATCH_JOB_INIT_AGGREGATE_QUANTITY_SOLD_FOODS_BY_PROVINCE:
        "restaurantInsight:initBatchJobToAggregateQuantitySoldFoodsByProvince",

      CREATE_BATCH_JOB_AGGREGATE_QUANTITY_SOLD_FOODS_BY_PROVINCE:
        "restaurantInsight:createBatchJobToAggregateQuantitySoldFoodsByProvince",
      INIT_BATCH_JOB_DELIVERY_REVENUE_DRIVERS_DAY:
        "restaurantInsight:initBatchJobDeliveryRevenueDriversDay",
      INIT_BATCH_JOB_DELIVERY_REVENUE_DRIVERS_WEEK:
        "restaurantInsight:initBatchJobDeliveryRevenueDriversWeek",

      CRON_BATCH_JOB_DELIVERY_REVENUE_DRIVERS_DAILY:
        "restaurantInsight:cronBatchJobDeliveryRevenueDriversDaily",
      CRON_BATCH_JOB_DELIVERY_REVENUE_DRIVERS_WEEKLY:
        "restaurantInsight:cronBatchJobDeliveryRevenueDriversWeekly",

      CREATE_BATCH_JOB_DELIVERY_REVENUE_DRIVERS_BY_PROVINCE:
        "restaurantInsight:createBatchJobDeliveryRevenueDriversByProvince",
    },
  },
  MERCHANT_Contract_QUEUE: {
    PROCESSOR: "merchant-contract",
    PROCESS: {
      GENERATE_MERCHANT_Contract: "merchant-contract:generate",
      SIGN_MERCHANT_Contract: "merchant-contract:sign",
    },
  },
  DRIVER_AGREEMENT_JOB_QUEUE: {
    PROCESSOR: "driver-agreement-job",
    PROCESS: {
      GENERATE_DRIVER_AGREEMENT: "driver-agreement-job:generate",
      GENERATE_DRIVER_AGREEMENT_FOR_NEW_DRIVER:
        "driver-agreement-job:generateForNewDriver",
    },
  },
  ORDER_STATS_JOB_QUEUE: {
    PROCESSOR: "order-stats-queue",
    PROCESS: {
      ORDER_COMPLETE_STATS_REGENERATE:
        "order-stats-queue:orderSuccessRegenerate",
      ORDER_CANCELLED_STATS_REGENERATE:
        "order-stats-queue:orderCancelRegenerate",
      ORDER_STATS_BY_PROVINCE: "order-stats-queue:orderStatsByProvince",
      ORDER_STATS_BY_PROVINCE_V2: "order-stats-queue:orderStatsByProvinceV2",
      ORDER_STATS_BY_GROUP_PROVINCES:
        "order-stats-queue:orderStatsByGroupProvinces",
      ORDER_STATS_REGENERATE: "order-stats-queue:orderStatsRegenerate",
    },
  },
  ARCHIVE_QUEUE: {
    PROCESSOR: "archive",
    PROCESS: {},
  },
  ARCHIVE_EXECUTION_MGTM_QUEUE: {
    PROCESSOR: "archive-execution-mgtm",
    PROCESS: {},
  },
  SELLER_JOB_QUEUE: {
    PROCESSOR: "seller",
    PROCESS: {
      SELLER_REVIEW_SUMMARY: "seller:review-summary",
    },
  },
  EMPLOYEE_ACTIVITY: {
    PROCESSOR: "employee-activity-queue",
    PROCESS: {
      CREATE: "employee-activity:create",
    },
  },
  ROLE_QUEUE: {
    PROCESSOR: "role-queue",
    PROCESS: {
      SET_PERMISSIONS_FOR_AUTH: "role:setPermissionsForAuth",
    },
  },
  DRIVER_TAX_REPORT_QUEUE: {
    PROCESSOR: "driver-tax-report",
    PROCESS: {
      AGGREGATE_DATE_DRIVER_TAX_REPORT: "driver-tax-report:aggregate-date",
      AGGREGATE_MONTH_DRIVER_TAX_REPORT: "driver-tax-report:aggregate-month",
    },
  },
};

export const VietNamTimeZone = "Asia/Ho_Chi_Minh";
export const VietNamTimeZoneNum = 7;

export enum ECronjobs {
  ResetUserMonthOrders = "ResetUserMonthOrders",
  CreateRestaurantRevenueDaily = "CreateRestaurantRevenueDaily",
  CreateRestaurantRevenueWeekly = "CreateRestaurantRevenueWeekly",
  CreateRestaurantRevenueMonth = "CreateRestaurantRevenueMonth",
  AddShipperRankHistories = "AddShipperRankHistories",
  CreateNewWeekShipperTimeKeeping = "CreateNewWeekShipperTimeKeeping",
  ResetDefaultTaxSetting = "ResetDefaultTaxSetting",
  InactiveExpiredPromotions = "InactiveExpiredPromotions",
  CheckFeeAutoUpdate = "CheckFeeAutoUpdate",
  CheckExpiredAds = "CheckExpiredAds",
  WeeklyUserStatistic = "WeeklyUserStatistic",
  UserOrdersRating = "UserOrdersRating",
  SellerReviewSummary = "SellerReviewSummary",
}

export const CronJobDescriptions = {
  [ECronjobs.ResetUserMonthOrders]:
    "Reset thông tin tổng hợp đơn hàng của client theo tháng (giành cho chương trình nuôi gấu mèo)",
  [ECronjobs.CreateRestaurantRevenueDaily]:
    "Tạo thông tin doanh thu mặc định cho nhà hàng theo ngày",
  [ECronjobs.CreateRestaurantRevenueWeekly]:
    "Tạo thông tin doanh thu mặc định cho nhà hàng theo tuần",
  [ECronjobs.CreateRestaurantRevenueMonth]:
    "Tạo thông tin doanh thu mặc định cho nhà hàng theo tháng",
  [ECronjobs.AddShipperRankHistories]: "Lưu lịch sử hạng của shipper theo tuần",
  [ECronjobs.CreateNewWeekShipperTimeKeeping]:
    "Tạo thông tin chấm công mặc định cho shipper",
};
