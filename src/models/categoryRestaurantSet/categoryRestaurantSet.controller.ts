import { Controller, Get, Post, Body, Delete, Param } from '@nestjs/common';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { CategoryRestaurantSetService } from './categoryRestaurantSet.service';
import { CreateCategoryRestaurantDto } from './dto/createCategoryRestaurantSet.dto';
import { DeleteCategoryRestaurantSetDto } from './dto/deleteCategoryRestaurantSet.dto';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('category-restaurant-sets')
export class CategoryRestaurantSetController {
    constructor(private categoryRestaurantSetService: CategoryRestaurantSetService) {}

    @Get('/:id')
    async findByCategory(@Param('id') id: number, @HeaderProvince() provinceId: string) {
        return this.categoryRestaurantSetService.findByCategory(id, provinceId);
    }

    @Post()
    async create(@Body() body: CreateCategoryRestaurantDto, @HeaderProvince() provinceId: string) {
        return this.categoryRestaurantSetService.create(body, provinceId);
    }

    @Delete()
    async delete(@Body() body: DeleteCategoryRestaurantSetDto, @HeaderProvince() provinceId: string) {
        return this.categoryRestaurantSetService.delete(body, provinceId);
    }
}
