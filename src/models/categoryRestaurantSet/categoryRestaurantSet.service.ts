import { Injectable } from '@nestjs/common';
import * as _ from 'lodash';
import { CategoryRestaurantSet } from 'src/entities/categoryRestaurantSet.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { CreateCategoryRestaurantDto } from './dto/createCategoryRestaurantSet.dto';
import { DeleteCategoryRestaurantSetDto } from './dto/deleteCategoryRestaurantSet.dto';
import { UpdateCategoryRestaurantSetDto } from './dto/updateCategoryRestaurantSet.dto';

@Injectable()
export class CategoryRestaurantSetService {
    constructor() {}
    getRepository(provinceId: string) {
        return DatabaseService.getRepositoryByProvinceId(CategoryRestaurantSet, provinceId);
    }

    async updateByCategory(dto: UpdateCategoryRestaurantSetDto, provinceId: string) {
        const { category_id, restaurant_ids } = dto;
        const categoryDb = await this.findByCategory(category_id, provinceId);
        const restaurantIds = categoryDb.map((item) => item.restaurant_id);
        const restaurantIdDeletes = _.difference(restaurantIds, restaurant_ids);
        const restaurantIdInserts = _.difference(restaurant_ids, restaurantIds);

        console.log({
            restaurantIdDeletes,
            restaurantIdInserts,
        });

        const promiseDeletes = restaurantIdDeletes.map((id) =>
            this.delete({ category_id, restaurant_id: id }, provinceId),
        );

        const promiseInserts = restaurantIdInserts.map((id) => {
            this.create({ category_id, restaurant_id: id }, provinceId);
        });
        await Promise.all(promiseDeletes);
        await Promise.all(promiseInserts);
        return true;
    }

    async findByCategory(categoryId: number, provinceId: string) {
        return await this.getRepository(provinceId)
            .createQueryBuilder('restaurantSet')
            .leftJoinAndSelect('restaurantSet.restaurant', 'restaurant')
            .where('restaurantSet.category_id = :categoryId', { categoryId })
            .getMany();
    }

    async finByRestaurant(restaurantId: number, provinceId: string) {
        return await this.getRepository(provinceId).find({
            where: {
                restaurant_id: restaurantId,
            },
            relations: ['category'],
        });
    }

    async delete(data: DeleteCategoryRestaurantSetDto, provinceId: string) {
        const { category_id, restaurant_id } = data;
        return await this.getRepository(provinceId).delete({ category_id, restaurant_id });
    }

    async create(data: CreateCategoryRestaurantDto, provinceId: string) {
        const { category_id, restaurant_id } = data;
        const categoryRestaurantSet = new CategoryRestaurantSet();
        categoryRestaurantSet.category_id = category_id;
        categoryRestaurantSet.restaurant_id = restaurant_id;

        return await this.getRepository(provinceId).save(categoryRestaurantSet);
    }
}
