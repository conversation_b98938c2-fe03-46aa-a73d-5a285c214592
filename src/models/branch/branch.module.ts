import { Module } from '@nestjs/common';
import { BranchService } from './branch.service';
import { BranchController } from './branch.controller';
import { RestaurantService } from '../restaurant/services/restaurant.service';
import { AwsS3Module } from 'src/providers/aws/awsS3.module';
import { RestaurantReviewSummaryService } from '../restaurantReview/services/restaurantReviewSummary.service';
import { RestaurantUpdateHistoryService } from '../restaurantUpdateHistory/restaurantUpdateHistory.service';
import { RestaurantPublisher } from '../restaurant/publishers/restaurant.publisher';
import { RestaurantLocationService } from '../restaurant/services/restaurantLocation.service';
import { ProvinceService } from '../province/province.service';
import { FirebaseAdmin } from 'src/providers/firebase/firebase.service';
import { LocationModule } from '../location/location.module';
import { RestaurantJobModule } from 'src/jobs/restaurants/restaurantJob.module';
import { RestaurantPaymentMethodsModule } from '../restaurant-payment-methods/restaurant-payment-methods.module';
import { RestaurantCounterModule } from '../restaurantCounter/restaurantCounter.module';
import { DynamicLinkModule } from '../dynamicLink/dynamicLink.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserModule } from '../user/user.module';
import { RabbitMQEventModule } from 'src/rabbitMQ/rabbitMQ.module';
import { EmployeeService } from '../employee/employee.service';
import { RestaurantBusinessHourService } from '../restaurant/services/restaurantBusinessHour.service';
@Module({
    controllers: [BranchController],
    providers: [
        BranchService,
        FirebaseAdmin,
        RestaurantService,
        RestaurantReviewSummaryService,
        RestaurantBusinessHourService,
        RestaurantUpdateHistoryService,
        RestaurantPublisher,
        ProvinceService,
        RestaurantLocationService,
        EmployeeService,
    ],
    imports: [
        AwsS3Module,
        UserModule,
        LocationModule,
        RestaurantJobModule,
        RestaurantPaymentMethodsModule,
        RestaurantCounterModule,
        /* DynamicLinkModule.forRoot({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                const {
                    domainUrlPrefix,
                    AndroidPackageName,
                    iOSBundleId,
                    iOSAppStoreId,
                    firebaseDynamicLinksAPIKey,
                    desktopFallbackLink,
                    domainUrlBranchIO,
                    branchIOKey,
                } = configService.get('dynamicLinkConfig');
                return {
                    domainUrlPrefix,
                    AndroidPackageName,
                    iOSBundleId,
                    iOSAppStoreId,
                    firebaseDynamicLinksAPIKey,
                    desktopFallbackLink,
                    domainUrlBranchIO,
                    branchIOKey,
                };
            },
        }), */
        RabbitMQEventModule,
    ],
})
export class BranchModule {}
