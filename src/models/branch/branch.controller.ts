import { Body, Controller, Post, UseGuards, Query, Get, Put, Param, ParseIntPipe, Delete } from '@nestjs/common';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { BranchService } from './branch.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { GetBranchDto } from './dto/get-branch.dto';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { UpdateBranchDto } from './dto/update-branch.dto';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('branches')
@UseGuards(AuthGuard)
export class BranchController {
    constructor(private branchService: BranchService) {}

    @Get()
    async getListBranch(@Query() param: GetBranchDto, @HeaderProvince() provinceId: string) {
        const branches = await this.branchService.getList(param, provinceId);
        return { items: branches[0], total: branches[1] };
    }

    @Post('branch')
    async createBranch(@Body(new HttpValidationPipe()) params: CreateBranchDto, @HeaderProvince() provinceId: string) {
        return await this.branchService.createBranch(params, provinceId);
    }

    @Put(':branchId')
    async updateBranch(
        @Param('branchId', new ParseIntPipe()) branchId: number,
        @Body(new HttpValidationPipe()) body: UpdateBranchDto,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.branchService.updateBranch(branchId, provinceId, body);
    }

    @Delete(':branchId')
    async deleteBranch(@Param('branchId', new ParseIntPipe()) branchId: number, @HeaderProvince() provinceId: string) {
        return await this.branchService.deleteBranch(branchId, provinceId);
    }
}
