import { Injectable, BadRequestException } from '@nestjs/common';
import { Branch } from 'src/entities/branch.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { GetBranchDto } from './dto/get-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { RestaurantService } from '../restaurant/services/restaurant.service';
import { EntityManager } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { SEARCH_RESTAURANTS_API_ENDPOINT } from 'src/common/constants/api.constant';

@Injectable()
export class BranchService {
    constructor(private restaurantService: RestaurantService, private configService: ConfigService) {}
    async getList({ page, limit, search }: GetBranchDto, provinceId: string) {
        const builder = DatabaseService.getRepositoryByProvinceId(Branch, provinceId).createQueryBuilder('branch');
        if (search) {
            builder.andWhere(`branch.name LIKE :search OR branch.description LIKE :search`, { search: `%${search}%` });
        }
        builder
            .orderBy('branch.id', 'DESC')
            .limit(limit)
            .offset(page * limit);
        return await builder.getManyAndCount();
    }
    async createBranch({ name, description, image }: CreateBranchDto, provinceId: string) {
        // const branch = new Branch({ name, description, image });
        const existed_branch = await DatabaseService.getRepositoryByProvinceId(Branch, provinceId).findOne({
            where: { name },
        });
        if (existed_branch) {
            throw new BadRequestException('Tên này đã tồn tại!');
        }
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(
            async (entityManager: EntityManager) => {
                const newBranch = new Branch({
                    name,
                    description,
                    image,
                });
                const data = await entityManager.save(newBranch);
                data.api =
                    this.configService.get('CLIENT_API_DOMAIN') +
                    '/' +
                    SEARCH_RESTAURANTS_API_ENDPOINT +
                    '?branchId=' +
                    data.id;
                return await entityManager.save(data);
            },
        );
        // return await DatabaseService.getRepositoryByProvinceId(Branch, provinceId).save(branch);
    }

    async getBranchById(branch_id: number, provinceId: string) {
        const branch = await DatabaseService.getRepositoryByProvinceId(Branch, provinceId).findOne({
            where: { id: branch_id },
            relations: ['restaurants'],
        });
        return branch;
    }

    async updateBranch(branch_id: number, provinceId: string, updateBranchDto: UpdateBranchDto) {
        const { name, description, image, restaurant_ids } = updateBranchDto;
        const branch = await this.getBranchById(branch_id, provinceId);
        if (!branch) {
            throw new BadRequestException('Branch not found!');
        }
        const updateData: QueryDeepPartialEntity<Branch> = {};
        if (name) {
            updateData.name = name;
        }
        if (description) {
            updateData.description = description;
        }
        if (image) {
            updateData.image = image;
        }
        if (restaurant_ids && restaurant_ids.length) {
            await this.restaurantService.updateBranchForRestaurants(restaurant_ids, provinceId, branch_id);
        }
        return await DatabaseService.getRepositoryByProvinceId(Branch, provinceId).update(
            { id: branch_id },
            updateData,
        );
    }

    async deleteBranch(branch_id: number, provinceId: string) {
        const branch = await this.getBranchById(branch_id, provinceId);
        if (!branch) throw new BadRequestException('Không tìm thấy branch.');
        if (branch.restaurants && branch.restaurants.length > 0) {
            const restaurant_ids = branch.restaurants.map((e) => e.id);
            await this.restaurantService.deleteBranchForRestaurants(restaurant_ids, provinceId, branch_id);
        }
        return await DatabaseService.getRepositoryByProvinceId(Branch, provinceId).delete({ id: branch_id });
    }
}
