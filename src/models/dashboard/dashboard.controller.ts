import { Controller, Get, Query, Req } from '@nestjs/common';
import * as _ from 'lodash';
import { Request } from 'express';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { IncomeSummaryDto } from './dashboard.dto';
import { DashboardService } from './dashboard.service';
import { PROVINCE_HEADER } from 'src/common/constants';
import { CacheService } from 'src/providers/cache/cache.service';

const CLIENT_INCOME_DATA_KEY = 'CLIENT_INCOME_DATA_KEY';
const RESTAURANT_INCOME_DATA_KEY = 'RESTAURANT_INCOME_DATA_KEY';
const SUMMARY = 'SUMMARY';
import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('dashboard')
export class DashboardController {
    constructor(private readonly dashboardService: DashboardService, private cacheService: CacheService) {}

    @Get('summary')
    async summary(@Req() req: Request) {
        let data = await this.cacheService.get(SUMMARY, req.get(PROVINCE_HEADER));
        if (_.isEmpty(data)) {
            data = await this.dashboardService.summary();
            if (!_.isEmpty(data)) {
                this.cacheService.set(`SUMMARY`, req.get(PROVINCE_HEADER), data);
            }
            return data;
        } else {
            return data;
        }
    }

    @Get('client-income-data')
    async clientIncomeData(@Query(new HttpValidationPipe()) query: IncomeSummaryDto, @Req() req: Request) {
        let data = await this.cacheService.get(`${CLIENT_INCOME_DATA_KEY}_${query.year}`, req.get(PROVINCE_HEADER));
        if (_.isEmpty(data)) {
            data = await this.dashboardService.clientIncomeSummary(query);
            if (!_.isEmpty(data)) {
                this.cacheService.set(`${CLIENT_INCOME_DATA_KEY}_${query.year}`, req.get(PROVINCE_HEADER), data);
            }
            return data;
        } else {
            return data;
        }
    }

    @Get('restaurant-income-data')
    async restaurantIncomeData(@Query(new HttpValidationPipe()) query: IncomeSummaryDto, @Req() req: Request) {
        let data = await this.cacheService.get(`${RESTAURANT_INCOME_DATA_KEY}_${query.year}`, req.get(PROVINCE_HEADER));
        data = null;
        if (_.isEmpty(data)) {
            data = await this.dashboardService.restaurantIncomeSummary(query);
            if (!_.isEmpty(data)) {
                this.cacheService.set(`${RESTAURANT_INCOME_DATA_KEY}_${query.year}`, req.get(PROVINCE_HEADER), data);
            }
            return data;
        } else {
            return data;
        }
    }
}
