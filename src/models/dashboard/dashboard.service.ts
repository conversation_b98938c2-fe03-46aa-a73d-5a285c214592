import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { OrderStatusEnum, PROVINCE_HEADER, Role } from 'src/common/constants';
import { Order } from 'src/entities/order.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { EntityManager, LessThan, MoreThan, MoreThanOrEqual } from 'typeorm';
import * as moment from 'moment';
import { Restaurant } from 'src/entities/restaurant.entity';
import { User } from 'src/entities/user.entity';
import * as _ from 'lodash';
import { IncomeSummaryDto } from './dashboard.dto';
import { FoodOrder } from 'src/entities/foodOrder.entity';
import { RestaurantRevenue } from 'src/entities/restaurantRevenue.entity';

@Injectable({ scope: Scope.REQUEST })
export class DashboardService {
    private entityManager: EntityManager;
    constructor(@Inject(REQUEST) private request: Request) {
        this.entityManager = DatabaseService.getEntityManager(request);
    }

    async summary() {
        const [
            totalOrders,
            totalOrdersToday,
            totalSuccessfullOrdersToday,
            totalCanceledOrdersToday,
            totalRestaurants,
            totalRestaurantParners,
            totalClosedRestaurants,
            totalUsers,
            totalClients,
            totalShippers,
            totalMerchants,
        ] = await Promise.all([
            this.entityManager.count(Order, { where: { order_status_id: OrderStatusEnum.ARRIVED } }),
            this.entityManager
                .createQueryBuilder(Order, 'order')
                .where({ order_date: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()) })
                .getCount(),
            // this.entityManager.count(Order, {
            //     where: {
            //         created_at: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()),
            //     },
            // }),
            this.entityManager
                .createQueryBuilder(Order, 'order')
                .where({ order_status_id: OrderStatusEnum.ARRIVED })
                .andWhere({ order_date: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()) })
                .getCount(),
            // this.entityManager.count(Order, {
            //     where: {
            //         order_status_id: OrderStatusEnum.ARRIVED,
            //         created_at: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()),
            //     },
            // }),
            this.entityManager
                .createQueryBuilder(Order, 'order')
                .where({ order_status_id: OrderStatusEnum.CANCELED })
                .andWhere({ order_date: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()) })
                .getCount(),
            // this.entityManager.count(Order, {
            //     where: {
            //         order_status_id: OrderStatusEnum.CANCELED,
            //         created_at: MoreThanOrEqual(moment().utcOffset(0).startOf('day').toISOString()),
            //     },
            // }),
            this.entityManager.count(Restaurant),
            this.entityManager.count(Restaurant, {
                where: {
                    cooperating: true,
                    status: true,
                },
            }),
            this.entityManager.count(Restaurant, {
                where: {
                    status: false,
                },
            }),
            this.entityManager
                .createQueryBuilder(User, 'user')
                .innerJoin(
                    'user.roles',
                    'roles',
                    `roles.name IN (${[Role.client, Role.driver, Role.manager].map((r) => `'${r}'`).join(',')})`,
                )
                .getCount(),
            this.entityManager
                .createQueryBuilder(User, 'user')
                .innerJoin('user.roles', 'roles', `roles.name = '${Role.client}'`)
                .getCount(),
            this.entityManager
                .createQueryBuilder(User, 'user')
                .innerJoin('user.roles', 'roles', `roles.name = '${Role.driver}'`)
                .getCount(),
            this.entityManager
                .createQueryBuilder(User, 'user')
                .innerJoin('user.roles', 'roles', `roles.name = '${Role.manager}'`)
                .getCount(),
        ]);

        return {
            order: {
                totalOrders,
                totalCanceledOrdersToday,
                totalOrdersToday,
                totalSuccessfullOrdersToday,
            },
            restaurant: {
                totalRestaurants,
                totalRestaurantParners,
                totalClosedRestaurants,
            },
            user: {
                totalUsers,
                totalClients,
                totalShippers,
                totalMerchants,
            },
        };
    }

    async clientIncomeSummary({ year }: IncomeSummaryDto) {
        const promises = [];

        for (let i = 0; i <= 11; i++) {
            const startTime = moment(year, 'YYYY').set({ months: i }).startOf('months');
            const endTime = moment(year, 'YYYY').set({ months: i }).endOf('months');

            const builder = this.entityManager
                .getRepository(Order)
                .createQueryBuilder('order')
                .select('SUM(order.delivery_fee + order.tax)', 'totalDeliveryFee')
                .addSelect('SUM(order.service_fee)', 'totalServiceFee')
                .where(`order.order_date BETWEEN '${startTime.toISOString()}' AND '${endTime.toISOString()}'`)
                .andWhere(`order.order_status_id = ${OrderStatusEnum.ARRIVED}`)
                .execute();

            promises.push(builder);
        }
        const arrResult = await Promise.all(promises);

        return arrResult.map((item) => {
            return {
                totalDeliveryFee: _.toNumber(item[0].totalDeliveryFee),
                totalServiceFee: _.toNumber(item[0].totalServiceFee),
                totalTradeDiscountOfDeliveryFee: _.toNumber((_.toNumber(item[0].totalDeliveryFee) * 20) / 100),
            };
        });
    }

    async restaurantIncomeSummary({ year }: IncomeSummaryDto) {
        const promises = [];

        for (let i = 0; i <= 11; i++) {
            const startTime = moment(year, 'YYYY').set({ months: i }).startOf('months');
            const endTime = moment(year, 'YYYY').set({ months: i }).endOf('months');
            /* let builder = this.entityManager.getRepository(FoodOrder)
            .createQueryBuilder('foodOrder')
            .select('SUM((foodOrder.price + COALESCE(extras.price, 0)) * foodOrder.quantity)', 'totalPrice')
            .leftJoinAndSelect('foodOrder.extras', 'extras')
            .andWhere(qb => {
                const subQuery = qb.subQuery().from(Order, 'order')
                    .select('order.id')
                    .where(`order.created_at BETWEEN '${startTime.toISOString()}' AND '${endTime.toISOString()}'`)
                    .andWhere(`order.order_status_id = ${OrderStatusEnum.ARRIVED}`)
                    .getQuery();
                return `foodOrder.order_id IN ${subQuery}`
            })
            .execute() */

            const builder = this.entityManager
                .getRepository(RestaurantRevenue)
                .createQueryBuilder('restaurantRevenue')
                .select('SUM(restaurantRevenue.revenue)', 'totalPrice')
                .addSelect('SUM(restaurantRevenue.trade_discount)', 'tradeDiscount')
                .where(`restaurantRevenue.date BETWEEN '${startTime.toISOString()}' AND '${endTime.toISOString()}'`)
                .execute();

            promises.push(builder);
        }
        const arrResult = await Promise.all(promises);

        return arrResult.map((item) => {
            console.log(item);

            return {
                totalPrice: _.toNumber(item[0].totalPrice),
                tradeDiscount: _.toNumber(item[0].tradeDiscount).toFixed(3),
            };
        });
    }
}
