import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsO<PERSON>, ValidateNested } from "class-validator";
import { Type, Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class ResponseBodyDto {
  @ApiProperty({
    description: "Response index",
    example: 1,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  index: number;

  @ApiProperty({
    description: "Reference code for the response",
    example: "REF_123456789",
  })
  @IsString()
  referenceCode: string;
}

export class AcbWebhookResponseDto {
  @ApiProperty({
    description: "Response timestamp in ISO format",
    example: "2024-01-15T10:30:00Z",
  })
  @IsString()
  timestamp: string;

  @ApiProperty({
    description: "ACB response code - 00000000 for success",
    example: "00000000",
  })
  @IsString()
  responseCode: string;

  @ApiProperty({
    description: "Response message",
    example: "Success",
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: "Response body containing details",
    type: ResponseBodyDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ResponseBodyDto)
  responseBody: ResponseBodyDto;
}

// Helper function to create success response
export function createAcbSuccessResponse(
  referenceCode: string,
  index: number = 1
): AcbWebhookResponseDto {
  return {
    timestamp: new Date().toISOString(),
    responseCode: "00000000",
    message: "Success",
    responseBody: {
      index,
      referenceCode,
    },
  };
}

// Helper function to create error response
export function createAcbErrorResponse(
  errorCode: string,
  message: string,
  referenceCode: string,
  index: number = 1
): AcbWebhookResponseDto {
  return {
    timestamp: new Date().toISOString(),
    responseCode: errorCode,
    message,
    responseBody: {
      index,
      referenceCode,
    },
  };
}
