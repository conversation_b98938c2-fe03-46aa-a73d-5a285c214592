import { IsOptional, IsString, IsN<PERSON>ber, IsEnum, IsDateString } from 'class-validator';
import { EWebhookStatus } from '../../../entities/TrackingBankWebhookHistory.entity';

export class CreateWebhookHistoryDto {

    @IsOptional()
    @IsEnum(EWebhookStatus)
    status?: EWebhookStatus;

    @IsString()
    request_payload: string;

    @IsOptional()
    @IsString()
    response_data?: string;

    @IsOptional()
    @IsNumber()
    response_status_code?: number;

    @IsOptional()
    @IsString()
    error_message?: string;

    @IsOptional()
    @IsNumber()
    retry_count?: number;

    @IsOptional()
    @IsDateString()
    next_retry_at?: Date;

    @IsNumber()
    tracking_bank_transaction_id: number;

    @IsOptional()
    @IsNumber()
    notification_setting_id?: number;
}

export class UpdateWebhookHistoryDto {
    @IsOptional()
    @IsEnum(EWebhookStatus)
    status?: EWebhookStatus;

    @IsOptional()
    @IsString()
    response_data?: string;

    @IsOptional()
    @IsNumber()
    response_status_code?: number;

    @IsOptional()
    @IsString()
    error_message?: string;

    @IsOptional()
    @IsNumber()
    retry_count?: number;

    @IsOptional()
    @IsDateString()
    next_retry_at?: Date;
}

export class GetWebhookHistoryDto {
    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;

    @IsOptional()
    @IsEnum(EWebhookStatus)
    status?: EWebhookStatus;

    @IsOptional()
    @IsNumber()
    tracking_bank_transaction_id?: number;

    @IsOptional()
    @IsNumber()
    notification_setting_id?: number;

    @IsOptional()
    @IsString()
    from_date?: string = '';

    @IsOptional()
    @IsString()
    to_date?: string = '';
}
