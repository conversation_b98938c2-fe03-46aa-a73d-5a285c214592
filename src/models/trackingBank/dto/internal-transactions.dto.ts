import { <PERSON><PERSON><PERSON><PERSON>, Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { EWebhookStatus } from '../../../entities/TrackingBankWebhookHistory.entity';
import { ENotificationTrigger } from '../../../entities/TrackingBankNotificationSetting.entity';

export class GetInternalTransactionsDto {
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    limit?: number = 10;

    @IsOptional()
    @IsEnum(ENotificationTrigger)
    trigger_type?: ENotificationTrigger;

    @IsOptional()
    @IsEnum(EWebhookStatus)
    webhook_status?: EWebhookStatus;

    @IsOptional()
    @IsString()
    from_date?: string;

    @IsOptional()
    @IsString()
    to_date?: string;

    @IsOptional()
    @IsString()
    search?: string;

    @IsOptional()
    @IsString()
    notification_setting_name?: string;
}

export interface IInternalTransactionStatistics {
    total_transactions: number;
    success_webhooks: number;
    failed_webhooks: number;
    pending_webhooks: number;
    retry_webhooks: number;
    total_amount: number;
    deposit_amount: number;
    withdraw_amount: number;
}

export interface IInternalTransactionItem {
    id: number;
    transaction_code: string;
    amount: number;
    transaction_type: string;
    transaction_time: Date;
    current_balance: number;
    raw_data: string;
    is_verified: number;
    bank_code: string;
    tracking_bank_account: {
        id: number;
        account_number: string;
        account_name: string;
        bank_name: string;
    };
    latest_webhook_status?: EWebhookStatus;
    webhook_count: number;
    trigger_type?: ENotificationTrigger;
    notification_setting_name?: string;
    created_at: Date;
    updated_at: Date;
}
