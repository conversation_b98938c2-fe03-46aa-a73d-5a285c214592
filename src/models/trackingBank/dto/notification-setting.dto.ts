import { IsOptional, IsString, IsN<PERSON>ber, IsEnum, IsBoolean, IsObject, IsUrl } from 'class-validator';
import { ENotificationTrigger } from '../../../entities/TrackingBankNotificationSetting.entity';

export class CreateNotificationSettingDto {
    @IsString()
    name: string;

    @IsUrl()
    webhook_url: string;

    @IsOptional()
    @IsEnum(ENotificationTrigger)
    trigger_type?: ENotificationTrigger;

    @IsOptional()
    @IsString()
    custom_code?: string;

    @IsOptional()
    @IsNumber()
    custom_code_suffix_length?: number;

    @IsOptional()
    @IsNumber()
    amount_threshold?: number;

    @IsOptional()
    @IsObject()
    filter_conditions?: any;

    @IsOptional()
    @IsBoolean()
    is_active?: boolean;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsNumber()
    max_retry_attempts?: number;

    @IsOptional()
    @IsNumber()
    retry_delay_seconds?: number;

    @IsOptional()
    @IsObject()
    headers?: any;
}

export class UpdateNotificationSettingDto {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsUrl()
    webhook_url?: string;

    @IsOptional()
    @IsEnum(ENotificationTrigger)
    trigger_type?: ENotificationTrigger;

    @IsOptional()
    @IsString()
    custom_code?: string;

    @IsOptional()
    @IsNumber()
    custom_code_suffix_length?: number;

    @IsOptional()
    @IsNumber()
    amount_threshold?: number;

    @IsOptional()
    @IsObject()
    filter_conditions?: any;

    @IsOptional()
    @IsBoolean()
    is_active?: boolean;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsNumber()
    max_retry_attempts?: number;

    @IsOptional()
    @IsNumber()
    retry_delay_seconds?: number;

    @IsOptional()
    @IsObject()
    headers?: any;
}

export class GetNotificationSettingsDto {
    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;

    @IsOptional()
    @IsBoolean()
    is_active?: boolean;

    @IsOptional()
    @IsEnum(ENotificationTrigger)
    trigger_type?: ENotificationTrigger;

    @IsOptional()
    @IsString()
    search?: string = '';
}
