import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsIn, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SepayWebhookDto {
    @ApiProperty({
        description: 'Gateway name (e.g., TPBank)',
        example: 'TPBank'
    })
    @IsString()
    gateway: string;

    @ApiProperty({
        description: 'Transaction date in format YYYY-MM-DD HH:mm:ss',
        example: '2025-07-15 14:35:49'
    })
    @IsString()
    transactionDate: string;

    @ApiProperty({
        description: 'Bank account number',
        example: '***********'
    })
    @IsString()
    accountNumber: string;

    @ApiPropertyOptional({
        description: 'Sub account (nullable)',
        example: null
    })
    @IsOptional()
    @IsString()
    subAccount?: string;

    @ApiProperty({
        description: 'Transaction code',
        example: 'DIBINwsHAkwl'
    })
    @IsOptional()
    code?: string;

    @ApiProperty({
        description: 'Transaction content/description',
        example: 'DIBINwsHAkwl DibiVoice subscription Plan Standard Plan ****************'
    })
    @IsString()
    content: string;

    @ApiProperty({
        description: 'Transfer type: in (deposit) or out (withdrawal)',
        example: 'in'
    })
    @IsIn(['in', 'out'])
    transferType: 'in' | 'out';

    @ApiProperty({
        description: 'Detailed description of the transaction',
        example: 'BankAPINotify DIBINwsHAkwl DibiVoice subscription Plan Standard Plan ****************'
    })
    @IsString()
    description: string;

    @ApiProperty({
        description: 'Transfer amount in VND',
        example: 99500
    })
    @Transform(({ value }) => Number(value))
    @IsNumber()
    transferAmount: number;

    @ApiProperty({
        description: 'Reference code from bank',
        example: '669V602251966608'
    })
    @IsString()
    referenceCode: string;

    @ApiProperty({
        description: 'Accumulated balance after transaction',
        example: 1427034
    })
    @Transform(({ value }) => Number(value))
    @IsNumber()
    accumulated: number;

    @ApiProperty({
        description: 'Unique transaction ID from SEPAY',
        example: ********
    })
    @Transform(({ value }) => Number(value))
    @IsNumber()
    id: number;

    @ApiPropertyOptional({
        description: 'API key for authentication (can be passed via x-api-key header)',
        example: 'your-sepay-api-key'
    })
    @IsOptional()
    @IsString()
    apiKey?: string;
}

export class SepayWebhookResponseDto {
    @ApiProperty({
        description: 'Response status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Response message',
        example: 'Webhook processed successfully'
    })
    message: string;

    @ApiPropertyOptional({
        description: 'Transaction ID if created',
        example: 12345
    })
    @IsOptional()
    transactionId?: number;
}
