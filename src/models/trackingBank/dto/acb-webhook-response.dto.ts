import { ApiProperty } from "@nestjs/swagger";

export class AcbWebhookResponseBodyDto {
  @ApiProperty({
    description: "Response index",
    example: 1,
  })
  index: number;

  @ApiProperty({
    description: "Reference code from the request",
    example: "REQ_123456789",
  })
  referenceCode: string;
}

export class AcbWebhookResponseDto {
  @ApiProperty({
    description: "Response timestamp in ISO format",
    example: "2024-01-15T10:30:00.000Z",
  })
  timestamp: string;

  @ApiProperty({
    description: "Response code from ACB",
    example: "00000000",
    enum: ["00000000", "99999999"],
  })
  responseCode: string;

  @ApiProperty({
    description: "Response message",
    example: "Success",
  })
  message: string;

  @ApiProperty({
    description: "Response body data",
    type: AcbWebhookResponseBodyDto,
  })
  responseBody: AcbWebhookResponseBodyDto;
}