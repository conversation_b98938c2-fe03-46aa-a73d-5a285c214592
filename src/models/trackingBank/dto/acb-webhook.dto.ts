import {
  IsS<PERSON>,
  IsNumber,
  IsArray,
  IsObject,
  IsOptional,
  ValidateNested,
  IsIn,
  ArrayMinSize,
} from "class-validator";
import { Type, Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class VirtualAccountInfoDto {
  @ApiProperty({
    description: "Virtual account prefix code",
    example: "VA001",
  })
  @IsString()
  vaPrefixCd: string;

  @ApiPropertyOptional({
    description: "Virtual account number (nullable)",
    example: null,
  })
  @IsOptional()
  @IsString()
  vaNbr?: string | null;
}

export class TransactionEntityAttributeDto {
  @ApiPropertyOptional({
    description: "Transaction trace number",
    example: null,
  })
  @IsOptional()
  @IsString()
  traceNumber?: string | null;

  @ApiPropertyOptional({
    description: "Beneficiary name",
    example: null,
  })
  @IsOptional()
  @IsString()
  beneficiaryName?: string | null;

  @ApiPropertyOptional({
    description: "Beneficiary account number",
    example: null,
  })
  @IsOptional()
  @IsString()
  beneficiaryAccountNumber?: string | null;

  @ApiProperty({
    description: "Receiver bank name",
    example: "Asia Commercial Bank",
  })
  @IsString()
  receiverBankName: string;

  @ApiPropertyOptional({
    description: "Remitter name",
    example: null,
  })
  @IsOptional()
  @IsString()
  remitterName?: string | null;

  @ApiPropertyOptional({
    description: "Remitter account number",
    example: null,
  })
  @IsOptional()
  @IsString()
  remitterAccountNumber?: string | null;

  @ApiProperty({
    description: "Issuer bank name",
    example: "Asia Commercial Bank",
  })
  @IsString()
  issuerBankName: string;

  @ApiProperty({
    description: "Virtual account identifier",
    example: "VA001*********",
  })
  @IsString()
  virtualAccount: string;

  @ApiProperty({
    description: "Transaction reference number",
    example: "REF*********",
  })
  @IsString()
  referenceNumber: string;

  @ApiProperty({
    description: "Partner customer code",
    example: "CUST001",
  })
  @IsString()
  partnerCustomerCode: string;

  @ApiProperty({
    description: "Partner customer name",
    example: "Vill Vietnam Co., Ltd.",
  })
  @IsString()
  partnerCustomerName: string;

  @ApiProperty({
    description: "Partner customer type",
    example: "ORG",
    enum: ["ORG", "IND"],
  })
  @IsIn(["ORG", "IND"])
  partnerCustomerType: string;

  @ApiPropertyOptional({
    description: "Custom field 1",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom1?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 2",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom2?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 3",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom3?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 4",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom4?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 5",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom5?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 6",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom6?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 7",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom7?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 8",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom8?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 9",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom9?: string | null;

  @ApiPropertyOptional({
    description: "Custom field 10",
    example: null,
  })
  @IsOptional()
  @IsString()
  custom10?: string | null;

  @ApiProperty({
    description: "Transaction content/description",
    example: "Payment for order #12345",
  })
  @IsString()
  transactionContent: string;
}

export class TransactionDto {
  @ApiProperty({
    description: "Transaction status",
    example: "COMPLETED",
    enum: ["COMPLETED", "PENDING", "FAILED", "CANCELLED"],
  })
  @IsIn(["COMPLETED", "PENDING", "FAILED", "CANCELLED"])
  transactionStatus: string;

  @ApiProperty({
    description: "Transaction channel",
    example: "IBFT",
  })
  @IsString()
  transactionChannel: string;

  @ApiProperty({
    description: "Unique transaction code",
    example: "TXN*********",
  })
  @IsString()
  transactionCode: string;

  @ApiProperty({
    description: "Bank account number",
    example: *********,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  accountNumber: number;

  @ApiProperty({
    description: "Transaction date in ISO format",
    example: "2024-01-15T10:30:00Z",
  })
  @IsString()
  transactionDate: string;

  @ApiProperty({
    description: "Effective date in ISO format",
    example: "2024-01-15T10:30:00Z",
  })
  @IsString()
  effectiveDate: string;

  @ApiProperty({
    description: "Transaction type: debit or credit",
    example: "credit",
    enum: ["debit", "credit"],
  })
  @IsIn(["debit", "credit"])
  debitOrCredit: string;

  @ApiProperty({
    description: "Virtual account information",
    type: VirtualAccountInfoDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => VirtualAccountInfoDto)
  virtualAccountInfo: VirtualAccountInfoDto;

  @ApiProperty({
    description: "Transaction amount in VND",
    example: 100000,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: "Transaction entity attributes",
    type: TransactionEntityAttributeDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => TransactionEntityAttributeDto)
  transactionEntityAttribute: TransactionEntityAttributeDto;
}

export class RequestParamsDto {
  @ApiProperty({
    description: "Array of transactions",
    type: [TransactionDto],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => TransactionDto)
  transactions: TransactionDto[];
}

export class RequestMetaDto {
  @ApiProperty({
    description: "Request type",
    example: "NOTIFICATION",
    enum: ["NOTIFICATION"],
  })
  @IsIn(["NOTIFICATION"])
  requestType: string;

  @ApiProperty({
    description: "Request code",
    example: "TRANSACTION_UPDATE",
    enum: ["TRANSACTION_UPDATE", "BALANCE_UPDATE", "ACCOUNT_UPDATE"],
  })
  @IsIn(["TRANSACTION_UPDATE", "BALANCE_UPDATE", "ACCOUNT_UPDATE"])
  requestCode: string;
}

export class RequestDto {
  @ApiProperty({
    description: "Request metadata",
    type: RequestMetaDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => RequestMetaDto)
  requestMeta: RequestMetaDto;

  @ApiProperty({
    description: "Request parameters",
    type: RequestParamsDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => RequestParamsDto)
  requestParams: RequestParamsDto;
}

export class PaginationDto {
  @ApiProperty({
    description: "Current page number",
    example: 1,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  page: number;

  @ApiProperty({
    description: "Page size",
    example: 10,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  pageSize: number;

  @ApiProperty({
    description: "Total number of pages",
    example: 1,
  })
  @Transform(({ value }) => Number(value))
  @IsNumber()
  totalPage: number;
}

export class MasterMetaDto {
  @ApiProperty({
    description: "Client ID from ACB",
    example: "ACB_CLIENT_001",
  })
  @IsString()
  clientId: string;

  @ApiProperty({
    description: "Client request ID for tracking",
    example: "REQ_*********",
  })
  @IsString()
  clientRequestId: string;

  @ApiProperty({
    description: "Checksum for data integrity",
    example: "abc123def456",
  })
  @IsString()
  checksum: string;
}

export class AcbWebhookPayloadDto {
  @ApiProperty({
    description: "Master metadata for the webhook",
    type: MasterMetaDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => MasterMetaDto)
  masterMeta: MasterMetaDto;

  @ApiProperty({
    description: "Array of requests in the webhook",
    type: [RequestDto],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => RequestDto)
  requests: RequestDto[];

  @ApiProperty({
    description: "Pagination information",
    type: PaginationDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PaginationDto)
  pagination: PaginationDto;
}
