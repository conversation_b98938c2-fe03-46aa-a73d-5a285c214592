/**
 * ACB Response Codes Enum
 * Based on ACB webhook API documentation
 */
export enum AcbResponseCode {
  // Success codes
  SUCCESS = "00000000",

  // Client error codes (4xx equivalent)
  INVALID_REQUEST = "40000001",
  INVALID_SIGNATURE = "40000002",
  INVALID_TIMESTAMP = "40000003",
  INVALID_PAYLOAD = "40000004",
  MISSING_HEADERS = "40000005",
  UNAUTHORIZED = "40100001",
  FORBIDDEN = "40300001",

  // Server error codes (5xx equivalent)
  INTERNAL_ERROR = "50000001",
  SERVICE_UNAVAILABLE = "50300001",
  TIMEOUT = "50400001",
  DATABASE_ERROR = "50000002",
  PROCESSING_ERROR = "50000003",
}

/**
 * ACB Response Messages
 */
export const AcbResponseMessage = {
  [AcbResponseCode.SUCCESS]: "Success",
  [AcbResponseCode.INVALID_REQUEST]: "Invalid request format",
  [AcbResponseCode.INVALID_SIGNATURE]: "Invalid webhook signature",
  [AcbResponseCode.INVALID_TIMESTAMP]: "Invalid or expired timestamp",
  [AcbResponseCode.INVALID_PAYLOAD]: "Invalid payload structure",
  [AcbResponseCode.MISSING_HEADERS]: "Missing required headers",
  [AcbResponseCode.UNAUTHORIZED]: "Unauthorized request",
  [AcbResponseCode.FORBIDDEN]: "Forbidden access",
  [AcbResponseCode.INTERNAL_ERROR]: "Internal server error",
  [AcbResponseCode.SERVICE_UNAVAILABLE]: "Service temporarily unavailable",
  [AcbResponseCode.TIMEOUT]: "Request timeout",
  [AcbResponseCode.DATABASE_ERROR]: "Database operation failed",
  [AcbResponseCode.PROCESSING_ERROR]: "Webhook processing failed",
} as const;

/**
 * Helper function to get response message by code
 */
export function getAcbResponseMessage(code: AcbResponseCode): string {
  return AcbResponseMessage[code] || "Unknown error";
}
