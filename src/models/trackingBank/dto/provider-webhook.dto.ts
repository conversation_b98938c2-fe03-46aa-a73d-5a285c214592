import { IsEnum, IsString, IsBoolean, IsOptional, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { WebhookProviderType } from '../../../entities/TrackingBankProviderWebhook.entity';

export class CreateProviderWebhookDto {
  @ApiProperty({
    enum: WebhookProviderType,
    description: 'The webhook provider type',
  })
  @IsEnum(WebhookProviderType)
  provider_name: WebhookProviderType;

  @ApiPropertyOptional({
    description: 'API key for the webhook provider',
  })
  @IsOptional()
  @IsString()
  api_key?: string;



  @ApiPropertyOptional({
    description: 'Whether the provider is enabled',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Additional configuration options',
  })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Provider description',
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateProviderWebhookDto {
  @ApiPropertyOptional({
    description: 'API key for the webhook provider',
  })
  @IsOptional()
  @IsString()
  api_key?: string;

  @ApiPropertyOptional({
    description: 'Whether the provider is enabled',
  })
  @IsOptional()
  @IsBoolean()
  is_enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Additional configuration options',
  })
  @IsOptional()
  @IsObject()
  configuration?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Provider description',
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class WebhookPayloadDto {
  @ApiProperty({
    description: 'Webhook payload data',
  })
  @IsObject()
  data: Record<string, any>;

  @ApiProperty({
    description: 'API key for authentication',
  })
  @IsString()
  api_key: string;
}

export class ToggleProviderStatusDto {
  @ApiProperty({
    description: 'Whether the provider should be enabled',
  })
  @IsBoolean()
  is_enabled: boolean;
}