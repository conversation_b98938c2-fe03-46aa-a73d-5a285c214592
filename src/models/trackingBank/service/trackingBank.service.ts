import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { Request } from "express";
import { In } from "typeorm";

import * as _ from "lodash";
import * as moment from "moment";
import { TrackingBankAccount } from "src/entities/TrackingBankAccount.entity";
import { TrackingBankNotificationSetting } from "src/entities/TrackingBankNotificationSetting.entity";
import { TrackingBankProvider } from "src/entities/TrackingBankProvider.entity";
import { WebhookProviderType } from "src/entities/TrackingBankProviderWebhook.entity";
import {
  ETrackingBankTransactionType,
  TrackingBankTransaction,
} from "src/entities/TrackingBankTransaction.entity";
import { TrackingBankWebhookHistory } from "src/entities/TrackingBankWebhookHistory.entity";
import { DatabaseService } from "src/providers/database/database.service";
import { AcbWebhookPayloadDto } from "../dto/acb-webhook.dto";
import { CreateTrackingBankDTO } from "../dto/create-tracking-bank-account.dto";
import { CreateBankTransactionDto } from "../dto/create-tracking-bank-trans.dto";
import { GetTrackingTransactionBankDto } from "../dto/get-tracking-transaction-bank-dto";
import {
  GetInternalTransactionsDto,
  IInternalTransactionStatistics,
} from "../dto/internal-transactions.dto";
import {
  CreateNotificationSettingDto,
  GetNotificationSettingsDto,
  UpdateNotificationSettingDto,
} from "../dto/notification-setting.dto";
import { SepayWebhookDto } from "../dto/sepay-webhook.dto";
import { TestWebhookDto } from "../dto/test-webhook.dto";
import {
  BulkUpdateTransactionDto,
  CreateBankProviderDto,
  UpdateBankProviderDto,
  UpdateTransactionDto,
} from "../dto/tracking-bank-provider-dto";
import { UpdateTrackingBankAccountDTO } from "../dto/update-tracking-bank-account.dto";
import { GetWebhookHistoryDto } from "../dto/webhook-history.dto";
import { WebhookService } from "./webhook.service";
import { WebhookProviderService } from "./webhookProvider.service";

@Injectable()
export class TrackingBankService {
  private readonly logger = new Logger(TrackingBankService.name);

  constructor(
    private readonly webhookService: WebhookService,
    private readonly webhookProviderService: WebhookProviderService
  ) {}

  async create(req: Request, args: CreateBankTransactionDto) {
    // Kiểm tra SMS provider có được bật hay không
    try {
      const smsProvider = await this.webhookProviderService.getEnabledProvider(
        req,
        WebhookProviderType.SMS
      );
      if (!smsProvider || !smsProvider.is_enabled) {
        throw new HttpException(
          "SMS provider không được bật. Không thể tạo giao dịch.",
          HttpStatus.FORBIDDEN
        );
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      // Nếu không tìm thấy SMS provider hoặc có lỗi khác
      throw new HttpException(
        "SMS provider không khả dụng. Không thể tạo giao dịch.",
        HttpStatus.FORBIDDEN
      );
    }

    const omitedTransaction = _.omit(args, ["tracking_bank_transaction"]);
    const transaction = new TrackingBankTransaction(omitedTransaction);

    if (args.account_number) {
      const bankAccount = await DatabaseService.getRepository(
        TrackingBankAccount,
        req
      ).findOne({
        where: { account_number: args.account_number },
      });
      if (!bankAccount)
        throw new NotFoundException("Không tìm thấy bank account");

      if (bankAccount.is_active === false)
        throw new NotFoundException("Bank account không hoạt động");

      transaction.tracking_bank_account = bankAccount;
    }
    if (args.transaction_code) {
      const isExistTransactionCode = await DatabaseService.getRepository(
        TrackingBankTransaction,
        req
      ).findOne({
        where: { transaction_code: args.transaction_code },
      });
      if (isExistTransactionCode)
        throw new NotFoundException("Giao dịch đã tồn tại");
    }

    if (args.transaction_time) {
      const transactionTime = moment(args.transaction_time);
      if (!transactionTime.isValid())
        throw new NotFoundException("Thời gian giao dịch không hợp lệ");

      transaction.transaction_time = transactionTime.utc().toDate();
    }

    const isExistRawData = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { raw_data: args.raw_data },
    });
    if (isExistRawData) throw new NotFoundException("Giao dịch đã tồn tại");

    const transactionSaved = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).save(transaction);

    if (!transactionSaved)
      throw new HttpException(
        "Không thể tạo Giao dịch",
        HttpStatus.INTERNAL_SERVER_ERROR
      );

    if (transactionSaved.current_balance) {
      await DatabaseService.getRepository(TrackingBankAccount, req).update(
        transactionSaved.tracking_bank_account_id,
        { current_balance: transactionSaved.current_balance }
      );
    }

    // Trigger webhooks after transaction is saved
    this.webhookService.processTransactionWebhooks(req, transactionSaved);

    return transactionSaved;
  }

  async getBankAccounts(req: Request) {
    return DatabaseService.getRepository(TrackingBankAccount, req).find({
      relations: ["tracking_bank_provider"],
    });
  }

  async createBankAccount(req: Request, args: CreateTrackingBankDTO) {
    const omitedTransaction = _.omit(args, ["tracking_bank_account"]);

    const bankAccount = new TrackingBankAccount(omitedTransaction);

    const isExist = await this.isExistBankAccount(
      req,
      args.account_number,
      args.tracking_bank_provider_id
    );
    if (isExist) throw new NotFoundException("Bank account đã tồn tại");

    const bankAccountCreated = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).save(bankAccount);

    if (!bankAccountCreated)
      throw new HttpException(
        "Không thể tạo bank account",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return bankAccountCreated;
  }

  async createBankProvider(req: Request, args: CreateBankProviderDto) {
    const provider = new TrackingBankProvider(args);
    const isExist = await DatabaseService.getRepository(
      TrackingBankProvider,
      req
    ).findOne({
      where: { bank_code: args.bank_code },
    });
    if (isExist) throw new NotFoundException("Provider đã tồn tại");

    const providerCreated = await DatabaseService.getRepository(
      TrackingBankProvider,
      req
    ).save(provider);

    if (!providerCreated)
      throw new HttpException(
        "Không thể tạo provider",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return providerCreated;
  }

  async updateBankAccount(
    req: Request,
    id: number,
    args: UpdateTrackingBankAccountDTO
  ) {
    const bankAccount = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).findOne({ where: { id } });
    if (!bankAccount)
      throw new NotFoundException("Không tìm thấy bank account");
    const omitedTransaction = _.omit(args, ["tracking_bank_account"]);
    const updatedBankAccount = new TrackingBankAccount({
      ...bankAccount,
      ...omitedTransaction,
    });

    const provider = await this.findProviderById(
      req,
      args.tracking_bank_provider_id
    );
    if (!provider) throw new NotFoundException("Không tìm thấy provider");
    if (args.account_number) {
      const isExist = await this.isExistBankAccount(
        req,
        args.account_number,
        args.tracking_bank_provider_id
      );
      if (isExist) throw new NotFoundException("Bank account đã tồn tại");
    }
    const bankAccountUpdated = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).update(id, updatedBankAccount);

    if (!bankAccountUpdated)
      throw new HttpException(
        "Không thể cập nhật bank account",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return bankAccountUpdated;
  }

  async deleteBankAccount(req: Request, id: number) {
    const bankAccount = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).findOne({ where: { id } });
    if (!bankAccount)
      throw new NotFoundException("Không tìm thấy bank account");

    const bankAccountDeleted = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).delete(id);

    if (!bankAccountDeleted)
      throw new HttpException(
        "Không thể xóa bank account",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return bankAccountDeleted;
  }

  async getBankProviders(req: Request) {
    return DatabaseService.getRepository(TrackingBankProvider, req).find();
  }

  async updateBankProvider(
    req: Request,
    id: number,
    args: UpdateBankProviderDto
  ) {
    const provider = await DatabaseService.getRepository(
      TrackingBankProvider,
      req
    ).findOne({ where: { id } });
    if (!provider) throw new NotFoundException("Không tìm thấy provider");

    const omitedTransaction = _.omit(args, ["tracking_bank_provider"]);
    const updatedProvider = new TrackingBankProvider({
      ...provider,
      ...omitedTransaction,
    });

    const providerUpdated = await DatabaseService.getRepository(
      TrackingBankProvider,
      req
    ).save(updatedProvider);

    if (!providerUpdated)
      throw new HttpException(
        "Không thể cập nhật provider",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return providerUpdated;
  }

  async updateTransaction(
    req: Request,
    id: number,
    args: UpdateTransactionDto
  ) {
    const provider = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({ where: { id } });
    if (!provider) throw new NotFoundException("Không tìm thấy giao dịch");

    const omitedTransaction = _.omit(args, ["tracking_bank_transaction"]);
    const updatedTransaction = new TrackingBankTransaction({
      ...provider,
      ...omitedTransaction,
    });

    const transactionUpdated = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).save(updatedTransaction);

    if (!transactionUpdated)
      throw new HttpException(
        "Không thể cập nhật transaction",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return transactionUpdated;
  }

  async bulkUpdateTransactions(req: Request, args: BulkUpdateTransactionDto) {
    const { transaction_ids, is_verified } = args;

    if (!transaction_ids || transaction_ids.length === 0) {
      throw new NotFoundException(
        "Danh sách transaction_ids không được để trống"
      );
    }

    // Kiểm tra xem các transaction có tồn tại không
    const existingTransactions = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).find({
      where: { id: In(transaction_ids) },
    });

    if (existingTransactions.length !== transaction_ids.length) {
      throw new NotFoundException("Một số giao dịch không tồn tại");
    }

    // Thực hiện bulk update
    const updateResult = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).update({ id: In(transaction_ids) }, { is_verified });

    if (!updateResult.affected) {
      throw new HttpException(
        "Không thể cập nhật các giao dịch",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    return {
      success: true,
      message: `Đã cập nhật thành công ${updateResult.affected} giao dịch`,
      updated_count: updateResult.affected,
    };
  }

  findProviderById(req: Request, id: number) {
    return DatabaseService.getRepository(TrackingBankProvider, req).findOne({
      where: { id },
    });
  }

  findBankAccountById(req: Request, id: number) {
    return DatabaseService.getRepository(TrackingBankAccount, req).findOne({
      where: { id },
    });
  }

  isExistBankAccount(
    req: Request,
    account_number: string,
    tracking_bank_provider_id: number
  ) {
    return DatabaseService.getRepository(TrackingBankAccount, req).findOne({
      where: { account_number, tracking_bank_provider_id },
    });
  }

  findById(req: Request, id: number) {
    return DatabaseService.getRepository(TrackingBankTransaction, req).findOne({
      where: { id },
    });
  }

  async findAll(req: Request, body: GetTrackingTransactionBankDto) {
    const from_date = body.from_date ? moment(body.from_date) : null;
    const to_date = body.to_date ? moment(body.to_date) : null;
    const builder = DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).createQueryBuilder("tracking_bank_transaction");
    if (body.account_number) {
      builder.andWhere(
        "tracking_bank_account.account_number = :account_number",
        {
          account_number: body.account_number,
        }
      );
    }

    if (from_date && to_date) {
      //same day
      if (from_date.isSame(to_date, "day")) {
        builder.andWhere(
          "DATE(tracking_bank_transaction.transaction_time) = :date",
          {
            date: from_date.format("YYYY-MM-DD"),
          }
        );
      } else {
        builder.andWhere(
          "tracking_bank_transaction.transaction_time >= :from_date",
          {
            from_date: from_date.format("YYYY-MM-DD"),
          }
        );
        builder.andWhere(
          "tracking_bank_transaction.transaction_time <= :to_date",
          {
            to_date: to_date.format("YYYY-MM-DD"),
          }
        );
      }
    }
    if (body.is_verified !== undefined && body.is_verified !== null) {
      builder.andWhere("tracking_bank_transaction.is_verified = :is_verified", {
        is_verified: body.is_verified,
      });
    }

    if (body.transaction_type) {
      builder.andWhere(
        "tracking_bank_transaction.transaction_type = :transaction_type",
        {
          transaction_type: body.transaction_type,
        }
      );
    }

    if (body.bank_code) {
      builder.andWhere("tracking_bank_transaction.bank_code = :bank_code", {
        bank_code: body.bank_code,
      });
    }

    if (body.transaction_id) {
      builder.andWhere("tracking_bank_transaction.id = :transaction_id", {
        transaction_id: body.transaction_id,
      });
    }

    if (
      body.tracking_bank_provider_webhook_id !== undefined &&
      body.tracking_bank_provider_webhook_id !== null
    ) {
      if (body.tracking_bank_provider_webhook_id === -1) {
        // SMS: Rỗng hoặc ID của SMS provider
        builder.andWhere(
          "(tracking_bank_transaction.tracking_bank_provider_webhook_id IS NULL OR tracking_bank_provider_webhook.provider_name = :sms_provider)",
          { sms_provider: "SMS" }
        );
      } else {
        // SEPAY hoặc ID cụ thể
        builder.andWhere(
          "tracking_bank_transaction.tracking_bank_provider_webhook_id = :tracking_bank_provider_webhook_id",
          {
            tracking_bank_provider_webhook_id:
              body.tracking_bank_provider_webhook_id,
          }
        );
      }
    }

    builder
      .leftJoinAndSelect(
        "tracking_bank_transaction.tracking_bank_account",
        "tracking_bank_account"
      )
      .leftJoinAndSelect(
        "tracking_bank_account.tracking_bank_provider",
        "tracking_bank_provider"
      )
      .leftJoinAndSelect(
        "tracking_bank_transaction.tracking_bank_provider_webhook",
        "tracking_bank_provider_webhook"
      );

    const [items, total] = await builder
      .skip((body.page - 1) * body.limit)
      .take(body.limit)
      .orderBy("tracking_bank_transaction.transaction_time", "DESC")
      .getManyAndCount();

    return [items, total];
  }

  private async getBankAccount(req: Request, account_number: string) {
    return await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).findOne({ where: { account_number } });
  }

  // Notification Settings Methods
  async createNotificationSetting(
    req: Request,
    args: CreateNotificationSettingDto
  ) {
    const setting = new TrackingBankNotificationSetting(args);

    const isExist = await DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).findOne({
      where: { name: args.name },
    });
    if (isExist)
      throw new NotFoundException(
        "Notification setting với tên này đã tồn tại"
      );

    const settingCreated = await DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).save(setting);

    if (!settingCreated)
      throw new HttpException(
        "Không thể tạo notification setting",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return settingCreated;
  }

  async getNotificationSettings(
    req: Request,
    query: GetNotificationSettingsDto
  ) {
    const builder = DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).createQueryBuilder("setting");

    if (!_.isNil(query.is_active)) {
      builder.andWhere("setting.is_active = :is_active", {
        is_active: query.is_active,
      });
    }

    if (!_.isNil(query.trigger_type)) {
      builder.andWhere("setting.trigger_type = :trigger_type", {
        trigger_type: query.trigger_type,
      });
    }

    if (!_.isNil(query.search)) {
      builder.andWhere(
        "(setting.name LIKE :search OR setting.description LIKE :search)",
        {
          search: `%${query.search}%`,
        }
      );
    }

    const [items, total] = await builder
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .orderBy("setting.created_at", "DESC")
      .getManyAndCount();

    return [items, total];
  }

  async getNotificationSettingById(req: Request, id: number) {
    const setting = await DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).findOne({
      where: { id },
    });
    if (!setting)
      throw new NotFoundException("Không tìm thấy notification setting");
    return setting;
  }

  async updateNotificationSetting(
    req: Request,
    id: number,
    args: UpdateNotificationSettingDto
  ) {
    const setting = await this.getNotificationSettingById(req, id);

    if (args.name && args.name !== setting.name) {
      const isExist = await DatabaseService.getRepository(
        TrackingBankNotificationSetting,
        req
      ).findOne({
        where: { name: args.name },
      });
      if (isExist)
        throw new NotFoundException(
          "Notification setting với tên này đã tồn tại"
        );
    }

    const updatedSetting = await DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).update(id, args);

    if (!updatedSetting)
      throw new HttpException(
        "Không thể cập nhật notification setting",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return updatedSetting;
  }

  async deleteNotificationSetting(req: Request, id: number) {
    const setting = await this.getNotificationSettingById(req, id);

    const deleted = await DatabaseService.getRepository(
      TrackingBankNotificationSetting,
      req
    ).delete(id);

    if (!deleted)
      throw new HttpException(
        "Không thể xóa notification setting",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    return deleted;
  }

  // Webhook History Methods
  async getWebhookHistory(req: Request, query: GetWebhookHistoryDto) {
    const builder = DatabaseService.getRepository(
      TrackingBankWebhookHistory,
      req
    ).createQueryBuilder("history");

    if (query.status) {
      builder.andWhere("history.status = :status", { status: query.status });
    }

    if (query.tracking_bank_transaction_id) {
      builder.andWhere(
        "history.tracking_bank_transaction_id = :transactionId",
        {
          transactionId: query.tracking_bank_transaction_id,
        }
      );
    }

    if (query.notification_setting_id) {
      builder.andWhere("history.notification_setting_id = :settingId", {
        settingId: query.notification_setting_id,
      });
    }

    if (query.from_date) {
      builder.andWhere("history.created_at >= :from_date", {
        from_date: query.from_date,
      });
    }

    if (query.to_date) {
      builder.andWhere("history.created_at <= :to_date", {
        to_date: query.to_date,
      });
    }

    builder
      .leftJoinAndSelect("history.tracking_bank_transaction", "transaction")
      .leftJoinAndSelect("transaction.tracking_bank_account", "account")
      .leftJoinAndSelect("account.tracking_bank_provider", "provider")
      .leftJoinAndSelect("history.notification_setting", "setting");

    const [items, total] = await builder
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .orderBy("history.created_at", "DESC")
      .getManyAndCount();

    return [items, total];
  }

  async getWebhookHistoryById(req: Request, id: number) {
    const history = await DatabaseService.getRepository(
      TrackingBankWebhookHistory,
      req
    ).findOne({
      where: { id },
      relations: [
        "tracking_bank_transaction",
        "tracking_bank_transaction.tracking_bank_account",
        "notification_setting",
      ],
    });
    if (!history) throw new NotFoundException("Không tìm thấy webhook history");
    return history;
  }

  async retryWebhook(req: Request, id: number) {
    try {
      const result = await this.webhookService.retryWebhookById(req, id);
      return result;
    } catch (error) {
      console.log("Error when retry webhook", error);
      throw new HttpException(
        `Lỗi khi retry webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Manual webhook trigger for specific transaction
  async triggerWebhookForTransaction(req: Request, transactionId: number) {
    const transaction = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { id: transactionId },
      relations: [
        "tracking_bank_account",
        "tracking_bank_account.tracking_bank_provider",
      ],
    });

    if (!transaction) {
      throw new NotFoundException("Không tìm thấy giao dịch");
    }

    // Trigger webhooks manually - this will create new webhook history entries
    await this.webhookService.processTransactionWebhooks(req, transaction);

    return {
      message: "Webhook đã được bắn thành công",
      transaction_id: transactionId,
    };
  }

  // Get webhook history for specific transaction
  async getTransactionWebhookHistory(req: Request, transactionId: number) {
    const transaction = await this.findById(req, transactionId);
    if (!transaction) {
      throw new NotFoundException("Không tìm thấy giao dịch");
    }

    const webhookHistory = await DatabaseService.getRepository(
      TrackingBankWebhookHistory,
      req
    ).find({
      where: { tracking_bank_transaction_id: transactionId },
      order: { created_at: "DESC" },
    });

    return {
      transaction_id: transactionId,
      webhook_history: webhookHistory,
    };
  }

  // Retry all failed webhooks
  async retryAllFailedWebhooks(req: Request) {
    try {
      const result = await this.webhookService.retryFailedWebhooks(req);

      return {
        message: "Retry tất cả failed/retry webhooks hoàn thành",
        total_webhooks: result.total,
        successful_retries: result.successful,
        failed_retries: result.failed,
        note: "Chỉ retry các webhook có status FAILED hoặc RETRY",
      };
    } catch (error) {
      throw new HttpException(
        `Lỗi khi retry webhooks: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Reset stuck webhooks (pending > 30 seconds)
  async resetStuckWebhooks(req: Request) {
    try {
      const resetCount = await this.webhookService.checkAndFailTimeoutWebhooks(
        req
      );

      return {
        message:
          resetCount > 0
            ? `Đã reset thành công ${resetCount} webhook bị stuck`
            : "Không có webhook nào bị stuck cần reset",
        reset_count: resetCount,
        note: "Các webhook PENDING quá 30 giây đã được chuyển thành FAILED",
      };
    } catch (error) {
      throw new HttpException(
        `Lỗi khi reset stuck webhooks: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Create test transaction with auto-generated data for webhook testing
  async createTestTransaction(req: Request) {
    // Get first active bank account for testing
    const bankAccount = await DatabaseService.getRepository(
      TrackingBankAccount,
      req
    ).findOne({
      where: { is_active: true },
      relations: ["tracking_bank_provider"],
    });

    if (!bankAccount) {
      throw new NotFoundException(
        "Không tìm thấy bank account hoạt động để test"
      );
    }

    // Generate test transaction data
    const currentTime = new Date();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const testTransactionCode = `VILL-${randomSuffix}`;

    // Calculate new balance for test transaction
    const newBalance = bankAccount.current_balance + 1000000;

    const testData: CreateBankTransactionDto = {
      transaction_id: testTransactionCode,
      amount: 1000000,
      transaction_time: currentTime,
      transaction_type: ETrackingBankTransactionType.DEPOSIT,
      transaction_status: "SUCCESS",
      transaction_code: testTransactionCode,
      raw_data: `${moment().format("DD/MM/YYYY HH:mm")}|TK:${
        bankAccount.account_number
      }|GD:+1,000,000VND|SDC:${newBalance}VND|ND:CT DEN:************ PHAM TEST ACCOUNT chuyen tien ${testTransactionCode} ; tai test`,
      transaction_description: `Test transaction for webhook testing - ${testTransactionCode}`,
      bank_code: bankAccount.tracking_bank_provider?.bank_code || "Vietinbank",
      account_number: bankAccount.account_number,
      tracking_bank_transaction_type: ETrackingBankTransactionType.DEPOSIT,
    };

    // Set current_balance manually since it's not in DTO but needed in entity
    const transaction = new TrackingBankTransaction(testData);
    transaction.current_balance = newBalance;
    transaction.tracking_bank_account = bankAccount;

    // Check for duplicate transaction_code and raw_data
    const isExistTransactionCode = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { transaction_code: testTransactionCode },
    });
    if (isExistTransactionCode) {
      throw new NotFoundException(
        "Test transaction code đã tồn tại, vui lòng thử lại"
      );
    }

    const isExistRawData = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { raw_data: transaction.raw_data },
    });
    if (isExistRawData) {
      throw new NotFoundException(
        "Test transaction raw data đã tồn tại, vui lòng thử lại"
      );
    }

    // Save the test transaction
    const createdTransaction = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).save(transaction);

    if (!createdTransaction) {
      throw new HttpException(
        "Không thể tạo test transaction",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Update bank account balance
    await DatabaseService.getRepository(TrackingBankAccount, req).update(
      bankAccount.id,
      {
        current_balance: newBalance,
      }
    );

    // Trigger webhooks after transaction is saved
    await this.webhookService.processTransactionWebhooks(
      req,
      createdTransaction
    );

    return {
      message: "Test transaction đã được tạo và webhook đã được bắn thành công",
      transaction: {
        id: createdTransaction.id,
        transaction_code: createdTransaction.transaction_code,
        amount: createdTransaction.amount,
        raw_data: createdTransaction.raw_data,
      },
    };
  }

  // Create test transaction with custom payload for webhook testing
  async createTestTransactionWithCustomPayload(
    req: Request,
    customData: TestWebhookDto
  ) {
    // Get bank account for testing - either by provided account_number or first active account
    let bankAccount;
    if (customData.account_number) {
      bankAccount = await DatabaseService.getRepository(
        TrackingBankAccount,
        req
      ).findOne({
        where: { account_number: customData.account_number, is_active: true },
        relations: ["tracking_bank_provider"],
      });
      if (!bankAccount) {
        throw new NotFoundException(
          `Không tìm thấy bank account hoạt động với số tài khoản: ${customData.account_number}`
        );
      }
    } else {
      bankAccount = await DatabaseService.getRepository(
        TrackingBankAccount,
        req
      ).findOne({
        where: { is_active: true },
        relations: ["tracking_bank_provider"],
      });
      if (!bankAccount) {
        throw new NotFoundException(
          "Không tìm thấy bank account hoạt động để test"
        );
      }
    }

    // Generate test transaction data with custom or default values
    const currentTime = new Date();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const testTransactionCode =
      customData.transaction_code || `VILL-CUSTOM-${randomSuffix}`;
    const testAmount = customData.amount || 1000000;

    // Calculate new balance for test transaction
    const newBalance = bankAccount.current_balance + testAmount;

    // Use custom raw_data if provided, otherwise generate default
    let rawData = customData.custom_raw_data;
    if (!rawData) {
      rawData = `${moment().format("DD/MM/YYYY HH:mm")}|TK:${
        bankAccount.account_number
      }|GD:+${testAmount.toLocaleString()}VND|SDC:${newBalance.toLocaleString()}VND|ND:CT DEN:************ PHAM TEST ACCOUNT chuyen tien ${testTransactionCode} ; tai test custom`;
    }

    const testData: CreateBankTransactionDto = {
      transaction_id: testTransactionCode,
      amount: testAmount,
      transaction_time: currentTime,
      transaction_type: ETrackingBankTransactionType.DEPOSIT,
      transaction_status: "SUCCESS",
      transaction_code: testTransactionCode,
      raw_data: rawData,

      transaction_description: `Custom test transaction for webhook testing - ${testTransactionCode}`,
      bank_code: bankAccount.tracking_bank_provider?.bank_code || "Vietinbank",
      external_transaction_id: undefined,
      external_transaction_data: undefined,
      account_number: bankAccount.account_number,
      tracking_bank_transaction_type: ETrackingBankTransactionType.DEPOSIT,
    };

    // Set current_balance manually since it's not in DTO but needed in entity
    const transaction = new TrackingBankTransaction(testData);
    transaction.current_balance = newBalance;
    transaction.tracking_bank_account = bankAccount;

    // Check for duplicate transaction_code and raw_data
    const isExistTransactionCode = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { transaction_code: testTransactionCode },
    });
    if (isExistTransactionCode) {
      throw new NotFoundException(
        "Test transaction code đã tồn tại, vui lòng thử lại với mã khác"
      );
    }

    const isExistRawData = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).findOne({
      where: { raw_data: transaction.raw_data },
    });
    if (isExistRawData) {
      throw new NotFoundException(
        "Test transaction raw data đã tồn tại, vui lòng thử lại với raw data khác"
      );
    }

    // Save the test transaction
    const createdTransaction = await DatabaseService.getRepository(
      TrackingBankTransaction,
      req
    ).save(transaction);

    if (!createdTransaction) {
      throw new HttpException(
        "Không thể tạo custom test transaction",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Update bank account balance
    await DatabaseService.getRepository(TrackingBankAccount, req).update(
      bankAccount.id,
      {
        current_balance: newBalance,
      }
    );

    // Trigger webhooks after transaction is saved
    await this.webhookService.processTransactionWebhooks(
      req,
      createdTransaction
    );

    return {
      message:
        "Custom test transaction đã được tạo và webhook đã được bắn thành công",
      transaction: {
        id: createdTransaction.id,
        transaction_code: createdTransaction.transaction_code,
        amount: createdTransaction.amount,
        raw_data: createdTransaction.raw_data,
        custom_payload_used: !!customData.custom_raw_data,
      },
    };
  }

  // Internal Transactions Methods
  async getInternalTransactions(
    req: Request,
    query: GetInternalTransactionsDto
  ) {
    const builder = DatabaseService.getRepository(TrackingBankTransaction, req)
      .createQueryBuilder("transaction")
      .leftJoinAndSelect("transaction.tracking_bank_account", "account")
      .leftJoinAndSelect("transaction.webhook_histories", "webhook_history")
      .leftJoinAndSelect(
        "webhook_history.notification_setting",
        "notification_setting"
      );

    // Apply filters
    if (query.trigger_type) {
      builder.andWhere("notification_setting.trigger_type = :trigger_type", {
        trigger_type: query.trigger_type,
      });
    }

    // Note: webhook_status filter will be applied after getting latest webhook status
    // Exclude RETRY status from internal transactions list (chỉ khi có webhook history)
    builder.andWhere(
      "(webhook_history.id IS NULL OR webhook_history.status != :retry_status)",
      {
        retry_status: "RETRY",
      }
    );

    // Date filtering with proper moment handling
    const from_date = query.from_date ? moment(query.from_date) : null;
    const to_date = query.to_date ? moment(query.to_date) : null;

    if (from_date && to_date) {
      // Same day
      if (from_date.isSame(to_date, "day")) {
        builder.andWhere("DATE(transaction.transaction_time) = :date", {
          date: from_date.format("YYYY-MM-DD"),
        });
      } else {
        // Date range
        builder.andWhere("transaction.transaction_time >= :from_date", {
          from_date: from_date.startOf("day").toDate(),
        });
        builder.andWhere("transaction.transaction_time <= :to_date", {
          to_date: to_date.endOf("day").toDate(),
        });
      }
    } else if (from_date) {
      builder.andWhere("transaction.transaction_time >= :from_date", {
        from_date: from_date.startOf("day").toDate(),
      });
    } else if (to_date) {
      builder.andWhere("transaction.transaction_time <= :to_date", {
        to_date: to_date.endOf("day").toDate(),
      });
    }

    if (query.search) {
      builder.andWhere(
        "(transaction.transaction_code LIKE :search OR account.account_number LIKE :search OR account.account_name LIKE :search)",
        { search: `%${query.search}%` }
      );
    }

    if (query.notification_setting_name) {
      if (query.notification_setting_name === "UNDEFINED") {
        // Filter những giao dịch không có webhook history (không xác định)
        builder.andWhere("webhook_history.id IS NULL");
      } else {
        // Nếu filter theo notification_setting_name, chỉ hiển thị giao dịch có webhook history với setting đó
        builder.andWhere(
          "notification_setting.name = :notification_setting_name",
          {
            notification_setting_name: query.notification_setting_name,
          }
        );
        builder.andWhere("webhook_history.id IS NOT NULL");
      }
    }

    // Không bắt buộc phải có webhook history - hiển thị tất cả giao dịch
    // builder.andWhere('webhook_history.id IS NOT NULL');

    // Get all items first (without pagination) to apply webhook_status filter correctly
    const allItems = await builder
      .orderBy("transaction.transaction_time", "DESC")
      .getMany();

    // Process items to add computed fields
    const processedItems = allItems.map((transaction) => {
      const webhookHistories = transaction.webhook_histories || [];
      const latestWebhook =
        webhookHistories.length > 0
          ? webhookHistories.reduce((latest, current) =>
              new Date(current.created_at) > new Date(latest.created_at)
                ? current
                : latest
            )
          : null;

      // Calculate extracted_transaction_code if applicable
      let extractedTransactionCode = null;
      if (
        latestWebhook?.notification_setting?.trigger_type === "CUSTOM_CODE" &&
        latestWebhook?.notification_setting?.custom_code &&
        latestWebhook?.notification_setting?.custom_code_suffix_length
      ) {
        extractedTransactionCode = this.extractTransactionCodeWithSuffix(
          transaction,
          latestWebhook.notification_setting.custom_code,
          latestWebhook.notification_setting.custom_code_suffix_length
        );
      }

      // Determine notification_setting_name based on transaction type and webhook history
      let notificationSettingName = latestWebhook?.notification_setting?.name;
      if (!notificationSettingName) {
        // Nếu không có webhook history, hiển thị tên dựa trên loại giao dịch
        if (
          transaction.transaction_type === ETrackingBankTransactionType.DEPOSIT
        ) {
          notificationSettingName = "Giao dịch nạp tiền";
        } else if (
          transaction.transaction_type === ETrackingBankTransactionType.WITHDRAW
        ) {
          notificationSettingName = "Giao dịch rút tiền";
        } else {
          notificationSettingName = "Giao dịch khác";
        }
      }

      return {
        ...transaction,
        webhook_count: webhookHistories.length,
        latest_webhook_status: latestWebhook?.status,
        trigger_type: latestWebhook?.notification_setting?.trigger_type,
        notification_setting_name: notificationSettingName,
        extracted_transaction_code: extractedTransactionCode,
      };
    });

    // Apply webhook_status filter at application level after determining latest status
    let filteredItems = processedItems;
    if (query.webhook_status) {
      filteredItems = processedItems.filter((item) => {
        // Chỉ filter những giao dịch có webhook history
        if (item.webhook_count === 0) {
          return false; // Không hiển thị giao dịch không có webhook khi filter theo webhook_status
        }
        const matches = item.latest_webhook_status === query.webhook_status;

        return matches;
      });
      console.log(`After filter: ${filteredItems.length} items`);
    }

    // Apply pagination after filtering
    const total = filteredItems.length;
    const startIndex = (query.page - 1) * query.limit;
    const endIndex = startIndex + query.limit;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    return [paginatedItems, total];
  }

  private extractTransactionCodeWithSuffix(
    transaction: TrackingBankTransaction,
    customCode: string,
    suffixLength?: number
  ): string | null {
    if (!customCode || !suffixLength) return null;

    try {
      const rawData = transaction.raw_data || "";
      const codes = customCode.split(",").map((code) => code.trim());

      for (const code of codes) {
        // Tìm tất cả các vị trí xuất hiện của code
        const matches = this.findAllMatches(rawData, code);

        if (matches.length > 0) {
          // Ưu tiên tìm trong phần nội dung (ND:) trước
          const ndMatch = this.findInContent(rawData, code, suffixLength);
          if (ndMatch) {
            return ndMatch;
          }

          // Nếu không tìm thấy trong ND, lấy match đầu tiên
          const firstMatch = matches[0];
          const endIndex = Math.min(
            firstMatch.index + code.length + suffixLength,
            rawData.length
          );
          return rawData.substring(firstMatch.index, endIndex);
        }
      }

      return null;
    } catch (error) {
      console.error("Error extracting transaction code with suffix:", error);
      return null;
    }
  }

  private findAllMatches(
    text: string,
    searchTerm: string
  ): Array<{ index: number; match: string }> {
    const matches: Array<{ index: number; match: string }> = [];
    const lowerText = text.toLowerCase();
    const lowerSearchTerm = searchTerm.toLowerCase();
    let index = 0;

    while ((index = lowerText.indexOf(lowerSearchTerm, index)) !== -1) {
      matches.push({
        index,
        match: text.substring(index, index + searchTerm.length),
      });
      index += searchTerm.length;
    }

    return matches;
  }

  private findInContent(
    rawData: string,
    code: string,
    suffixLength: number
  ): string | null {
    // Tìm trong phần nội dung (ND:) của giao dịch
    const ndPattern = /ND:([^;]+)/i;
    const ndMatch = rawData.match(ndPattern);

    if (ndMatch && ndMatch[1]) {
      const content = ndMatch[1];
      const codeIndex = content.toLowerCase().indexOf(code.toLowerCase());

      if (codeIndex !== -1) {
        // Tìm vị trí thực tế trong raw data
        const actualIndex =
          rawData.toLowerCase().indexOf(content.toLowerCase()) + codeIndex;
        const endIndex = Math.min(
          actualIndex + code.length + suffixLength,
          rawData.length
        );
        return rawData.substring(actualIndex, endIndex);
      }
    }

    return null;
  }

  async getInternalTransactionsStatistics(
    req: Request,
    query: GetInternalTransactionsDto
  ): Promise<IInternalTransactionStatistics> {
    const builder = DatabaseService.getRepository(TrackingBankTransaction, req)
      .createQueryBuilder("transaction")
      .leftJoinAndSelect("transaction.webhook_histories", "webhook_history")
      .leftJoin("webhook_history.notification_setting", "notification_setting");

    // Apply same filters as main query (except webhook_status which will be applied later)
    if (query.trigger_type) {
      builder.andWhere("notification_setting.trigger_type = :trigger_type", {
        trigger_type: query.trigger_type,
      });
    }

    // Date filtering with proper moment handling
    const from_date = query.from_date ? moment(query.from_date) : null;
    const to_date = query.to_date ? moment(query.to_date) : null;

    if (from_date && to_date) {
      // Same day
      if (from_date.isSame(to_date, "day")) {
        builder.andWhere("DATE(transaction.transaction_time) = :date", {
          date: from_date.format("YYYY-MM-DD"),
        });
      } else {
        // Date range
        builder.andWhere("transaction.transaction_time >= :from_date", {
          from_date: from_date.startOf("day").toDate(),
        });
        builder.andWhere("transaction.transaction_time <= :to_date", {
          to_date: to_date.endOf("day").toDate(),
        });
      }
    } else if (from_date) {
      builder.andWhere("transaction.transaction_time >= :from_date", {
        from_date: from_date.startOf("day").toDate(),
      });
    } else if (to_date) {
      builder.andWhere("transaction.transaction_time <= :to_date", {
        to_date: to_date.endOf("day").toDate(),
      });
    }

    if (query.search) {
      builder.andWhere(
        "(transaction.transaction_code LIKE :search OR transaction.raw_data LIKE :search)",
        {
          search: `%${query.search}%`,
        }
      );
    }

    if (query.notification_setting_name) {
      if (query.notification_setting_name === "UNDEFINED") {
        // Filter những giao dịch không có webhook history (không xác định)
        builder.andWhere("webhook_history.id IS NULL");
      } else {
        // Nếu filter theo notification_setting_name, chỉ tính giao dịch có webhook history với setting đó
        builder.andWhere(
          "notification_setting.name = :notification_setting_name",
          {
            notification_setting_name: query.notification_setting_name,
          }
        );
        builder.andWhere("webhook_history.id IS NOT NULL");
      }
    }

    // Exclude RETRY status from statistics (chỉ khi có webhook history)
    if (
      query.notification_setting_name ||
      query.trigger_type ||
      query.webhook_status
    ) {
      builder.andWhere("webhook_history.status != :retry_status", {
        retry_status: "RETRY",
      });
    }

    // Không bắt buộc phải có webhook history cho statistics
    // builder.andWhere('webhook_history.id IS NOT NULL');

    const result = await builder
      .select([
        "COUNT(DISTINCT transaction.id) as total_transactions",
        'SUM(CASE WHEN webhook_history.status = "SUCCESS" THEN 1 ELSE 0 END) as success_webhooks',
        'SUM(CASE WHEN webhook_history.status = "FAILED" THEN 1 ELSE 0 END) as failed_webhooks',
        'SUM(CASE WHEN webhook_history.status = "PENDING" THEN 1 ELSE 0 END) as pending_webhooks',
        'SUM(CASE WHEN webhook_history.status = "RETRY" THEN 1 ELSE 0 END) as retry_webhooks',
        "SUM(transaction.amount) as total_amount",
        'SUM(CASE WHEN transaction.tracking_bank_transaction_type = "DEPOSIT" THEN transaction.amount ELSE 0 END) as deposit_amount',
        'SUM(CASE WHEN transaction.tracking_bank_transaction_type = "WITHDRAW" THEN transaction.amount ELSE 0 END) as withdraw_amount',
      ])
      .getRawOne();

    return {
      total_transactions: parseInt(result.total_transactions) || 0,
      success_webhooks: parseInt(result.success_webhooks) || 0,
      failed_webhooks: parseInt(result.failed_webhooks) || 0,
      pending_webhooks: parseInt(result.pending_webhooks) || 0,
      retry_webhooks: parseInt(result.retry_webhooks) || 0,
      total_amount: parseFloat(result.total_amount) || 0,
      deposit_amount: parseFloat(result.deposit_amount) || 0,
      withdraw_amount: parseFloat(result.withdraw_amount) || 0,
    };
  }

  /**
   * Nhận webhook từ SEPAY và tạo bank transaction
   */
  async processSepayWebhook(
    req: Request,
    webhookData: SepayWebhookDto
  ): Promise<TrackingBankTransaction> {
    try {
      // Kiểm tra SEPAY provider có được bật hay không
      const sepayProvider =
        await this.webhookProviderService.getEnabledProvider(
          req,
          WebhookProviderType.SEPAY
        );
      if (!sepayProvider || !sepayProvider.is_enabled) {
        throw new HttpException(
          "SEPAY provider không được bật",
          HttpStatus.FORBIDDEN
        );
      }

      // Kiểm tra API key có trùng khớp không
      if (!webhookData.apiKey) {
        throw new HttpException(
          "API key không được cung cấp",
          HttpStatus.UNAUTHORIZED
        );
      }

      if (sepayProvider.api_key !== webhookData.apiKey) {
        throw new HttpException(
          "API key không hợp lệ",
          HttpStatus.UNAUTHORIZED
        );
      }

      // Tìm bank account theo account number
      const bankAccount = await DatabaseService.getRepository(
        TrackingBankAccount,
        req
      ).findOne({
        where: { account_number: webhookData.accountNumber, is_active: true },
      });

      if (!bankAccount) {
        throw new NotFoundException(
          `Không tìm thấy bank account: ${webhookData.accountNumber}`
        );
      }

      // Kiểm tra transaction đã tồn tại chưa (dựa vào SEPAY ID)
      const existingTransaction = await DatabaseService.getRepository(
        TrackingBankTransaction,
        req
      ).findOne({
        where: { external_transaction_id: webhookData.id },
      });

      if (existingTransaction) {
        throw new HttpException(
          "Transaction đã tồn tại",
          HttpStatus.BAD_REQUEST
        );
      }

      // Tạo transaction mới
      const transactionData = new TrackingBankTransaction({
        tracking_bank_account_id: bankAccount.id,
        bank_code: webhookData.gateway,
        transaction_code: webhookData.code,
        transaction_type:
          webhookData.transferType === "in"
            ? ETrackingBankTransactionType.DEPOSIT
            : ETrackingBankTransactionType.WITHDRAW,
        amount: webhookData.transferAmount,
        tracking_bank_transaction_type:
          webhookData.transferType === "in"
            ? ETrackingBankTransactionType.DEPOSIT
            : ETrackingBankTransactionType.WITHDRAW,
        transaction_time: moment(
          webhookData.transactionDate,
          "YYYY-MM-DD HH:mm:ss"
        )
          .utc()
          .toDate(),
        current_balance: webhookData.accumulated,
        raw_data: webhookData.content,
        external_transaction_data: JSON.stringify(webhookData),
        external_transaction_id: webhookData.id,
        tracking_bank_account: bankAccount,
      });

      const savedTransaction = await DatabaseService.getRepository(
        TrackingBankTransaction,
        req
      ).save(transactionData);

      if (!savedTransaction) {
        throw new HttpException(
          "Không thể tạo transaction",
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      // Cập nhật balance của bank account
      await DatabaseService.getRepository(TrackingBankAccount, req).update(
        bankAccount.id,
        {
          current_balance: webhookData.accumulated,
        }
      );

      // Trigger webhooks sau khi transaction được tạo
      this.webhookService.processTransactionWebhooks(req, savedTransaction);

      return savedTransaction;
    } catch (error) {
      throw new HttpException(
        `Lỗi xử lý SEPAY webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Nhận webhook từ ACB và tạo bank transaction
   */
  async processAcbWebhook(
    req: Request,
    webhookData: AcbWebhookPayloadDto
  ): Promise<TrackingBankTransaction> {
    try {
      this.logger.log("Processing ACB webhook");
      this.logger.log(JSON.stringify(webhookData, null, 2));
      // Kiểm tra ACB provider có được bật hay không
      const acbProvider = await this.webhookProviderService.getEnabledProvider(
        req,
        WebhookProviderType.ACB
      );
      if (!acbProvider || !acbProvider.is_enabled) {
        throw new HttpException(
          "ACB provider không được bật",
          HttpStatus.FORBIDDEN
        );
      }

      // Kiểm tra checksum để xác thực dữ liệu
      if (!webhookData.masterMeta.checksum) {
        throw new HttpException(
          "Checksum không được cung cấp",
          HttpStatus.UNAUTHORIZED
        );
      }

      // const processedTransactions: TrackingBankTransaction[] = [];

      // Xử lý từng request trong webhook
      for (const request of webhookData.requests) {
        if (
          request.requestMeta.requestType !== "NOTIFICATION" ||
          request.requestMeta.requestCode !== "TRANSACTION_UPDATE"
        ) {
          continue; // Bỏ qua các request không phải transaction update
        }

        // Xử lý từng transaction trong request
        for (const transaction of request.requestParams.transactions) {
          // Chỉ xử lý transaction COMPLETED
          if (transaction.transactionStatus !== "COMPLETED") {
            continue;
          }

          // Tìm bank account theo account number
          const bankAccount = await DatabaseService.getRepository(
            TrackingBankAccount,
            req
          ).findOne({
            where: {
              account_number: transaction.accountNumber.toString(),
              is_active: true,
            },
          });

          if (!bankAccount) {
            console.warn(
              `Không tìm thấy bank account: ${transaction.accountNumber}`
            );
            continue; // Tiếp tục với transaction tiếp theo thay vì throw error
          }

          // Kiểm tra transaction đã tồn tại chưa (dựa vào transaction code)
          const existingTransaction = await DatabaseService.getRepository(
            TrackingBankTransaction,
            req
          ).findOne({
            where: {
              external_transaction_id: parseInt(transaction.transactionCode),
            },
          });

          if (existingTransaction) {
            console.warn(
              `Transaction đã tồn tại: ${transaction.transactionCode}`
            );
            continue; // Tiếp tục với transaction tiếp theo
          }

          // Tạo transaction mới
          const transactionData = new TrackingBankTransaction({
            tracking_bank_account_id: bankAccount.id,
            bank_code: "ACB",
            transaction_code: transaction.transactionCode,
            transaction_type:
              transaction.debitOrCredit === "credit"
                ? ETrackingBankTransactionType.DEPOSIT
                : ETrackingBankTransactionType.WITHDRAW,
            amount: transaction.amount,
            tracking_bank_transaction_type:
              transaction.debitOrCredit === "credit"
                ? ETrackingBankTransactionType.DEPOSIT
                : ETrackingBankTransactionType.WITHDRAW,
            transaction_time: new Date(transaction.transactionDate),
            current_balance: null, // ACB không cung cấp balance hiện tại
            raw_data: transaction.transactionEntityAttribute.transactionContent,
            external_transaction_data: JSON.stringify(transaction),
            external_transaction_id: parseInt(transaction.transactionCode),
            tracking_bank_account: bankAccount,
          });

          const savedTransaction = await DatabaseService.getRepository(
            TrackingBankTransaction,
            req
          ).save(transactionData);

          if (savedTransaction) {
            // Trigger webhooks sau khi transaction được tạo
            this.webhookService.processTransactionWebhooks(
              req,
              savedTransaction
            );
            return savedTransaction;
          }
          throw new HttpException(
            "Không thể tạo transaction",
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi xử lý ACB webhook: ${error.message}`, {
        error: error.message,
        stack: error.stack,
        webhookData: JSON.stringify(webhookData, null, 2),
      });
      throw new HttpException(
        `Lỗi xử lý ACB webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
