import { Injectable, Logger } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { Request } from "express";
import { firstValueFrom } from "rxjs";
import { LessThanOrEqual } from "typeorm";
import { AxiosResponse } from "axios";
import { TrackingBankTransaction } from "src/entities/TrackingBankTransaction.entity";
import {
  TrackingBankNotificationSetting,
  ENotificationTrigger,
  EWebhookProvider,
} from "src/entities/TrackingBankNotificationSetting.entity";
import {
  TrackingBankWebhookHistory,
  EWebhookStatus,
} from "src/entities/TrackingBankWebhookHistory.entity";
import { DatabaseService } from "src/providers/database/database.service";

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(private readonly httpService: HttpService) {}

  async processTransactionWebhooks(
    req: Request,
    transaction: TrackingBankTransaction
  ) {
    try {
      // L<PERSON>y tất cả notification settings đang active
      const notificationSettings = await DatabaseService.getRepository(
        TrackingBankNotificationSetting,
        req
      ).find({
        where: { is_active: true },
      });

      for (const setting of notificationSettings) {
        if (await this.shouldTriggerWebhook(transaction, setting)) {
          await this.sendWebhook(req, transaction, setting);
        }
      }
    } catch (error) {
      this.logger.error("Error processing transaction webhooks:", error);
    }
  }

  private async shouldTriggerWebhook(
    transaction: TrackingBankTransaction,
    setting: TrackingBankNotificationSetting
  ): Promise<boolean> {
    switch (setting.trigger_type) {
      case ENotificationTrigger.ALL_TRANSACTIONS:
        return true;

      case ENotificationTrigger.DEPOSIT_ONLY:
        return transaction.tracking_bank_transaction_type === "DEPOSIT";

      case ENotificationTrigger.WITHDRAW_ONLY:
        return transaction.tracking_bank_transaction_type === "WITHDRAW";

      case ENotificationTrigger.AMOUNT_THRESHOLD:
        return (
          setting.amount_threshold &&
          transaction.amount >= setting.amount_threshold
        );

      case ENotificationTrigger.CUSTOM_CODE:
        return this.checkCustomCode(transaction, setting.custom_code);

      default:
        return false;
    }
  }

  private checkCustomCode(
    transaction: TrackingBankTransaction,
    customCode: string
  ): boolean {
    if (!customCode) return false;

    try {
      // Kiểm tra xem raw_data có chứa custom code không
      const rawData = transaction.raw_data?.toLowerCase() || "";
      const codes = customCode
        .toLowerCase()
        .split(",")
        .map((code) => code.trim());

      return codes.some((code) => rawData.includes(code));
    } catch (error) {
      this.logger.error("Error checking custom code:", error);
      return false;
    }
  }

  private extractTransactionCodeWithSuffix(
    transaction: TrackingBankTransaction,
    customCode: string,
    suffixLength?: number
  ): string | null {
    if (!customCode || !suffixLength) return null;

    try {
      const rawData = transaction.raw_data || "";
      const codes = customCode.split(",").map((code) => code.trim());

      for (const code of codes) {
        // Tìm tất cả các vị trí xuất hiện của code
        const matches = this.findAllMatches(rawData, code);

        if (matches.length > 0) {
          // Ưu tiên tìm trong phần nội dung (ND:) trước
          const ndMatch = this.findInContent(rawData, code, suffixLength);
          if (ndMatch) {
            return ndMatch;
          }

          // Nếu không tìm thấy trong ND, lấy match đầu tiên
          const firstMatch = matches[0];
          const endIndex = Math.min(
            firstMatch.index + code.length + suffixLength,
            rawData.length
          );
          return rawData.substring(firstMatch.index, endIndex);
        }
      }

      return null;
    } catch (error) {
      this.logger.error(
        "Error extracting transaction code with suffix:",
        error
      );
      return null;
    }
  }

  private findAllMatches(
    text: string,
    pattern: string
  ): Array<{ index: number; match: string }> {
    const matches = [];
    const lowerText = text.toLowerCase();
    const lowerPattern = pattern.toLowerCase();
    let index = 0;

    while ((index = lowerText.indexOf(lowerPattern, index)) !== -1) {
      matches.push({
        index: index,
        match: text.substring(index, index + pattern.length),
      });
      index += pattern.length;
    }

    return matches;
  }

  private findInContent(
    rawData: string,
    code: string,
    suffixLength: number
  ): string | null {
    // Tìm trong phần nội dung (ND:) của giao dịch
    const ndPattern = /ND:([^;]+)/i;
    const ndMatch = rawData.match(ndPattern);

    if (ndMatch && ndMatch[1]) {
      const content = ndMatch[1];
      const codeIndex = content.toLowerCase().indexOf(code.toLowerCase());

      if (codeIndex !== -1) {
        // Tìm vị trí thực tế trong raw data
        const actualIndex =
          rawData.toLowerCase().indexOf(content.toLowerCase()) + codeIndex;
        const endIndex = Math.min(
          actualIndex + code.length + suffixLength,
          rawData.length
        );
        return rawData.substring(actualIndex, endIndex);
      }
    }

    return null;
  }

  private async sendWebhook(
    req: Request,
    transaction: TrackingBankTransaction,
    setting: TrackingBankNotificationSetting
  ) {
    const webhookHistory = new TrackingBankWebhookHistory({
      status: EWebhookStatus.PENDING,
      request_payload: JSON.stringify(
        this.buildWebhookPayload(transaction, setting)
      ),
      tracking_bank_transaction_id: transaction.id,
      notification_setting_id: setting.id,
      webhook_url: setting.webhook_url,
    });

    try {
      const savedHistory = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).save(webhookHistory);
      await this.executeWebhook(req, savedHistory, setting);
    } catch (error) {
      this.logger.error("Error saving webhook history:", error);
    }
  }

  private async executeWebhook(
    req: Request,
    webhookHistory: TrackingBankWebhookHistory,
    setting: TrackingBankNotificationSetting
  ) {
    try {
      const payload = JSON.parse(webhookHistory.request_payload);
      const headers = {
        "Content-Type": "application/json",
        "User-Agent": "VILL-Bank-Webhook/1.0",
        ...setting.headers,
      };

      // Kiểm tra xem có webhook_url không
      if (!setting.webhook_url) {
        throw new Error("Webhook URL is not configured");
      }

      this.logger.log(`Sending webhook to: ${setting.webhook_url}`);

      // Gửi HTTP request với timeout 30 giây
      const response: AxiosResponse = await firstValueFrom(
        this.httpService.post(setting.webhook_url, payload, {
          headers,
          timeout: 30000,
        })
      );

      // Webhook thành công
      await this.updateWebhookHistory(req, webhookHistory.id, {
        status: EWebhookStatus.SUCCESS,
        response_data: JSON.stringify(response.data),
        response_status_code: response.status,
      });

      this.logger.log(
        `Webhook sent successfully to ${setting.webhook_url}, status: ${response.status}`
      );
    } catch (error) {
      await this.handleWebhookError(req, webhookHistory, setting, error);
    }
  }

  private async handleWebhookError(
    req: Request,
    webhookHistory: TrackingBankWebhookHistory,
    setting: TrackingBankNotificationSetting,
    error: any
  ) {
    const errorMessage =
      error.response?.data || error.message || "Unknown error";
    const statusCode = error.response?.status || 0;
    const currentRetryCount = webhookHistory.retry_count || 0;

    this.logger.error(
      `Webhook failed: ${errorMessage}, status: ${statusCode}, retry count: ${currentRetryCount}`
    );

    // Cập nhật webhook hiện tại thành FAILED ngay lập tức
    await this.updateWebhookHistory(req, webhookHistory.id, {
      status: EWebhookStatus.FAILED,
      error_message: errorMessage,
      response_status_code: statusCode,
    });

    // Nếu còn lần retry, retry ngay lập tức với delay
    if (currentRetryCount < setting.max_retry_attempts) {
      this.logger.log(
        `Retrying webhook immediately, attempt ${currentRetryCount + 1}/${
          setting.max_retry_attempts
        }`
      );

      // Delay trước khi retry
      await this.delay(setting.retry_delay_seconds * 1000);

      // Tạo webhook history mới cho retry
      const newWebhookHistory = new TrackingBankWebhookHistory({
        status: EWebhookStatus.PENDING,
        request_payload: webhookHistory.request_payload,
        tracking_bank_transaction_id:
          webhookHistory.tracking_bank_transaction_id,
        notification_setting_id: webhookHistory.notification_setting_id,
        retry_count: currentRetryCount + 1,
        webhook_url: webhookHistory.webhook_url,
      });

      const savedRetryHistory = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).save(newWebhookHistory);
      this.logger.log(
        `Created retry webhook ${savedRetryHistory.id} for attempt ${
          currentRetryCount + 1
        }`
      );

      // Execute retry ngay lập tức
      await this.executeWebhook(req, savedRetryHistory, setting);
    } else {
      this.logger.warn(
        `Max retry attempts (${setting.max_retry_attempts}) reached for webhook ${webhookHistory.id}`
      );
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async updateWebhookHistory(
    req: Request,
    id: number,
    updateData: Partial<TrackingBankWebhookHistory>
  ) {
    await DatabaseService.getRepository(TrackingBankWebhookHistory, req).update(
      id,
      updateData
    );
  }

  // Kiểm tra và cập nhật các webhook PENDING quá 30 giây thành FAILED
  async checkAndFailTimeoutWebhooks(req: Request): Promise<number> {
    try {
      const thirtySecondsAgo = new Date();
      thirtySecondsAgo.setSeconds(thirtySecondsAgo.getSeconds() - 30);

      const timeoutWebhooks = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).find({
        where: {
          status: EWebhookStatus.PENDING,
          created_at: LessThanOrEqual(thirtySecondsAgo),
        },
      });

      if (timeoutWebhooks.length > 0) {
        await DatabaseService.getRepository(
          TrackingBankWebhookHistory,
          req
        ).update(
          {
            status: EWebhookStatus.PENDING,
            created_at: LessThanOrEqual(thirtySecondsAgo),
          },
          {
            status: EWebhookStatus.FAILED,
            error_message:
              "Webhook timeout after 30 seconds - automatically failed",
          }
        );

        this.logger.warn(`Failed ${timeoutWebhooks.length} timeout webhooks`);
      }

      return timeoutWebhooks.length;
    } catch (error) {
      this.logger.error("Error checking timeout webhooks:", error);
      return 0;
    }
  }

  // Retry specific webhook with correct payload - retry ngay lập tức
  async retrySpecificWebhook(
    req: Request,
    webhookHistory: TrackingBankWebhookHistory,
    setting: TrackingBankNotificationSetting,
    transaction: TrackingBankTransaction
  ) {
    try {
      this.logger.log(
        `Starting immediate retry for webhook ${
          webhookHistory.id
        }, retry count: ${webhookHistory.retry_count || 0}`
      );

      // Rebuild payload with current setting to ensure extracted_transaction_code is included
      const newPayload = this.buildWebhookPayload(transaction, setting);

      // Tạo record mới cho retry thay vì cập nhật record cũ
      const newWebhookHistory = new TrackingBankWebhookHistory({
        status: EWebhookStatus.PENDING,
        request_payload: JSON.stringify(newPayload),
        tracking_bank_transaction_id: transaction.id,
        notification_setting_id: setting.id,
        retry_count: (webhookHistory.retry_count || 0) + 1,
      });

      this.logger.log(
        `Creating new webhook record for immediate retry of ${webhookHistory.id}`
      );

      // Lưu record mới
      const savedHistory = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).save(newWebhookHistory);

      // Execute webhook ngay lập tức
      await this.executeWebhook(req, savedHistory, setting);

      this.logger.log(
        `Immediate retry completed for webhook ${webhookHistory.id}`
      );
    } catch (error) {
      this.logger.error("Error retrying specific webhook:", error);
      throw error;
    }
  }

  // Retry failed webhooks immediately (can be called from controller or scheduler)
  async retryFailedWebhooks(req: Request) {
    try {
      // Find all webhooks with FAILED status only (không retry RETRY status để tránh duplicate)
      const webhooksToRetry = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).find({
        where: { status: EWebhookStatus.FAILED },
        relations: ["tracking_bank_transaction"],
        order: { created_at: "ASC" }, // Retry theo thứ tự cũ nhất trước
      });

      let successCount = 0;
      let failedCount = 0;

      this.logger.log(
        `Found ${webhooksToRetry.length} failed webhooks to retry`
      );

      for (const webhookHistory of webhooksToRetry) {
        try {
          if (
            webhookHistory.tracking_bank_transaction &&
            webhookHistory.notification_setting_id
          ) {
            // Get notification setting
            const setting = await DatabaseService.getRepository(
              TrackingBankNotificationSetting,
              req
            ).findOne({
              where: { id: webhookHistory.notification_setting_id },
            });

            if (setting && setting.is_active) {
              // Kiểm tra xem đã vượt quá max retry chưa
              const currentRetryCount = webhookHistory.retry_count || 0;
              if (currentRetryCount < setting.max_retry_attempts) {
                await this.retrySpecificWebhook(
                  req,
                  webhookHistory,
                  setting,
                  webhookHistory.tracking_bank_transaction
                );
                successCount++;
              } else {
                this.logger.warn(
                  `Webhook ${webhookHistory.id} has reached max retry attempts (${setting.max_retry_attempts})`
                );
                failedCount++;
              }
            } else {
              this.logger.warn(
                `Notification setting ${webhookHistory.notification_setting_id} not found or inactive`
              );
              failedCount++;
            }
          } else {
            this.logger.warn(
              `Webhook ${webhookHistory.id} missing transaction or notification setting`
            );
            failedCount++;
          }
        } catch (error) {
          failedCount++;
          this.logger.error(
            `Failed to retry webhook ${webhookHistory.id}:`,
            error
          );
        }
      }

      this.logger.log(
        `Immediate retry completed: ${successCount} successful, ${failedCount} failed out of ${webhooksToRetry.length} total`
      );

      return {
        total: webhooksToRetry.length,
        successful: successCount,
        failed: failedCount,
      };
    } catch (error) {
      this.logger.error("Error retrying failed webhooks:", error);
      throw error;
    }
  }

  // Retry specific webhook by ID
  async retryWebhookById(req: Request, webhookHistoryId: number) {
    try {
      const webhookHistory = await DatabaseService.getRepository(
        TrackingBankWebhookHistory,
        req
      ).findOne({
        where: { id: webhookHistoryId },
        relations: ["tracking_bank_transaction"],
      });

      if (!webhookHistory) {
        throw new Error(
          `Webhook history with ID ${webhookHistoryId} not found`
        );
      }

      if (webhookHistory.status === EWebhookStatus.SUCCESS) {
        throw new Error("Cannot retry successful webhook");
      }

      if (webhookHistory.status === EWebhookStatus.PENDING) {
        throw new Error("Cannot retry pending webhook");
      }

      if (
        !webhookHistory.tracking_bank_transaction ||
        !webhookHistory.notification_setting_id
      ) {
        throw new Error("Missing transaction or notification setting data");
      }

      // Get notification setting
      const setting = await DatabaseService.getRepository(
        TrackingBankNotificationSetting,
        req
      ).findOne({
        where: { id: webhookHistory.notification_setting_id },
      });

      if (!setting) {
        throw new Error(
          `Notification setting with ID ${webhookHistory.notification_setting_id} not found`
        );
      }

      await this.retrySpecificWebhook(
        req,
        webhookHistory,
        setting,
        webhookHistory.tracking_bank_transaction
      );

      this.logger.log(`Successfully retried webhook ${webhookHistoryId}`);
      return { success: true, message: "Webhook retried successfully" };
    } catch (error) {
      this.logger.error(`Error retrying webhook ${webhookHistoryId}:`, error);
      throw error;
    }
  }

  private buildWebhookPayload(
    transaction: TrackingBankTransaction,
    setting?: TrackingBankNotificationSetting
  ) {
    const payload = {
      event: "transaction.created",
      timestamp: new Date().toISOString(),
      webhook_provider:
        (setting as any)?.webhook_provider || EWebhookProvider.SMS,
      data: {
        transaction_id: transaction.id,
        bank_code: transaction.bank_code,
        transaction_code: transaction.transaction_code,
        transaction_time: transaction.transaction_time,
        transaction_type: transaction.transaction_type,
        amount: transaction.amount,
        current_balance: transaction.current_balance,
        raw_data: transaction.raw_data,
        tracking_bank_transaction_type:
          transaction.tracking_bank_transaction_type,
        is_verified: transaction.is_verified,
        tracking_bank_account_id: transaction.tracking_bank_account_id,
        created_at: transaction.created_at,
      },
    };

    // Nếu là CUSTOM_CODE và có suffix_length, thêm extracted_transaction_code
    if (
      setting?.trigger_type === ENotificationTrigger.CUSTOM_CODE &&
      setting.custom_code &&
      setting.custom_code_suffix_length
    ) {
      const extractedCode = this.extractTransactionCodeWithSuffix(
        transaction,
        setting.custom_code,
        setting.custom_code_suffix_length
      );
      if (extractedCode) {
        payload.data["extracted_transaction_code"] = extractedCode;
      }
    }

    return payload;
  }
}
