import { Injectable, BadRequestException, UnauthorizedException, Logger } from '@nestjs/common';
import { Request } from 'express';
import { TrackingBankProviderWebhook, WebhookProviderType } from '../../../entities/TrackingBankProviderWebhook.entity';
import { CreateProviderWebhookDto, UpdateProviderWebhookDto } from '../dto/provider-webhook.dto';
import { DatabaseService } from 'src/providers/database/database.service';

@Injectable()
export class WebhookProviderService {
    private readonly logger = new Logger(WebhookProviderService.name);

    async createProviderWebhook(
        req: Request,
        createDto: CreateProviderWebhookDto,
    ): Promise<TrackingBankProviderWebhook> {
        const existingProvider = await DatabaseService.getRepository(TrackingBankProviderWebhook, req).findOne({
            where: { provider_name: createDto.provider_name },
        });

        if (existingProvider) {
            throw new BadRequestException(`Provider ${createDto.provider_name} already exists`);
        }

        const providerWebhook = DatabaseService.getRepository(TrackingBankProviderWebhook, req).create(createDto);
        return await DatabaseService.getRepository(TrackingBankProviderWebhook, req).save(providerWebhook);
    }

    async updateProviderWebhook(
        req: Request,
        id: number,
        updateDto: UpdateProviderWebhookDto,
    ): Promise<TrackingBankProviderWebhook> {
        const providerWebhook = await DatabaseService.getRepository(TrackingBankProviderWebhook, req).findOne({
            where: { id },
        });

        if (!providerWebhook) {
            throw new BadRequestException('Provider webhook not found');
        }

        Object.assign(providerWebhook, updateDto);
        return await DatabaseService.getRepository(TrackingBankProviderWebhook, req).save(providerWebhook);
    }

    async getProviderWebhooks(req: Request): Promise<TrackingBankProviderWebhook[]> {
        return await DatabaseService.getRepository(TrackingBankProviderWebhook, req).find({
            order: { created_at: 'DESC' },
        });
    }

    async getProviderWebhookById(req: Request, id: number): Promise<TrackingBankProviderWebhook> {
        const providerWebhook = await DatabaseService.getRepository(TrackingBankProviderWebhook, req).findOne({
            where: { id },
        });

        if (!providerWebhook) {
            throw new BadRequestException('Provider webhook not found');
        }

        return providerWebhook;
    }

    async deleteProviderWebhook(req: Request, id: number): Promise<void> {
        const result = await DatabaseService.getRepository(TrackingBankProviderWebhook, req).delete(id);

        if (result.affected === 0) {
            throw new BadRequestException('Provider webhook not found');
        }
    }

    async getEnabledProvider(req: Request, providerType: WebhookProviderType): Promise<TrackingBankProviderWebhook> {
        const provider = await DatabaseService.getRepository(TrackingBankProviderWebhook, req).findOne({
            where: {
                provider_name: providerType,
                is_enabled: true,
            },
        });

        if (!provider) {
            throw new UnauthorizedException(`Provider ${providerType} is not enabled`);
        }

        return provider;
    }

    async validateSepayWebhook(req: Request, apiKey: string): Promise<boolean> {
        const provider = await this.getEnabledProvider(req, WebhookProviderType.SEPAY);
        return provider.api_key === apiKey;
    }

    async validateSmsWebhook(req: Request, apiKey: string): Promise<boolean> {
        const provider = await this.getEnabledProvider(req, WebhookProviderType.SMS);
        return provider.api_key === apiKey;
    }

    async validateAcbWebhook(req: Request, apiKey: string): Promise<boolean> {
        const provider = await this.getEnabledProvider(req, WebhookProviderType.ACB);
        return provider.api_key === apiKey;
    }

    async processSepayWebhook(req: Request, payload: any, apiKey: string) {
        const isValid = await this.validateSepayWebhook(req, apiKey);

        if (!isValid) {
            throw new UnauthorizedException('Invalid SEPAY API key');
        }

        return payload;
    }

    async processSmsWebhook(req: Request, payload: any, apiKey: string) {
        const isValid = await this.validateSmsWebhook(req, apiKey);

        if (!isValid) {
            throw new UnauthorizedException('Invalid SMS API key');
        }

        // Process SMS webhook logic here
        this.logger.log('Processing SMS webhook:', payload);

        return payload;
    }

    async processAcbWebhook(req: Request, payload: any, apiKey: string) {
        const isValid = await this.validateAcbWebhook(req, apiKey);

        if (!isValid) {
            throw new UnauthorizedException('Invalid ACB API key');
        }

        // Process ACB webhook logic here
        this.logger.log('Processing ACB webhook:', payload);

        return payload;
    }

    async toggleProviderStatus(req: Request, id: number, isEnabled: boolean): Promise<TrackingBankProviderWebhook> {
        const providerWebhook = await this.getProviderWebhookById(req, id);
        providerWebhook.is_enabled = isEnabled;
        return await DatabaseService.getRepository(TrackingBankProviderWebhook, req).save(providerWebhook);
    }
}
