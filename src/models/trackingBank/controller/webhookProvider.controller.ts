import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import { LoggingInterceptor } from "src/common/interceptors/logging.interceptor";
import AuthGuard from "src/common/middlewares/auth.gaurd";
import { RequirePermissions } from "src/common/middlewares/permissions.decorator";
import { PermissionsAccessAction } from "src/entities/permission.entity";
import { TrackingBankProviderWebhook } from "../../../entities/TrackingBankProviderWebhook.entity";
import {
  CreateProviderWebhookDto,
  ToggleProviderStatusDto,
  UpdateProviderWebhookDto,
  WebhookPayloadDto,
} from "../dto/provider-webhook.dto";
import { WebhookProviderService } from "../service/webhookProvider.service";

@ApiTags("Webhook Provider")
@UseInterceptors(LoggingInterceptor)
@Controller("webhook-provider")
@UseGuards(AuthGuard)
export class WebhookProviderController {
  constructor(
    private readonly webhookProviderService: WebhookProviderService
  ) {}

  @Post()
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_CREATE
  )
  async createProviderWebhook(
    @Req() req: Request,
    @Body(new ValidationPipe()) createDto: CreateProviderWebhookDto
  ): Promise<TrackingBankProviderWebhook> {
    return await this.webhookProviderService.createProviderWebhook(
      req,
      createDto
    );
  }

  @Get()
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async getProviderWebhooks(
    @Req() req: Request
  ): Promise<TrackingBankProviderWebhook[]> {
    return await this.webhookProviderService.getProviderWebhooks(req);
  }

  @Get(":id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async getProviderWebhookById(
    @Req() req: Request,
    @Param("id", ParseIntPipe) id: number
  ): Promise<TrackingBankProviderWebhook> {
    return await this.webhookProviderService.getProviderWebhookById(req, id);
  }

  @Put(":id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async updateProviderWebhook(
    @Req() req: Request,
    @Param("id", ParseIntPipe) id: number,
    @Body(new ValidationPipe()) updateDto: UpdateProviderWebhookDto
  ): Promise<TrackingBankProviderWebhook> {
    return await this.webhookProviderService.updateProviderWebhook(
      req,
      id,
      updateDto
    );
  }

  @Delete(":id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async deleteProviderWebhook(
    @Req() req: Request,
    @Param("id", ParseIntPipe) id: number
  ): Promise<void> {
    return await this.webhookProviderService.deleteProviderWebhook(req, id);
  }

  @Post("sms/webhook")
  async handleSmsWebhook(
    @Req() req: Request,
    @Body(new ValidationPipe()) payload: WebhookPayloadDto
  ) {
    return await this.webhookProviderService.processSmsWebhook(
      req,
      payload.data,
      payload.api_key
    );
  }

  @Put(":id/toggle-status")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async toggleProviderStatus(
    @Req() req: Request,
    @Param("id", ParseIntPipe) id: number,
    @Body(new ValidationPipe()) toggleDto: ToggleProviderStatusDto
  ): Promise<TrackingBankProviderWebhook> {
    return await this.webhookProviderService.toggleProviderStatus(
      req,
      id,
      toggleDto.is_enabled
    );
  }
}
