import { Body, Controller, Post, Req, ValidationPipe } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import { AcbWebhookResponseDto } from "../dto/acb-webhook-response.dto";
import { AcbWebhookPayloadDto } from "../dto/acb-webhook.dto";
import { TrackingBankService } from "../service/trackingBank.service";

@ApiTags("Tracking Bank Transactions")
@Controller("tracking-bank-transactions/acb/rtxn-notification")
export class AcbWebhookController {
  constructor(private readonly trackingBankService: TrackingBankService) {}

  @Post()
  @ApiOperation({
    summary: "Receive ACB webhook notification",
    description:
      "Endpoint to receive webhook notifications from ACB bank for transaction updates",
  })
  // @ApiHeader({
  //   name: "Authorization",
  //   required: false,
  //   description:
  //     'API key for authentication with format "Apikey YOUR_API_KEY" (alternative to body parameter)',
  //   example: "Apikey your-acb-api-key",
  // })
  @ApiResponse({
    status: 200,
    description: "Webhook processed successfully",
    type: AcbWebhookResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Invalid webhook data or transaction already exists",
  })
  @ApiResponse({
    status: 401,
    description: "Invalid or missing API key",
  })
  @ApiResponse({
    status: 403,
    description: "ACB provider not enabled",
  })
  @ApiResponse({
    status: 404,
    description: "Bank account not found",
  })
  @ApiResponse({
    status: 500,
    description: "Internal server error",
  })
  async processAcbWebhook(
    @Req() req: Request,
    @Body(new ValidationPipe()) webhookData: AcbWebhookPayloadDto
  ): Promise<AcbWebhookResponseDto> {
    try {
      const trackingTransactions =
        await this.trackingBankService.processAcbWebhook(req, webhookData);

      // Trả về response theo format ACB yêu cầu
      return {
        timestamp: new Date().toISOString(),
        responseCode: "********",
        message: "Success",
        responseBody: {
          index: 1,
          referenceCode: trackingTransactions.id.toString(),
        },
      };
    } catch (error) {
      // Trả về error response theo format ACB
      return {
        timestamp: new Date().toISOString(),
        responseCode: "********",
        message: error.message || "Internal Server Error",
        responseBody: {
          index: 1,
          referenceCode: webhookData.masterMeta?.clientRequestId || "UNKNOWN",
        },
      };
    }
  }
}
