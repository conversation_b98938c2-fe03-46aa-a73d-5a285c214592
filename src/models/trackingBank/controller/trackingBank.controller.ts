import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import AuthGuard from "src/common/middlewares/auth.gaurd";
import { RequirePermissions } from "src/common/middlewares/permissions.decorator";
import { PermissionsAccessAction } from "src/entities/permission.entity";
import { CreateTrackingBankDTO } from "../dto/create-tracking-bank-account.dto";
import { CreateBankTransactionDto } from "../dto/create-tracking-bank-trans.dto";
import { GetTrackingTransactionBankDto } from "../dto/get-tracking-transaction-bank-dto";
import { GetInternalTransactionsDto } from "../dto/internal-transactions.dto";
import {
  CreateNotificationSettingDto,
  GetNotificationSettingsDto,
  UpdateNotificationSettingDto,
} from "../dto/notification-setting.dto";
import { TestWebhookDto } from "../dto/test-webhook.dto";
import {
  BulkUpdateTransactionDto,
  CreateBankProviderDto,
  UpdateTransactionDto,
} from "../dto/tracking-bank-provider-dto";
import { UpdateTrackingBankAccountDTO } from "../dto/update-tracking-bank-account.dto";
import { GetWebhookHistoryDto } from "../dto/webhook-history.dto";

import { TrackingBankService } from "../service/trackingBank.service";

import { LoggingInterceptor } from "src/common/interceptors/logging.interceptor";

@ApiTags("Tracking Bank Transactions")
@UseInterceptors(LoggingInterceptor)
@Controller("tracking-bank-transactions")
@UseGuards(AuthGuard)
export class TrackingBankController {
  constructor(private service: TrackingBankService) {}

  @Get("bank-account")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async getBankAccounts(@Req() req: Request) {
    return await this.service.getBankAccounts(req);
  }

  @Post("bank-account")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_CREATE
  )
  async createBankAccount(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: CreateTrackingBankDTO
  ) {
    return await this.service.createBankAccount(req, body);
  }

  @Put("bank-account/:id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async updateBankAccount(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number,
    @Body(new ValidationPipe()) body: UpdateTrackingBankAccountDTO
  ) {
    return await this.service.updateBankAccount(req, id, body);
  }

  @Delete("bank-account/:id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async deleteBankAccount(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.deleteBankAccount(req, id);
  }

  @Post("bank-provider")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_CREATE
  )
  async createBankProvider(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: CreateBankProviderDto
  ) {
    return await this.service.createBankProvider(req, body);
  }

  @Get("bank-provider")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async getBankProviders(@Req() req: Request) {
    return await this.service.getBankProviders(req);
  }

  @Post("list")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async getList(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: GetTrackingTransactionBankDto
  ) {
    const res = await this.service.findAll(req, body);
    return {
      items: res[0],
      total: res[1],
    };
  }
  @Post()
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_CREATE
  )
  async create(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: CreateBankTransactionDto
  ) {
    return await this.service.create(req, body);
  }

  @Get(":id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX
  )
  async findById(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.findById(req, id);
  }

  @Put(":id")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async updateByID(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number,
    @Body(new ValidationPipe()) body: UpdateTransactionDto
  ) {
    return await this.service.updateTransaction(req, id, body);
  }

  @Post("bulk-update")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_UPDATE
  )
  async bulkUpdate(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: BulkUpdateTransactionDto
  ) {
    return await this.service.bulkUpdateTransactions(req, body);
  }

  @Post("notification-settings/list")
  async getNotificationSettings(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: GetNotificationSettingsDto
  ) {
    const res = await this.service.getNotificationSettings(req, body);
    return {
      items: res[0],
      total: res[1],
    };
  }

  @Post("notification-settings")
  async createNotificationSetting(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: CreateNotificationSettingDto
  ) {
    return await this.service.createNotificationSetting(req, body);
  }

  @Get("notification-settings/:id")
  async getNotificationSettingById(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.getNotificationSettingById(req, id);
  }

  @Put("notification-settings/:id")
  async updateNotificationSetting(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number,
    @Body(new ValidationPipe()) body: UpdateNotificationSettingDto
  ) {
    return await this.service.updateNotificationSetting(req, id, body);
  }

  @Delete("notification-settings/:id")
  async deleteNotificationSetting(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.deleteNotificationSetting(req, id);
  }

  // Webhook History Endpoints
  @Post("webhook-history/list")
  // @RequirePermissions(PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_INDEX)
  async getWebhookHistory(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: GetWebhookHistoryDto
  ) {
    const res = await this.service.getWebhookHistory(req, body);
    return {
      items: res[0],
      total: res[1],
    };
  }

  @Get("webhook-history/:id")
  async getWebhookHistoryById(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.getWebhookHistoryById(req, id);
  }

  @Post("webhook-history/:id/retry")
  async retryWebhook(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.retryWebhook(req, id);
  }

  // Manual webhook trigger for specific transaction
  @Post(":id/trigger-webhook")
  async triggerWebhookForTransaction(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.triggerWebhookForTransaction(req, id);
  }

  // Get webhook history for specific transaction
  @Get(":id/webhook-history")
  async getTransactionWebhookHistory(
    @Req() req: Request,
    @Param("id", new ParseIntPipe()) id: number
  ) {
    return await this.service.getTransactionWebhookHistory(req, id);
  }

  // Retry all failed webhooks
  @Post("webhook-history/retry-all")
  async retryAllFailedWebhooks(@Req() req: Request) {
    return await this.service.retryAllFailedWebhooks(req);
  }

  @Post("webhook-history/reset-stuck")
  async resetStuckWebhooks(@Req() req: Request) {
    return await this.service.resetStuckWebhooks(req);
  }

  @Post("test-webhook")
  @RequirePermissions(
    PermissionsAccessAction.TRACKING_TRANSACTION_BANKING_CREATE
  )
  async testWebhook(@Req() req: Request) {
    return await this.service.createTestTransaction(req);
  }

  @Post("test-webhook-custom")
  async testWebhookCustom(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: TestWebhookDto
  ) {
    return await this.service.createTestTransactionWithCustomPayload(req, body);
  }

  @Post("internal-transactions/list")
  async getInternalTransactions(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: GetInternalTransactionsDto
  ) {
    const res = await this.service.getInternalTransactions(req, body);
    return {
      items: res[0],
      total: res[1],
    };
  }

  @Post("internal-transactions/statistics")
  async getInternalTransactionsStatistics(
    @Req() req: Request,
    @Body(new ValidationPipe()) body: GetInternalTransactionsDto
  ) {
    return await this.service.getInternalTransactionsStatistics(req, body);
  }
}
