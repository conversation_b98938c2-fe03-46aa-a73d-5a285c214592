import {
    Body,
    Controller,
    Delete,
    Get,
    NotFoundException,
    Param,
    ParseIntPipe,
    Post,
    Put,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { CreateCollectionDto, UpdateCollectionDto } from './collection.dto';
import { CollectionService } from './collection.service';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('collections')
@UseGuards(AuthGuard)
export class CollectionController {
    constructor(private readonly collectionService: CollectionService) {}
    @Post()
    @RequirePermissions(PermissionsAccessAction.COLLECTION_CREATE)
    async create(@Body(new HttpValidationPipe()) data: CreateCollectionDto) {
        return await this.collectionService.create(data);
    }

    @Get()
    @RequirePermissions(PermissionsAccessAction.COLLECTION_FIND_LIST)
    async list() {
        return await this.collectionService.list();
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_FIND_ONE)
    async getById(@Param('id', new ParseIntPipe()) id: number) {
        const result = await this.collectionService.getById(id);
        if (!result) throw new NotFoundException('category not found');
        return result;
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_REMOVE)
    async deleteById(@Param('id', new ParseIntPipe()) id: number) {
        return await this.collectionService.delete(id);
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_UPDATE)
    async update(
        @Body(new HttpValidationPipe()) data: UpdateCollectionDto,
        @Param('id', new ParseIntPipe()) id: number,
    ) {
        return await this.collectionService.update(id, data);
    }

    @Post(':id/image')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_UPDATE)
    @UseInterceptors(FileInterceptor('image'))
    async uploadImage(@Param('id', new ParseIntPipe()) id: number, @UploadedFile() file: Express.Multer.File) {
        return await this.collectionService.uploadImage(id, file);
    }

    @Post(':id/inactive-image')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_UPDATE)
    @UseInterceptors(FileInterceptor('image'))
    async uploadInactiveImage(@Param('id', new ParseIntPipe()) id: number, @UploadedFile() file: Express.Multer.File) {
        return await this.collectionService.uploadInactiveImage(id, file);
    }

    @Post(':id/new-image')
    @RequirePermissions(PermissionsAccessAction.COLLECTION_UPDATE)
    @UseInterceptors(FileInterceptor('image'))
    async uploadNewImage(@Param('id', new ParseIntPipe()) id: number, @UploadedFile() file: Express.Multer.File) {
        return await this.collectionService.uploadNewImage(id, file);
    }
}
