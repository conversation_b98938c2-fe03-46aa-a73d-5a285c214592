import { BadRequestException, Inject, Injectable, NotFoundException, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import * as _ from 'lodash';
import { isNil, isUndefined } from 'lodash';
import * as moment from 'moment';
import { PROVINCE_HEADER } from 'src/common/constants';
import { createImageUrlDependingOnProvince } from 'src/common/helpers/common.helper';
import { Collection, ICollectionBadge } from 'src/entities/collection.entity';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { Repository } from 'typeorm';
import { CreateCollectionDto, UpdateCollectionDto } from './collection.dto';

@Injectable({ scope: Scope.REQUEST })
export class CollectionService {
    private collectionRepo: Repository<Collection>;

    constructor(@Inject(REQUEST) private request: Request, private readonly s3Service: AwsS3Service) {
        this.collectionRepo = DatabaseService.getRepository(Collection, this.request);
    }

    async list() {
        return this.collectionRepo.find({
            order: {
                ordinal_numbers: 'ASC',
                id: 'ASC',
            },
        });
    }

    async getById(id: number): Promise<Collection> {
        return await this.collectionRepo.findOne({ where: { id } });
    }

    async create({ code, name }: CreateCollectionDto) {
        const cate = new Collection(name, code);
        return await this.collectionRepo.save(cate);
    }

    async delete(id: number): Promise<any> {
        const item = await this.collectionRepo.findOne({ where: { id } });
        if (!item) throw new NotFoundException();

        return await this.collectionRepo.delete({ id: item.id });
    }

    async update(
        id: number,
        {
            active,
            name,
            code,
            ordinal_numbers,
            open_24h,
            open_time,
            close_time,
            badge,
            badge_active,
            badge_active_from,
            badge_active_to,
            badge_bg_color,
            badge_text_color,
        }: UpdateCollectionDto,
    ) {
        const item = await this.collectionRepo.findOne({ where: { id } });
        if (!item) throw new NotFoundException();

        if (!isUndefined(code)) {
            item.code = code;
        }
        if (!isUndefined(active)) {
            item.active = active;
        }
        if (!isUndefined(name)) {
            item.name = name;
        }
        if (!isUndefined(close_time)) {
            item.close_time = close_time;
        }
        if (!isUndefined(active)) {
            item.active = active;
        }
        if (!isUndefined(open_24h)) {
            item.open_24h = open_24h;
        }
        if (item.open_24h) {
            item.open_time = item.close_time = null;
        } else if (!isNil(open_time) && !isNil(close_time)) {
            if (!moment(open_time, 'HH:mm').isBefore(moment(close_time, 'HH:mm'))) {
                throw new BadRequestException('Thời gian bắt đầu nhỏ hơn thời gian kết thúc');
            }
            item.open_time = open_time;
            item.close_time = close_time;
        }
        if (!_.isNil(ordinal_numbers)) item.ordinal_numbers = ordinal_numbers;

        const badgeInfo: ICollectionBadge = {
            text: '',
            active: 0,
            active_from: '',
            active_to: '',
            bg_color: '',
            text_color: '',
        };

        if (!isUndefined(badge)) {
            badgeInfo['text'] = badge;
        }
        if (!isUndefined(badge_active)) {
            badgeInfo['active'] = badge_active;
        }
        if (!isUndefined(badge_active_from)) {
            badgeInfo['active_from'] = badge_active_from;
        }
        if (!isUndefined(badge_active_to)) {
            badgeInfo['active_to'] = badge_active_to;
        }
        if (!isUndefined(badge_bg_color)) {
            badgeInfo['bg_color'] = badge_bg_color;
        }
        if (!isUndefined(badge_text_color)) {
            badgeInfo['text_color'] = badge_text_color;
        }

        item.setBadge(badgeInfo);

        return await this.collectionRepo.save(item);
    }

    async uploadImage(id: number, file: Express.Multer.File): Promise<Collection> {
        const collection = await this.collectionRepo.findOne({ where: { id } });
        if (!collection) throw new NotFoundException('collection not found');

        const fileName = createImageUrlDependingOnProvince(
            id,
            file.originalname.split('.').pop(),
            this.request.get(PROVINCE_HEADER),
            'COLLECTION',
        );

        const data = await this.s3Service.uploadPublicFile(fileName, file.mimetype, file.buffer);

        const { Location, Key } = data;
        if (collection.image) {
            const oldKey = new URL(collection.image).pathname.replace('/', '').trim();
            if (!_.isEqual(Key, oldKey)) {
                this.s3Service.deletePublicFile(oldKey);
            }
        }
        collection.image = Location;

        return await this.collectionRepo.save(collection);
    }
    async uploadInactiveImage(id: number, file: Express.Multer.File): Promise<Collection> {
        const collection = await this.collectionRepo.findOne({ where: { id } });
        if (!collection) throw new NotFoundException('collection not found');

        const fileName = createImageUrlDependingOnProvince(
            id,
            file.originalname.split('.').pop(),
            this.request.get(PROVINCE_HEADER),
            'COLLECTION',
        );

        const data = await this.s3Service.uploadPublicFile(fileName, file.mimetype, file.buffer);

        const { Location, Key } = data;
        if (collection.inactive_image) {
            const oldKey = new URL(collection.inactive_image).pathname.replace('/', '').trim();
            if (!_.isEqual(Key, oldKey)) {
                this.s3Service.deletePublicFile(oldKey);
            }
        }
        collection.inactive_image = Location;

        return await this.collectionRepo.save(collection);
    }

    async uploadNewImage(id: number, file: Express.Multer.File): Promise<Collection> {
        const collection = await this.collectionRepo.findOne({ where: { id } });
        if (!collection) throw new NotFoundException('collection not found');

        const fileName = createImageUrlDependingOnProvince(
            id,
            file.originalname.split('.').pop(),
            this.request.get(PROVINCE_HEADER),
            'COLLECTION',
        );

        const data = await this.s3Service.uploadPublicFile(fileName, file.mimetype, file.buffer);

        const { Location, Key } = data;
        if (collection.inactive_image) {
            const oldKey = new URL(collection.inactive_image).pathname.replace('/', '').trim();
            if (!_.isEqual(Key, oldKey)) {
                this.s3Service.deletePublicFile(oldKey);
            }
        }
        collection.new_image = Location;

        return await this.collectionRepo.save(collection);
    }
}
