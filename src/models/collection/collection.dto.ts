import {
    IsBoolean,
    IsHexColor,
    IsIn,
    IsMilitaryTime,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    MaxLength,
    ValidateIf,
} from 'class-validator';

export class CreateCollectionDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsNotEmpty()
    @IsString()
    code: string;
}

export class UpdateCollectionDto extends CreateCollectionDto {
    @IsNotEmpty()
    @IsIn([0, 1])
    active: 0 | 1;

    @IsOptional()
    @ValidateIf((obj, value) => value != null && value != undefined)
    @IsNumber()
    ordinal_numbers: number;

    @IsOptional()
    @IsIn([0, 1])
    open_24h: 0 | 1;

    @ValidateIf((obj: UpdateCollectionDto) => obj.open_24h == 0)
    @IsMilitaryTime()
    open_time: string;

    @ValidateIf((obj: UpdateCollectionDto) => obj.open_24h == 0)
    @IsMilitaryTime()
    close_time: string;

    @IsOptional()
    @IsString()
    @MaxLength(10)
    badge: string;

    @IsOptional()
    @IsIn([0, 1])
    badge_active: 0 | 1;

    @IsOptional()
    badge_active_from: string;

    @IsOptional()
    badge_active_to: string;

    @IsOptional()
    @IsHexColor()
    badge_bg_color: string;

    @IsOptional()
    @IsHexColor()
    badge_text_color: string;
}
