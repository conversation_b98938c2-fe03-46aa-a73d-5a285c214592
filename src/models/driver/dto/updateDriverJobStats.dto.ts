import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>In, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { TinyInt } from 'src/common/constants';

export class UpdateDriverJobStatsDto {
    @IsOptional()
    @IsNumber()
    auto_jobs?: number;

    @IsOptional()
    @IsNumber()
    manual_jobs?: number;

    @IsOptional()
    @IsNumber()
    bonus_jobs?: number;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    in_job_cycle?: TinyInt;

    @IsOptional()
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    auto_job_ids?: number[];

    @IsOptional()
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    manual_job_ids?: number[];

    @IsOptional()
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    bonus_job_ids?: number[];

    @IsOptional()
    @IsNumber()
    @Min(0)
    priority_times?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    rejected_jobs_count?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    priority_mode_rejected_jobs_count?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(1)
    is_received_bonus_time_in_prev_order?: number = 0;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Type(() => Number)
    unconfirmed_jobs_count?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Type(() => Number)
    consecutive_rejected_jobs_count?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Type(() => Number)
    in_app_timeout_jobs_count?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Type(() => Number)
    proactively_reject_jobs_count?: number;
}
