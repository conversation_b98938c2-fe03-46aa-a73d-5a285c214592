import { IsIn, IsOptional, IsString } from 'class-validator';
import { TinyInt } from 'src/common/constants';

export class UpdateDriverJobSettingDto {
    @IsOptional()
    @IsString()
    waiting_time?: Date;

    @IsOptional()
    @IsString()
    waiting_time_in_discipline?: Date;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    can_receiving_job?: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    enabled_receiving_job?: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    in_discipline?: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    assignment_without_consent?: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    enabled_priority?: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    job_paused?: TinyInt;

    @IsOptional()
    @IsString()
    message?: string;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    stop_suspend_if_sufficient_balance?: TinyInt;
}
