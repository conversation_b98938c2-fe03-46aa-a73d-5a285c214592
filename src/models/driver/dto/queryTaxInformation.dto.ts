import { IsOptional, IsString, IsNumber, IsNotEmpty } from 'class-validator';

export class QueryTaxInformationByTaxCodeDto {
    @IsNotEmpty()
    @IsString()
    taxCode: string;

    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;
}

export class QueryTaxInformationByCitizenIdDto {
    @IsNotEmpty()
    @IsString()
    citizenId: string;

    @IsOptional()
    @IsNumber()
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    limit?: number = 10;
}