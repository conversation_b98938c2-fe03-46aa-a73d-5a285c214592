import { IsOptional, IsString } from 'class-validator';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { EDriverWalletProvider } from 'src/entities/driver.entity';

export class InfoFilterDto extends BaseQueryFilterDto {
    @IsOptional()
    @IsString()
    startWalletDate: string;

    @IsOptional()
    @IsString()
    endWalletDate: string;

    @IsOptional()
    @IsString()
    walletProvider: EDriverWalletProvider;
}
