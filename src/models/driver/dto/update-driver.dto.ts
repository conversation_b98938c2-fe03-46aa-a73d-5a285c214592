import { IsBoolean, IsIn, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, ValidateIf } from 'class-validator';
import { isNil } from 'lodash';

export class UpdateDriverDto {
    @ValidateIf((obj, val) => !isNil(val))
    @IsBoolean()
    available: boolean;

    @ValidateIf((obj, val) => !isNil(val))
    @IsIn([0, 1])
    is_active: number;

    @ValidateIf((obj, val) => !isNil(val))
    @IsIn([0, 1])
    tax_declared: number;

    @ValidateIf((obj, val) => !isNil(val))
    @IsIn([0, 1])
    emptyOrderSlots: number;

    @IsOptional()
    @IsString()
    license_plate: string;

    @IsOptional()
    @IsNumber()
    ranking_id: number;

    // @IsOptional()
    // @IsString()
    // personal_tax_code: string;

    // @IsOptional()
    // @IsString()
    // address: string;
}
