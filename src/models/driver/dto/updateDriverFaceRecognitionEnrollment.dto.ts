import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { EDriverFaceRecognitionEnrollmentStatus } from 'src/entities/driverFaceRecognitionEnrollment.entity';

export class UpdateDriverFaceRecognitionEnrollmentDto {
    @ApiProperty({
        description: 'Note',
        example: 'Driver face recognition enrollment note',
    })
    @IsOptional()
    @IsString()
    note?: string;

    @ApiProperty({
        description: `Status values: ${Object.values(EDriverFaceRecognitionEnrollmentStatus)}`,
        example: EDriverFaceRecognitionEnrollmentStatus.PENDING,
    })
    @IsOptional()
    @IsString()
    status?: EDriverFaceRecognitionEnrollmentStatus;
}
