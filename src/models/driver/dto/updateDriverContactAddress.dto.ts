import { Transform } from 'class-transformer';
import { IsOptional, IsString, IsNumber } from 'class-validator';
import * as _ from 'lodash';

export class UpdateDriverContactAddressDto {
    

    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    province: number;

    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    district: number;

    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    ward: number;

    @IsOptional()
    @IsString()
    street: string;

    @IsOptional()
    @IsString()
    note: string;
}
