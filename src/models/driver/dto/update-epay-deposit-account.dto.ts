import { Transform, TransformFnParams } from 'class-transformer';
import { IsDate, IsNotEmpty, IsNumber } from 'class-validator';
import * as _ from 'lodash';

export class UpdateEpayDepositAccountDto {
    @IsNotEmpty()
    @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    @IsNumber()
    userId: number;

    // @IsNotEmpty()
    // @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    // @IsNumber()
    // bankId: number;

    // @IsNotEmpty()
    // @IsDate()
    // startDate: string;

    // @IsNotEmpty()
    // @IsDate()
    // endDate: string;

    @IsNotEmpty()
    provinceId: string;
}
