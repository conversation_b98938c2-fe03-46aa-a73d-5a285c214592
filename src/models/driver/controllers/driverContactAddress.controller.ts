import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { Request } from 'express';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { CreateDriverContactAddressDto } from '../dto/createDriverContactAddress.dto';
import { UpdateDriverContactAddressDto } from '../dto/updateDriverContactAddress.dto';
import { DriverContactAddressService } from '../services/driverContactAddress.service';

@UseInterceptors(LoggingInterceptor)
@Controller('drivers/:driverId/contact-address')
@UseGuards(AuthGuard)
export class DriverContactAddressController {
    constructor(private readonly driverContactAddressService: DriverContactAddressService) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.USER_FIND_ONE_DRIVER)
    async getContactAddress(@Param('driverId', ParseIntPipe) driverId: number, @Req() req: Request) {
        return await this.driverContactAddressService.findByDriverId(req, driverId);
    }

    @Post()
    @RequirePermissions(PermissionsAccessAction.USER_UPDATE_DRIVER)
    async createContactAddress(
        @Param('driverId', ParseIntPipe) driverId: number,
        @Body(new HttpValidationPipe()) body: CreateDriverContactAddressDto,
        @Req() req: Request,
    ) {
        return await this.driverContactAddressService.create(req, driverId, body);
    }

    @Put()
    @RequirePermissions(PermissionsAccessAction.USER_UPDATE_DRIVER)
    async updateContactAddress(
        @Param('driverId', ParseIntPipe) driverId: number,
        @Body(new HttpValidationPipe()) body: UpdateDriverContactAddressDto,
        @Req() req: Request,
    ) {
        return await this.driverContactAddressService.update(req, driverId, body);
    }

    @Delete()
    @RequirePermissions(PermissionsAccessAction.USER_UPDATE_DRIVER)
    async deleteContactAddress(
        @Param('driverId', ParseIntPipe) driverId: number,
        @Req() req: Request,
    ) {
        await this.driverContactAddressService.delete(req, driverId);
        return { success: true };
    }
}
