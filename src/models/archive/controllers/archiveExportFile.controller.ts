import { Controller, Get, Inject, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ARCHIVE_PROXY } from '../archive.constant';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive/export-files')
@UseGuards(AuthGuard)
export class ArchiveExportFileController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_EXPORT_FILE_INDEX)
    getPagedList(@Query() query: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'exportFile.getPagedList' },
            {
                ...query,
                limit: query.limit ? parseInt(query.limit) : 10,
                page: query.page ? parseInt(query.page) : 1,
                dbConfigId: query.dbConfigId ? parseInt(query.dbConfigId) : null,
            },
        );
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_EXPORT_FILE_INDEX)
    findOneById(@Param('id', new ParseIntPipe()) id: number) {
        return this.archiveProxy.send({ cmd: 'exportFile.findOneById' }, { id });
    }

    @Get(':id/download')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_EXPORT_FILE_INDEX)
    download(@Param('id', new ParseIntPipe()) id: number, @Query() query: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'exportFile.download' }, { id, ...query });
    }
}
