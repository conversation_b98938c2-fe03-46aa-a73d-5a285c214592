import { Controller, Get, Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { ARCHIVE_PROXY } from '../archive.constant';
import AuthGuard from 'src/common/middlewares/auth.gaurd';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive')
@UseGuards(AuthGuard)
export class ArchiveOrderBoxController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get('order-box/orders')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_ORDER_BOX_INDEX)
    getPagedOrderList(@Query() query: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'orderBox.orders.getPagedList' },
            {
                ...query,
                limit: query.limit ? parseInt(query.limit) : 10,
                page: query.page ? parseInt(query.page) : 1,
                dbExportConfigurationId: query.dbExportConfigurationId ? parseInt(query.dbExportConfigurationId) : null,
            },
        );
    }

    @Get('order-box/orders/:id')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_ORDER_BOX_INDEX)
    async getOrder(
        @Param('id') id: string,
        @Query('dbExportConfigurationId')
        dbExportConfigurationId: string,
    ) {
        return this.archiveProxy.send(
            { cmd: 'orderBox.orders.getOneById' },
            {
                id: parseInt(id, 10),
                dbExportConfigurationId: parseInt(dbExportConfigurationId, 10),
            },
        );
    }
}
