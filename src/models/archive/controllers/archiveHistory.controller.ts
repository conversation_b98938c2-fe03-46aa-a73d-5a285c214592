import { Controller, Get, Inject, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { ARCHIVE_PROXY } from '../archive.constant';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive/archive-histories')
@UseGuards(AuthGuard)
export class ArchiveHistoryController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_HISTORY_INDEX)
    findPagedList(@Query() query: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'archiveHistory.getPagedList' },
            {
                ...query,
                dbConfigId: query.dbConfigId ? parseInt(query.dbConfigId) : null,
                limit: query.limit ? parseInt(query.limit) : 10,
                page: query.page ? parseInt(query.page) : 1,
            },
        );
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_HISTORY_INDEX)
    async findOneById(@Param('id', new ParseIntPipe()) id: number) {
        return this.archiveProxy.send({ cmd: 'archiveHistory.findOneById' }, { id });
    }
}
