import { UseGuards, Body, Controller, Get, Inject, Post, Put } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ARCHIVE_PROXY } from '../archive.constant';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive/configurations')
@UseGuards(AuthGuard)
export class ArchiveConfigurationsController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_INDEX)
    findAll() {
        return this.archiveProxy.send({ cmd: 'configuration.findAll' }, {});
    }

    @Post('batch_job_execution_range')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_CREATE)
    createBatchJobExecutionRange(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'configuration.createBatchJobExecutionTimeRange' }, body);
    }

    @Put('batch_job_execution_range')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_UPDATE)
    updateBatchJobExecutionRange(
        @Body()
        body: Record<string, any>,
    ) {
        return this.archiveProxy.send({ cmd: 'configuration.updateBatchJobExecutionTimeRange' }, body);
    }

    @Post('webhook')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_CREATE)
    createWebhook(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'configuration.createWebhook' }, body);
    }

    @Put('webhook')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_UPDATE)
    updateWebhook(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'configuration.updateWebhook' }, body);
    }

    @Post('daily_archive_retention_policy')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_CREATE)
    createDailyRetentionPolicyConfiguration(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'configuration.createDailyRetentionPolicy' }, body);
    }

    @Put('daily_archive_retention_policy')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_UPDATE)
    updateDailyRetentionPolicyConfiguration(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'configuration.updateDailyRetentionPolicy' }, body);
    }
}
