import { Controller, Inject, Get, Param, ParseIntPipe, Put, Body, Post, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ARCHIVE_PROXY } from '../archive.constant';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive/db-export-configurations')
@UseGuards(AuthGuard)
export class DBArchiveConfigController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get('all')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_INDEX)
    findAllDBExportConfigurations() {
        return this.archiveProxy.send({ cmd: 'dbExportConfiguration.findAll' }, {});
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_INDEX)
    findOneById(@Param('id', new ParseIntPipe()) id: number) {
        return this.archiveProxy.send({ cmd: 'dbExportConfiguration.findOneById' }, id);
    }

    @Post('')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_CREATE)
    create(@Body() body: Record<string, any>) {
        return this.archiveProxy.send({ cmd: 'dbExportConfiguration.create' }, body);
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_UPDATE)
    update(@Param('id', new ParseIntPipe()) id: number, @Body() body: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'dbExportConfiguration.updateById' },
            {
                id,
                ...body,
            },
        );
    }

    @Put(':id/source-configurations')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_CONFIGURATIONS_UPDATE)
    updateSourceConfiguration(@Param('id', new ParseIntPipe()) id: number, @Body() body: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'dbExportConfiguration.updateSourceConfiguration' },
            {
                id,
                ...body,
            },
        );
    }
}
