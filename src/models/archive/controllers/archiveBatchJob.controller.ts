import { Body, Controller, Get, Inject, Post, Query, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { ARCHIVE_PROXY } from '../archive.constant';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('archive/batch-jobs')
@UseGuards(AuthGuard)
export class ArchiveBatchJobController {
    constructor(@Inject(ARCHIVE_PROXY) private readonly archiveProxy: ClientProxy) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_BATCH_JOB_INDEX)
    getPagedBatchJobs(@Query() query: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'batchJob.getPagedList' },
            {
                ...query,
                limit: query.limit ? parseInt(query.limit) : 10,
                page: query.page ? parseInt(query.page) : 1,
                dbConfigId: query.dbConfigId ? parseInt(query.dbConfigId) : null,
            },
        );
    }

    @Post('bulk-create')
    @RequirePermissions(PermissionsAccessAction.ARCHIVE_BATCH_JOB_CREATE)
    bulkCreateBatchJobs(@Body() body: Record<string, any>) {
        return this.archiveProxy.send(
            { cmd: 'batchJob.bulkCreate' },
            {
                type: 'EXPORT_AND_IMPORT',
                ...body,
            },
        );
    }
}
