import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ARCHIVE_PROXY } from './archive.constant';
import { ArchiveBatchJobController } from './controllers/archiveBatchJob.controller';
import { ArchiveConfigurationsController } from './controllers/archiveConfiguration.controller';
import { ArchiveExportFileController } from './controllers/archiveExportFile.controller';
import { ArchiveHistoryController } from './controllers/archiveHistory.controller';
import { ArchiveOrderBoxController } from './controllers/archiveOrderBox.controller';
import { DBArchiveConfigController } from './controllers/archiveDBMigrationConfig.controller';

@Module({
    controllers: [
        ArchiveConfigurationsController,
        ArchiveBatchJobController,
        ArchiveOrderBoxController,
        DBArchiveConfigController,
        ArchiveExportFileController,
        ArchiveHistoryController,
    ],
    imports: [],
    providers: [
        {
            provide: ARCHIVE_PROXY,
            useFactory: async (configService: ConfigService) => {
                const proxyConfig = configService.get('archiveService');
                const client = ClientProxyFactory.create({
                    transport: Transport.TCP,
                    options: {
                        host: proxyConfig.options.host,
                        port: proxyConfig.options.port,
                    },
                });
                setInterval(async () => {
                    try {
                        client.emit({ cmd: 'ping' }, '').subscribe({
                            error: (err) => {
                                Logger.error(`Archive Service is down at ${new Date()} | err: ${err.message}`);
                            },
                        });
                    } catch (error) {
                        Logger.error(`Archive Service is down at ${new Date()}`, 'ArchiveModule');
                    }
                }, 60000);
                try {
                    await client.connect();
                    Logger.log('Archive Service connected', 'ArchiveModule');
                } catch (error) {
                    Logger.error('Archive Service cannot connect', error, 'ArchiveModule');
                }
                return client;
            },
            inject: [ConfigService],
        },
    ],
})
export class ArchiveModule {}
