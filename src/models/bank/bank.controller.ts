import { Body, Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import { PROVINCE_HEADER } from 'src/common/constants';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DriverWalletCommandService } from 'src/providers/microservices/driverWallet/driverWalletCommand.service';
import { GetAccountDto } from 'src/providers/microservices/driverWallet/dto/onepay-payout-get-account.dto';
import { GetBankAccountDto } from './dto';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('banks')
@UseGuards(AuthGuard)
export class BankController {
    constructor(private driverWalletCommandService: DriverWalletCommandService) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.USER_UPDATE)
    find(@Req() req: Request) {
        return this.driverWalletCommandService.getBanks({ provinceId: req.get(PROVINCE_HEADER) });
    }

    @Get('epay-support-banks')
    getEpaySupportBank(@HeaderProvince() provinceId: string) {
        return this.driverWalletCommandService.getEpayCollectSupportBank({ provinceId });
    }

    @Get('9pay-support-banks')
    get9PaySupportBank(@HeaderProvince() provinceId: string) {
        return this.driverWalletCommandService.get9PayCollectSupportBank({ provinceId });
    }

    @Get('account')
    @RequirePermissions(PermissionsAccessAction.USER_UPDATE)
    getAccount(
        @Query(new HttpValidationPipe()) { accountNumber, cardNumber, bankId }: GetBankAccountDto,
        @Req() req: Request,
    ) {
        const getBankAccountParamsDto = new GetAccountDto();
        getBankAccountParamsDto.accountNumber = accountNumber;
        getBankAccountParamsDto.cardNumber = cardNumber;
        getBankAccountParamsDto.bankId = bankId;
        getBankAccountParamsDto.provinceId = req.get(PROVINCE_HEADER);
        return this.driverWalletCommandService.onepayPayoutGetAccount(getBankAccountParamsDto);
    }
}
