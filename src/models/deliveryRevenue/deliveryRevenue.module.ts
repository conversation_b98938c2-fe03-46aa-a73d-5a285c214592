import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { DeliveryRevenueController } from './deliveryRevenue.controller';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { SERVICE_NAME } from 'src/common/constants/serviceName.constant';
import { ConfigService } from '@nestjs/config';
import { DriverModule } from '../driver/driver.module';
@Module({
    imports: [DriverModule],
    controllers: [DeliveryRevenueController],
    providers: [
        {
            provide: SERVICE_NAME.INSIGHT_SERVICE,
            useFactory: (configService: ConfigService) => {
                const clientProxy = ClientProxyFactory.create({
                    transport: Transport.TCP,
                    options: {
                        host: configService.get('insightHost'),
                        port: configService.get('insightPort'),
                    },
                });

                setInterval(async () => {
                    try {
                        clientProxy.emit({ cmd: 'ping' }, '').subscribe({
                            next: () => {},
                            complete: () => {},
                            error: (err) => {
                                Logger.error(
                                    `Insight service is not available | message: ${err.message} | stack: ${err.stack}`,
                                );
                            },
                        });
                    } catch (error) {
                        Logger.error(
                            `Insight service is not available | message: ${error.message} | stack: ${error.stack}`,
                        );
                    }
                }, 60000);
                return clientProxy;
            },
            inject: [ConfigService],
        },
    ],
})
export class DeliveryRevenueModule {}
