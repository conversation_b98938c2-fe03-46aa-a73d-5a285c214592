import { Controller, Query, Get, Inject, Param, Body, Post } from '@nestjs/common';
import * as _ from 'lodash';
import { ClientProxy } from '@nestjs/microservices';
import { SERVICE_NAME } from 'src/common/constants/serviceName.constant';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { filterDeliveryRevenueDto } from './dto/filterDeliveryRevenue.dto';
import { IFilterDeliveryRevenuePayload } from './interfaces/filterDeliveryRevenuePayload.interface';
import { DriverService } from '../driver/services/driver.service';

import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('/delivery-revenues')
export class DeliveryRevenueController {
    constructor(
        @Inject(SERVICE_NAME.INSIGHT_SERVICE) private client: ClientProxy,
        private driverService: DriverService,
    ) {}

    @Get('/:driverId/list')
    async getList(
        @Query(new HttpValidationPipe()) query: filterDeliveryRevenueDto,
        @Param('driverId') driverId: number,
        @HeaderProvince() provinceId: string,
    ) {
        const { interval, startDate, endDate } = query;
        const driver = await this.driverService.getDriverByIdAndProvinceId(driverId, provinceId);

        const payload: IFilterDeliveryRevenuePayload = {
            provinceId: +provinceId,
            driverId: +driver.user_id,
            interval,
            startDate,
            endDate,
        };

        return this.client.send(
            {
                cmd: 'deliveryRevenue.findFromDatabase',
            },
            _.omitBy(payload, _.isNull),
        );
    }

    @Post('/aggregate/daily')
    async aggregateDailyNow(
        @HeaderProvince() provinceId: string,
        @Body() body: { fromDate?: string; toDate?: string },
    ) {
        const payload = {
            fromDate: body.fromDate,
            toDate: body.toDate,
            provinceId: +provinceId,
        };

        return this.client.send(
            {
                cmd: 'deliveryRevenue.aggregateDailyNow',
            },
            _.omitBy(payload, _.isNull),
        );
    }

    @Post('/aggregate/weekly')
    async aggregateWeeklyNow(
        @HeaderProvince() provinceId: string,
        @Body() body: { fromDate?: string; toDate?: string },
    ) {
        const payload = {
            fromDate: body.fromDate,
            toDate: body.toDate,
            provinceId: +provinceId,
        };

        return this.client.send(
            {
                cmd: 'deliveryRevenue.aggregateWeeklyNow',
            },
            _.omitBy(payload, _.isNull),
        );
    }
}
