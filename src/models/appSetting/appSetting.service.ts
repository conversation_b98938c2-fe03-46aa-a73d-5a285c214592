import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { Request } from 'express';
import * as _ from 'lodash';
import { PROVINCE_HEADER, TinyInt } from 'src/common/constants';
import { EAdminEmployeeActivityAction, EAdminEmployeeActivityType } from 'src/entities/adminUserActivity.entity';
import {
    AppSetting,
    EAppSettingKey,
    IAppSetting,
    IAutoAssignedOrder,
    IDriverAppVersion,
    IDriverWalletConfig,
    IFoodCatalogAlert,
    IGroupOrderFeatureConfiguration,
    IHolidayBonus,
    ILargeOrderBonus,
    ILongDistanceOrderBonusConf,
    IPriorityTimeOrderBonusConf,
    IRaiseRaccoonProgram,
    IRestaurantCodeConfig,
    IShiftWork,
    IShipperPoint,
    ISystemMaintenance,
    ITipsConf,
    ITradeDiscountBonus,
    IUserOrderInvoiceConfig,
    IVietmapConfig,
    IVillBikeSettingFee,
    IVillCarSettingFee,
    IVillExpressSettingFee,
    IVillfoodSettingFee,
    IWeekShipperTimeKeeping,
    IWeekShipperTimeKeepingV2,
    IWorkTime,
} from 'src/entities/appSetting.entity';
import { OrderType } from 'src/entities/order.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { IsNull, Not } from 'typeorm';
import { UserActivityService } from '../adminUserActivity/userActivity.service';
import { UpdateDeliveryFeeDto, VillFoodSettingFees } from './app-setting.dto';
import { getSubSettingsBySubProvinceIdDto } from './dto';
import { VillCarSettingFee } from './models';
import { DriverTimeKeepingSetting, DriverWeekTimeKeepingSettingV2 } from './models/DriverWeekTimeKeepingSetting.model';
import { VillBikeSettingFeesV2 } from './models/VillBikeSettingFeesV2.model';
import { VillExpressSettingFeesV2 } from './models/VillExpressSettingFeesV2.model';
import { VillFoodSettingFeesV2 } from './models/VillFoodSettingFeesV2.model';
import { DriverTradeDiscountSetting } from './models/driverTradeDiscountSetting.model';
import { IDriverVAT, IRestaurantTAX } from './types/vat.interface';
import { Logger } from '@nestjs/common';

@Injectable()
export class AppSettingService {
    private readonly logger = new Logger(AppSettingService.name);

    constructor(
        private userActivityService: UserActivityService, // private readonly appSettingJobTask: AppSettingJobTask,
    ) {}

    private parseSetting(key: string, value: string) {
        let parseValue = null;
        switch (key) {
            case EAppSettingKey.DRIVER_ORDER_NOTIFICATION_CONF:
            case EAppSettingKey.DRIVER_RANKING_SETTING: {
                try {
                    parseValue = JSON.parse(value);
                } catch (error) {
                    parseValue = {};
                }
                break;
            }
            case EAppSettingKey.FEESHIP_RAINNY:
            case EAppSettingKey.FEESHIP_LESS_THAN_OR_EQUAL_2KM:
            case EAppSettingKey.MAX_TRANSPORT_ORDER_SETTING:
            case EAppSettingKey.FEESHIP_PER_1KM: {
                parseValue = _.toNumber(value);
                break;
            }
            case EAppSettingKey.FREESHIP_3KM:
            case EAppSettingKey.ENABLE_APP_ORDER:
            case EAppSettingKey.ENABLE_NEWBIE_COUPON:
                parseValue = Boolean(_.toNumber(value));
                break;
            case EAppSettingKey.VILLEXPRESS_SETTING_FEES:
            case EAppSettingKey.VILLBIKE_SETTING_FEES:
            case EAppSettingKey.VILLFOOD_SETTING_FEES:
            case EAppSettingKey.WORK_TIME:
            case EAppSettingKey.RAISE_RACCOON_PROGRAM:
            case EAppSettingKey.TRADE_DISCOUNT_BONUS:
            case EAppSettingKey.SYSTEM_MAINTENANCE:
            case EAppSettingKey.AUTO_ORDER_ASSIGNED_SETTING:
            case EAppSettingKey.TIPS_CONF:
            case EAppSettingKey.FOOD_CATALOG_ALERT:
            case EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF:
            case EAppSettingKey.ORDER_POINT:
            case EAppSettingKey.LARGE_ORDER_BONUS_CONF:
            case EAppSettingKey.LONG_DISTANCE_ORDER_BONUS_CONF:
            case EAppSettingKey.DRIVER_APP_VERSION:
            case EAppSettingKey.CLIENT_APP_VERSION:
            case EAppSettingKey.DRIVER_WALLET_CONF:
            case EAppSettingKey.PRIORITY_TIMES_ORDER_BONUS_CONF:
            case EAppSettingKey.SHIFT_WORK:
            case EAppSettingKey.HOLIDAY_ORDER_BONUS:
            case EAppSettingKey.COLLECTION_BROADCAST_NOTI:
            case EAppSettingKey.DRIVER_TRADE_DISCOUNT_PERCENT:
            case EAppSettingKey.CLIENT_MAP_CONF:
            case EAppSettingKey.TRADE_DISCOUNT_DEFAULT:
            case EAppSettingKey.VILLCAR_SETTING_FEES:
            case EAppSettingKey.VILL_FOOD_SETTING_FEES_V2:
            case EAppSettingKey.VILL_BIKE_SETTING_FEES_V2:
            case EAppSettingKey.VILL_EXPRESS_SETTING_FEES_V2:
            case EAppSettingKey.FOOD_DESCRIPTION_BY_CHATGPT:
            case EAppSettingKey.WORK_TIMES:
            case EAppSettingKey.CLIENT_FOOD_DESCRIPTION_BY_CHATGPT:
            case EAppSettingKey.MERCHANT_APP_ALERT:
            case EAppSettingKey.DRIVER_TRADE_DISCOUNT_SETTING:
            case EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF_V2:
            case EAppSettingKey.order_point_template:
            case EAppSettingKey.RESTAURANT_TRADE_DISCOUNT_DEFAULT:
            case EAppSettingKey.DRIVER_HOLIDAY_BONUS:
            case EAppSettingKey.DRIVER_WALLET_WORK_TIMES:
            case EAppSettingKey.DRIVER_BONUS_TIME:
            case EAppSettingKey.SOLD_OUT_UNTIL:
            case EAppSettingKey.RESTAURANT_TAX_SETTING:
            case EAppSettingKey.DRIVER_WALLET_PROVIDER:
            case EAppSettingKey.USER_ORDER_INVOICE_CONFIG:
            case EAppSettingKey.VIETMAP_CONFIG:
            case EAppSettingKey.CLIENT_ADDRESS_CONFIG:
                try {
                    parseValue = JSON.parse(value);
                } catch (error) {
                    parseValue = {};
                }
                break;
            case EAppSettingKey.GROUP_ORDER_FEATURE_CONF: {
                try {
                    parseValue = JSON.parse(value);
                } catch (error) {
                    parseValue = {};
                }
                break;
            }
            default:
                parseValue = value;
        }
        return parseValue;
    }

    private parseUpdateDataToString(value: any) {
        let parsedVal: string;
        if (_.isString(value)) {
            parsedVal = value;
        } else {
            if (_.isObject(value)) {
                parsedVal = JSON.stringify(value);
            } else if (_.isNumber(value) || _.isBoolean(value)) {
                parsedVal = value.toString();
            }
        }
        return parsedVal;
    }

    async list(ctx: Request) {
        const settings = await DatabaseService.getRepository(AppSetting, ctx).find({
            where: {
                api: 1,
                province_id: IsNull(),
            },
        });
        return settings.map(({ key, value }) => ({
            key,
            value: this.parseSetting(key, value),
        }));
    }

    async getSettingCodeByProvinceId<T>(key: string, provinceId: string): Promise<T> {
        if (!provinceId || !key) {
            return null;
        }
        const setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
            .createQueryBuilder('setting')
            .where(`setting.key = '${key}'`)
            .andWhere(`( setting.province_id IS NULL OR setting.province_id = ${provinceId} )`)
            .getOne();

        return this.parseSetting(key, setting?.value);
    }

    async getSettingCodeByProvinceIdAndSubProvinceId<T>(
        key: string,
        provinceId: string,
        subProvinceId: number = null,
    ): Promise<T> {
        if (!provinceId || !key) {
            return null;
        }
        let setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
            .createQueryBuilder('setting')
            .where(`setting.key = '${key}'`)
            .andWhere(`( setting.province_id IS NULL OR setting.province_id = ${subProvinceId} )`)
            .getOne();
        if (_.isEmpty(setting)) {
            setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
                .createQueryBuilder('setting')
                .where(`setting.key = '${key}'`)
                .andWhere(`( setting.province_id IS NULL OR setting.province_id = ${provinceId} )`)
                .getOne();
        }

        return this.parseSetting(key, setting?.value);
    }

    async getById(ctx: Request, id: number): Promise<AppSetting> {
        return await DatabaseService.getRepository(AppSetting, ctx).findOne({ where: { id } });
    }

    async delete(ctx: Request, id: number): Promise<any> {
        const item = await DatabaseService.getRepository(AppSetting, ctx).findOne({ where: { id } });
        if (!item) throw new NotFoundException();

        return await DatabaseService.getRepository(AppSetting, ctx).delete({ id: item.id });
    }

    async getSubSettingsByKey(provinceId: string, subProvinceId: string, key: string) {
        const setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
            .createQueryBuilder('app_setting')
            .where('app_setting.key = :key', { key })
            .andWhere('(app_setting.province_id = :province_id OR app_setting.province_id IS NULL)', {
                province_id: subProvinceId,
            })
            .getOne();

        if (!setting) {
            throw new NotFoundException();
        }

        const value = this.parseSetting(setting.key, setting.value);

        return {
            key,
            value,
        };
    }

    async updateByKey(ctx: Request, key: EAppSettingKey, value: any) {
        const parsedVal = this.parseUpdateDataToString(value);
        const setting = await this.getByKey(ctx, key);

        const updateResult = await DatabaseService.getRepository(AppSetting, ctx)
            .createQueryBuilder()
            .update()
            .set({
                value: parsedVal,
            })
            .where('key = :key', { key })
            .andWhere('province_id IS NULL')
            .execute();

        this.userActivityService.createEmployeeActivity(
            EAdminEmployeeActivityType.SETTING_APP_SETTING,
            EAdminEmployeeActivityAction.UPDATE,
            { key, value: setting },
            { key, value: await this.getByKey(ctx, key) },
            ctx.get(PROVINCE_HEADER),
            ctx['user']?.id,
        );
        return updateResult;
    }

    async getByKey(ctx: Request, key: EAppSettingKey) {
        let setting = await DatabaseService.getRepository(AppSetting, ctx).findOne({ where: { key } });
        if (!setting) {
            setting = await DatabaseService.getRepository(AppSetting, ctx).save({
                key,
                value: '',
                api: 1,
            });
        }
        return this.parseSetting(setting.key, setting.value);
    }

    async getOneByKeyAndProvinceId<T>(key: EAppSettingKey, provinceId: string): Promise<T | null> {
        if (!provinceId || !key) {
            return null;
        }
        const setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId).findOne({
            where: { key },
        });

        if (!setting) {
            return null;
        }

        return this.parseSetting(key, setting?.value);
    }

    async getShiftWorkSetting(provinceId: string): Promise<IShiftWork | null> {
        const setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId).findOne({
            where: {
                key: EAppSettingKey.SHIFT_WORK,
            },
        });
        if (setting) {
            return this.parseSetting(EAppSettingKey.SHIFT_WORK, setting.value);
        } else {
            return null;
        }
    }

    async getSubSettingsBySubProvinceId(ctx: Request, { subProvinceId }: getSubSettingsBySubProvinceIdDto) {
        const settings = await DatabaseService.getRepository(AppSetting, ctx).find({
            where: {
                api: 1,
                province_id: subProvinceId,
            },
        });
        return settings.map(({ key, value }) => ({
            key,
            value: this.parseSetting(key, value),
        }));
    }

    async updateSubSettingByKey(ctx: Request, key: EAppSettingKey, value: any, subProvinceId: number) {
        const parsedVal = this.parseUpdateDataToString(value);

        const setting = await DatabaseService.getRepository(AppSetting, ctx).findOne({
            where: { key, province_id: subProvinceId },
        });
        if (!setting) {
            return DatabaseService.getRepository(AppSetting, ctx).insert({
                key,
                value: parsedVal,
                province_id: subProvinceId,
                api: 1,
            });
        }

        return DatabaseService.getRepository(AppSetting, ctx)
            .createQueryBuilder()
            .update()
            .set({
                value: parsedVal,
            })
            .where('key = :key', { key })
            .andWhere('province_id = :province_id', { province_id: subProvinceId })
            .execute();
    }

    async getVillCarSettingFee(provinceId: string): Promise<VillCarSettingFee> {
        const setting = await this.getSettingCodeByProvinceId<IVillCarSettingFee>(
            EAppSettingKey.VILLCAR_SETTING_FEES,
            provinceId,
        );
        return new VillCarSettingFee(setting);
    }

    // async getDriverTradeDiscountPercent(provinceId: string): Promise<DriverTradeDiscountPercentSetting> {
    //     const setting = await this.getSettingCodeByProvinceId(EAppSettingKey.DRIVER_TRADE_DISCOUNT_PERCENT, provinceId);
    //     return new DriverTradeDiscountPercentSetting(setting);
    // }

    // async getDriverTradeDiscountPercentSettingByType(provinceId: string, type: OrderType) {
    //     const setting = await this.getDriverTradeDiscountPercent(provinceId);
    //     switch (type) {
    //         case OrderType.VILLFOOD:
    //             return setting?.VILLFOOD;
    //         case OrderType.VILLEXPRESS:
    //             return setting?.VILLEXPRESS;
    //         case OrderType.VILLBIKE:
    //             return setting?.VILLBIKE;
    //         case OrderType.VILLCAR:
    //             return setting?.VILLCAR;
    //         default:
    //             return null;
    //     }
    // }

    async getDriverTradeDiscountSettingByDistance(
        provinceId: string,
        subProvinceId?: number,
    ): Promise<DriverTradeDiscountSetting> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId(
            EAppSettingKey.DRIVER_TRADE_DISCOUNT_SETTING,
            provinceId,
            subProvinceId,
        );
        return new DriverTradeDiscountSetting(setting);
    }

    async getDeliveryFee(key: EAppSettingKey, provinceId: string, subProvinceId: string) {
        const keyAccepted = [
            EAppSettingKey.VILL_FOOD_SETTING_FEES_V2,
            EAppSettingKey.VILL_BIKE_SETTING_FEES_V2,
            EAppSettingKey.VILL_EXPRESS_SETTING_FEES_V2,
        ];

        if (!keyAccepted.includes(key)) {
            throw new BadRequestException('Không hỗ trợ key này');
        }

        return await this.getDeliverySettingFee(provinceId, subProvinceId, key);
    }

    async getDeliverySettingFee(provinceId: string, subProvinceId: string, key: EAppSettingKey) {
        const VILL_SETTING_FEES_V2: VillFoodSettingFees = await this.getSettingCodeByProvinceId<VillFoodSettingFees>(
            key,
            subProvinceId,
        );
        const isEmpty = Object.keys(VILL_SETTING_FEES_V2).length == 0;

        if (!VILL_SETTING_FEES_V2 || isEmpty) {
            throw new NotFoundException('Không tìm thấy cài đặt ' + key);
        }
        return VILL_SETTING_FEES_V2;
    }

    async updateDeliveryFee(provinceId: string, updateDeliveryFeeDto: UpdateDeliveryFeeDto, authorId: number) {
        const { key, data, subProvinceId } = updateDeliveryFeeDto;
        this.validateDeliveryFee(data);

        return await this.updateSettingFee(provinceId, key, data, subProvinceId.toString(), authorId);
    }

    async updateSettingFee(
        provinceId: string,
        key: EAppSettingKey,
        data: VillFoodSettingFees,
        subProvinceId: string,
        authorId: number,
    ) {
        const VILL_SETTING_FEES_V2 = await this.getSettingCodeByProvinceId<VillFoodSettingFees>(key, provinceId);

        if (!VILL_SETTING_FEES_V2) {
            throw new NotFoundException('Không tìm thấy cài đặt ' + key);
        }

        const updateResult = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
            .createQueryBuilder()
            .update()
            .set({
                value: JSON.stringify(data),
            })
            .where('key = :key', { key })
            .andWhere('province_id = :province_id', { province_id: subProvinceId })
            .execute();

        if (updateResult.affected === 0) {
            throw new BadRequestException('Cập nhật không thành công');
        }

        this.userActivityService.createEmployeeActivity(
            EAdminEmployeeActivityType.SETTING_APP_SETTING,
            EAdminEmployeeActivityAction.UPDATE,
            { key, value: VILL_SETTING_FEES_V2 },
            { key, value: data },
            provinceId,
            authorId,
        );

        /*  const [oldTimesFee, newTimesFee, mode] = [
            VILL_SETTING_FEES_V2.DELIVERY_FEE?.TIMES_FEE || [],
            data.DELIVERY_FEE?.TIMES_FEE || [],
            data.DELIVERY_FEE_MODE,
        ];
        this.appSettingJobTask.updateJobAutoSetDeliveryFeeInTimeMode(
            subProvinceId,
            key,
            oldTimesFee,
            newTimesFee,
            mode,
        ); */

        return data;
    }

    private validateDeliveryFee(obj: VillFoodSettingFees) {
        if (typeof obj !== 'object' || obj === null) {
            throw new BadRequestException('Dữ liệu không hợp lệ');
        }
        if (typeof obj.SERVICE_FEE !== 'number') {
            throw new BadRequestException('SERVICE_FEE không hợp lệ');
        }

        const subOrder = obj.SUB_ORDER;
        if (subOrder) {
            if (typeof subOrder !== 'object') {
                throw new BadRequestException('SUB_ORDER không hợp lệ');
            }
            if (typeof subOrder.TOLERANCE !== 'number') {
                throw new BadRequestException('SUB_ORDER.TOLERANCE không hợp lệ');
            }
            if (typeof subOrder.DEFAULT_FEE !== 'number') {
                throw new BadRequestException('SUB_ORDER.DEFAULT_FEE không hợp lệ');
            }
            if (typeof subOrder.MAX_EXTRA_DISTANCE !== 'number') {
                throw new BadRequestException('SUB_ORDER.MAX_EXTRA_DISTANCE không hợp lệ');
            }
            if (typeof subOrder.EXTEND_DISTANCE_FEE_PER_KM !== 'number') {
                throw new BadRequestException('SUB_ORDER.EXTEND_DISTANCE_FEE_PER_KM không hợp lệ');
            }
        }

        const deliveryFee = obj.DELIVERY_FEE;
        if (typeof deliveryFee !== 'object' || deliveryFee === null) {
            throw new BadRequestException('DELIVERY_FEE không hợp lệ');
        }

        const defaultFee = deliveryFee.DEFAULT_FEE;
        if (typeof defaultFee !== 'object' || defaultFee === null) {
            throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE không hợp lệ');
        }
        if (!Array.isArray(defaultFee.MIN_DISTANCE_FEES)) {
            throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES không hợp lệ');
        }
        for (const fee of defaultFee.MIN_DISTANCE_FEES) {
            if (typeof fee !== 'object' || fee === null) {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES item không hợp lệ');
            }
            if (typeof fee.DISTANCE !== 'number') {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.DISTANCE không hợp lệ');
            }
            if (typeof fee.FEE !== 'number') {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.FEE không hợp lệ');
            }
            if (typeof fee.SUBSIDY !== 'number') {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.SUBSIDY không hợp lệ');
            }
        }

        if (!Array.isArray(defaultFee.EXTRA_DISTANCE_FEES)) {
            throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.EXTRA_DISTANCE_FEES không hợp lệ');
        }
        for (const fee of defaultFee.EXTRA_DISTANCE_FEES) {
            if (typeof fee !== 'object' || fee === null) {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.EXTRA_DISTANCE_FEES item không hợp lệ');
            }
            if (typeof fee.DISTANCE_FROM !== 'number') {
                throw new BadRequestException(
                    'DELIVERY_FEE.DEFAULT_FEE.EXTRA_DISTANCE_FEES.DISTANCE_FROM không hợp lệ',
                );
            }
            if (typeof fee.FEE !== 'number') {
                throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.EXTRA_DISTANCE_FEES.FEE không hợp lệ');
            }
        }

        if (!Array.isArray(deliveryFee.TIMES_FEE)) {
            throw new BadRequestException('DELIVERY_FEE.TIMES_FEE không hợp lệ');
        }
        for (const timeFee of deliveryFee.TIMES_FEE) {
            if (typeof timeFee !== 'object' || timeFee === null) {
                throw new BadRequestException('DELIVERY_FEE.TIMES_FEE item không hợp lệ');
            }
            if (typeof timeFee.START_TIME !== 'string') {
                throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.START_TIME không hợp lệ');
            }
            if (typeof timeFee.END_TIME !== 'string') {
                throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.END_TIME không hợp lệ');
            }
            if (!Array.isArray(timeFee.MIN_DISTANCE_FEES)) {
                throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.MIN_DISTANCE_FEES không hợp lệ');
            }
            for (const fee of defaultFee.MIN_DISTANCE_FEES) {
                if (typeof fee !== 'object' || fee === null) {
                    throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES item không hợp lệ');
                }
                if (typeof fee.DISTANCE !== 'number') {
                    throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.DISTANCE không hợp lệ');
                }
                if (typeof fee.FEE !== 'number') {
                    throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.FEE không hợp lệ');
                }
                if (typeof fee.SUBSIDY !== 'number') {
                    throw new BadRequestException('DELIVERY_FEE.DEFAULT_FEE.MIN_DISTANCE_FEES.SUBSIDY không hợp lệ');
                }
            }

            if (!Array.isArray(timeFee.EXTRA_DISTANCE_FEES)) {
                throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.EXTRA_DISTANCE_FEES không hợp lệ');
            }
            for (const fee of timeFee.EXTRA_DISTANCE_FEES) {
                if (typeof fee !== 'object' || fee === null) {
                    throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.EXTRA_DISTANCE_FEES item không hợp lệ');
                }
                if (typeof fee.DISTANCE_FROM !== 'number') {
                    throw new BadRequestException(
                        'DELIVERY_FEE.TIMES_FEE.EXTRA_DISTANCE_FEES.DISTANCE_FROM không hợp lệ',
                    );
                }
                if (typeof fee.FEE !== 'number') {
                    throw new BadRequestException('DELIVERY_FEE.TIMES_FEE.EXTRA_DISTANCE_FEES.FEE không hợp lệ');
                }
            }
        }

        if (obj.SERVICE_FEES_BY_TOTAL_PRICE) {
            if (!Array.isArray(obj.SERVICE_FEES_BY_TOTAL_PRICE)) {
                throw new BadRequestException('SERVICE_FEES_BY_TOTAL_PRICE không hợp lệ');
            }
            for (const serviceFee of obj.SERVICE_FEES_BY_TOTAL_PRICE) {
                if (typeof serviceFee !== 'object' || serviceFee === null) {
                    throw new BadRequestException('SERVICE_FEES_BY_TOTAL_PRICE item không hợp lệ');
                }
                if (typeof serviceFee.TOTAL_PRICE !== 'number') {
                    throw new BadRequestException('SERVICE_FEES_BY_TOTAL_PRICE.TOTAL_PRICE không hợp lệ');
                }
                if (typeof serviceFee.FEE !== 'number') {
                    throw new BadRequestException('SERVICE_FEES_BY_TOTAL_PRICE.FEE không hợp lệ');
                }
            }
        }
    }

    async getDriverTimeKeepingSetting(provinceId: string): Promise<DriverTimeKeepingSetting | null> {
        const setting = await this.getOneByKeyAndProvinceId<IWeekShipperTimeKeeping>(
            EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF,
            provinceId,
        );

        if (setting) {
            return new DriverTimeKeepingSetting(setting.enable, setting.daily_minimum_required_orders, setting.levels);
        } else {
            return null;
        }
    }

    async getDriverTimeKeepingSettingV2(
        provinceId: string,
        subProvinceId: number | null,
    ): Promise<DriverWeekTimeKeepingSettingV2 | null> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<IWeekShipperTimeKeepingV2>(
            EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF_V2,
            provinceId,
            subProvinceId,
        );

        if (setting) {
            return new DriverWeekTimeKeepingSettingV2(
                setting.enable,
                setting.daily_minimum_required_orders,
                setting.daily_minimum_required_car_orders,
                setting.bonus_configs,
            );
        } else {
            return null;
        }
    }

    async getDriverTimeKeepingSettingV2BySettingProvinceId(
        settingProvinceId: number,
        provinceId: string,
    ): Promise<DriverWeekTimeKeepingSettingV2 | null> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<IWeekShipperTimeKeepingV2>(
            EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF_V2,
            provinceId,
            settingProvinceId,
        );

        if (setting) {
            return new DriverWeekTimeKeepingSettingV2(
                setting.enable,
                setting.daily_minimum_required_orders,
                setting.daily_minimum_required_car_orders,
                setting.bonus_configs,
            );
        } else {
            return null;
        }
    }

    // async getSettingCodeByProvinceIdAndSubProvinceId<T>(
    //     key: string,
    //     provinceId: string,
    //     subProvinceId: number = null,
    // ): Promise<T> {
    //     const finalProvinceId = subProvinceId || provinceId;
    //     let data = await this.cacheService.get(CACHE_KEYS.SETTING_BY_CODE(key), finalProvinceId);
    //     if (_.isEmpty(data)) {
    //         let setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
    //             .createQueryBuilder('setting')
    //             .where(`setting.key = '${key}'`)
    //             .andWhere(`( setting.province_id IS NULL OR setting.province_id = ${subProvinceId} )`)
    //             .getOne();
    //         if (_.isEmpty(setting)) {
    //             setting = await DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId)
    //                 .createQueryBuilder('setting')
    //                 .where(`setting.key = '${key}'`)
    //                 .andWhere(`( setting.province_id IS NULL OR setting.province_id = ${provinceId} )`)
    //                 .getOne();
    //         }
    //         if (!_.isEmpty(setting)) {
    //             data = this.parseSetting(setting.key, setting.value);
    //             this.cacheService.set(CACHE_KEYS.SETTING_BY_CODE(key), finalProvinceId, data);
    //             return data;
    //         } else {
    //             return null;
    //         }
    //     } else {
    //         return data;
    //     }
    // }

    async getVillFoodDeliveryFeeSettingV2(provinceId: string, subProvinceId: number): Promise<VillFoodSettingFeesV2> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<VillFoodSettingFeesV2>(
            EAppSettingKey.VILL_FOOD_SETTING_FEES_V2,
            provinceId,
            subProvinceId,
        );

        try {
            return new VillFoodSettingFeesV2(setting);
        } catch (error) {
            throw new InternalServerErrorException('Đã có lỗi xảy ra!');
        }
    }

    async getVillExpressDeliveryFeeSettingV2(
        provinceId: string,
        subProvinceId: number,
    ): Promise<VillExpressSettingFeesV2> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<VillExpressSettingFeesV2>(
            EAppSettingKey.VILL_EXPRESS_SETTING_FEES_V2,
            provinceId,
            subProvinceId,
        );

        try {
            return new VillExpressSettingFeesV2(setting);
        } catch (error) {
            throw new InternalServerErrorException('Đã có lỗi xảy ra!');
        }
    }

    async getVillBikeDeliveryFeeSettingV2(provinceId: string, subProvinceId: number): Promise<VillBikeSettingFeesV2> {
        const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<VillBikeSettingFeesV2>(
            EAppSettingKey.VILL_BIKE_SETTING_FEES_V2,
            provinceId,
            subProvinceId,
        );

        try {
            return new VillBikeSettingFeesV2(setting);
        } catch (error) {
            throw new InternalServerErrorException('Đã có lỗi xảy ra!');
        }
    }

    async getVatPercentSettingByType(provinceId: string, type: OrderType, subProvinceId?: number): Promise<IDriverVAT> {
        const vatPercent: IDriverVAT = {
            VAT_PERCENT: 0,
            SURCHARGE_VAT_PERCENT: 0,
            PERSONAL_INCOME_TAX_PERCENT: 0,
        };
        switch (type) {
            case OrderType.VILLFOOD:
                // eslint-disable-next-line no-case-declarations
                const settingVillFood = await this.getVillFoodDeliveryFeeSettingV2(provinceId, subProvinceId || null);
                vatPercent.VAT_PERCENT = settingVillFood?.VAT_PERCENT || 0;
                vatPercent.SURCHARGE_VAT_PERCENT = settingVillFood?.SURCHARGE_VAT_PERCENT || 0;
                vatPercent.PERSONAL_INCOME_TAX_PERCENT = settingVillFood?.PERSONAL_INCOME_TAX_PERCENT || 0;
                return vatPercent;
            case OrderType.VILLEXPRESS:
                // eslint-disable-next-line no-case-declarations
                const settingVillExpress = await this.getVillExpressDeliveryFeeSettingV2(
                    provinceId,
                    subProvinceId || null,
                );
                vatPercent.VAT_PERCENT = settingVillExpress?.VAT_PERCENT || 0;
                vatPercent.SURCHARGE_VAT_PERCENT = settingVillExpress?.SURCHARGE_VAT_PERCENT || 0;
                vatPercent.PERSONAL_INCOME_TAX_PERCENT = settingVillExpress?.PERSONAL_INCOME_TAX_PERCENT || 0;
                return vatPercent;
            case OrderType.VILLBIKE:
                // eslint-disable-next-line no-case-declarations
                const settingVillBike = await this.getVillBikeDeliveryFeeSettingV2(provinceId, subProvinceId || null);
                vatPercent.VAT_PERCENT = settingVillBike?.VAT_PERCENT || 0;
                vatPercent.SURCHARGE_VAT_PERCENT = settingVillBike?.SURCHARGE_VAT_PERCENT || 0;
                vatPercent.PERSONAL_INCOME_TAX_PERCENT = settingVillBike?.PERSONAL_INCOME_TAX_PERCENT || 0;
                return vatPercent;
            case OrderType.VILLCAR:
                // eslint-disable-next-line no-case-declarations
                const settingVillCar = await this.getVillCarSettingFee(provinceId);
                vatPercent.VAT_PERCENT = settingVillCar?.VAT_PERCENT || 0;
                vatPercent.SURCHARGE_VAT_PERCENT = settingVillCar?.SURCHARGE_VAT_PERCENT || 0;
                vatPercent.PERSONAL_INCOME_TAX_PERCENT = settingVillCar?.PERSONAL_INCOME_TAX_PERCENT || 0;
                return vatPercent;
            default:
                return vatPercent;
        }
    }

    async getTradeDiscount(provinceId: string) {
        return await this.getOneByKeyAndProvinceId<number>(
            EAppSettingKey.RESTAURANT_TRADE_DISCOUNT_DEFAULT,
            provinceId,
        );
    }

    async getRestaurantTaxSetting(provinceId: string): Promise<IRestaurantTAX[]> {
        const setting = await this.getOneByKeyAndProvinceId<IRestaurantTAX[]>(
            EAppSettingKey.RESTAURANT_TAX_SETTING,
            provinceId,
        );
        if (setting) {
            return setting;
        } else {
            return [];
        }
    }
    
    async getDriverRankingEnabled(provinceId: string, subProvinceId?: number): Promise<boolean> {
        try {
            const setting = await this.getSettingCodeByProvinceIdAndSubProvinceId<{ enabled: boolean }>(
                EAppSettingKey.DRIVER_RANKING_SETTING,
                provinceId,
                subProvinceId,
            );
            return setting?.enabled || false;
        } catch (error) {
            this.logger.error(`Error getting driver ranking enabled status for province ${provinceId}:`, error);
            return false;
        }
    }

    async getUserOrderInvoiceConfig(provinceId: string): Promise<IUserOrderInvoiceConfig> {
        const setting = await this.getSettingCodeByProvinceId<IUserOrderInvoiceConfig>(
            EAppSettingKey.USER_ORDER_INVOICE_CONFIG,
            provinceId,
        );

        if (!setting) {
            return {
                enable: TinyInt.YES,
                enable_company_profile_management: TinyInt.YES,
                enable_invoice_option_at_checkout: TinyInt.YES,
                checkout_invoice_description: '',
                profile_invoice_description: '',
                split_invoice: TinyInt.NO,
                delivery_vat_percent: 8,
                service_vat_percent: 8,
                surcharge_vat_percent: 8,
            };
        }

        return setting;
    }
}
