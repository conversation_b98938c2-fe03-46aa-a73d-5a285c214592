import { isNil } from 'lodash';
export class DriverTradeDiscountPercentSetting {
    // eslint-disable-next-line @typescript-eslint/no-inferrable-types
    VILLFOOD: number = 20;
    // eslint-disable-next-line @typescript-eslint/no-inferrable-types
    VILLBIKE: number = 20;
    // eslint-disable-next-line @typescript-eslint/no-inferrable-types
    VILLEXPRESS = 20;
    // eslint-disable-next-line @typescript-eslint/no-inferrable-types
    VILLCAR: number = 10;
    constructor(partial: Partial<DriverTradeDiscountPercentSetting>) {
        if (!isNil(partial.VILLFOOD)) {
            this.VILLFOOD = partial.VILLFOOD;
        }
        if (!isNil(partial.VILLBIKE)) {
            this.VILLBIKE = partial.VILLBIKE;
        }
        if (!isNil(partial.VILLEXPRESS)) {
            this.VILLEXPRESS = partial.VILLEXPRESS;
        }
        if (!isNil(partial.VILLCAR)) {
            this.VILLCAR = partial.VILLCAR;
        }
    }
}
