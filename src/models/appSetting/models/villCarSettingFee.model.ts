import { isNil } from 'lodash';
import { IVillCarSettingFee, IVillCarTypeFee } from 'src/entities/appSetting.entity';

export class VillCarSettingFee {
    CAR_TYPE_LIST: IVillCarTypeFee[];
    SERVICE_FEE: number;
    VAT_PERCENT: number;
    PERSONAL_INCOME_TAX_PERCENT: number;
    SURCHARGE_VAT_PERCENT: number;

    constructor(settings: IVillCarSettingFee) {
        this.CAR_TYPE_LIST = settings.CAR_TYPE_LIST;
        this.SERVICE_FEE = settings.SERVICE_FEE;
        this.VAT_PERCENT = settings.VAT_PERCENT;
        this.PERSONAL_INCOME_TAX_PERCENT = settings.PERSONAL_INCOME_TAX_PERCENT;
        this.SURCHARGE_VAT_PERCENT = settings.SURCHARGE_VAT_PERCENT;
    }

    calcPersonalTax(income: number): number {
        if (
            isNil(this.PERSONAL_INCOME_TAX_PERCENT) ||
            this.PERSONAL_INCOME_TAX_PERCENT >= 100 ||
            this.PERSONAL_INCOME_TAX_PERCENT < 0
        ) {
            return 0;
        }
        return Math.round(income * (this.PERSONAL_INCOME_TAX_PERCENT / 100));
    }
}
