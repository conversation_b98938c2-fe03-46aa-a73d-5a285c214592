import * as moment from 'moment';
import { DefaultDeliveryFee, DeliveryFee, VillSettingFees } from './VillFoodSettingFeesV2.model';

export class VillFoodSettingFees extends VillSettingFees {}

export class VillExpressSettingFeesV2 {
    private SERVICE_FEE: number;
    private DELIVERY_FEE: DeliveryFee;
    VAT_PERCENT: number;
    SURCHARGE_VAT_PERCENT: number;
    PERSONAL_INCOME_TAX_PERCENT: number;

    constructor(partial: Partial<VillExpressSettingFeesV2>) {
        Object.assign(this, partial);
    }

    private getFee(timeCalc: moment.Moment): DefaultDeliveryFee {
        const timeBasedFees = this.DELIVERY_FEE.TIMES_FEE;
        // sort by time from max to min
        timeBasedFees.sort((a, b) => {
            const timeA = moment(a.START_TIME, 'HH:mm');
            const timeB = moment(b.START_TIME, 'HH:mm');
            return timeB.diff(timeA);
        });

        const timeBasedFee = timeBasedFees.find((timeFee) => {
            const startTime = moment(timeFee.START_TIME, 'HH:mm');
            const endTime = moment(timeFee.END_TIME, 'HH:mm');
            return timeCalc.isSameOrAfter(startTime) && timeCalc.isSameOrBefore(endTime);
        });

        if (timeBasedFee) {
            return {
                MIN_DISTANCE_FEES: timeBasedFee.MIN_DISTANCE_FEES,
                EXTRA_DISTANCE_FEES: timeBasedFee.EXTRA_DISTANCE_FEES,
            };
        } else {
            return this.DELIVERY_FEE.DEFAULT_FEE;
        }
    }

    calcDeliveryFee(distance: number, timeCalc: moment.Moment): number {
        let deliveryFeeValue = 11;
        const timeBasedFee = this.getFee(timeCalc);
        const minDistanceFees = timeBasedFee.MIN_DISTANCE_FEES.sort((a, b) => a.DISTANCE - b.DISTANCE);
        const minDistanceFee = minDistanceFees.find((min) => distance <= min.DISTANCE);

        if (minDistanceFee) {
            deliveryFeeValue = minDistanceFee.FEE;
        } else {
            const extraDistanceFees = timeBasedFee.EXTRA_DISTANCE_FEES.sort(
                (a, b) => b.DISTANCE_FROM - a.DISTANCE_FROM,
            );
            const extraDistanceFee = extraDistanceFees.find((extra) => distance >= extra.DISTANCE_FROM);
            deliveryFeeValue = extraDistanceFee.FEE * distance;
        }

        return Math.round(deliveryFeeValue);
    }

    calcServiceFee(): number {
        return Math.round(this.SERVICE_FEE);
    }

    calcDeliverySubsidy(distance: number): number {
        const timeCalc = moment().utc().add(7, 'hours');
        let deliverySubsidy = 0;
        const timeBasedFee = this.getFee(timeCalc);
        const minDistanceFees = timeBasedFee.MIN_DISTANCE_FEES.sort((a, b) => a.DISTANCE - b.DISTANCE);
        const minDistanceFee = minDistanceFees.find((min) => distance <= min.DISTANCE);

        if (minDistanceFee) {
            deliverySubsidy = minDistanceFee.SUBSIDY;
        }

        return deliverySubsidy;
    }
}
