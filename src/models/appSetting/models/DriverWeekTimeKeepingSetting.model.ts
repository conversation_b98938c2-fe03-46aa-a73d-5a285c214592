import { Injectable } from '@nestjs/common';
import { WeekShipperTimeKeeping } from 'src/entities/weekShipperTimeKeeping.entity';

@Injectable()
export class DriverTimeKeepingSetting {
    private enable = false;
    private daily_minimum_required_orders = 0;
    private levels: Record<string, number> = {};
    constructor(enable: boolean, daily_minimum_required_orders: number, levels: Record<string, number>) {
        this.enable = enable;
        this.daily_minimum_required_orders = daily_minimum_required_orders;
        this.levels = levels;
    }

    get isEnabled(): boolean {
        return this.enable;
    }
}

export class DriverWeekTimeKeepingSettingV2 {
    private enable = false;
    daily_minimum_required_orders = 0;
    daily_minimum_required_car_orders = 0;
    private bonus_configs: IDriverTimeKeepingBonusConfig[] = [];
    constructor(
        enable: boolean,
        daily_minimum_required_orders: number,
        daily_minimum_required_car_orders: number,
        bonus_configs: IDriverTimeKeepingBonusConfig[],
    ) {
        this.enable = enable;
        this.daily_minimum_required_orders = daily_minimum_required_orders;
        daily_minimum_required_car_orders = daily_minimum_required_car_orders;
        this.bonus_configs = bonus_configs;
    }

    get isEnabled(): boolean {
        return this.enable;
    }

    actualBonus(timeKeeping: WeekShipperTimeKeeping) {
        const activeDays = [
            timeKeeping.is_mon_active,
            timeKeeping.is_tue_active,
            timeKeeping.is_wed_active,
            timeKeeping.is_thu_active,
            timeKeeping.is_fri_active,
            timeKeeping.is_sat_active,
            timeKeeping.is_sun_active,
        ];

        const activeDayCount = activeDays.reduce((count, day) => count + (day === 1 ? 1 : 0), 0);
        if (activeDayCount === 0) return 0;

        const config = this.bonus_configs.find((level) => level.active_days === activeDayCount);
        if (!config) return 0;

        if (timeKeeping.is_sat_active && timeKeeping.is_sun_active) {
            return config.sat_sun_active ?? config.value;
        }

        return timeKeeping.is_sat_active
            ? config.sat_active ?? config.value
            : timeKeeping.is_sun_active
            ? config.sun_active ?? config.value
            : config.value;
    }
}

export interface IDriverTimeKeepingBonusConfig {
    active_days: number;
    value: number;
    sat_active: number | null;
    sun_active: number | null;
    sat_sun_active: number | null;
}
