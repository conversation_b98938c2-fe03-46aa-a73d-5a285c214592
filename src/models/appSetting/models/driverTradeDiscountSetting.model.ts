import { isNil } from 'lodash';
import { OrderType } from 'src/entities/order.entity';

export interface DriverTradeDiscountSettingByType {
    trade_discount: {
        distance_from: number;
        value: number;
    }[];
}

const defaultValues = {
    trade_discount: [
        {
            distance_from: 0,
            value: 20,
        },
    ],
};

export class DriverTradeDiscountSetting {
    VILLFOOD: DriverTradeDiscountSettingByType = defaultValues;
    VILLBIKE: DriverTradeDiscountSettingByType = defaultValues;
    VILLEXPRESS: DriverTradeDiscountSettingByType = defaultValues;
    VILLCAR: DriverTradeDiscountSettingByType = {
        trade_discount: [{ distance_from: 0, value: 10 }],
    };
    constructor(partial: Partial<DriverTradeDiscountSetting>) {
        console.log('partial', partial);
        if (!isNil(partial?.VILLFOOD) && this.validateTradeDiscountSettingByType(partial.VILLFOOD)) {
            this.VILLFOOD = partial.VILLFOOD;
        }
        if (!isNil(partial?.VILLBIKE) && this.validateTradeDiscountSettingByType(partial.VILLBIKE)) {
            this.VILLBIKE = partial.VILLBIKE;
        }
        if (!isNil(partial?.VILLEXPRESS) && this.validateTradeDiscountSettingByType(partial.VILLEXPRESS)) {
            this.VILLEXPRESS = partial.VILLEXPRESS;
        }
        if (!isNil(partial?.VILLCAR) && this.validateTradeDiscountSettingByType(partial.VILLCAR)) {
            this.VILLCAR = partial.VILLCAR;
        }
    }

    private validateTradeDiscountSettingByType(tradeDiscountSetting: DriverTradeDiscountSettingByType): boolean {
        return (
            Array.isArray(tradeDiscountSetting.trade_discount) &&
            tradeDiscountSetting.trade_discount.every(
                (discount) => typeof discount.distance_from === 'number' && typeof discount.value === 'number',
            )
        );
    }

    getTradeDiscountPercentByOrderTypeAndDistance(type: OrderType, distance: number): number {
        const tradeDiscountSetting = this.getTradeDiscountSettingByOrderType(type);

        console.log('tradeDiscountSetting', tradeDiscountSetting);

        let tradeDiscountValue = defaultValues.trade_discount[0].value;
        for (const tradeDiscount of tradeDiscountSetting.trade_discount) {
            if (tradeDiscount.distance_from <= distance) {
                tradeDiscountValue = tradeDiscount.value;
            }
        }
        return tradeDiscountValue;
    }

    private getTradeDiscountSettingByOrderType(type: OrderType): DriverTradeDiscountSettingByType {
        switch (type) {
            case OrderType.VILLFOOD:
                return this.VILLFOOD;
            case OrderType.VILLBIKE:
                return this.VILLBIKE;
            case OrderType.VILLEXPRESS:
                return this.VILLEXPRESS;
            case OrderType.VILLCAR:
                return this.VILLCAR;
            default:
                return this.VILLFOOD;
        }
    }
}
