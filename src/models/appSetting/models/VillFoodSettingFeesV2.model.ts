import * as moment from 'moment';

class SubOrderFees {
    TOLERANCE: number;
    DEFAULT_FEE: number;
    MAX_EXTRA_DISTANCE: number;
    EXTEND_DISTANCE_FEE_PER_KM: number;
}

export class DistanceFee {
    DISTANCE: number;
    FEE: number;
    SUBSIDY: number;
}

export class ExtraDistanceFee {
    DISTANCE_FROM: number;
    FEE: number;
}

export class DefaultDeliveryFee {
    MIN_DISTANCE_FEES: DistanceFee[];
    EXTRA_DISTANCE_FEES: ExtraDistanceFee[];
}

export class TimeBasedDeliveryFee {
    START_TIME: string;
    END_TIME: string;
    MIN_DISTANCE_FEES: DistanceFee[];
    EXTRA_DISTANCE_FEES: ExtraDistanceFee[];
}

export class DeliveryFee {
    DEFAULT_FEE: DefaultDeliveryFee;
    TIMES_FEE: TimeBasedDeliveryFee[];
}

export class ServiceFeeByTotalPrice {
    TOTAL_PRICE: number;
    FEE: number;
    SERVICE_FEE: number;
    SMALL_ORDER_FEE: number;
}

export class VillSettingFees {
    SERVICE_FEE: number;
    DELIVERY_FEE: DeliveryFee;
}

export class VillFoodSettingFeesV2 {
    private SERVICE_FEE: number;
    private SUB_ORDER: SubOrderFees;
    private DELIVERY_FEE: DeliveryFee;
    private SERVICE_FEES_BY_TOTAL_PRICE: ServiceFeeByTotalPrice[];
    VAT_PERCENT: number;
    SURCHARGE_VAT_PERCENT: number;
    PERSONAL_INCOME_TAX_PERCENT: number;

    constructor(partial: Partial<VillFoodSettingFeesV2>) {
        Object.assign(this, partial);
    }

    private getFee(timeCalc: moment.Moment): DefaultDeliveryFee {
        const feesBasedTime = this.DELIVERY_FEE.TIMES_FEE;
        // sort by time from max to min
        feesBasedTime.sort((a, b) => {
            const timeA = moment(a.START_TIME, 'HH:mm');
            const timeB = moment(b.START_TIME, 'HH:mm');
            return timeB.diff(timeA);
        });
        // get the first time fee that timeCalc is same or after START_TIME and before END_TIME
        const feeBasedTime = feesBasedTime.find((timeFee) => {
            const startTime = moment(timeFee.START_TIME, 'HH:mm');
            const endTime = moment(timeFee.END_TIME, 'HH:mm');
            return timeCalc.isSameOrAfter(startTime) && timeCalc.isSameOrBefore(endTime);
        });

        if (feeBasedTime) {
            return {
                MIN_DISTANCE_FEES: feeBasedTime.MIN_DISTANCE_FEES,
                EXTRA_DISTANCE_FEES: feeBasedTime.EXTRA_DISTANCE_FEES,
            };
        } else {
            return this.DELIVERY_FEE.DEFAULT_FEE;
        }
    }

    calcDeliveryFee(distance: number, timeCalc: moment.Moment): number {
        let deliveryFeeValue = 11;
        const timeBasedFee = this.getFee(timeCalc);
        const minDistanceFees = timeBasedFee.MIN_DISTANCE_FEES.sort((a, b) => a.DISTANCE - b.DISTANCE);
        const minDistanceFee = minDistanceFees.find((min) => distance <= min.DISTANCE);

        if (minDistanceFee) {
            deliveryFeeValue = minDistanceFee.FEE;
        } else {
            const extraDistanceFees = timeBasedFee.EXTRA_DISTANCE_FEES.sort(
                (a, b) => b.DISTANCE_FROM - a.DISTANCE_FROM,
            );
            const extraDistanceFee = extraDistanceFees.find((extra) => distance >= extra.DISTANCE_FROM);
            deliveryFeeValue = extraDistanceFee.FEE * distance;
        }

        return Math.round(deliveryFeeValue);
    }

    calcSubOrderDeliveryFee(extendedDistance: number, sameDirection: boolean): number {
        let deliveryFeeValue = this.SUB_ORDER.DEFAULT_FEE;
        if (!sameDirection) {
            const deliveryFee = this.SUB_ORDER.EXTEND_DISTANCE_FEE_PER_KM * extendedDistance;
            if (deliveryFeeValue < deliveryFee) {
                deliveryFeeValue = deliveryFee;
            }
        }
        return Math.round(deliveryFeeValue);
    }

    calcServiceFee(totalPrice: number): number {
        const serviceFeesConf = this.SERVICE_FEES_BY_TOTAL_PRICE;
        // sort by total price from max to min
        serviceFeesConf.sort((a, b) => b.TOTAL_PRICE - a.TOTAL_PRICE);
        const serviceFeeConf = serviceFeesConf.find((conf) => totalPrice >= conf.TOTAL_PRICE);

        return serviceFeeConf ? serviceFeeConf.FEE : this.SERVICE_FEE;
    }

    calcDeliverySubsidy(distance: number): number {
        const timeCalc = moment().utc().add(7, 'hours');
        let deliverySubsidy = 0;
        const timeBasedFee = this.getFee(timeCalc);
        const minDistanceFees = timeBasedFee.MIN_DISTANCE_FEES.sort((a, b) => a.DISTANCE - b.DISTANCE);
        const minDistanceFee = minDistanceFees.find((min) => distance <= min.DISTANCE);

        if (minDistanceFee) {
            deliverySubsidy = minDistanceFee.SUBSIDY;
        }

        return deliverySubsidy;
    }
}
