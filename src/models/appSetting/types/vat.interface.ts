import { ETaxableCategory } from "src/entities/restaurant.entity";

export interface IDriverVAT {
    VAT_PERCENT: number;
    SURCHARGE_VAT_PERCENT: number;
    PERSONAL_INCOME_TAX_PERCENT: number;
}

export class IRestaurantTAX {
    vat_percent: number;
    enable: boolean;
    personal_income_tax_percent: number;
    type: ETaxableCategory;

    constructor(partial: Partial<IRestaurantTAX>) {
        Object.assign(this, partial);
    }
}