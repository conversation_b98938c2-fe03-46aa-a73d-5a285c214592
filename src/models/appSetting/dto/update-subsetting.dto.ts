import { IsEnum, IsNotEmpty } from 'class-validator';
import { EAppSettingKey } from 'src/entities/appSetting.entity';
import { BaseAppSettingDto } from './baseAppSetting.dto';

export class UpdateSubSettingDto extends BaseAppSettingDto {
    @IsNotEmpty()
    @IsEnum(EAppSettingKey)
    key: EAppSettingKey;

    @IsNotEmpty()
    value: any;
}

export class UpdateDefaultTaxDto extends BaseAppSettingDto {
    @IsNotEmpty()
    value: number;
}
