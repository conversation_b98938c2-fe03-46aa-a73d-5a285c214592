import { Type } from 'class-transformer';
import { IsNotEmpty, IsEnum, ValidateIf, IsString, IsBoolean, IsNumber, Min, IsInt } from 'class-validator';
import { isNil } from 'lodash';
import { IDriverWalletProvider, IDriverWalletProviderPopupConfig } from 'src/entities/appSetting.entity';
import { EDriverWalletProvider } from 'src/entities/driver.entity';

export class UpdateDriverWalletProviderPopupConfig implements IDriverWalletProviderPopupConfig {
    @IsBoolean()
    enabled: boolean;

    @IsString()
    title: string;

    @IsString()
    description: string;

    @IsString()
    primary_button_text: string;

    @IsString()
    secondary_button_text: string;

    @IsBoolean()
    dismissible: boolean;

    @IsNumber()
    display_interval: number;
}

export class UpdateDriverWalletProviderDto implements IDriverWalletProvider {
    @IsInt()
    @Min(0)
    warning_threshold: number;

    @IsInt()
    @Min(0)
    critical_threshold: number;

    @IsNotEmpty()
    @IsEnum(EDriverWalletProvider)
    provider: EDriverWalletProvider;

    @ValidateIf((obj, value) => !isNil(value))
    @IsString()
    warning_text: string;

    @IsBoolean()
    enabled: boolean;

    @ValidateIf((obj, value) => !isNil(value))
    @Type(() => UpdateDriverWalletProviderPopupConfig)
    link_popup_config: IDriverWalletProviderPopupConfig;
}
