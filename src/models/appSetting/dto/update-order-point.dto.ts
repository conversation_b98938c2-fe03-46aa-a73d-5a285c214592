import {
    IsNotEmpty,
    IsNumberString,
    Validate,
    ValidationArguments,
    ValidatorConstraintInterface,
} from 'class-validator';
import * as _ from 'lodash';

class ShipperPointExchangeSetting implements ValidatorConstraintInterface {
    validate(value: { [key: string]: number }) {
        if (!_.isObject(value)) return false;
        let invalidNumber = Object.keys(value).some((key) => !_.isNumber(+key));
        if (invalidNumber) return false;
        invalidNumber = Object.values(value).some((val) => !_.isNumber(val));
        if (invalidNumber) return false;
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return 'Order point exchange không đúng định dạng!';
    }
}

export class ShipperPointSettingDto {
    @IsNotEmpty()
    @IsNumberString()
    food1: string;

    @IsNotEmpty()
    @IsNumberString()
    food2: string;

    @IsNotEmpty()
    @IsNumberString()
    express: string;

    @IsNotEmpty()
    @IsNumberString()
    bike: string;

    @IsNotEmpty()
    @Validate(ShipperPointExchangeSetting)
    exchange: { [key: string]: string };
}
