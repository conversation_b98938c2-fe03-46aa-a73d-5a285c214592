import { Type } from 'class-transformer';
import { ArrayMinSize, IsBoolean, IsInt, IsNotEmpty, IsNumber, Min, ValidateNested } from 'class-validator';


export class UpdateLargeOrderMonthlyRewardConfigDto {
    @IsInt()
    @Min(0)
    total_order: number;

    @IsInt()
    @Min(0)
    reward: number;
}

export class UpdateLargeOrderBonusLevelDto {
    @IsNumber({ allowInfinity: false, allowNaN: false })
    @Min(0)
    min_order_price: number;

    @IsNumber({ allowInfinity: false, allowNaN: false })
    @Min(0)
    value: number;

    @ValidateNested()
    @Type(() => UpdateLargeOrderMonthlyRewardConfigDto)
    monthly_reward_configs: UpdateLargeOrderMonthlyRewardConfigDto;
}

export class UpdateLargeOrderBonusDto {
    @IsBoolean()
    enable: boolean;

    // @IsNotEmpty()
    // @IsNumber()
    // @Min(0)
    // @Max(100)
    // bonus_percentage: number;

    // @IsNotEmpty()
    // @IsNumber()
    // @Min(0)
    // minimum_required_sub_orders: number;

    // @IsNotEmpty()
    // @IsNumber()
    // @Min(0)
    // bonus_amount: number;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @ArrayMinSize(0)
    @Type(() => UpdateLargeOrderBonusLevelDto)
    exchange: UpdateLargeOrderBonusLevelDto[];
}
