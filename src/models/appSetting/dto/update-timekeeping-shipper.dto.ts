import {
    IsBoolean,
    IsNotEmpty,
    IsNumber,
    IsObject,
    Min,
    Validate,
    ValidationArguments,
    ValidatorConstraintInterface,
} from 'class-validator';
import * as _ from 'lodash';

class ShipperPointExchangeSetting implements ValidatorConstraintInterface {
    validate(value: { [key: string]: number }) {
        if (!_.isObject(value)) return false;
        let invalidNumber = Object.keys(value).some((key) => !_.isNumber(+key));
        if (invalidNumber) return false;
        invalidNumber = Object.values(value).some((val) => !_.isNumber(val));
        if (invalidNumber) return false;
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return 'invalid level format!';
    }
}
export class UpdateTimeKeepingShipperDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    daily_minimum_required_orders: number;

    // @IsNotEmpty()
    // @IsNumber()
    // @Min(0)
    // daily_minimum_required_car_orders: number;

    @IsNotEmpty()
    @IsBoolean()
    enable: boolean;

    @IsObject()
    @Validate(ShipperPointExchangeSetting)
    levels: {
        [key: string]: number;
    };
}

class BonusConfigDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    active_days: number;

    @IsNumber()
    @Min(0)
    sun_active: number | null;

    @IsNumber()
    @Min(0)
    value: number;

    @IsNumber()
    @Min(0)
    sat_sun_active: number | null;

    @IsNumber()
    @Min(0)
    sat_active: number | null;
}

export class UpdateTimeKeepingShipperV2Dto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    daily_minimum_required_orders: number;

    // @IsNotEmpty()
    // @IsNumber()
    // @Min(0)
    // daily_minimum_required_car_orders: number;

    @IsNotEmpty()
    @IsBoolean()
    enable: boolean;

    @IsObject()
    @Validate(BonusConfigDto, { each: true })
    bonus_configs: BonusConfigDto[];
}
