import { Transform, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, ValidateNested } from 'class-validator';
import * as _ from 'lodash';
import { EVehicleTypeCode } from 'src/entities/vehicleType.entity';

export class UpdateVillCarFeeDto {
    @IsNotEmpty()
    @ValidateNested()
    @Type(() => CarTypeListDto)
    CAR_TYPE_LIST: CarTypeListDto[];

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    SERVICE_FEE: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    VAT_PERCENT: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    PERSONAL_INCOME_TAX_PERCENT: number;
    
}

export class CarDefaultFeeDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    distanceTo: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    fee: number;
}

export class CarTypeListDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    id: number;

    @IsNotEmpty()
    @IsEnum(EVehicleTypeCode)
    code: EVehicleTypeCode;

    @IsNotEmpty()
    @ValidateNested()
    @Type(() => CarDefaultFeeDto)
    defaultFee: CarDefaultFeeDto;

    @IsNotEmpty()
    @ValidateNested()
    @Type(() => CarDeliveryFeeDto)
    deliveryFees: CarDeliveryFeeDto[];
}

export class CarDeliveryFeeDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    distanceFrom: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    fee: number;
}


