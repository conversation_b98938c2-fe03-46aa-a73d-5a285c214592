import { Transform } from 'class-transformer';
import { IsEnum, IsIn, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import * as _ from 'lodash';
import { TinyInt } from 'src/common/constants';

export class UpdateClientMapConfDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    is_remove_tracking_path: number;

    @Transform(({ value }) => _.toString(value))
    @IsString()
    map_style: string;

    @IsNotEmpty()
    @IsEnum(TinyInt)
    enable_geocoding: TinyInt;
}

// interface IDriverNotiSetting {
//     driver_end_of_day_order_conf: {
//         enable:  boolean;
//         time: string;
//     },
//     driver_is_far_from_restaurant: {
//         enable: boolean
//     };
//     // con_order_types: OrderType[];
// }
