import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';

export class UpdateVillBikeDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    LT_3KM_STANDARD_LEVEL_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    LT_3KM_PREMIUM_LEVEL_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GT_3KM_STANDARD_LEVEL_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GT_3KM_PREMIUM_LEVEL_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    SERVICE_FEE: number;
}
