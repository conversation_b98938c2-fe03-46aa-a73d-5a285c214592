import { Transform } from 'class-transformer';
import { IsIn, <PERSON>NotEmpty, IsNumber, IsString } from 'class-validator';
import * as _ from 'lodash';


export class LateOrderUncompletedDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @IsIn([0, 1])

    enable: number;

    @IsNotEmpty()
    @IsNumber()
    @Transform(({ value }) => _.toNumber(value))
    first_time: number;

    @IsNotEmpty()
    @IsNumber()
    @Transform(({ value }) => _.toNumber(value))
    repeat_time: number;

    @IsNotEmpty()
    @IsNumber()
    @Transform(({ value }) => _.toNumber(value))
    attempt: number;

}


export class DriverOutOfRangeDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @IsIn([0, 1])
    enable: number;

}

export class UpdateDriverOrderNotiConfDto {
    @IsNotEmpty()
    order_uncompleted: LateOrderUncompletedDto;

    @IsNotEmpty()
    pickup_out_of_range: DriverOutOfRangeDto;

    @IsNotEmpty()
    drop_off_out_of_range: DriverOutOfRangeDto;


}

