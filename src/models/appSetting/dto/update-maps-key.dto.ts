import { IsIn, <PERSON>NotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';

export class UpdateMapsKeyDto {
    @IsOptional()
    @ValidateIf((value) => value != null)
    @IsString()
    google_maps_key: string = null;

    @IsOptional()
    @ValidateIf((value) => value != null)
    @IsString()
    google_maps_key2: string = null;

    @IsOptional()
    @ValidateIf((value) => value != null)
    @IsString()
    google_maps_key3: string = null;

    @IsOptional()
    @ValidateIf((value) => value != null)
    @IsString()
    mapbox_api_key: string;

    @IsNotEmpty()
    @IsIn(['google', 'mapbox', 'manual'])
    distance_calc_tool_type: 'google' | 'mapbox' | 'manual';

    @IsNotEmpty()
    @IsIn(['google', 'tomtom', 'manual'])
    geocoding_tool_type: 'google' | 'tomtom' | 'manual';
}
