import { Type } from 'class-transformer';
import {
    IsBoolean,
    IsNotEmpty,
    IsNotEmptyObject,
    IsNumber,
    IsString,
    IsUrl,
    Min,
    ValidateNested,
} from 'class-validator';
import 'reflect-metadata';

export class AppVersionDto {
    @IsNotEmpty()
    @IsString()
    version: string;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    build: number;

    @IsBoolean()
    isForceUpdate: boolean;

    @IsUrl()
    link: string;
}

export class UpdateDriverAppVersion {
    @IsNotEmptyObject()
    @ValidateNested()
    @Type(() => AppVersionDto)
    android: AppVersionDto;

    @IsNotEmptyObject()
    @ValidateNested()
    @Type(() => AppVersionDto)
    ios: AppVersionDto;
}
