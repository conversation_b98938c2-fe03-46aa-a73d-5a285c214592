import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';

export class UpdateVillExpressFeeDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    DEFAULT_LT_3KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GT_3KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GTE_6KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    SERVICE_FEE: number;
}
