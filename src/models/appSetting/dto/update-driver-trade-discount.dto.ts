import { Transform } from 'class-transformer';
import { IsNotEmpty, IsN<PERSON>ber } from 'class-validator';
import * as _ from 'lodash';

export class UpdateDriverTradeDiscountDto {
    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    VILLFOOD: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    VILLBIKE: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    VILLEXPRESS: number;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    VILLCAR: number;
}
