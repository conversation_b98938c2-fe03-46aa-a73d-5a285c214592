import { IsBoolean, IsIn, <PERSON>NotEmpty, IsNumber, Min } from 'class-validator';

export class UpdateAutoAssignmentDto {
    @IsNotEmpty()
    @IsIn([0, 1])
    turn_on: number;

    @IsNotEmpty()
    @IsNumber()
    orders_assigned_max_count: number;

    @IsNotEmpty()
    @IsNumber()
    distance_radius: number;

    @IsNotEmpty()
    @IsNumber()
    max_manually_select_orders_count: number;

    @IsNotEmpty()
    @IsNumber()
    order_lifecycle_max_count: number;

    @IsNotEmpty()
    @IsBoolean()
    priority_assignment_by_rank_enable: boolean;

    @IsNotEmpty()
    @IsBoolean()
    priority_assignment_by_last_order_done_and_rank_enable: boolean;

    @IsNotEmpty()
    @IsBoolean()
    auto_cancel_order_enable: boolean;

    @IsNotEmpty()
    @IsNumber()
    max_order_expired_seconds: number;

    @IsNotEmpty()
    @IsBoolean()
    manual_selected_order_enabled: boolean;

    @IsNotEmpty()
    @IsNumber()
    max_auto_assigned_order_expired_seconds: number;

    @IsNotEmpty()
    @IsBoolean()
    canceling_order_confirmation_enable: boolean;

    @IsNotEmpty()
    @IsNumber()
    canceling_order_confirmation_minutes: number;

    @IsNotEmpty()
    @IsBoolean()
    enabled_first_job_assignment_priority_by_rank: boolean;

    @IsNotEmpty()
    @IsBoolean()
    enabled_job_rejection_discipline: boolean;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    max_bonus_orders_count: number;
}
