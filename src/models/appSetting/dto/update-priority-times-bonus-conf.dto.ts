import { IsBoolean, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { TinyInt } from 'src/common/constants';

export class UpdateProrityTimesBonusConfDto {
    @IsBoolean()
    enable: boolean;

    @IsNumber({ allowNaN: false, allowInfinity: false })
    @Min(0)
    priority_times: number;

    @IsNumber({ allowNaN: false, allowInfinity: false })
    @Min(0)
    con_min_order_subtotal: number;

    @IsNumber({ allowNaN: false, allowInfinity: false })
    @Min(0)
    con_min_distance: number;

    @IsIn([TinyInt.NO, TinyInt.YES])
    con_restaurant_long_preparing_time: TinyInt;
}
