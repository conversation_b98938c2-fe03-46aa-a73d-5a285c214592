import { IsBoolean, IsIn, <PERSON>Not<PERSON>mpty, IsN<PERSON>ber, IsString } from 'class-validator';

export class UpdateSpecialClientCouponDto {
    @IsNotEmpty()
    @IsIn([0, 1])
    enable: number;

    @IsNotEmpty()
    @IsNumber()
    point: number;

    @IsNotEmpty()
    @IsString()
    view_link_url: string;

    @IsNotEmpty()
    @IsNumber()
    maximum_coupons_client_can_take: number;

    @IsNotEmpty()
    @IsNumber()
    minimum_required_sub_total_order_price: number;
}
