import { Type } from 'class-transformer';
import { IsArray, IsObject, Length, MaxLength, ValidateNested } from 'class-validator';

/* [{"exchange":{"240":50,"360":75,"600":125,"820":195,"1010":255,"1200":335,"1392":415,"1510":435},"carExchange":{}},{"exchange":{"240":35,"360":50,"600":100,"820":170,"1010":230,"1200":310,"1392":390,"1510":420},"carExchange":{}},{"exchange":{},"carExchange":{}}] */

export class OrderPointTemplate {
    @IsObject()
    @Type(() => Object)
    exchange: Record<string, number>;

    @IsObject()
    @Type(() => Object)
    carExchange: Record<string, number>;
}

export class UpdateOrderPointTemplateDto {
    @IsArray()
    @ValidateNested({
        each: true,
    })
    @Type(() => OrderPointTemplate)
    templates: OrderPointTemplate[];
}
