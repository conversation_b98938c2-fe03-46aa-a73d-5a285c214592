import { IsNotEmpty, IsString } from 'class-validator';

export class UpdateAppNotiDto {
    @IsNotEmpty()
    @IsString()
    app_custom_content: string;

    @IsNotEmpty()
    @IsString()
    field_custom_2: string;

    @IsNotEmpty()
    @IsString()
    field_custom_1: string;

    @IsNotEmpty()
    @IsString()
    food_catalog_title_1: string;

    @IsNotEmpty()
    @IsString()
    food_catalog_title_2: string;

    @IsNotEmpty()
    @IsString()
    food_catalog_title_3: string;

    @IsNotEmpty()
    @IsString()
    food_catalog_title_4: string;

    @IsNotEmpty()
    @IsString()
    food_catalog_title_5: string;
}
