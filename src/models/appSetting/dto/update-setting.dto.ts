import { IsEnum, IsNotEmpty, IsNumberString, IsPhoneNumber } from 'class-validator';
import { EAppSettingKey } from 'src/entities/appSetting.entity';
import { BaseAppSettingDto } from './baseAppSetting.dto';

export class UpdateSettingDto {
    @IsNotEmpty()
    @IsEnum(EAppSettingKey)
    key: EAppSettingKey;

    @IsNotEmpty()
    value: any;
}
export class UpdateSupportContactDto {
    @IsNotEmpty()
    @IsNumberString()
    hotline: string;

    @IsNotEmpty()
    @IsNumberString()
    support_phone: string;
}
