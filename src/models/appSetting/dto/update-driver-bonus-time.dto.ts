import { Type } from 'class-transformer';
import { Is<PERSON>rray, <PERSON>In, IsNotEmpty, IsNumber, IsString, ValidateNested } from 'class-validator';

export class UpdateDriverBonusTimeDto {
    @ValidateNested({ each: true })
    @IsArray()
    @IsNotEmpty()
    @Type(() => TimeBonus)
    TIMES_BONUS: TimeBonus[];
}

export class TimeBonus {
    @IsNotEmpty()
    @IsIn([0, 1])
    @Type(() => Number)
    ENABLE: number;

    @IsNotEmpty()
    @IsString()
    START_TIME: string;

    @IsNotEmpty()
    @IsString()
    END_TIME: string;

    @ValidateNested({ each: true })
    @IsArray()
    @IsNotEmpty()
    @Type(() => BonusMilestone)
    BONUS_MILESTONE: BonusMilestone[];
}

export class BonusMilestone {
    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number)
    ORDER_NUMBER_COMPLETED: number;

    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number)
    BONUS: number;
}
