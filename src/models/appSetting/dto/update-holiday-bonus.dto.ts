import {
    <PERSON><PERSON><PERSON>y,
    IsBoolean,
    <PERSON>In,
    IsNotEmpty,
    <PERSON>N<PERSON>ber,
    IsOptional,
    IsString,
    Min,
    ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class DefaultBonus {
    @IsNotEmpty()
    @IsNumber({}, { message: 'BONUS must be a number' })
    @Min(0, { message: 'BONUS must be at least 0' })
    BONUS: number;

    @IsNotEmpty()
    @IsNumber({}, { message: 'MIN_SUB_TOTAL must be a number' })
    @Min(0, { message: 'MIN_SUB_TOTAL must be at least 0' })
    MIN_SUB_TOTAL: number;
}

class TimesBonus {
    @IsNotEmpty()
    @IsString()
    START_TIME: string;

    @IsNotEmpty()
    @IsString()
    END_TIME: string;

    @IsNumber()
    @Type(() => Number)
    BONUS: number;

    @IsNumber()
    @Type(() => Number)
    MIN_SUB_TOTAL: number;
}

class DatesBonus {
    @IsNumber()
    @IsIn([0, 1])
    @Type(() => Number)
    ENABLE: number;

    @IsNotEmpty()
    @IsString()
    START_DATE: string;

    @IsNotEmpty()
    @IsString()
    END_DATE: string;

    @ValidateNested()
    @Type(() => DefaultBonus)
    DEFAULT_BONUS: DefaultBonus;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TimesBonus)
    TIMES_BONUS: TimesBonus[];
}

class WeeklyBonus {
    @IsNumber()
    @IsIn([0, 1])
    @Type(() => Number)
    ENABLE: number;

    @IsNotEmpty()
    @IsNumber({}, { message: 'DAY_OF_WEEK must be a number' })
    @Min(0, { message: 'DAY_OF_WEEK must be between 0 and 6' })
    DAY_OF_WEEK: number;

    @ValidateNested()
    @Type(() => DefaultBonus)
    DEFAULT_BONUS: DefaultBonus;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TimesBonus)
    TIMES_BONUS: TimesBonus[];
}

export class UpdateDriverHolidayBonusDto {
    @IsNumber()
    @IsIn([0, 1])
    @Type(() => Number)
    ENABLE: number;

    @ValidateNested()
    @Type(() => DefaultBonus)
    DEFAULT_BONUS: DefaultBonus;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DatesBonus)
    @IsOptional()
    DATES_BONUS?: DatesBonus[];

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => WeeklyBonus)
    @IsOptional()
    WEEKLY_BONUS?: WeeklyBonus[];
}

export class UpdateHolidayBonusDto {
    @IsBoolean()
    enable: boolean;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    value: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    con_min_subtotal: number;

    // @IsIn([OrderType.VILLBIKE, OrderType.VILLEXPRESS, OrderType.VILLFOOD], { each: true })
    // @IsArray()
    // con_order_types: OrderType[];
}
