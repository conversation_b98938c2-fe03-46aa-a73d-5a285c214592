import { Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsIn,
    IsNotEmpty,
    IsNotEmptyObject,
    IsNumber,
    IsObject,
    IsString,
    Min,
    ValidateNested,
} from 'class-validator';

class ConditionDto {
    @IsNotEmpty()
    @IsString()
    @IsIn(['LT', 'GTE', 'LTE', 'EQUAL'])
    COND_LABEL: 'LT' | 'GTE' | 'LTE' | 'EQUAL';

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    TOTAL_PRICE: number;
}
class ServiceFeeConditionDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    SERVICE_FEE: number;

    @IsNotEmptyObject()
    @IsObject()
    @ValidateNested()
    @Type(() => ConditionDto)
    CONDITION: ConditionDto;
}
export class UpdateVillFoodFeeDto {
    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    DEFAULT_LT_3KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GT_3KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    GTE_6KM_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    DEFAULT_SUB_ORDER_DELIVERY_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    MAX_SUBORDER_EXTRA_DISTANCE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    SERVICE_FEE: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    SUBORDER_EXTENDED_DISTANCE_FEE_PER_KM: number;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @IsArray()
    @ArrayMinSize(0)
    @Type(() => ServiceFeeConditionDto)
    SERVICE_FEE_CONF: ServiceFeeConditionDto[];
}
