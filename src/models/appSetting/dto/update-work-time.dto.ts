import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON>y, IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';

export class UpdateWorkTimeDto {
    @IsNotEmpty()
    @IsNumber()
    OPEN: boolean;

    @IsNotEmpty()
    @IsNumber()
    CLOSE: boolean;
}

export class WorkTimeDto {
    @IsNotEmpty()
    @IsString()
    open_time: string;

    @IsNotEmpty()
    @IsString()
    close_time: string;
}

export class UpdateWorkTimesDto {
    @IsArray({ each: true })
    @IsNotEmpty()
    @Type(() => UpdateWorkTimeDto)
    work_times: UpdateWorkTimeDto[];
}
