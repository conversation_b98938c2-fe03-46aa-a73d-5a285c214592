// {"direct":{"0":10,"1":20},"week":{"0":10,"1":20},"month":{"0":10,"1":20},"dr_wallet":{"0":10,"1":20}

import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, Max, Min } from 'class-validator';

export class TradeDiscount {
    @IsNotEmpty()
    @Type(() => Number)
    @Min(0)
    @Max(100)
    0: number;

    @IsNotEmpty()
    @Type(() => Number)
    @Min(0)
    @Max(100)
    1: number;
}

export class RestaurantTradeDiscountDefault {
    @IsObject()
    @IsNotEmpty()
    @Type(() => TradeDiscount)
    direct: TradeDiscount;

    @IsObject()
    @IsNotEmpty()
    @Type(() => TradeDiscount)
    week: TradeDiscount;

    @IsObject()
    @IsNotEmpty()
    @Type(() => TradeDiscount)
    month: TradeDiscount;

    @IsObject()
    @IsNotEmpty()
    @Type(() => TradeDiscount)
    dr_wallet: TradeDiscount;
}
