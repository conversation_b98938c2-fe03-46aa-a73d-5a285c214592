import { IsBoolean, IsEnum, IsNumber, IsOptional, Min, ValidateIf } from 'class-validator';
import { OrderType } from 'src/entities/order.entity';

export class UpdateLongDistanceOrderBonusDto {
    @IsBoolean()
    enable: boolean;

    @IsNumber()
    @Min(0)
    min_distance: number;

    @IsNumber()
    @Min(0)
    bonus_per_km: number;

    @IsOptional()
    @ValidateIf(({ value }) => value != null)
    @IsEnum(OrderType)
    order_type: OrderType | null;
}
