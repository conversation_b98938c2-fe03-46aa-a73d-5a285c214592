import { Type } from 'class-transformer';
import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Min, ValidateNested } from 'class-validator';

export class CollectionBroadcastNotify {
    @IsNotEmpty()
    @IsNumber()
    collection_id: number;

    @IsNotEmpty()
    @IsString()
    message: string;

    @IsNotEmpty()
    @IsNumber()
    @Min(1)
    restaurant_id: number;
}
export class CollectionBroadcastNotifyDto extends CollectionBroadcastNotify {
    @IsNotEmpty()
    @IsNumber()
    provinceId: number;
}

export class UpdateCollectionBroadcastNotifiesDto {
    @IsOptional()
    @Type(() => CollectionBroadcastNotifyDto)
    @ValidateNested({ each: true })
    collectionBroadcastNotifies: CollectionBroadcastNotifyDto[];
}
