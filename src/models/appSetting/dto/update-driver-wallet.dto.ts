import { Type } from 'class-transformer';
import { IsBoolean, IsIn, IsNotEmpty, IsNumber, Min, ValidateNested } from 'class-validator';
import { TinyInt } from 'src/common/constants';
export class DriverWalletTransactionFeeValue {
    @IsBoolean()
    enabled: boolean;

    @IsNumber()
    @Min(0)
    value: number;
}

export class NinePayWalletPayoutFee extends DriverWalletTransactionFeeValue {
    @IsIn([TinyInt.NO, TinyInt.YES])
    npay_withdraw_full_on_set_default: TinyInt;
}

export class DriverWalletTransactionFee {
    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    EXCHANGE: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    DEPOSIT: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    WITHDRAW: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ORDER_DEPOSIT: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ORDER_WITHDRAW: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    BONUS: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    PUNISH: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ONEPAY_PAYCOLLECT: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ONEPAY_PAYOUT: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ONEPAY_PAYOUT_FAILURE: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ORDER_DEPOSIT_REVERSAL: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFeeValue)
    ORDER_WITHDRAW_REVERSAL: DriverWalletTransactionFeeValue;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => NinePayWalletPayoutFee)
    NINE_PAY_WALLET_PAYOUT: NinePayWalletPayoutFee;
}
export class DriverWalletFeeSetting {
    @IsNumber()
    @Min(0)
    default_value: number;

    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletTransactionFee)
    transaction_types: DriverWalletTransactionFee;
}
export class UpdateDriverWalletSetting {
    @IsNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => DriverWalletFeeSetting)
    fee: DriverWalletFeeSetting;
}
