import {
    BadRequestException,
    Body,
    Controller,
    Delete,
    Get,
    NotFoundException,
    Param,
    ParseIntPipe,
    Patch,
    Put,
    Query,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { isBoolean, validate, ValidationError, ValidatorOptions } from 'class-validator';
import { Request } from 'express';
import { each, isNumber, isString } from 'lodash';
import { PROVINCE_HEADER } from 'src/common/constants';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { IRequestUser, RequestUser } from 'src/common/decorators/user.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { EAppSettingKey } from 'src/entities/appSetting.entity';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { EventService } from 'src/events/event.service';
import { AppSettingJobService } from 'src/jobs/appSetting/appSettingJob.service';
import { CacheService } from 'src/providers/cache/cache.service';
import { AutoOrderAssignedMcService } from 'src/providers/microservices/autoOrderAssignedService/autoOrderAssignedMic.service';
import { UpdateDeliveryFeeDto } from './app-setting.dto';
import { AppSettingService } from './appSetting.service';
import {
    getSubSettingsBySubProvinceIdDto,
    ShipperPointSettingDto,
    UpdateAutoAssignmentDto,
    UpdateClientAddressConfigDto,
    UpdateDefaultTaxDto,
    UpdateDriverAppVersion,
    UpdateDriverHolidayBonusDto,
    UpdateDriverWalletProviderDto,
    UpdateDriverWalletSetting,
    UpdateFoodCatalogDto,
    UpdateHolidayBonusDto,
    UpdateLargeOrderBonusDto,
    UpdateLongDistanceOrderBonusDto,
    UpdateMaintenanceDto,
    UpdateProrityTimesBonusConfDto,
    UpdateSettingDto,
    UpdateSpecialClientCouponDto,
    UpdateSubSettingDto,
    UpdateSupportContactDto,
    UpdateTimeKeepingShipperDto,
    UpdateTipsDto,
    UpdateVietmapConfigDto,
    UpdateVillBikeDto,
    UpdateVillExpressFeeDto,
    UpdateVillFoodFeeDto,
    UpdateWorkTimeDto,
    UpdateWorkTimesDto,
} from './dto';
import { UpdateClientMapConfDto } from './dto/update-client-map-conf.dto';
import { CollectionBroadcastNotifyDto } from './dto/update-collection-broadcast-notify-setting.dto';
import { UpdateDriverOrderNotiConfDto } from './dto/update-driver-order-noti-conf.dto';
import { UpdateDriverTradeDiscountDto } from './dto/update-driver-trade-discount.dto';
import { UpdateFoodDescription } from './dto/update-food-description.dto';
import { UpdateGroupOrderFeatureDto } from './dto/update-group-order-feature.dto';
import { UpdateOrderPointTemplateDto } from './dto/update-order-point-template.dto';
import { RestaurantTradeDiscountDefault } from './dto/update-restaurant-trade-discount.dto';
import { UpdateSmsOtpPartnerSettingDto } from './dto/update-sms-otp-partner-setting.dto';
import { UpdateVillCarFeeDto } from './dto/update-villcar-fee.dto';
import { UpdateMerchantAppAlertDto } from './dto/updateMerchantAppAlert.dto';
import { UpdateDriverBonusTimeDto } from './dto/update-driver-bonus-time.dto';
import { UpdateSoldOutUtilDto } from './dto/update-sold-out-util.dto';
import { UpdateDriverRankingDto } from './dto/update-driver-ranking.dto';


@UseInterceptors(LoggingInterceptor)
@Controller('app-settings')
@UseGuards(AuthGuard)
export class AppSettingController {
    constructor(
        private readonly appSettingService: AppSettingService,
        private readonly cacheService: CacheService,
        private autoOrderAssignedMcService: AutoOrderAssignedMcService,
        private appSettingJobService: AppSettingJobService,
        private internalEvent: EventService,
    ) {}

    @Delete('cache')
    async resetCache() {
        await this.cacheService.plushDb();
        return false;
    }

    @Get()
    @RequirePermissions(
        PermissionsAccessAction.APP_SETTINGS_FIND_LIST,
        PermissionsAccessAction.APP_SETTINGS_FIND_ONE_SUPPORT_CONTACT,
    )
    async list(@Req() req: Request) {
        const result = await this.appSettingService.list(req);
        return result;
    }

    @Get('sub-settings')
    @RequirePermissions(
        PermissionsAccessAction.APP_SETTINGS_FIND_LIST,
        PermissionsAccessAction.APP_SETTINGS_FIND_ONE_DEFAULT_TAX,
    )
    async getSubSettingsBySubProvinceId(
        @Req() req: Request,
        @Query(new HttpValidationPipe()) query: getSubSettingsBySubProvinceIdDto,
    ) {
        const result = await this.appSettingService.getSubSettingsBySubProvinceId(req, query);
        return result;
    }

    @Get('sub-settings/key')
    @RequirePermissions(
        PermissionsAccessAction.APP_SETTINGS_FIND_LIST,
        PermissionsAccessAction.APP_SETTINGS_FIND_ONE_DEFAULT_TAX,
    )
    async getSubSettingsByKey(
        @Query('key') key: string,
        @Query('subProvinceId') subProvinceId: string,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.appSettingService.getSubSettingsByKey(provinceId, subProvinceId, key);
    }

    @Get('keys/:key')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_FIND_LIST)
    async getSetting(@Req() req: Request, @Param('key') key: EAppSettingKey) {
        return await this.appSettingService.getByKey(req, key);
    }

    @Get('trade-discount')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_GET_TRADE_DISCOUNT)
    async getTradeDiscount(@HeaderProvince() provinceId: string) {
        return await this.appSettingService.getTradeDiscount(provinceId);
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_FIND_ONE)
    async getById(@Param('id', new ParseIntPipe()) id: number, @Req() req: Request) {
        const result = await this.appSettingService.getById(req, id);
        if (!result) throw new NotFoundException('Setting not found');
        return result;
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_REMOVE)
    async deleteById(@Param('id', new ParseIntPipe()) id: number, @Req() req: Request) {
        return await this.appSettingService.delete(req, id);
    }

    @Put()
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_UPDATE)
    async update(@Body(new HttpValidationPipe()) { key, value }: UpdateSettingDto, @Req() req: Request) {
        await this.validateSettingRequest(key, value);
        await this.appSettingService.updateByKey(req, key, value);
        const settings = await this.appSettingService.list(req);

        // send new settings to auto assignment service
        if (key == EAppSettingKey.AUTO_ORDER_ASSIGNED_SETTING) {
            const autoAssignmentSetting = await this.appSettingService.getByKey(req, key);
            this.autoOrderAssignedMcService.sendAutoAssigmentSetting(autoAssignmentSetting, req.get(PROVINCE_HEADER));
        } else if (key == EAppSettingKey.SHIFT_WORK) {
            const shiftWorkSetting = await this.appSettingService.getByKey(req, key);
            this.autoOrderAssignedMcService.sendShiftWorkSetting(shiftWorkSetting, req.get(PROVINCE_HEADER));
        }

        if (
            key == EAppSettingKey.VILLBIKE_SETTING_FEES ||
            key == EAppSettingKey.VILLEXPRESS_SETTING_FEES ||
            key == EAppSettingKey.VILLFOOD_SETTING_FEES
        ) {
            this.appSettingJobService.checkFeeUpdateJobByProvinceId(req.get(PROVINCE_HEADER));
        } else if (key == EAppSettingKey.SHIFT_WORK) {
            const provinceId = req.get(PROVINCE_HEADER);
            const shiftWorkSetting = await this.appSettingService.getShiftWorkSetting(provinceId);
            if (shiftWorkSetting && shiftWorkSetting.enable) {
                this.internalEvent.shiftWorkSettingChange(shiftWorkSetting, provinceId);
            }
        } else if (key == EAppSettingKey.COLLECTION_AUTO_ACTIVE_CONF) {
            this.appSettingJobService.checkCollectionAutoActive(req.get(PROVINCE_HEADER));
        }

        return settings;
    }

    @Put('support-contact')
    @RequirePermissions(
        PermissionsAccessAction.APP_SETTINGS_UPDATE_SUPPORT_CONTACT,
        PermissionsAccessAction.APP_SETTINGS_UPDATE,
    )
    async updateSupportContact(
        @Body(new HttpValidationPipe()) { hotline, support_phone }: UpdateSupportContactDto,
        @Req() req: Request,
    ) {
        await this.appSettingService.updateByKey(req, EAppSettingKey.HOTLINE, hotline);
        await this.appSettingService.updateByKey(req, EAppSettingKey.SUPPORT_PHONE, support_phone);
        return await this.appSettingService.list(req);
    }

    @Put('sub-settings')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_UPDATE)
    async updateSubSettings(
        @Body(new HttpValidationPipe()) { key, value, provinceId }: UpdateSubSettingDto,
        @Req() req: Request,
    ) {
        await this.validateSettingRequest(key, value);
        await this.appSettingService.updateSubSettingByKey(req, key, value, provinceId);

        return await this.appSettingService.getSubSettingsBySubProvinceId(req, { subProvinceId: provinceId });
    }

    @Put('sub-settings/default-tax')
    @RequirePermissions(
        PermissionsAccessAction.APP_SETTINGS_UPDATE,
        PermissionsAccessAction.APP_SETTING_UPDATE_DEFAULT_TAX,
    )
    async updateSubSettingsByKey(
        @Body(new HttpValidationPipe()) { value, provinceId }: UpdateDefaultTaxDto,
        @Req() req: Request,
    ) {
        const key = EAppSettingKey.FEESHIP_RAINNY;
        await this.validateSettingRequest(EAppSettingKey.FEESHIP_RAINNY, value);
        await this.appSettingService.updateSubSettingByKey(req, key, value, provinceId);
        return await this.appSettingService.getSubSettingsBySubProvinceId(req, { subProvinceId: provinceId });
    }

    async validateSettingRequest(key: EAppSettingKey, value: any) {
        if (
            [
                EAppSettingKey.ENABLE_APP_ORDER,
                EAppSettingKey.FEESHIP_PER_1KM,
                EAppSettingKey.FREESHIP_3KM,
                EAppSettingKey.FEESHIP_LESS_THAN_OR_EQUAL_2KM,
                EAppSettingKey.FEESHIP_PER_1KM,
                EAppSettingKey.ENABLE_NEWBIE_COUPON,
                EAppSettingKey.APP_NOTI_CONTENT,
                EAppSettingKey.FOOD_CATALOG_TITLE_1,
                EAppSettingKey.FOOD_CATALOG_TITLE_2,
                EAppSettingKey.FOOD_CATALOG_TITLE_3,
                EAppSettingKey.FOOD_CATALOG_TITLE_4,
                EAppSettingKey.FOOD_CATALOG_TITLE_5,
                EAppSettingKey.GOOGLE_MAPS_KEY,
                EAppSettingKey.GOOGLE_MAPS_KEY_2,
                EAppSettingKey.GOOGLE_MAPS_KEY_3,
                EAppSettingKey.MAPS_BOX_KEY,
                EAppSettingKey.DISTANCE_CALC_TOOL_TYPE,
                EAppSettingKey.GEOCODING_TOOL_TYPE,
                EAppSettingKey.APP_VERSION,
                EAppSettingKey.ENABLE_VERSION,
                EAppSettingKey.FEESHIP_RAINNY,
                EAppSettingKey.MOMO_FEE,
                EAppSettingKey.ZALOPAY_FEE,
                EAppSettingKey.AUTO_SUGGEST_TOOL,
                EAppSettingKey.DRIVER_MAX_DEVICE_LOGIN,
                EAppSettingKey.DRIVER_ORDER_NOTIFICATION_CONF,
                // EAppSettingKey.DRIVER_WALLET_PROVIDER,
                EAppSettingKey.DRIVER_RANKING_SETTING,
                EAppSettingKey.VIETMAP_CONFIG,
            ].includes(key)
        ) {
            // if (EAppSettingKey.ENABLE_APP_ORDER == key && !isBoolean(value))
            //     throw new BadRequestException('required boolean type!');
            if (
                [EAppSettingKey.FREESHIP_3KM, EAppSettingKey.IS_REMOVE_TRACKING_PATH].includes(key) &&
                !isBoolean(value)
            )
                throw new BadRequestException('required boolean type!');

            // Remove boolean validation for DRIVER_RANKING_SETTING to allow object validation like DRIVER_ORDER_NOTIFICATION_CONF

            if (
                [
                    EAppSettingKey.FEESHIP_PER_1KM,
                    EAppSettingKey.FEESHIP_LESS_THAN_OR_EQUAL_2KM,
                    EAppSettingKey.FEESHIP_RAINNY,
                    EAppSettingKey.MOMO_FEE,
                    EAppSettingKey.ZALOPAY_FEE,
                    EAppSettingKey.DRIVER_MAX_DEVICE_LOGIN,
                ].includes(key) &&
                !isNumber(value)
            ) {
                throw new BadRequestException('required number type!');
            }
            if (
                [
                    EAppSettingKey.FEESHIP_PER_1KM,
                    EAppSettingKey.ENABLE_NEWBIE_COUPON,
                    EAppSettingKey.APP_NOTI_CONTENT,
                    EAppSettingKey.FOOD_CATALOG_TITLE_1,
                    EAppSettingKey.FOOD_CATALOG_TITLE_2,
                    EAppSettingKey.FOOD_CATALOG_TITLE_3,
                    EAppSettingKey.FOOD_CATALOG_TITLE_4,
                    EAppSettingKey.FOOD_CATALOG_TITLE_5,
                    EAppSettingKey.GOOGLE_MAPS_KEY,
                    EAppSettingKey.GOOGLE_MAPS_KEY_2,
                    EAppSettingKey.GOOGLE_MAPS_KEY_3,
                    EAppSettingKey.MAPS_BOX_KEY,
                    EAppSettingKey.APP_VERSION,
                    EAppSettingKey.SUPPORT_PHONE,
                    EAppSettingKey.HOTLINE,
                ].includes(key) &&
                !isString(value)
            ) {
                throw new BadRequestException('required string type!');
            }
            if (
                EAppSettingKey.DISTANCE_CALC_TOOL_TYPE == key &&
                !['google', 'mapbox', 'heremap', 'manual'].includes(value)
            ) {
                throw new BadRequestException('value in google, mapbox, manual');
            }
            if (EAppSettingKey.GEOCODING_TOOL_TYPE == key && !['google', 'tomtom', 'geolocator'].includes(value)) {
                throw new BadRequestException('value in google, mapbox, manual');
            }
            if (EAppSettingKey.ENABLE_VERSION == key && !['1', '0'].includes(value)) {
                throw new BadRequestException('value should be in 1 or 0');
            }
            if (
                [EAppSettingKey.ENABLE_APP_ORDER, EAppSettingKey.FEED_ENABLED].includes(key) &&
                ![1, 0].includes(value)
            ) {
                throw new BadRequestException('value should be in 1 or 0');
            }
            if (EAppSettingKey.AUTO_SUGGEST_TOOL == key && !['google', 'map4d'].includes(value))
                throw new BadRequestException('value should be in google or map4d');
        } else {
            let validationData: any;
            let isArrayInputData = false;
            switch (key) {
                case EAppSettingKey.ORDER_POINT:
                    validationData = plainToClass(ShipperPointSettingDto, value);
                    break;
                case EAppSettingKey.SYSTEM_MAINTENANCE:
                    validationData = plainToClass(UpdateMaintenanceDto, value);
                    break;
                case EAppSettingKey.FOOD_CATALOG_ALERT:
                    validationData = plainToClass(UpdateFoodCatalogDto, value);
                    break;
                case EAppSettingKey.TIPS_CONF:
                    validationData = plainToClass(UpdateTipsDto, value);
                    break;
                case EAppSettingKey.RAISE_RACCOON_PROGRAM:
                    validationData = plainToClass(UpdateSpecialClientCouponDto, value);
                    break;
                case EAppSettingKey.WORK_TIME:
                    validationData = plainToClass(UpdateWorkTimeDto, value);
                    break;
                case EAppSettingKey.AUTO_ORDER_ASSIGNED_SETTING:
                    validationData = plainToClass(UpdateAutoAssignmentDto, value);
                    break;
                case EAppSettingKey.VILLFOOD_SETTING_FEES:
                    validationData = plainToClass(UpdateVillFoodFeeDto, value);
                    break;
                case EAppSettingKey.VILLBIKE_SETTING_FEES:
                    validationData = plainToClass(UpdateVillBikeDto, value);
                    break;
                case EAppSettingKey.VILLEXPRESS_SETTING_FEES:
                    validationData = plainToClass(UpdateVillExpressFeeDto, value);
                    break;
                case EAppSettingKey.LARGE_ORDER_BONUS_CONF:
                    validationData = plainToClass(UpdateLargeOrderBonusDto, value);
                    break;
                case EAppSettingKey.WEEK_SHIPPER_TIMEKEEPING_CONF:
                    validationData = plainToClass(UpdateTimeKeepingShipperDto, value);
                    break;
                case EAppSettingKey.LONG_DISTANCE_ORDER_BONUS_CONF:
                    validationData = plainToClass(UpdateLongDistanceOrderBonusDto, value);
                    break;
                case EAppSettingKey.DRIVER_APP_VERSION:
                    validationData = plainToClass(UpdateDriverAppVersion, value);
                    break;
                case EAppSettingKey.DRIVER_WALLET_CONF:
                    validationData = plainToClass(UpdateDriverWalletSetting, value);
                    break;
                case EAppSettingKey.COLLECTION_BROADCAST_NOTI:
                    validationData = plainToClass(CollectionBroadcastNotifyDto, value);
                    break;
                case EAppSettingKey.SMS_OTP_PARTNER:
                    validationData = plainToClass(UpdateSmsOtpPartnerSettingDto, value);
                    break;
                case EAppSettingKey.PRIORITY_TIMES_ORDER_BONUS_CONF:
                    validationData = plainToClass(UpdateProrityTimesBonusConfDto, value);
                    break;
                case EAppSettingKey.HOLIDAY_ORDER_BONUS:
                    validationData = plainToClass(UpdateHolidayBonusDto, value);
                    break;
                case EAppSettingKey.GROUP_ORDER_FEATURE_CONF:
                    validationData = plainToClass(UpdateGroupOrderFeatureDto, value);
                    break;
                case EAppSettingKey.VILLCAR_SETTING_FEES:
                    validationData = plainToClass(UpdateVillCarFeeDto, value);
                    break;
                case EAppSettingKey.DRIVER_TRADE_DISCOUNT_PERCENT:
                    validationData = plainToClass(UpdateDriverTradeDiscountDto, value);
                    break;
                case EAppSettingKey.CLIENT_MAP_CONF:
                    validationData = plainToClass(UpdateClientMapConfDto, value);
                    break;
                case EAppSettingKey.DRIVER_ORDER_NOTIFICATION_CONF:
                    validationData = plainToClass(UpdateDriverOrderNotiConfDto, value);
                    break;
                case EAppSettingKey.FOOD_DESCRIPTION_BY_CHATGPT:
                    validationData = plainToClass(UpdateFoodDescription, value);
                    break;
                case EAppSettingKey.WORK_TIMES:
                    validationData = plainToClass(UpdateWorkTimesDto, value);
                    break;
                case EAppSettingKey.MERCHANT_APP_ALERT:
                    validationData = plainToClass(UpdateMerchantAppAlertDto, value);
                    break;
                case EAppSettingKey.order_point_template:
                    validationData = plainToClass(UpdateOrderPointTemplateDto, { templates: value });
                    break;
                case EAppSettingKey.RESTAURANT_TRADE_DISCOUNT_DEFAULT:
                    validationData = plainToClass(RestaurantTradeDiscountDefault, value);
                    break;
                case EAppSettingKey.DRIVER_HOLIDAY_BONUS:
                    validationData = plainToClass(UpdateDriverHolidayBonusDto, value);
                    break;
                case EAppSettingKey.DRIVER_WALLET_WORK_TIMES:
                    validationData = plainToClass(UpdateWorkTimesDto, value);
                    break;
                case EAppSettingKey.DRIVER_BONUS_TIME:
                    validationData = plainToClass(UpdateDriverBonusTimeDto, value);
                    break;
                case EAppSettingKey.SOLD_OUT_UNTIL:
                    validationData = plainToClass(UpdateSoldOutUtilDto, value);
                    break;
                case EAppSettingKey.DRIVER_WALLET_PROVIDER:
                    validationData = plainToClass(UpdateDriverWalletProviderDto, value);
                    isArrayInputData = true;
                    break;
                case EAppSettingKey.DRIVER_RANKING_SETTING:
                    validationData = plainToClass(UpdateDriverRankingDto, value);
                    break;
                case EAppSettingKey.VIETMAP_CONFIG:
                    validationData = plainToClass(UpdateVietmapConfigDto, value);
                    break;
                case EAppSettingKey.CLIENT_ADDRESS_CONFIG:
                    validationData = plainToClass(UpdateClientAddressConfigDto, value);
                    break;
                default:
            }
            if (validationData) {
                let errors: ValidationError[] = [];
                if (isArrayInputData) {
                    for (const item of validationData) {
                        errors = errors.concat(await validate(item));
                    }
                } else {
                    errors = await validate(validationData);
                }
                if (errors.length > 0) throw new BadRequestException(errors.join(', '));
            }
        }
    }

    @Get('payment/fees')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_FIND_LIST)
    async getDeliveryFee(
        @HeaderProvince() provinceId: string,
        @Query('key') key: EAppSettingKey,
        @Query('subProvinceId') subProvinceId: string,
    ) {
        return await this.appSettingService.getDeliveryFee(key, provinceId, subProvinceId);
    }

    @Patch('payment/fees')
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_UPDATE)
    async updateDeliveryFee(
        @HeaderProvince() provinceId: string,
        @Body(new HttpValidationPipe()) updateDeliveryFeeDto: UpdateDeliveryFeeDto,
        @RequestUser() user: IRequestUser,
    ) {
        return await this.appSettingService.updateDeliveryFee(provinceId, updateDeliveryFeeDto, user.id);
    }

    @Put()
    @RequirePermissions(PermissionsAccessAction.APP_SETTINGS_UPDATE)
    async updateAppSetting(
        @Req() ctx: Request,
        @Body(new HttpValidationPipe()) updateSettingDto: UpdateSettingDto,
    ) {
        await this.validateSettingRequest(updateSettingDto.key, updateSettingDto.value);
        return await this.appSettingService.updateByKey(ctx, updateSettingDto.key, updateSettingDto.value);
    }
}
