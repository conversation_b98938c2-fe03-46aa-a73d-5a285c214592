import { Module, forwardRef } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { AppSettingController } from './appSetting.controller';
import { AppSettingService } from './appSetting.service';
import { JobQueue } from 'src/jobs';
import { AppSettingJobModule } from 'src/jobs/appSetting/appSettingJob.module';
import { AppSettingJobService } from 'src/jobs/appSetting/appSettingJob.service';
import { UserActivityModule } from '../adminUserActivity/userActivity.module';

@Module({
    imports: [
        forwardRef(() => AppSettingJobModule),
        BullModule.registerQueue({
            name: JobQueue.APPSETTING_QUEUE.PROCESSOR,
            defaultJobOptions: {
                removeOnComplete: 100,
                attempts: 2,
                backoff: 1000,
                priority: 1,
            },
        }),
        UserActivityModule,
    ],
    controllers: [AppSettingController],
    providers: [AppSettingService, AppSettingJobService],
    exports: [AppSettingService],
})
export class AppSettingModule {}
