import { IsN<PERSON>ber, <PERSON><PERSON>tring, IsArray, IsObject, ValidateNested, IsEnum, IsIn, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { EAppSettingKey } from 'src/entities/appSetting.entity';

export enum Condition {
    GREATER_THAN = '>',
    LESS_THAN = '<',
    GREATER_THAN_OR_EQUAL = '>=',
    LESS_THAN_OR_EQUAL = '<=',
    EQUAL = '=',
}

class SubOrderFees {
    @IsNumber()
    @Type(() => Number)
    TOLERANCE: number;

    @IsNumber()
    @Type(() => Number)
    DEFAULT_FEE: number;

    @IsNumber()
    @Type(() => Number)
    MAX_EXTRA_DISTANCE: number;

    @IsNumber()
    @Type(() => Number)
    EXTEND_DISTANCE_FEE_PER_KM: number;
}

class DistanceFee {
    @IsNumber()
    @Type(() => Number)
    DISTANCE: number;

    @IsNumber()
    @Type(() => Number)
    FEE: number;

    @IsNumber()
    @Type(() => Number)
    SUBSIDY: number;
}

class ExtraDistanceFee {
    @IsNumber()
    @Type(() => Number)
    DISTANCE_FROM: number;

    @IsNumber()
    @Type(() => Number)
    FEE: number;
}

class DefaultDeliveryFee {
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DistanceFee)
    MIN_DISTANCE_FEES: DistanceFee[];

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ExtraDistanceFee)
    EXTRA_DISTANCE_FEES: ExtraDistanceFee[];
}

export class TimeBasedDeliveryFee {
    @IsString()
    START_TIME: string;

    @IsString()
    END_TIME: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DistanceFee)
    MIN_DISTANCE_FEES: DistanceFee[];

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ExtraDistanceFee)
    EXTRA_DISTANCE_FEES: ExtraDistanceFee[];
}

class DeliveryFee {
    @ValidateNested()
    @IsObject()
    @Type(() => DefaultDeliveryFee)
    DEFAULT_FEE: DefaultDeliveryFee;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TimeBasedDeliveryFee)
    TIMES_FEE: TimeBasedDeliveryFee[];
}

class ServiceFeeByTotalPrice {
    @IsNumber()
    @Type(() => Number)
    TOTAL_PRICE: number;

    @IsNumber()
    @Type(() => Number)
    FEE: number;
}

class VillSettingFees {
    @IsNumber()
    @Type(() => Number)
    SERVICE_FEE: number;


    @IsNumber()
    @Type(() => Number)
    VAT_PERCENT: number;

    @IsNumber()
    @Type(() => Number)
    PERSONAL_INCOME_TAX_PERCENT: number;

    @IsNumber()
    @Type(() => Number)
    SURCHARGE_VAT_PERCENT: number;

    @IsObject()
    @ValidateNested()
    @ValidateIf((o) => o.SUB_ORDER)
    @Type(() => SubOrderFees)
    SUB_ORDER: SubOrderFees;

    @ValidateNested()
    @IsObject()
    @Type(() => DeliveryFee)
    DELIVERY_FEE: DeliveryFee;
}

export class VillFoodSettingFees extends VillSettingFees {
    @IsArray()
    @ValidateIf((o) => o.SERVICE_FEES_BY_TOTAL_PRICE)
    @ValidateNested({ each: true })
    @Type(() => ServiceFeeByTotalPrice)
    SERVICE_FEES_BY_TOTAL_PRICE: ServiceFeeByTotalPrice[];
}

export class UpdateDeliveryFeeDto {
    @ValidateNested()
    @IsObject()
    @Type(() => VillFoodSettingFees)
    data: VillFoodSettingFees;

    @IsEnum(EAppSettingKey)
    key: EAppSettingKey;

    @IsNumber()
    @Type(() => Number)
    subProvinceId: number;
}

export class VillExpressSettingFees extends VillSettingFees {}

export class VillBikeSettingFees extends VillSettingFees {}
