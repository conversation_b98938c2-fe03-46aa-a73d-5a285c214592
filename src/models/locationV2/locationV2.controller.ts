import { Controller, Get, Param, UseGuards, UseInterceptors } from '@nestjs/common';
import { LocationV2Service } from './locationV2.service';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';

@Controller('location-v2')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class LocationV2Controller {
    constructor(private readonly locationV2Service: LocationV2Service) {}

    @Get('provinces')
    async getProvinces() {
        return await this.locationV2Service.getProvinces();
    }

    @Get('provinces/:provinceCode')
    async getWardsByProvince(@Param('provinceCode') provinceCode: string) {
        return await this.locationV2Service.getWardsByProvinceCode(provinceCode);
    }
}
