import { Injectable } from '@nestjs/common';
import { LocationV2CommandService } from 'src/providers/microservices/deliveryServiceProxy/service/locationV2.service';

@Injectable()
export class LocationV2Service {
    constructor(private readonly locationV2CommandService: LocationV2CommandService) {}

    async getProvinces() {
        return await this.locationV2CommandService.getLocationV2Provinces();
    }

    async getWardsByProvinceCode(provinceCode: string) {
        return await this.locationV2CommandService.getLocationV2WardsByProvinceCode(provinceCode);
    }
}
