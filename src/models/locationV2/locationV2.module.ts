import { Module } from '@nestjs/common';
import { DeliveryServiceModule } from 'src/providers/microservices/deliveryServiceProxy/deliveryServiceProxy.module';
import { LocationV2Controller } from './locationV2.controller';
import { LocationV2Service } from './locationV2.service';

@Module({
    imports: [DeliveryServiceModule],
    controllers: [LocationV2Controller],
    providers: [LocationV2Service],
    exports: [LocationV2Service],
})
export class LocationV2Module {}
