import { <PERSON><PERSON><PERSON>, Lo<PERSON> } from '@nestjs/common';
import { Chat<PERSON>ontroller } from './chat.controller';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
@Module({
    controllers: [ChatController],
    providers: [
        {
            provide: 'CHAT_SERVICE',
            useFactory: (configService: ConfigService) => {
                const clientProxy = ClientProxyFactory.create({
                    transport: Transport.TCP,
                    options: {
                        host: configService.get('chatHost'),
                        port: configService.get('chatPort'),
                    },
                });
                setInterval(async () => {
                    try {
                        clientProxy.emit({ cmd: 'ping' }, '').subscribe({
                            next: () => {},
                            complete: () => {},
                            error: (err) => {
                                Logger.error(
                                    `Chat service is not available | message: ${err.message} | stack: ${err.stack}`,
                                );
                            },
                        });
                    } catch (error) {
                        Logger.error(
                            `Chat service is not available | message: ${error.message} | stack: ${error.stack}`,
                        );
                    }
                }, 60000);
                return clientProxy;
            },
            inject: [ConfigService],
        },
    ],
})
export class ChatModule {}
