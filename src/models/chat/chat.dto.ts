import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, ValidateIf } from 'class-validator';
import * as _ from 'lodash';

export class GetMessageTemplateDto {
    @IsOptional()
    role: string;

    @IsOptional()
    @IsBoolean()
    active: boolean;
}

export class GetMessageDto {
    @IsOptional()
    lastId: string;

    @IsNotEmpty()
    limit: number;
}

export class GetChatRoomDto {
    @IsOptional()
    @ValidateIf((o) => !o.firstId)
    cursor: string;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    limit: number;

    /*  @IsOptional()
    @ValidateIf((o) => !o.lastId)
    firstRoomId: string; */

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    orderId: number;
}
