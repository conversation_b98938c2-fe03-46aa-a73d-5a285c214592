import { Controller, Inject, Get, Query, ParseIntPipe, Param, Put, UseGuards } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { GetChatRoomDto, GetMessageDto, GetMessageTemplateDto } from './chat.dto';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('chat')
@UseGuards(AuthGuard)
export class ChatController {
    constructor(@Inject('CHAT_SERVICE') private client: ClientProxy) {}

    @Get('message-templates')
    @RequirePermissions(PermissionsAccessAction.CHAT_FIND_LIST)
    async getMessageTemplate(@Query(new HttpValidationPipe()) query: GetMessageTemplateDto) {
        return this.client.send({ cmd: 'messageTemplate.getAll' }, query);
    }

    @Get('rooms')
    @RequirePermissions(PermissionsAccessAction.CHAT_FIND_LIST)
    async getRooms(
        @Query(new HttpValidationPipe()) query: GetChatRoomDto,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
    ) {
        return this.client.send({ cmd: 'room.getRooms' }, { ...query, province_id });
    }

    @Get('rooms/:id')
    @RequirePermissions(PermissionsAccessAction.CHAT_FIND_LIST)
    async getRoom(@Param('id') id: string, @HeaderProvince(new ParseIntPipe()) provinceId: number) {
        return this.client.send({ cmd: 'room.getOne' }, { roomId: id, provinceId });
    }

    @Get('rooms/:roomId/messages')
    @RequirePermissions(PermissionsAccessAction.CHAT_FIND_LIST)
    async getMessages(@Param('roomId') roomId: string, @Query(new HttpValidationPipe()) { lastId, limit }: GetMessageDto) {
        return this.client.send({ cmd: 'message.getByRoomId' }, { roomId, lastId, limit });
    }

    @Put('rooms/:roomId/archive')
    @RequirePermissions(PermissionsAccessAction.CHAT_UPDATE)
    async archiveRoom(@Param('roomId') roomId: string, @HeaderProvince(new ParseIntPipe()) provinceId: number) {
        return this.client.send({ cmd: 'room.archive' }, { roomId, provinceId });
    }

    @Get('room-orders/:orderId')
    @RequirePermissions(PermissionsAccessAction.CHAT_FIND_LIST)
    async getRoomByOrderId(@Param('orderId') orderId: string, @HeaderProvince(new ParseIntPipe()) provinceId: number) {
        return this.client.send({ cmd: 'roomOrder.getByOrderId' }, { orderId, provinceId });
    }
}
