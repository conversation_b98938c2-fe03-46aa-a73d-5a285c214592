import { Module } from '@nestjs/common';
import { DeliveryServiceModule } from 'src/providers/microservices/deliveryServiceProxy/deliveryServiceProxy.module';
import { BankAccountController } from './controllers/bankAccount.controller';
import { BundleController } from './controllers/bundle.controller';
import { InventoryCategoryController } from './controllers/category.controller';
import { CustomerController } from './controllers/customer.controller';
import { OrdersController } from './controllers/orders.controller';
import { ProductController } from './controllers/product.controller';
import { ProductBatchController } from './controllers/productBatch.controller';
import { ProductBatchOnHandController } from './controllers/productBatchOnHand.controller';
import { ProductHistoryController } from './controllers/productHistory.controller';
import { PurchaseOrderController } from './controllers/purchaseOrders.controller';
import { ReportController } from './controllers/report.controller';
import { ReturnPurchaseOrderController } from './controllers/returnPurchaseOrders.controller';
import { StockTakeController } from './controllers/stockTake.controller';
import { SupplierController } from './controllers/supplier.controller';
import { TaxController } from './controllers/tax.controller';
import { TransferController } from './controllers/transfer.controller';

@Module({
    imports: [DeliveryServiceModule],
    controllers: [
        InventoryCategoryController,
        ProductController,
        SupplierController,
        PurchaseOrderController,
        ReturnPurchaseOrderController,
        TransferController,
        OrdersController,
        StockTakeController,
        BankAccountController,
        CustomerController,
        TaxController,
        ProductBatchController,
        ProductHistoryController,
        ProductBatchOnHandController,
        BundleController,
        ReportController,
    ],
    providers: [],
})
export class InventoryModule {}
