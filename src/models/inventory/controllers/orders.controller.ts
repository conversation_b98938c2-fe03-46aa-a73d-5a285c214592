import { Body, Controller, Inject, ParseIntPipe, Post, Res, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Response } from 'express';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { IRequestUser, RequestUser } from 'src/common/decorators/user.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
@Controller('inventory/orders')
export class OrdersController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_FIND_LIST)
    getOrderList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.get-paged-list' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.get-by-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/payments/get-by-order-id')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_FIND_ONE)
    getPaymentList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.payments.get-by-order-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/payments/make-payment')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_UPDATE)
    getPaymentDetail(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.payment.make-payment' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/payments/get-by-id')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_FIND_ONE)
    refund(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.payment.get-by-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('invoice/create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_INVOICE_CREATE)
    createInvoice(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.invoice.create' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('invoice/update-buyer-info')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_INVOICE_UPDATE_BUYER_INFO)
    getReturnOrder(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.invoice.update-buyer-info' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('payments/ipn')
    villPayIpn(@Body() body: Record<string, any>, @Res() res: Response) {
        return this.clientProxy.send({ cmd: 'inventory.order.payment.ipn' }, body).subscribe({
            next: (response) => {
                res.send(response).end();
            },
            error: (error) => {
                res.status(500).send(error).end();
            },
        });
    }

    @Post('payments/sepay-ipn')
    sePayIpn(@Body() body: Record<string, any>, @Res() res: Response) {
        return this.clientProxy.send({ cmd: 'inventory.order.payment.sepay-ipn' }, body).subscribe({
            next: (response) => {
                res.send(response).end();
            },
            error: (error) => {
                res.status(500).send(error).end();
            },
        });
    }

    @Post('update-status')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_UPDATE)
    updateOrderStatus(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.update-status' },
            {
                province_id,
                ...body,
            },
        );
    }

    /*     @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_CREATE)
    create(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.create-v2' },
            {
                
                created_by: user.id,
                ...body,
                province_id,
            },
        );
    } */

    @Post('quick-create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_QUICK_CREATE)
    createV2(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.quick-create' },
            {
                province_id,
                created_by: user.id,
                ...body,
            },
        );
    }

    @Post('advanced-create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_ADVANCED_CREATE)
    updateV2(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.advanced-create' },
            {
                province_id,
                created_by: user.id,
                ...body,
            },
        );
    }

    @Post('bundle-create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_BUNDLE_CREATE)
    updateStatus(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.bundle-create' },
            {
                province_id,
                ...body,
                created_by: user.id,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.update' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('delete')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_ORDER_DELETE)
    delete(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.order.delete' },
            {
                province_id,
                ...body,
            },
        );
    }
}
