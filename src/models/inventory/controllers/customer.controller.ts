import { Body, Controller, Inject, Post, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/customers')
@UseInterceptors(LoggingInterceptor)
export class CustomerController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CUSTOMER_FIND_LIST)
    getCustomerList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.customer.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CUSTOMER_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.customer.get-by-id' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CUSTOMER_CREATE)
    create(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.customer.create' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CUSTOMER_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.customer.update' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
