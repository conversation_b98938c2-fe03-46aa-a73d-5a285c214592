import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/purchase-orders')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class PurchaseOrderController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get-paged-list')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PURCHASE_ORDER_FIND_LIST)
    getPurchaseOrderList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.purchase-order.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('detail/get')
    getOne(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.purchase-order.get-by-id' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('create')
    create(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.purchase-order.create' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('update')
    update(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.purchase-order.update' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('make-payment')
    makePayment(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.purchase-order.make-payment' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
