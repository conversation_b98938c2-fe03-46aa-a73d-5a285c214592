import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/product-histories')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class ProductHistoryController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_FIND_LIST)
    getPagedList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-history.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
