import { Body, Controller, Inject, ParseIntPipe, Post, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@Controller('inventory/product-on-hands')
export class ProductOnHandController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    getProductOnHandList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-on-hand.get-list' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('update')
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-on-hand.update' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('summary')
    summary(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-on-hand.summary' },
            {
                province_id,
                ...body,
            },
        );
    }
}
