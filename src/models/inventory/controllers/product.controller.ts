import { Body, Controller, Inject, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
@Controller('inventory/products')
export class ProductController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_FIND_LIST)
    getProductList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product.get-paged-list' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product.get-by-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_CREATE)
    create(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product.create' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product.update' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('delete')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_PRODUCT_DELETE)
    delete(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.product.delete' },
            {
                province_id,
                ...body,
            },
        );
    }
}
