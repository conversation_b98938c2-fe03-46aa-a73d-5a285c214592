import { Body, Controller, Inject, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/stock-takes')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class StockTakeController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get-paged-list')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_STOCK_TAKE_FIND_LIST)
    getStockTakeList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.stock-take.get-paged-list' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_STOCK_TAKE_CREATE)
    create(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.stock-take.create' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_STOCK_TAKE_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.stock-take.update' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_STOCK_TAKE_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.stock-take.get-by-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('update-status')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_STOCK_TAKE_UPDATE)
    updateStatus(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.stock-take.update-status' },
            {
                province_id,
                ...body,
            },
        );
    }
}
