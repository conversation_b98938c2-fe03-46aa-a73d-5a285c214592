import { Body, Controller, Inject, Post, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@Controller('inventory/return-purchase-orders')
export class ReturnPurchaseOrderController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    getReturnPurchaseOrderList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.return-purchase-order.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('create')
    create(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.return-purchase-order.create' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('update')
    update(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.return-purchase-order.update' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
