import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
@Controller('inventory/categories')
export class InventoryCategoryController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_FIND_LIST)
    getPagedList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('list/get-all')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_FIND_LIST)
    getAllActive(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.get-all-active' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.get-by-id' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_CREATE)
    create(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.create' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.update' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('delete')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_CATEGORY_DELETE)
    delete(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-category.delete' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
