import { Body, Controller, Inject, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/reports')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class ReportController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('on-hands/summary')
    getReportList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.reports.on-hand.summary' },
            {
                ...body,
                province_id,
            },
        );
    }

    @Post('on-hands/get-paged-list')
    getReportListPaged(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.reports.on-hand.get-paged-list' },
            {
                ...body,
                province_id,
            },
        );
    }

    @Post('stock-in-out/summary')
    getStockInOutSummaryF(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.reports.stock-in-out.summary' },
            {
                ...body,
                province_id,
            },
        );
    }

    @Post('stock-in-out/get-paged-list')
    getStockInOutListPaged(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.reports.stock-in-out.get-paged-list' },
            {
                ...body,
                province_id,
            },
        );
    }
}
