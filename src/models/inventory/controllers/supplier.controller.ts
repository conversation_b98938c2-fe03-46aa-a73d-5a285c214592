import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/suppliers')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class SupplierController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get-all')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_SUPPLIER_FIND_LIST)
    getSupplierList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.supplier.get-all' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('list/get-paged-list')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_SUPPLIER_FIND_LIST)
    getSupplierListPaged(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.supplier.get-paged-list' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_SUPPLIER_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.supplier.get-by-id' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_SUPPLIER_CREATE)
    create(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.supplier.create' },
            {
                ...body,
                provinceId,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_SUPPLIER_UPDATE)
    update(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.supplier.update' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
