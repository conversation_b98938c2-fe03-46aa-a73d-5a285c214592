import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@Controller('inventory/product-batch-on-hands')
@UseGuards(AuthGuard)
export class ProductBatchOnHandController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    getProductOnHandList(@Body() body: Record<string, any>, @HeaderProvince() provinceId: string) {
        return this.clientProxy.send(
            { cmd: 'inventory.product-batch-on-hand.get-list' },
            {
                ...body,
                provinceId,
            },
        );
    }
}
