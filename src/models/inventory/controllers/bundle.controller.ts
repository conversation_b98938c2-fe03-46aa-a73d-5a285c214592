import { Body, Controller, Inject, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/bundles')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class BundleController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get-all-active')
    // @RequirePermissions(PermissionsAccessAction.INVENTORY_BUNDLE_FIND_LIST)
    getBundleList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.bundle.get-active-list' },
            {
                ...body,
                province_id,
            },
        );
    }

    @Post('detail/get')
    // @RequirePermissions(PermissionsAccessAction.INVENTORY_BUNDLE_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.bundle.get-by-id' },
            {
                ...body,
                province_id,
            },
        );
    }
}
