import { Body, Controller, Inject, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { IRequestUser, RequestUser } from 'src/common/decorators/user.decorator';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@Controller('inventory/transfers')
@UseGuards(AuthGuard)
@UseInterceptors(LoggingInterceptor)
export class TransferController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get-paged-list')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_TRANSFER_FIND_LIST)
    getTransferList(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.transfer.get-paged-list' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('create')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_TRANSFER_CREATE)
    create(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.transfer.create' },
            {
                province_id,
                ...body,
                user_id: user.id,
            },
        );
    }

    @Post('update-status')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_TRANSFER_UPDATE)
    updateStatus(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.transfer.update-status' },
            {
                province_id,
                ...body,
                user_id: user.id,
            },
        );
    }

    @Post('detail/get')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_TRANSFER_FIND_ONE)
    getOne(@Body() body: Record<string, any>, @HeaderProvince(new ParseIntPipe()) province_id: number) {
        return this.clientProxy.send(
            { cmd: 'inventory.transfer.get-by-id' },
            {
                province_id,
                ...body,
            },
        );
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.INVENTORY_TRANSFER_UPDATE)
    update(
        @Body() body: Record<string, any>,
        @HeaderProvince(new ParseIntPipe()) province_id: number,
        @RequestUser() user: IRequestUser,
    ) {
        return this.clientProxy.send(
            { cmd: 'inventory.transfer.update' },
            {
                province_id,
                ...body,
                user_id: user.id,
            },
        );
    }
}
