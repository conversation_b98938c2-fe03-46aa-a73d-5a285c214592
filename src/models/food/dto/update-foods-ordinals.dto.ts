import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class UpdateFoodsOrdinalDto {
    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number)
    foodMenuId: number;

    @IsNotEmpty()
    @IsArray()
    @ArrayMinSize(1)
    @Type(() => Number)
    @IsNumber({}, { each: true })
    ids: number[];

    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number)
    restaurantId: number;
}
