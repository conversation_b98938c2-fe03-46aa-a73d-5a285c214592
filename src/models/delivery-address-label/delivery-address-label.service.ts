import { Inject, Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
import { DeliveryAddressLabel } from 'src/entities/delivery-address-label.entity';
import { CreateDeliveryAddressLabelDto } from './delivery-address-label.dto';
import { In } from 'typeorm';

@Injectable()
export class DeliveryAddressLabelService {
    async checkDeliveryAddressLabelExist(code: number) {
        return (
            (await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel).findOne({
                where: { code },
            })) !== null
        );
    }

    async getDeliveryAddressLabelById(id: number) {
        return await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel).findOne({ where: { id } });
    }

    async getDeliveryAddressLabelByCodes(code: number[]) {
        return await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel).find({
            where: { code: In(code) },
        });
    }
    async getDeliveryAddressLabelByCode(code: number) {
        return await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel).findOne({
            where: { code },
        });
    }

    async getListDeliveryAddressLabel() {
        return await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel).find();
    }

    async create(createDeliveryAddressLabelDto: CreateDeliveryAddressLabelDto, iconUrl: string) {
        const { title } = createDeliveryAddressLabelDto;
        const newCode = await DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel)
            .createQueryBuilder()
            .select('MAX(code)', 'max')
            .getRawOne();
        const code = newCode.max + 1;
        if (!code) throw new BadRequestException('Có lỗi xảy ra, vui lòng thử lại!');
        const deliveryAddressLabelRepository = DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel);
        const deliveryAddressLabel = new DeliveryAddressLabel({
            title,
            code,
        });
        return await deliveryAddressLabelRepository.save(deliveryAddressLabel);
    }

    async update(id: number, createDeliveryAddressLabelDto: CreateDeliveryAddressLabelDto, Location?: string) {
        const deliveryAddressLabelRepository = DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel);
        const deliveryAddressLabel = await deliveryAddressLabelRepository.findOne({ where: { id } });
        if (!deliveryAddressLabel) throw new NotFoundException('Không tìm thấy nhãn địa chỉ');
        const { title } = createDeliveryAddressLabelDto;
        if (title) deliveryAddressLabel.title = title;
        if (Location) deliveryAddressLabel.icon = Location;
        return await deliveryAddressLabelRepository.save(deliveryAddressLabel);
    }

    async delete(id: number) {
        const deliveryAddressLabelRepository = DatabaseService.getRepositoryByDefaultConnection(DeliveryAddressLabel);
        const deliveryAddressLabel = await deliveryAddressLabelRepository.findOne({ where: { id } });
        if (!deliveryAddressLabel) throw new NotFoundException('Không tìm thấy nhãn địa chỉ');
        return await deliveryAddressLabelRepository.remove(deliveryAddressLabel);
    }
}
