import {
    Controller,
    Get,
    Post,
    Body,
    UseInterceptors,
    UploadedFile,
    BadRequestException,
    Put,
    Delete,
    Param,
} from '@nestjs/common';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DeliveryAddressLabelService } from './delivery-address-label.service';
import { CreateDeliveryAddressLabelDto } from './delivery-address-label.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { createImageUrlDependingOnProvince } from 'src/common/helpers/common.helper';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('delivery-address-labels')
export class DeliveryAddressLabelController {
    constructor(
        private readonly deliveryAddressLabelService: DeliveryAddressLabelService,
        private readonly s3Service: AwsS3Service,
    ) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_UPDATE)
    async getListDeliveryAddressLabel() {
        return await this.deliveryAddressLabelService.getListDeliveryAddressLabel();
    }

    @Post()
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_CREATE)
    async create(
        @Body() createDeliveryAddressLabelDto: CreateDeliveryAddressLabelDto,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.deliveryAddressLabelService.create(createDeliveryAddressLabelDto, provinceId);
    }

    @Post(':id/image')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_UPDATE)
    @UseInterceptors(FileInterceptor('image'))
    async uploadImage(
        @Body() createDeliveryAddressLabelDto: CreateDeliveryAddressLabelDto,
        @UploadedFile() file: Express.Multer.File,
        @HeaderProvince() provinceId: string,
        @Param('id') id: number,
    ) {
        try {
            if (file) {
                const fileName = createImageUrlDependingOnProvince(
                    id,
                    file.originalname.split('.').pop(),
                    provinceId,
                    'LABELS-ICON',
                );

                const { Location } = await this.s3Service.uploadPublicFile(fileName, file.mimetype, file.buffer);

                return await this.deliveryAddressLabelService.update(id, createDeliveryAddressLabelDto, Location);
            }
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_UPDATE)
    async update(@Body() createDeliveryAddressLabelDto: CreateDeliveryAddressLabelDto, @Param('id') id: number) {
        return await this.deliveryAddressLabelService.update(id, createDeliveryAddressLabelDto);
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_REMOVE)
    async delete(@Param('id') id: number) {
        return await this.deliveryAddressLabelService.delete(id);
    }
}
