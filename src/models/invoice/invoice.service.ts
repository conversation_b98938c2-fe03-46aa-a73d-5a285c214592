import { Injectable, NotFoundException, BadRequestException, Logger, Inject } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { Invoice, EOrderInvoiceStatus, EInvoiceType } from '../../entities/invoice.entity';

import { DatabaseService } from 'src/providers/database/database.service';
import { InvoiceQueryDto, UpdateInvoiceDto, SyncInvoiceStatusDto } from './dto/invoice.dto';
import { OrderInvoiceJobService } from '../order/services/orderInvoiceJob.service';
import { OrderInvoiceService } from '../order/services/orderInvoice.service';
import { MISA_HTTP } from 'src/misaHttp/misaHttp.constant';
import { MisaHttpService } from 'src/misaHttp/misaHttp.service';
import * as moment from 'moment';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';

@Injectable()
export class InvoiceService {
    private readonly logger = new Logger(InvoiceService.name);

    constructor(
        private orderInvoiceJobService: OrderInvoiceJobService,
        private orderInvoiceService: OrderInvoiceService,
        @Inject(MISA_HTTP) private misaHttpService: MisaHttpService,
        private s3Service: AwsS3Service,
    ) {}

    getRepository(provinceId: string) {
        return DatabaseService.getRepositoryByProvinceId(Invoice, provinceId);
    }

    async getInvoices(query: InvoiceQueryDto, provinceId: string) {
        const { page, limit, status, type, email_sent, date_from, date_to, search, order_id } = query;

        const queryBuilder = this.getRepository(provinceId).createQueryBuilder('invoice');

        this.applyFilters(queryBuilder, {
            status,
            type,
            email_sent,
            date_from,
            date_to,
            search,
            order_id,
        });

        queryBuilder
            .orderBy('invoice.id', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);

        const [data, total] = await queryBuilder.getManyAndCount();

        return {
            items: data,
            total,
        };
    }

    async getInvoiceDetails(id: number, provinceId: string): Promise<Invoice> {
        const invoice = await this.getRepository(provinceId).findOne({
            where: { id },
            relations: ['order'],
        });

        if (!invoice) {
            throw new NotFoundException(`Invoice with ID ${id} not found`);
        }

        return invoice;
    }

    async updateInvoice(id: number, updateData: UpdateInvoiceDto, provinceId: string) {
        const invoice = await this.getInvoiceDetails(id, provinceId);

        Object.assign(invoice, updateData);
        invoice.updated_at = new Date();

        await this.getRepository(provinceId).save(invoice);

        this.logger.log(`Invoice ${id} updated successfully`);
        return invoice;
    }

    async deleteInvoice(id: number, provinceId: string) {
        const invoice = await this.getInvoiceDetails(id, provinceId);

        await this.getRepository(provinceId).remove(invoice);

        this.logger.log(`Invoice ${id} deleted successfully`);
        return { message: 'Invoice deleted successfully' };
    }

    // async bulkUpdateInvoices(bulkData: BulkUpdateInvoicesDto, provinceId: string) {
    //     const { invoice_ids, updates } = bulkData;

    //     const result = await this.getRepository(provinceId)
    //         .createQueryBuilder()
    //         .update(Invoice)
    //         .set({
    //             ...updates,
    //             updated_at: new Date(),
    //         })
    //         .whereInIds(invoice_ids)
    //         .execute();

    //     this.logger.log(`Bulk updated ${result.affected} invoices`);
    //     return {
    //         message: `Successfully updated ${result.affected} invoices`,
    //         affected: result.affected,
    //     };
    // }

    // async bulkDeleteInvoices(invoice_ids: number[], provinceId: string) {
    //     const result = await this.getRepository(provinceId).delete(invoice_ids);

    //     this.logger.log(`Bulk deleted ${result.affected} invoices`);
    //     return {
    //         message: `Successfully deleted ${result.affected} invoices`,
    //         affected: result.affected,
    //     };
    // }

    // async exportInvoices(exportData: ExportInvoicesDto, provinceId: string) {
    //     const { status, date_from, date_to, format = 'excel', invoice_ids } = exportData;

    //     const queryBuilder = this.getRepository(provinceId).createQueryBuilder('invoice');

    //     if (invoice_ids && invoice_ids.length > 0) {
    //         queryBuilder.whereInIds(invoice_ids);
    //     } else {
    //         this.applyFilters(queryBuilder, {
    //             status,
    //             date_from,
    //             date_to,
    //         });
    //     }

    //     const invoices = await queryBuilder.getMany();

    //     // Here you would implement the actual export logic
    //     // For now, return the data that would be exported
    //     this.logger.log(`Exporting ${invoices.length} invoices in ${format} format`);

    //     return {
    //         message: `Export initiated for ${invoices.length} invoices`,
    //         format,
    //         count: invoices.length,
    //         // In a real implementation, you would return a download URL or file
    //         data: invoices,
    //     };
    // }

    async retryFailedInvoice(id: number, provinceId: string) {
        const invoice = await this.getInvoiceDetails(id, provinceId);

        if (invoice.status !== EOrderInvoiceStatus.FAILED) {
            throw new BadRequestException('Only failed invoices can be retried');
        }

        // Phân biệt loại invoice để xử lý retry phù hợp
        if (invoice.type === EInvoiceType.ADS_CAMPAIGN) {
            // Retry ads campaign invoice trực tiếp
            if (!invoice.ads_campaign_id) {
                throw new BadRequestException('Ads campaign invoice missing ads_campaign_id');
            }
            await this.orderInvoiceService.retryAdsCampaignInvoice(invoice.ads_campaign_id, provinceId);
            this.logger.log(`Ads campaign invoice ${id} marked for retry`);
        } else {
            // Existing logic cho order invoice
            if (!invoice.order_id) {
                throw new BadRequestException('Order invoice missing order_id');
            }
            await this.orderInvoiceJobService.queueOrderInvoiceExport(invoice.order_id, provinceId);
            this.logger.log(`Order invoice ${id} marked for retry`);
        }

        return {
            message: 'Invoice retry initiated successfully',
            invoice_id: id,
        };
    }

    async syncInvoiceStatus(syncData: SyncInvoiceStatusDto, provinceId: string) {
        const { invoice_ids } = syncData;

        let invoicesToSync: Invoice[];

        if (invoice_ids && invoice_ids.length > 0) {
            invoicesToSync = await this.getRepository(provinceId)
                .createQueryBuilder('invoice')
                .whereInIds(invoice_ids)
                .andWhere('invoice.ref_id IS NOT NULL')
                .getMany();

            if (invoicesToSync.length === 0) {
                throw new NotFoundException('No invoices found with the provided IDs that have ref_id');
            }
        }

        const refIds = invoicesToSync.map((invoice) => invoice.ref_id).filter(Boolean);

        if (refIds.length === 0) {
            throw new BadRequestException('No valid ref_ids found for synchronization');
        }

        this.logger.log(`Syncing status for ${refIds.length} invoices with ref_ids: ${refIds.join(', ')}`);

        try {
            const misaResponse = await this.misaHttpService.getInvoiceStatus({ RefIDs: refIds });

            if (!misaResponse.success || misaResponse.errorCode) {
                throw new BadRequestException('Failed to fetch invoice status from MISA');
            }

            const syncResults = [];
            const updatedInvoices = [];

            for (const invoice of invoicesToSync) {
                const misaInvoiceData = misaResponse.data.find((data) => data.RefID === invoice.ref_id);

                if (misaInvoiceData) {
                    const oldStatus = invoice.status;
                    let newStatus = invoice.status;

                    if (misaInvoiceData.PublishStatus === 1 && misaInvoiceData.EInvoiceStatus === 1) {
                        newStatus = EOrderInvoiceStatus.PUBLISHED;
                    } else if (misaInvoiceData.PublishStatus === 0) {
                        newStatus = EOrderInvoiceStatus.CREATED;
                    } else if (misaInvoiceData.PublishStatus === -1) {
                        newStatus = EOrderInvoiceStatus.FAILED;
                    }

                    if (misaInvoiceData.InvoiceCode && !invoice.invoice_number) {
                        invoice.invoice_number = misaInvoiceData.InvNo;
                    }

                    if (misaInvoiceData.TransactionID && !invoice.transaction_id) {
                        invoice.transaction_id = misaInvoiceData.TransactionID;
                    }

                    if (newStatus !== oldStatus) {
                        invoice.status = newStatus;
                        invoice.updated_at = new Date();
                        updatedInvoices.push(invoice);

                        syncResults.push({
                            invoice_id: invoice.id,
                            ref_id: invoice.ref_id,
                            old_status: oldStatus,
                            new_status: newStatus,
                            updated: true,
                        });

                        this.logger.log(`Invoice ${invoice.id} status updated from ${oldStatus} to ${newStatus}`);
                    } else {
                        syncResults.push({
                            invoice_id: invoice.id,
                            ref_id: invoice.ref_id,
                            status: invoice.status,
                            updated: false,
                        });
                    }
                } else {
                    syncResults.push({
                        invoice_id: invoice.id,
                        ref_id: invoice.ref_id,
                        error: 'No status data found in MISA response',
                        updated: false,
                    });
                }
            }

            if (updatedInvoices.length > 0) {
                await this.getRepository(provinceId).save(updatedInvoices);
                this.logger.log(`Successfully updated ${updatedInvoices.length} invoices`);
                for (const invoice of updatedInvoices) {
                    if (invoice.status === EOrderInvoiceStatus.PUBLISHED && !invoice.pdf_url) {
                        const invoiceDetail = await this.misaHttpService.downloadInvoice([invoice.transaction_id]);
                        if (invoiceDetail.length > 0) {
                            const fileName = `invoice/${invoice.name}.pdf`;
                            const pdfUrl = await this.s3Service.uploadPublicFile(
                                fileName,
                                'application/pdf',
                                invoiceDetail[0],
                                undefined,
                                false,
                                false,
                            );
                            await this.orderInvoiceService.updateInvoicePdfUrl(invoice.id, pdfUrl.Location, provinceId);
                        }
                    }
                }
            }

            const result = {
                message: `Sync completed for ${invoicesToSync.length} invoices`,
                total_processed: invoicesToSync.length,
                total_updated: updatedInvoices.length,
                results: syncResults,
            };

            return result;
        } catch (error) {
            this.logger.error(`Error syncing invoice status: ${error.message}`, error.stack);
            throw new BadRequestException(`Failed to sync invoice status: ${error.message}`);
        }
    }

    // async sendInvoiceEmail(id: number, emailData: SendEmailDto, provinceId: string) {
    //     const invoice = await this.getInvoiceDetails(id, provinceId);

    //     if (invoice.email_sent && !emailData.force_resend) {
    //         throw new BadRequestException('Email already sent. Use force_resend to send again.');
    //     }

    //     // Here you would implement the actual email sending logic
    //     invoice.email_sent = true;
    //     invoice.updated_at = new Date();

    //     await this.getRepository(provinceId).save(invoice);

    //     this.logger.log(`Email sent for invoice ${id}`);

    //     return {
    //         message: 'Email sent successfully',
    //         invoice_id: id,
    //         email: emailData.email || invoice.buyer_email,
    //     };
    // }

    // async bulkSendInvoiceEmails(bulkEmailData: BulkSendEmailDto, provinceId: string) {
    //     const { invoice_ids, force_resend = false } = bulkEmailData;

    //     const queryBuilder = this.getRepository(provinceId).createQueryBuilder('invoice').whereInIds(invoice_ids);

    //     if (!force_resend) {
    //         queryBuilder.andWhere('invoice.email_sent = :emailSent', { emailSent: false });
    //     }

    //     const invoices = await queryBuilder.getMany();

    //     // Update all invoices to mark emails as sent
    //     await this.getRepository(provinceId)
    //         .createQueryBuilder()
    //         .update(Invoice)
    //         .set({
    //             email_sent: true,
    //             updated_at: new Date(),
    //         })
    //         .whereInIds(invoices.map((inv) => inv.id))
    //         .execute();

    //     this.logger.log(`Bulk email sending initiated for ${invoices.length} invoices`);

    //     return {
    //         message: `Email sending initiated for ${invoices.length} invoices`,
    //         processed: invoices.length,
    //     };
    // }

    private applyFilters(
        queryBuilder: SelectQueryBuilder<Invoice>,
        filters: {
            status?: string;
            type?: string;
            email_sent?: boolean;
            date_from?: string;
            date_to?: string;
            search?: string;
            order_id?: string;
        },
    ) {
        const { status, type, email_sent, date_from, date_to, search, order_id } = filters;

        if (status) {
            queryBuilder.andWhere('invoice.status = :status', { status });
        }

        if (type) {
            queryBuilder.andWhere('invoice.type = :type', { type });
        }

        // if (email_sent !== undefined) {
        //     queryBuilder.andWhere('invoice.email_sent = :email_sent', { email_sent });
        // }

        // if (date_from) {
        //     queryBuilder.andWhere('invoice.created_at >= :date_from', { date_from });
        // }

        // if (date_to) {
        //     queryBuilder.andWhere('invoice.created_at <= :date_to', { date_to });
        // }

        if (date_from && date_to) {
            queryBuilder.andWhere('invoice.invoice_date BETWEEN :date_from AND :date_to', {
                date_from: moment(date_from).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                date_to: moment(date_to).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            });
        }

        if (search) {
            queryBuilder.andWhere(
                '(invoice.ref_id LIKE :search OR invoice.buyer_name LIKE :search OR invoice.invoice_number LIKE :search)',
                { search: `%${search}%` },
            );
        }

        if (order_id) {
            // Support both exact and partial matches for order_id
            // Convert to string for LIKE comparison to handle partial matches
            queryBuilder.andWhere('invoice.order_id = :order_id', {
                order_id,
            });
        }
    }
}
