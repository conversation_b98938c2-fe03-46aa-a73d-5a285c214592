import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsArray, IsEnum, IsDateString, IsInt, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { EOrderInvoiceStatus, EInvoiceType } from '../../../entities/invoice.entity';

export class InvoiceQueryDto {
    @ApiPropertyOptional({ description: 'Page number (0-based)', minimum: 0 })
    @IsOptional()
    @Type(() => Number)
    @Min(0)
    page?: number = 0;

    @ApiPropertyOptional({ description: 'Number of items per page', minimum: 1, maximum: 100 })
    @IsOptional()
    @Type(() => Number)
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @ApiPropertyOptional({ description: 'Invoice status filter', enum: EOrderInvoiceStatus })
    @IsOptional()
    @IsEnum(EOrderInvoiceStatus)
    status?: EOrderInvoiceStatus;

    @ApiPropertyOptional({ description: 'Invoice type filter', enum: EInvoiceType })
    @IsOptional()
    @IsEnum(EInvoiceType)
    type?: EInvoiceType;

    @ApiPropertyOptional({ description: 'Email sent status filter' })
    @IsOptional()
    @Transform(({ value }) => value === 'true' || value === true)
    @IsBoolean()
    email_sent?: boolean;

    @ApiPropertyOptional({ description: 'Date from filter (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    date_from?: string;

    @ApiPropertyOptional({ description: 'Date to filter (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    date_to?: string;

    @ApiPropertyOptional({ description: 'Search term for ref_id, buyer_name, or invoice_number' })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({ description: 'Filter by order ID (exact or partial match)' })
    @IsOptional()
    @IsString()
    order_id?: string;
}

export class UpdateInvoiceDto {
    @ApiPropertyOptional({ description: 'Invoice status', enum: EOrderInvoiceStatus })
    @IsOptional()
    @IsEnum(EOrderInvoiceStatus)
    status?: EOrderInvoiceStatus;

    @ApiPropertyOptional({ description: 'Buyer name' })
    @IsOptional()
    @IsString()
    buyer_name?: string;

    @ApiPropertyOptional({ description: 'Buyer tax code' })
    @IsOptional()
    @IsString()
    buyer_tax_code?: string;

    @ApiPropertyOptional({ description: 'Buyer email' })
    @IsOptional()
    @IsString()
    buyer_email?: string;

    @ApiPropertyOptional({ description: 'Buyer phone' })
    @IsOptional()
    @IsString()
    buyer_phone?: string;

    @ApiPropertyOptional({ description: 'Additional metadata' })
    @IsOptional()
    metadata?: any;
}

export class BulkUpdateInvoicesDto {
    @ApiProperty({ description: 'Array of invoice IDs to update', type: [Number] })
    @IsArray()
    @Type(() => Number)
    invoice_ids: number[];

    @ApiProperty({ description: 'Updates to apply to all selected invoices' })
    updates: {
        status?: EOrderInvoiceStatus;
        email_sent?: boolean;
    };
}

export class BulkDeleteInvoicesDto {
    @ApiProperty({ description: 'Array of invoice IDs to delete', type: [Number] })
    @IsArray()
    @Type(() => Number)
    invoice_ids: number[];
}

export class ExportInvoicesDto {
    @ApiPropertyOptional({ description: 'Invoice status filter', enum: EOrderInvoiceStatus })
    @IsOptional()
    @IsEnum(EOrderInvoiceStatus)
    status?: EOrderInvoiceStatus;

    @ApiPropertyOptional({ description: 'Invoice type filter', enum: EInvoiceType })
    @IsOptional()
    @IsEnum(EInvoiceType)
    type?: EInvoiceType;

    @ApiPropertyOptional({ description: 'Date from filter (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    date_from?: string;

    @ApiPropertyOptional({ description: 'Date to filter (YYYY-MM-DD)' })
    @IsOptional()
    @IsDateString()
    date_to?: string;

    @ApiPropertyOptional({ description: 'Search term for ref_id, buyer_name, or invoice_number' })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({ description: 'Filter by order ID (exact or partial match)' })
    @IsOptional()
    @IsString()
    order_id?: string;

    @ApiPropertyOptional({ description: 'Export format', enum: ['excel', 'csv'] })
    @IsOptional()
    @IsEnum(['excel', 'csv'])
    format?: 'excel' | 'csv' = 'excel';

    @ApiPropertyOptional({ description: 'Specific invoice IDs to export', type: [Number] })
    @IsOptional()
    @IsArray()
    @Type(() => Number)
    invoice_ids?: number[];
}

export class SendEmailDto {
    @ApiPropertyOptional({ description: 'Override email address' })
    @IsOptional()
    @IsString()
    email?: string;

    @ApiPropertyOptional({ description: 'Force resend even if already sent' })
    @IsOptional()
    @IsBoolean()
    force_resend?: boolean = false;
}

export class BulkSendEmailDto {
    @ApiProperty({ description: 'Array of invoice IDs to send emails for', type: [Number] })
    @IsArray()
    @Type(() => Number)
    invoice_ids: number[];

    @ApiPropertyOptional({ description: 'Force resend even if already sent' })
    @IsOptional()
    @IsBoolean()
    force_resend?: boolean = false;
}

export class SyncInvoiceStatusDto {
    @IsOptional()
    @IsArray()
    @Type(() => Number)
    @IsInt({ each: true })
    invoice_ids?: number[];
}
