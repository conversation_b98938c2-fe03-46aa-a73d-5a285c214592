import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Param,
    Query,
    Body,
    ParseIntPipe,
    UseInterceptors,
    UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { InvoiceService } from './invoice.service';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { InvoiceQueryDto, UpdateInvoiceDto, SyncInvoiceStatusDto } from './dto/invoice.dto';
import AuthGuard from 'src/common/middlewares/auth.gaurd';

@ApiTags('Admin Invoices')
@UseInterceptors(LoggingInterceptor)
@Controller('admin/invoices')
@UseGuards(AuthGuard)
export class InvoiceController {
    constructor(private readonly invoiceService: InvoiceService) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_LIST)
    async getInvoices(@Query() query: InvoiceQueryDto, @HeaderProvince() provinceId: string) {
        return this.invoiceService.getInvoices(query, provinceId);
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_ONE)
    async getInvoiceDetails(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        return this.invoiceService.getInvoiceDetails(id, provinceId);
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_UPDATE)
    async updateInvoice(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateData: UpdateInvoiceDto,
        @HeaderProvince() provinceId: string,
    ) {
        return this.invoiceService.updateInvoice(id, updateData, provinceId);
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_DELETE)
    async deleteInvoice(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        return this.invoiceService.deleteInvoice(id, provinceId);
    }

    @Post(':id/retry')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_UPDATE)
    async retryFailedInvoice(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        return this.invoiceService.retryFailedInvoice(id, provinceId);
    }

    @Post('sync-status')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_UPDATE)
    async syncInvoiceStatus(@Body() syncData: SyncInvoiceStatusDto, @HeaderProvince() provinceId: string) {
        return this.invoiceService.syncInvoiceStatus(syncData, provinceId);
    }
}
