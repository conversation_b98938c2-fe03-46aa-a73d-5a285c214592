import { Module } from '@nestjs/common';
import { InvoiceController } from './invoice.controller';
import { InvoiceService } from './invoice.service';
import { OrderModule } from '../order/order.module';
import { MisaHttpModule } from 'src/misaHttp/misaHttp.module';
import { AwsS3Module } from 'src/providers/aws/awsS3.module';

@Module({
    controllers: [InvoiceController],
    providers: [InvoiceService],
    imports: [OrderModule, MisaHttpModule, AwsS3Module],
    exports: [InvoiceService],
})
export class InvoiceModule {}
