import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, IsString, Min } from 'class-validator';
import { Transform } from "class-transformer";

export class GetMany9PayAccountLinkingWithPaginationDto {
    @IsNotEmpty()
    @Transform(({ value }) => +value)
    @IsNumber()
    @Min(0)
    page: number;

    @IsNotEmpty()
    @Transform(({ value }) => +value)
    @IsNumber()
    @Min(1)
    limit: number;

    @IsOptional()
    @IsString()
    phone: string;

    @IsOptional()
    @IsString()
    name: string;

    @IsOptional()
    @IsString()
    email: string;

    @IsOptional()
    status: string;
}