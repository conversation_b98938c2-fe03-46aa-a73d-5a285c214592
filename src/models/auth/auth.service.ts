import {
    ForbiddenException,
    HttpException,
    HttpStatus,
    Injectable,
    BadRequestException,
    NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcryptjs from 'bcryptjs';
import { Request } from 'express';
import * as _ from 'lodash';
import { LoginDto } from 'src/app.dto';
import { ERole } from 'src/entities/role.entity';
import * as jwt from 'jsonwebtoken';
import { UserService } from '../user/services/user.service';

@Injectable()
export class AuthService {
    constructor(
        private readonly userService: UserService,
        private readonly jwtService: JwtService,
    ) {}

    async login(ctx: Request, { username, password }: LoginDto): Promise<any> {
        // find user in db

        const user = await this.userService.getUserByEmail(ctx, username);
        if (
            !user ||
            _.isEmpty(user.roles) ||
            !user.roles.some(({ name }) => !([ERole.driver, ERole.client] as string[]).includes(name)) ||
            !bcryptjs.compareSync(password, user.password) ||
            user.is_locked
        ) {
            throw new ForbiddenException('wrong username or password');
        }

        let permissions = [];
        user.roles.forEach(({ permissions: p = [] }) => {
            permissions.push(...p.map(({ name }) => name));
        });
        permissions = _.uniq(permissions);

        // generate and sign token
        let data = _.pick(user, ['id', 'name', 'avatar', 'email', 'phone', 'created_at', 'updated_at']);

        data = _.assign(data, {
            roles: _.map(user.roles, (role) => ({ id: role.id, name: role.name })),
        });
        const provinceIds = _.union(
            _.flattenDeep(
                user.modelHasRoles.map(({ modelRolesProvinces }) =>
                    modelRolesProvinces.map(({ province_id }) => province_id.toString()),
                ),
            ),
        );
        const token = this.jwtService.sign(_.toPlainObject({ ...data, provinceIds }), {
            expiresIn: process.env.JWT_EXPIRE,
        });
        const refreshToken = jwt.sign(data, process.env.REFRESH_TOKEN_SECRET, {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRE,
        });
        return {
            ...user,
            token,
            refreshToken,
            permissions,
        };
    }

    async authenticate(accessToken: string): Promise<any> {
        try {
            const user = await this.jwtService.verify(accessToken);
            return user;
        } catch (e) {
            throw new ForbiddenException(e.message);
        }
    }

    async renewToken(refreshToken: string, ctx: Request) {
        let user: any = null;
        try {
            user = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
            delete user['iat'];
            delete user['exp'];
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.UNAUTHORIZED);
        }
        user = await this.userService.getUserByEmail(ctx, user.email);

        let token: any = null;
        if (user && !user.is_locked) {
            let data = _.pick(user, ['id', 'name', 'avatar', 'email', 'phone', 'created_at', 'updated_at']);

            data = _.assign(data, {
                roles: _.map(user.roles, (role) => ({ id: role.id, name: role.name })),
            });
            const provinceIds = _.union(
                _.flattenDeep(
                    user.modelHasRoles.map(({ modelRolesProvinces }) =>
                        modelRolesProvinces.map(({ province_id }) => province_id.toString()),
                    ),
                ),
            );
            token = jwt.sign({ ...data, provinceIds }, process.env.JWT_SECRET, {
                expiresIn: process.env.JWT_EXPIRE,
            });
        }
        return token;
    }
}
