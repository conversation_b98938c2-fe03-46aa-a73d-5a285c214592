import {
    <PERSON>,
    <PERSON>,
    Body,
    Req,
    Inject,
    BadRequestException,
    Logger,
    UnauthorizedException,
    HttpStatus,
} from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { Request } from 'express';

import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { LoginDto, RenewTokenDto } from './auth.dto';
import { AuthService } from './auth.service';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { ClientProxy } from '@nestjs/microservices';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';
import { UserJwt } from 'src/common/middlewares/auth.gaurd';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('auth')
export class AuthController {
    constructor(
        private readonly authService: AuthService,

        @Inject(DELIVERY_SERVICE) private readonly deliveryServiceClient: ClientProxy,
    ) {}

    async sendCommand<T>(cmd: string, data: unknown) {
        try {
            return this.deliveryServiceClient.send({ cmd }, data);
        } catch (error) {
            throw new BadRequestException(error);
        }
    }

    @Post('login')
    async login(@Body(new HttpValidationPipe()) body: LoginDto, @Req() req: Request) {
        return await this.authService.login(req, body);
    }

    @Post('renewToken')
    async renewToken(@Body(new HttpValidationPipe()) { refreshToken }: RenewTokenDto, @Req() ctx: Request) {
        return await this.authService.renewToken(refreshToken, ctx);
    }

    @Post('sign-in')
    async signIn(@Body(new HttpValidationPipe()) loginDto: LoginDto, @HeaderProvince() provinceId: string) {
        return await this.sendCommand('signIn', { ...loginDto, provinceId });
    }

    @Post('renew-token')
    async renewTokenEmployee(
        @Body(new HttpValidationPipe()) { refreshToken }: RenewTokenDto,
        @HeaderProvince() provinceId: string,
    ) {
        let user: UserJwt = null;
        try {
            user = jwt.verify(refreshToken, process.env.JWT_SECRET) as UserJwt;
        } catch (e) {
            throw new UnauthorizedException(e.message + ' refresh token');
        }
        if (!user || !user.email) {
            throw new UnauthorizedException('Token không hợp lệ');
        }
        return await this.sendCommand('renewToken', { email: user.email, provinceId });
    }
}
