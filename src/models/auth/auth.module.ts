import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerModule } from 'src/common/logger/logger.module';
import { AwsS3Module } from 'src/providers/aws/awsS3.module';
import { RabbitMQEventModule } from 'src/rabbitMQ/rabbitMQ.module';
import { UserActivityModule } from '../adminUserActivity/userActivity.module';
import { DriverModule } from '../driver/driver.module';
import { RoleModule } from '../role/role.module';
import { UserEventPublisherService } from '../user/publishers/userEvent.publisher';
import { UserService } from '../user/services/user.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

@Module({
    imports: [
        LoggerModule,
        PassportModule.register({
            defaultStrategy: 'jwt',
            property: 'user',
            session: false,
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get('jwtSecretKey'),
            }),
        }),
        AwsS3Module,
        DriverModule,
        RoleModule,
        UserActivityModule,
        RabbitMQEventModule,
    ],
    controllers: [AuthController],
    providers: [UserService, AuthService, ConfigService, UserEventPublisherService],
    exports: [JwtModule],
})
export class AuthModule {}
