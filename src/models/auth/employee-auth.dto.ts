import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class EmployeeSignInDto {
    @IsNotEmpty()
    @IsString()
    username: string;

    @IsNotEmpty()
    @IsString()
    password: string;

    @IsOptional()
    @IsString()
    provinceId: string;
}

export class RenewTokenDto {
    @IsNotEmpty()
    @IsString()
    refreshToken: string;

    @IsOptional()
    @IsString()
    provinceId: string;
}
