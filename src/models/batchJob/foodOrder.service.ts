import { Injectable } from '@nestjs/common';
import moment = require('moment');
import { FoodOrder } from 'src/entities/foodOrder.entity';
import { VietNamTimeZoneNum } from 'src/jobs';
import { DatabaseService } from 'src/providers/database/database.service';

@Injectable()
export class FoodOrderService {
    getRepository(provinceId: number) {
        return DatabaseService.getRepositoryByProvinceId(FoodOrder, provinceId.toString());
    }

    async getStartDate(provinceId: string) {
        const start_order = await DatabaseService.getRepositoryByProvinceId(FoodOrder, provinceId)
            .createQueryBuilder()
            .select('MIN(created_at) as created_at')
            .execute();
        let start_date = moment().utcOffset(VietNamTimeZoneNum).format('YYYY-MM-DD');
        if (start_order && start_order.length > 0) {
            start_date = moment(start_order[0].created_at).utcOffset(VietNamTimeZoneNum).format('YYYY-MM-DD');
        }
        return start_date;
    }
}
