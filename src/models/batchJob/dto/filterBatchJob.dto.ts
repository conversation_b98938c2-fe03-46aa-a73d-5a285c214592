import { IsNumber, IsOptional, IsString } from 'class-validator';
import { TinyInt } from 'src/common/constants';
import { BaseRPCQPayloadDto } from 'src/common/pipes/global.dto';

import {
    EBatchJobRestaurantInsightStatus,
    BatchJobRestaurantInsightType,
} from 'src/entities/batchJobRestaurantInsight.entity';

export class FilterBatchJobDto extends BaseRPCQPayloadDto {
    @IsOptional()
    @IsString()
    status?: EBatchJobRestaurantInsightStatus;

    @IsOptional()
    @IsString()
    type?: BatchJobRestaurantInsightType;

    @IsNumber()
    @IsOptional()
    isFailed?: TinyInt;

    @IsNumber()
    @IsOptional()
    parentId?: number;

    @IsNumber()
    @IsOptional()
    isAll?: TinyInt = TinyInt.NO;

    @IsOptional()
    @IsString()
    date?: string;
}
