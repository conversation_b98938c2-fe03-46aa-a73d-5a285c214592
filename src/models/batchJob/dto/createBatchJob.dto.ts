import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { BaseRPCQPayloadDto } from 'src/common/pipes/global.dto';
import { BatchJobRestaurantInsightType } from 'src/entities/batchJobRestaurantInsight.entity';

export class CreateBatchJobDto {
    @IsOptional()
    @IsString()
    @ApiProperty()
    name: string;

    @IsNotEmpty()
    @ApiProperty()
    @IsString()
    type: BatchJobRestaurantInsightType;

    @IsString()
    @IsOptional()
    @ApiProperty()
    from_date: string;

    @IsOptional()
    @IsString()
    @ApiProperty()
    to_date: string;

    @IsOptional()
    @IsString({ each: true })
    @ApiProperty()
    trade_discount_period_types?: string[];

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    restaurant_id?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    food_id?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    merchant_id?: number;

    @IsOptional()
    @ApiProperty()
    restaurant_ids?: number[];

    @IsOptional()
    @ApiProperty()
    template_sheet?: string;

    @IsOptional()
    @ApiProperty()
    payment_types?: string[];

    @IsOptional()
    @ApiProperty()
    province_ids?: number[];

    @IsOptional()
    @ApiProperty()
    order_status?: number;

    @IsOptional()
    @ApiProperty()
    payment_status?: string;

    @IsOptional()
    cod_tax_percent?: number;

    @IsOptional()
    excluded_restaurant_ids?: number[];

    @IsOptional()
    min_order?: number;

    @IsOptional()
    driver_max_income?: number;

    @IsOptional()
    high_revenue_restaurants?: number[];

    @IsOptional()
    high_revenue?: number;

    @IsOptional()
    high_revenue_cod_tax_percent?: number;

    @IsOptional()
    bike_car_express_cod_tax_order_percent?: number;

    @IsOptional()
    types?: string[];

    @IsOptional()
    statuses?: string[];

    @IsOptional()
    driver_order_bonus?: number;

    @IsOptional()
    aggregation_id?: number;

    @IsOptional()
    batch_size?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    vat_rate?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    driver_delivery_vat_rate?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    driver_surcharge_vat_rate?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    vill_delivery_vat_rate?: number;

    @IsOptional()
    @ApiProperty()
    @IsNumber()
    vill_service_fee_vat_rate?: number;
}

export interface IContext {
    from_date: string;
    to_date: string;
    restaurant_id?: number;
    food_id?: number;
    merchant_id?: number;
    trade_discount_period_types?: string[];
    restaurant_ids?: number[];
    template_sheet?: string;

    payment_types?: string[];
    province_ids?: number[];
    order_status?: number;
    cod_tax_percent?: number;
    excluded_restaurant_ids?: number[];
    min_order?: number;
    driver_max_income?: number;
    high_revenue?: number;
    high_revenue_cod_tax_percent?: number;
    bike_car_express_cod_tax_order_percent?: number;
    types?: string[];
    statuses?: string[];
    order_trade_discount_period_types?: string[];
    driver_order_bonus?: number;
    aggregation_id?: number;
    batch_size?: number;
    vat_rate?: number;
    driver_delivery_vat_rate?: number;
    driver_surcharge_vat_rate?: number;
    vill_delivery_vat_rate?: number;
    vill_service_fee_vat_rate?: number;
    payment_status?: string;
}

export class CreateBatchJobPayload extends BaseRPCQPayloadDto {
    name: string;
    type: string;
    context: IContext;
}
