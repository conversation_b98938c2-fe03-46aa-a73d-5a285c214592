import { IsN<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';

export class CreateBatchJobContextDto {
    @IsString()
    from_date: string;

    @IsString()
    to_date: string;

    // @IsEnum(IntervalType)
    // interval_type: IntervalType;

    // @ValidateIf((obj: CreateBatchJobDto, options) => {
    //     console.log(
    //         'CreateBatchJobContextDto obj',
    //         JSON.stringify(obj),
    //         JSON.stringify(options)
    //     );
    //     return true;
    // })\
    // @Validate(CustomTextLength)
    @IsOptional()
    @IsNumber()
    restaurant_id?: number;
}
