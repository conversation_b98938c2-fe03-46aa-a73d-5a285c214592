import {
    Controller,
    Get,
    Inject,
    Param,
    ParseIntPipe,
    UseInterceptors,
    Query,
    Post,
    Put,
    Body,
    UseGuards,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { SERVICE_NAME } from 'src/common/constants/serviceName.constant';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';

@Controller('restaurant-taxes')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class RestaurantTaxController {
    constructor(@Inject(SERVICE_NAME.INSIGHT_SERVICE) private client: ClientProxy) {}
    @Get()
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getRestaurantTaxAggregation(@Query() query: any) {
        return this.client.send({ cmd: 'restaurantTax.get-list-aggregation' }, query);
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_ONE)
    async getRestaurantTaxDetail(@Param('id', new ParseIntPipe()) id: number) {
        return this.client.send({ cmd: 'restaurantTax.get-detail' }, { id });
    }

    @Post(':id/misa-export')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_CREATE)
    async createMisaExportJob(
        @Param('id', new ParseIntPipe()) id: number,
        @Body()
        body: {
            send_email?: boolean;
            restaurant_ids?: number[];
            batch_size?: number;
        },
    ) {
        return this.client.send(
            { cmd: 'restaurantTax.create-misa-export-job' },
            {
                aggregation_id: id,
                send_email: body.send_email,
                restaurant_ids: body.restaurant_ids,
                batch_size: body.batch_size,
            },
        );
    }

    @Get(':id/misa-invoices')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getMisaInvoices(
        @Param('id', new ParseIntPipe()) id: number,
        @Query() query: { page?: number; limit?: number; status?: string; restaurant_id?: number },
    ) {
        return this.client.send({ cmd: 'restaurantTax.get-misa-invoices' }, { aggregation_id: id, ...query });
    }

    @Get(':id/misa-stats')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_ONE)
    async getMisaInvoiceStats(@Param('id', new ParseIntPipe()) id: number) {
        return this.client.send({ cmd: 'restaurantTax.get-misa-invoice-stats' }, { aggregation_id: id });
    }

    @Post(':id/misa-retry')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_CREATE)
    async retryFailedMisaInvoices(@Param('id', new ParseIntPipe()) id: number) {
        return this.client.send({ cmd: 'restaurantTax.retry-failed-misa' }, { id });
    }

    @Post(':id/misa-send-email')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_CREATE)
    async sendMisaEmail(@Param('id', new ParseIntPipe()) id: number, @Body() body: { invoice_ids?: number[] }) {
        return this.client.send(
            { cmd: 'restaurantTax.send-misa-email' },
            { aggregation_id: id, invoice_ids: body.invoice_ids },
        );
    }

    @Get('misa-invoices/by-type/:type')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getMisaInvoicesByType(
        @Param('type') type: string,
        @Query()
        query: {
            page?: number;
            limit?: number;
            status?: string;
            entity_id?: number;
            restaurant_id?: number;
        },
    ) {
        return this.client.send({ cmd: 'restaurantTax.get-misa-invoices-by-type' }, { type, ...query });
    }

    @Put('misa-invoices/:refId/buyer-info')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async updateMisaBuyerInfo(
        @Param('refId') refId: string,
        @Body()
        body: {
            buyer_name?: string;
            buyer_tax_code?: string;
            buyer_email?: string;
            buyer_phone?: string;
            buyer_address?: string;
        },
    ) {
        return this.client.send({ cmd: 'restaurantTax.update-misa-buyer-info' }, { ref_id: refId, ...body });
    }

    @Put('misa-invoices/:refId/email-status')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async updateMisaEmailStatus(@Param('refId') refId: string, @Body() body: { email_sent: boolean }) {
        return this.client.send(
            { cmd: 'restaurantTax.update-misa-email-status' },
            { ref_id: refId, email_sent: body.email_sent },
        );
    }

    @Get('misa-invoices/email-status/:status')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getMisaInvoicesByEmailStatus(@Param('status') status: string) {
        const emailSent = status === 'sent';
        return this.client.send({ cmd: 'restaurantTax.get-misa-invoices-by-email-status' }, { email_sent: emailSent });
    }

    @Get(':id/restaurants')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getRestaurantTaxList(@Param('id', new ParseIntPipe()) id: number, @Query() query: any) {
        return this.client.send({ cmd: 'restaurantTax.get-list-restaurant' }, { aggregation_id: id, ...query });
    }

    @Get(':id/merchants')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getMerchantList(@Param('id', new ParseIntPipe()) id: number, @Query() query: any) {
        return this.client.send({ cmd: 'restaurantTax.get-list-merchant' }, { aggregation_id: id, ...query });
    }

    @Get(':id/driver-summary/:provinceId')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_FIND_LIST)
    async getDriverSummary(
        @Param('id', new ParseIntPipe()) id: number,
        @Param('provinceId', new ParseIntPipe()) provinceId: number,
    ) {
        return this.client.send(
            { cmd: 'restaurantTax.get-driver-summary-by-province' },
            { aggregation_id: id, province_id: provinceId },
        );
    }

    @Post('download')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async downloadRestaurantTax(@Body() body: any) {
        return this.client.send({ cmd: 'restaurantTax.download' }, body);
    }

    @Post('send-email')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async sendEmail(@Body() body: any) {
        return this.client.send({ cmd: 'restaurantTax.send_merchant_revenue_mail' }, body);
    }

    @Post('send-revenue-verify-email')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async sendEmailVerify(@Body() body: any) {
        return this.client.send({ cmd: 'restaurantTax.send_merchant_revenue_verify_revenue_mail' }, body);
    }

    @Post('download-revenue-verify-file')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async downloadRevenueVerifyFile(@Body() body: any) {
        return this.client.send({ cmd: 'restaurantTax.download_revenue_verify_file' }, body);
    }

    @Post(':id/send-verification-email')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async sendVerificationEmail(@Param('id', new ParseIntPipe()) id: number) {
        return this.client.send({ cmd: 'restaurantTax.send_verification_email' }, { aggregation_id: id });
    }

    @Post(':id/send-revenue-email')
    @RequirePermissions(PermissionsAccessAction.RESTAURANT_TAX_UPDATE)
    async sendRevenueEmail(@Param('id', new ParseIntPipe()) id: number) {
        return this.client.send({ cmd: 'restaurantTax.send_revenue_email' }, { aggregation_id: id });
    }
}
