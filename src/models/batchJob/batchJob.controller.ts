import {
    Body,
    Controller,
    Delete,
    Get,
    Inject,
    Logger,
    Param,
    Post,
    Put,
    Query,
    UseInterceptors,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { ApiHeader, ApiResponse, ApiTags } from '@nestjs/swagger';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { SERVICE_NAME } from 'src/common/constants/serviceName.constant';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { BaseRPCQPayloadDto, SortedByEnum } from 'src/common/pipes/global.dto';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import {
    BatchJobRestaurantInsight,
    BatchJobRestaurantInsightType,
} from 'src/entities/batchJobRestaurantInsight.entity';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { Restaurant, RestaurantTradeDiscountPeriodType } from 'src/entities/restaurant.entity';
import { VietNamTimeZoneNum } from 'src/jobs';
import { DatabaseService } from 'src/providers/database/database.service';
import { In, UpdateResult } from 'typeorm';
import {
    CreateBatchJobDto,
    CreateBatchJobPayload,
    FilterBatchJobDto,
    RetryBatchJobDto,
    ReTryBatchJobPayload,
    UpdateBatchJobDto,
} from './dto';
import { PauseBatchJobDto, PauseBatchJobPayload } from './dto/pauseBatchJob.dto';
import { FoodOrderService } from './foodOrder.service';
import moment = require('moment');
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@ApiTags('Batch Jobs')
@ApiHeader({
    name: 'x-province',
})
@UseInterceptors(LoggingInterceptor)
@Controller('batch-jobs')
export class BatchJobController {
    private logger = new Logger(BatchJobController.name);
    constructor(
        @Inject(SERVICE_NAME.INSIGHT_SERVICE) private client: ClientProxy,
        private foodOrderService: FoodOrderService,
    ) {}

    @RequirePermissions(PermissionsAccessAction.BATCH_JOBS_FIND_LIST)
    @ApiResponse({})
    @Get()
    findChildrenById(
        @HeaderProvince() provinceId: string,
        @Query() query: FilterBatchJobDto,
    ): Observable<{ items: BatchJobRestaurantInsight[]; total: number; parent_id: number }> {
        const {
            limit = 20,
            page = 0,
            orderBy = 'id',
            sortedBy = SortedByEnum.DESC,
            parentId,
            status,
            type,
            isAll,
            date,
        } = query;

        const payload: FilterBatchJobDto = {
            provinceId: +provinceId,
            limit,
            page,
            orderBy,
            sortedBy,
            parentId: +parentId,
            status,
            type,
            isAll: +isAll,
            date,
        };

        return this.client.send(
            {
                cmd: 'batch.find',
            },
            _.omitBy(payload, _.isNull),
        );
    }
    @ApiResponse({})
    @Get('/count')
    count(@HeaderProvince() provinceId: string, @Query() query: FilterBatchJobDto) {
        const { parentId } = query;
        const payload: FilterBatchJobDto = {
            provinceId: +provinceId,
            parentId: +parentId,
        };
        return this.client.send({ cmd: 'batch.count' }, payload);
    }

    @ApiResponse({})
    @Get('/:id')
    findOne(@Param('id') id: number, @HeaderProvince() provinceId: string) {
        const payload: BaseRPCQPayloadDto = {
            provinceId: +provinceId,

            id: +id,
        };
        return this.client.send({ cmd: 'batch.findOne' }, payload);
    }

    @ApiResponse({})
    @Put('/:id')
    update(
        @Param('id') id: number,
        @Body(new HttpValidationPipe()) body: UpdateBatchJobDto,
        @HeaderProvince() provinceId: string,
    ): Observable<UpdateResult> {
        const { status, result } = body;
        const payload: UpdateBatchJobDto = {
            batchJobId: id.toString(),
            status,
            result,
            provinceId: parseInt(provinceId),
        };
        return this.client.send(
            {
                cmd: 'batch.update',
            },
            payload,
        );
    }

    @ApiResponse({})
    @Post()
    async create(@Body(new HttpValidationPipe()) body: CreateBatchJobDto, @HeaderProvince() provinceId: string) {
        const {
            name,
            type,
            restaurant_id,
            from_date,
            to_date,
            merchant_id,
            trade_discount_period_types,
            restaurant_ids,
            template_sheet,
            food_id,

            payment_types,
            province_ids,
            order_status,
            payment_status,
            cod_tax_percent,
            excluded_restaurant_ids,
            min_order,
            driver_max_income,
            high_revenue,
            high_revenue_cod_tax_percent,
            bike_car_express_cod_tax_order_percent,
            types,
            statuses,
            driver_order_bonus,
            aggregation_id,
            batch_size,
            vat_rate,
            driver_delivery_vat_rate,
            driver_surcharge_vat_rate,
            vill_delivery_vat_rate,
            vill_service_fee_vat_rate,
        } = body;

        const payload: CreateBatchJobPayload = {
            name,
            type,
            context: {
                from_date,
                to_date,
                restaurant_id,
                merchant_id,
                trade_discount_period_types,
                restaurant_ids,
                template_sheet,
                food_id: food_id ? +food_id : undefined,

                payment_types,
                province_ids,
                order_status,
                cod_tax_percent,
                excluded_restaurant_ids,
                min_order,
                driver_max_income,
                high_revenue,
                high_revenue_cod_tax_percent,
                bike_car_express_cod_tax_order_percent,
                types,
                statuses,
                driver_order_bonus,
                aggregation_id,
                batch_size,
                vat_rate,
                driver_delivery_vat_rate,
                driver_surcharge_vat_rate,
                vill_delivery_vat_rate,
                vill_service_fee_vat_rate,
                payment_status,
            },
            provinceId: +provinceId,
        };

        if (type === BatchJobRestaurantInsightType.AGG_QUANTITY_SOLD_FOODS_BY_RANGE_TIME) {
            const start_date = await this.foodOrderService.getStartDate(provinceId);
            const toDate = moment().utcOffset(VietNamTimeZoneNum).format('YYYY-MM-DD');
            payload.context.from_date = start_date;
            payload.context.to_date = toDate;
        }
        if (template_sheet === 'SHEET_RESTAURANT') {
            this.handleCreateBatchJobsRestaurantPerSheet(body, provinceId);
            return 'success';
        } else {
            return this.client.send(
                {
                    cmd: 'batch.create',
                },
                _.omitBy(payload, _.isNull),
            );
        }
    }
    @ApiResponse({})
    @Post('/:id/put-pending-to-queue')
    putQueu(@Param('id') id: number, @HeaderProvince() provinceId: string) {
        const payload: BaseRPCQPayloadDto = {
            id,
            provinceId: +provinceId,
        };
        this.logger.verbose(JSON.stringify(payload));
        return this.client.send(
            {
                cmd: 'batch.putJobsPendingToQueue',
            },
            payload,
        );
    }
    @ApiResponse({})
    @Post('/retry')
    retry(@Body() body: RetryBatchJobDto, @HeaderProvince() provinceId: string) {
        const payload: ReTryBatchJobPayload = {
            batchJobsId: body.batchJobsId.map((id) => id.toString()),
            provinceId: +provinceId,
        };
        return this.client.send(
            {
                cmd: 'batch.retry',
            },
            payload,
        );
    }

    @Put('/:id/pause')
    pause(
        @Param('id') id: number,
        @Body(new HttpValidationPipe()) body: PauseBatchJobDto,
        @HeaderProvince() provinceId: string,
    ) {
        const payload: PauseBatchJobPayload = {
            id,
            provinceId: +provinceId,
            paused: body.paused,
        };
        return this.client.send(
            {
                cmd: 'batch.pause',
            },
            payload,
        );
    }

    @Delete('/:id')
    delete(@Param('id') id: number, @HeaderProvince() provinceId: string) {
        const payload: BaseRPCQPayloadDto = {
            id,
            provinceId: +provinceId,
        };
        return this.client.send(
            {
                cmd: 'batch.delete',
            },
            payload,
        );
    }
    async handleCreateBatchJobsRestaurantPerSheet(payload: CreateBatchJobDto, provinceId: string) {
        let restaurant_ids = payload.restaurant_ids;
        if (payload.trade_discount_period_types) {
            const restaurants = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).find({
                where: {
                    trade_discount_period_type: In(
                        payload.trade_discount_period_types as RestaurantTradeDiscountPeriodType[],
                    ),
                },
            });
            restaurant_ids = restaurants.map((restaurant) => restaurant.id);
        }
        const { from_date, to_date, trade_discount_period_types } = payload;
        for (const restaurant_id of restaurant_ids) {
            const payload: CreateBatchJobPayload = {
                name: 'Export dữ liệu nhà hàng',
                type: 'AGG_RESTAURANT_ORDER_EXPORT_DATE',
                context: {
                    from_date,
                    to_date,
                    restaurant_id,
                    trade_discount_period_types,
                },
                provinceId: +provinceId,
            };
            //log
            this.client
                .send(
                    {
                        cmd: 'batch.create',
                    },
                    _.omitBy(payload, _.isNull),
                )
                .subscribe();
        }
    }
}
