import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { SERVICE_NAME } from 'src/common/constants/serviceName.constant';
import { BatchJobController } from './batchJob.controller';
import { FoodOrderService } from './foodOrder.service';
import { RestaurantTaxController } from './controller/restaurantTax.controller';

@Module({
    imports: [],
    providers: [
        {
            provide: SERVICE_NAME.INSIGHT_SERVICE,
            useFactory: (configService: ConfigService) => {
                const clientProxy = ClientProxyFactory.create({
                    transport: Transport.TCP,
                    options: {
                        host: configService.get('insightHost'),
                        port: configService.get('insightPort'),
                    },
                });

                setInterval(async () => {
                    try {
                        clientProxy.emit({ cmd: 'ping' }, '').subscribe({
                            next: () => {},
                            complete: () => {},
                            error: (err) => {
                                Logger.error(
                                    `Insight service is not available | message: ${err.message} | stack: ${err.stack}`,
                                );
                            },
                        });
                    } catch (error) {
                        Logger.error(
                            `Insight service is not available | message: ${error.message} | stack: ${error.stack}`,
                        );
                    }
                }, 60000);
                return clientProxy;
            },
            inject: [ConfigService],
        },
        FoodOrderService,
    ],

    controllers: [BatchJobController, RestaurantTaxController],
    exports: [],
})
export class BatchJobModule {}
