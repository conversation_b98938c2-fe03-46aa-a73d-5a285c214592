import { Injectable, Logger } from '@nestjs/common';
import { EntityManager, UpdateResult } from 'typeorm';
import { AdsContractInfo, EAdsContractStatus } from 'src/entities/adsContractInfo.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { UpdateAdsContractDto } from '../dto/adsContract.dto';
import { QueryPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class AdsContractInfoService {
    private readonly logger = new Logger(AdsContractInfoService.name);
    constructor() {}

    async getAdsContractInfoByCampaignId(campaignId: number, provinceId: string): Promise<AdsContractInfo> {
        if (!campaignId) {
            return null;
        }
        return await DatabaseService.getRepositoryByProvinceId(AdsContractInfo, provinceId).findOne({
            where: { ads_campaign_id: campaignId },
        });
    }

    /**
     * Updates AdsContractInfo for a given campaign.
     * @param provinceId - The province ID to get the database connection.
     * @param campaignId - The ID of the campaign.
     * @param contractId - The ID of the contract.
     * @param contractStatus - The status of the contract.
     * @returns The updated AdsContractInfo entity.
     */
    async updateAdsContractInfo(payload: UpdateAdsContractDto): Promise<UpdateResult> {
        const { provinceId, campaignId, contractId, contractStatus, message } = payload;
        const contractInfo = await DatabaseService.getRepositoryByProvinceId(AdsContractInfo, provinceId).findOne({
            where: { ads_campaign_id: campaignId },
        });
        if (!contractInfo) {
            throw new Error(`AdsContractInfo not found for campaignId: ${campaignId}`);
        }
        // Update existing contract info
        const updateValues: QueryPartialEntity<AdsContractInfo> = {};
        if (contractId) {
            updateValues.contract_id = contractId;
        }
        if (contractStatus) {
            updateValues.contract_status = contractStatus;
        }
        if (message) {
            updateValues.message = message;
        }
        return await DatabaseService.getRepositoryByProvinceId(AdsContractInfo, provinceId)
            .createQueryBuilder()
            .update()
            .set(updateValues)
            .where({ ads_campaign_id: campaignId })
            .execute();
    }

    /**
     * Creates or retrieves AdsContractInfo for a given campaign.
     * @param transaction - The transaction manager for database operations.
     * @param campaignId - The AdsCampaignId.
     * @returns The AdsContractInfo entity.
     */
    async CheckExistOrCreateAdsContractInfoWithTW(
        transaction: EntityManager,
        campaignId: number,
    ): Promise<AdsContractInfo> {
        const contractInfo = await transaction
            .getRepository(AdsContractInfo)
            .findOne({ where: { ads_campaign_id: campaignId } });
        if (contractInfo) {
            return contractInfo;
        }
        const newContractInfo = new AdsContractInfo({
            ads_campaign_id: campaignId,
            contract_status: EAdsContractStatus.NOT_YET_CREATED,
        });
        return await transaction.getRepository(AdsContractInfo).save(newContractInfo);
    }
}
