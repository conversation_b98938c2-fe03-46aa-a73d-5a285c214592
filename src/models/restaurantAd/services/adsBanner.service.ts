import { Injectable, Logger } from '@nestjs/common';
import { AdsBannertemplate } from 'src/entities/adsBannerTemplate.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { CreateAdsBannerTemplateDto, GetAdsBannerTemplatesDto } from '../dto/adsBannerTemplate.dto';
import { CreateAdBannerDto } from 'src/models/bannerGroup/bannerGroup.dto';
import { AdBanner } from 'src/entities/adBanner.entity';
import { TinyInt } from 'src/common/constants';
import { BannerGroup } from 'src/entities/bannerGroup.entity';
import { GetAdsBannerGroupDto } from '../dto/adsBannerGroupDto';
import { EntityManager } from 'typeorm';
@Injectable()
export class AdsBannerService {
    private readonly logger = new Logger(AdsBannerService.name);

    async getAdsBannerTemplateById(id: number, provinceId: string): Promise<AdsBannertemplate> {
        return await DatabaseService.getRepositoryByProvinceId(AdsBannertemplate, provinceId).findOne({
            where: { id },
        });
    }

    async getAdsBannerTemplates(
        provinceId: string,
        filters: Partial<GetAdsBannerTemplatesDto> = {},
    ): Promise<AdsBannertemplate[]> {
        const {
            title, // from BaseQueryFilterDto
            position_slot,
            is_active,
            orderBy = 'id',
            sortedBy = 'ASC',
            limit,
            page,
        } = filters;
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsBannertemplate, provinceId)
            .createQueryBuilder('adsBannerTemplate')
            .skip((page - 1) * limit)
            .take(limit)
            .orderBy({ [`adsBannerTemplate.${orderBy}`]: sortedBy });

        if (title) {
            queryBuilder.andWhere('adsBannerTemplate.title LIKE :title', { title: `%${title}%` });
        }
        if (position_slot) {
            queryBuilder.andWhere('adsBannerTemplate.position_slot = :position_slot', { position_slot });
        }
        if (is_active !== undefined) {
            queryBuilder.andWhere('adsBannerTemplate.is_active = :is_active', { is_active });
        }

        return await queryBuilder.getMany();
    }

    async createAdsBannerTemplate(provinceId: string, dto: CreateAdsBannerTemplateDto): Promise<AdsBannertemplate> {
        return await DatabaseService.getRepositoryByProvinceId(AdsBannertemplate, provinceId).save(dto);
    }

    async updateAdsBannerTemplate(
        provinceId: string,
        id: number,
        dto: Partial<CreateAdsBannerTemplateDto>,
    ): Promise<AdsBannertemplate> {
        await DatabaseService.getRepositoryByProvinceId(AdsBannertemplate, provinceId).update(id, dto);
        return await this.getAdsBannerTemplateById(id, provinceId);
    }

    async deleteAdsBannerTemplate(provinceId: string, id: number): Promise<void> {
        await DatabaseService.getRepositoryByProvinceId(AdsBannertemplate, provinceId).delete(id);
    }

    async createAdsBanner(params: CreateAdBannerDto, provinceId: string) {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            return await this.createAdsBannerWT(entityManager, params);
        });
    }

    async createAdsBannerWT(entityManager: EntityManager, params: CreateAdBannerDto) {
        const newAdBanner = new AdBanner({
            ...params,
            is_global: TinyInt.NO,
        });

        return await entityManager.getRepository(AdBanner).save(newAdBanner);
    }

    async getBannerGroupV3(dto: GetAdsBannerGroupDto, provinceId: string) {
        const { adsCampaignItemId, position, page_display } = dto;

        const queryBuilder = DatabaseService.getRepositoryByProvinceId(BannerGroup, provinceId)
            .createQueryBuilder('bannerGroup')
            .leftJoinAndSelect('bannerGroup.collection', 'collection');

        if (adsCampaignItemId) {
            queryBuilder.andWhere('bannerGroup.ads_campaign_item_id = :adsCampaignItemId', { adsCampaignItemId });
        }

        if (position) {
            queryBuilder.andWhere('bannerGroup.position = :position', { position });
        }

        if (page_display) {
            queryBuilder.andWhere('bannerGroup.page_display = :page_display', { page_display });
        }

        return await queryBuilder.getMany();
    }

    async getBannerGroupDetails(id: number, provinceId: string) {
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(BannerGroup, provinceId)
            .createQueryBuilder('bannerGroup')
            .leftJoinAndSelect('bannerGroup.collection', 'collection')
            .leftJoinAndSelect('bannerGroup.items', 'items')
            .where('bannerGroup.id = :id', { id });

        return await queryBuilder.getOne();
    }

    async getBannerDetails(id: number, provinceId: string) {
        return await DatabaseService.getRepositoryByProvinceId(AdBanner, provinceId).findOne({
            where: { id },
        });
    }

    async updateIsActivatedBanner(provinceId: string, id: number, is_activated: boolean) {
        const adBanner = await DatabaseService.getRepositoryByProvinceId(AdBanner, provinceId).findOne({
            where: { id },
        });
        if (!adBanner) {
            return;
        }
        adBanner.is_activated = is_activated;
        return await DatabaseService.getRepositoryByProvinceId(AdBanner, provinceId).save(adBanner);
    }
}
