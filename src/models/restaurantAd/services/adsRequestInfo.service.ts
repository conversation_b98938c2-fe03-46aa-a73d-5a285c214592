import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
import { AdsRequestInfo } from 'src/entities/adsRequestInfo.entity';
import { CreateAdsRequestInfoDto } from '../dto/adsRequestinfo.dto';
import { EntityManager } from 'typeorm';

@Injectable()
export class AdsRequestInfoService {
    constructor() {}

    async createAdsRequestInfoByTransaction(
        transaction: EntityManager,
        dto: CreateAdsRequestInfoDto,
    ): Promise<AdsRequestInfo> {
        return await transaction.getRepository(AdsRequestInfo).save(dto);
    }
}
