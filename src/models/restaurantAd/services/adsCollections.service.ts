import { BadRequestException, Injectable } from '@nestjs/common';
import { EntityManager, In } from 'typeorm';
import { DatabaseService } from 'src/providers/database/database.service';
import { AdsCollection } from 'src/entities/adsCollections.entity';
import { AdsCategory } from 'src/entities/adsCategoryV2.entity';
import { AdsCollectionCategory } from 'src/entities/adsCollectionCategory.entity';
import {
    CreateAdsCollectionDto,
    UpdateAdsCollectionDto,
    GetAdsCollectionsDto,
    AssignCategoriesToCollectionDto,
    RemoveCategoriesFromCollectionDto,
} from '../dto/adsCollections.dto';

@Injectable()
export class AdsCollectionsService {
    /**
     * Get all ads collections with optional filters
     * @param provinceId - Province ID
     * @param filters - Query filters
     * @returns Promise<AdsCollection[]>
     */
    async getCollections(provinceId: string, filters: Partial<GetAdsCollectionsDto> = {}): Promise<AdsCollection[]> {
        const {
            name, // from BaseQueryFilterDto
            code,
            is_active,
            display_type,
            include_categories = false,
            orderBy = 'ordinal_number',
            sortedBy = 'ASC',
            limit,
            page = 0,
        } = filters;

        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsCollection, provinceId).createQueryBuilder(
            'collection',
        );

        // Include categories if requested (Many-to-Many support with backward compatibility)
        if (include_categories) {
            queryBuilder
                .leftJoinAndSelect('collection.collection_categories', 'collection_categories', 'collection_categories.is_active = 1')
                .leftJoinAndSelect('collection_categories.ads_category', 'categories', 'categories.is_active = 1')
                .addOrderBy('collection_categories.ordinal_number', 'ASC')
                .addOrderBy('categories.name', 'ASC');
        }

        // Apply filters
        if (name) {
            queryBuilder.andWhere('collection.name LIKE :name', { name: `%${name}%` });
        }

        if (code) {
            queryBuilder.andWhere('collection.code = :code', { code });
        }

        if (is_active !== undefined) {
            queryBuilder.andWhere('collection.is_active = :is_active', { is_active });
        }

        if (display_type) {
            queryBuilder.andWhere('collection.display_type = :display_type', { display_type });
        }

        // Apply ordering
        queryBuilder.orderBy(`collection.${orderBy}`, sortedBy);

        // Apply pagination if specified
        if (limit) {
            queryBuilder.skip(page * limit).take(limit);
        }

        const collections = await queryBuilder.getMany();

        // Transform data to maintain backward compatibility with FE
        if (include_categories) {
            return this.transformCollectionsForBackwardCompatibility(collections);
        }

        return collections;
    }

    /**
     * Transform collections data to maintain backward compatibility with FE
     * Convert collection_categories -> ads_categories format
     * @param collections - Collections with collection_categories
     * @returns Collections with ads_categories format
     */
    private transformCollectionsForBackwardCompatibility(collections: AdsCollection[]): AdsCollection[] {
        return collections.map(collection => {
            // Extract categories from collection_categories and flatten to ads_categories
            const ads_categories = collection.collection_categories
                ?.filter(cc => cc.is_active === 1 && cc.ads_category?.is_active === 1)
                ?.map(cc => {
                    // Transform category to include collection_id for backward compatibility
                    const category = { ...cc.ads_category };
                    category.collection_id = collection.id;
                    return category;
                })
                ?.sort((a, b) => {
                    // Sort by ordinal_number from junction table, then by name
                    const aOrdinal = collection.collection_categories?.find(cc => cc.category_id === a.id)?.ordinal_number || 0;
                    const bOrdinal = collection.collection_categories?.find(cc => cc.category_id === b.id)?.ordinal_number || 0;
                    if (aOrdinal !== bOrdinal) return aOrdinal - bOrdinal;
                    return (a.name || '').localeCompare(b.name || '');
                }) || [];

            // Return collection with ads_categories instead of collection_categories
            const transformedCollection = { ...collection };
            transformedCollection.ads_categories = ads_categories;

            // Remove collection_categories to avoid confusion
            delete (transformedCollection as any).collection_categories;

            return transformedCollection;
        });
    }

    /**
     * Get ads collection by ID
     * @param provinceId - Province ID
     * @param id - Collection ID
     * @param includeCategories - Whether to include categories
     * @returns Promise<AdsCollection>
     */
    async getCollectionById(provinceId: string, id: number, includeCategories = true): Promise<AdsCollection> {
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsCollection, provinceId)
            .createQueryBuilder('collection')
            .where('collection.id = :id', { id });

        if (includeCategories) {
            // Use many-to-many relationship
            queryBuilder
                .leftJoinAndSelect('collection.collection_categories', 'collection_categories', 'collection_categories.is_active = 1')
                .leftJoinAndSelect('collection_categories.ads_category', 'categories', 'categories.is_active = 1')
                .addOrderBy('collection_categories.ordinal_number', 'ASC')
                .addOrderBy('categories.name', 'ASC');
        }

        const collection = await queryBuilder.getOne();

        if (!collection) {
            throw new BadRequestException(`Không tìm thấy collection với ID: ${id}`);
        }

        // Transform data for backward compatibility if categories are included
        if (includeCategories && collection.collection_categories) {
            const transformedCollections = this.transformCollectionsForBackwardCompatibility([collection]);
            return transformedCollections[0];
        }

        return collection;
    }

    /**
     * Create new ads collection
     * @param provinceId - Province ID
     * @param dto - Create collection DTO
     * @returns Promise<AdsCollection>
     */
    async createCollection(provinceId: string, dto: CreateAdsCollectionDto): Promise<AdsCollection> {
        try {
            const collection = await DatabaseService.getConnectionByProvinceId(provinceId).transaction(
                async (entityManager) => {
                    // Check if code already exists
                    const existingCollection = await entityManager.getRepository(AdsCollection).findOne({
                        where: { code: dto.code },
                    });

                    if (existingCollection) {
                        throw new BadRequestException(`Collection với code '${dto.code}' đã tồn tại`);
                    }

                    // Create collection
                    const collection = await entityManager.getRepository(AdsCollection).save(
                        new AdsCollection({
                            name: dto.name,
                            code: dto.code,
                            description: dto.description,
                            image: dto.image,
                            icon: dto.icon,
                            ordinal_number: dto.ordinal_number || 0,
                            display_type: dto.display_type,
                            metadata: dto.metadata,
                            is_active: 1,
                        }),
                    );

                    // Assign categories if provided
                    if (dto.category_ids?.length) {
                        await this.assignCategoriesToCollectionWithTW(
                            entityManager,
                            provinceId,
                            collection.id,
                            dto.category_ids,
                        );
                    }
                    return collection;
                },
            );

            return await this.getCollectionById(provinceId, collection.id);
        } catch (error) {
            throw new BadRequestException(`Không thể tạo collection: ${error.message}`);
        }
    }

    /**
     * Update ads collection
     * @param provinceId - Province ID
     * @param id - Collection ID
     * @param dto - Update collection DTO
     * @returns Promise<AdsCollection>
     */
    async updateCollection(provinceId: string, id: number, dto: UpdateAdsCollectionDto): Promise<AdsCollection> {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            const collection = await entityManager.getRepository(AdsCollection).findOne({ where: { id } });

            if (!collection) {
                throw new BadRequestException(`Không tìm thấy collection với ID: ${id}`);
            }

            // Check if code already exists (if updating code)
            if (dto.code && dto.code !== collection.code) {
                const existingCollection = await entityManager.getRepository(AdsCollection).findOne({
                    where: { code: dto.code },
                });

                if (existingCollection) {
                    throw new BadRequestException(`Collection với code '${dto.code}' đã tồn tại`);
                }
            }

            // Update collection
            await entityManager.getRepository(AdsCollection).update(id, {
                name: dto.name,
                code: dto.code,
                description: dto.description,
                image: dto.image,
                icon: dto.icon,
                ordinal_number: dto.ordinal_number,
                display_type: dto.display_type,
                is_active: dto.is_active,
                metadata: dto.metadata,
            });

            // Update categories if provided (Many-to-Many)
            if (dto.category_ids !== undefined) {
                // Use replaceExisting=true to replace all current assignments
                await this.assignCategoriesToCollectionWithTW(
                    entityManager,
                    provinceId,
                    id,
                    dto.category_ids,
                    true // replaceExisting
                );
            }

            return await this.getCollectionById(provinceId, id);
        });
    }

    /**
     * Delete ads collection
     * @param provinceId - Province ID
     * @param id - Collection ID
     * @returns Promise<void>
     */
    async deleteCollection(provinceId: string, id: number): Promise<void> {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            const collection = await entityManager.getRepository(AdsCollection).findOne({ where: { id } });

            if (!collection) {
                throw new BadRequestException(`Không tìm thấy collection với ID: ${id}`);
            }

            // Remove collection_id from all categories in this collection
            await entityManager.getRepository(AdsCategory).update({ collection_id: id }, { collection_id: null });

            // Delete collection
            await entityManager.getRepository(AdsCollection).delete(id);
        });
    }

    /**
     * Assign categories to collection with transaction (Many-to-Many)
     * @param entityManager - Transaction entity manager
     * @param provinceId - Province ID
     * @param collectionId - Collection ID
     * @param categoryIds - Array of category IDs
     * @param replaceExisting - Whether to replace existing assignments (default: false)
     * @returns Promise<void>
     */
    private async assignCategoriesToCollectionWithTW(
        entityManager: EntityManager,
        _provinceId: string,
        collectionId: number,
        categoryIds: number[],
        replaceExisting: boolean = false,
    ): Promise<void> {
        // Validate categories exist
        const categories = await entityManager.getRepository(AdsCategory).findBy({
            id: In(categoryIds),
        });

        if (categories.length !== categoryIds.length) {
            const foundIds = categories.map((c) => c.id);
            const missingIds = categoryIds.filter((id) => !foundIds.includes(id));
            throw new BadRequestException(`Không tìm thấy categories với IDs: ${missingIds.join(', ')}`);
        }

        // If replacing existing, remove all current assignments
        if (replaceExisting) {
            await entityManager.getRepository(AdsCollectionCategory).delete({
                collection_id: collectionId,
            });
        }

        // Create new assignments (use INSERT IGNORE to handle duplicates)
        const assignments = categoryIds.map((categoryId, index) => ({
            collection_id: collectionId,
            category_id: categoryId,
            ordinal_number: index,
            is_active: 1,
        }));

        // Use upsert to handle duplicates gracefully
        for (const assignment of assignments) {
            await entityManager
                .getRepository(AdsCollectionCategory)
                .createQueryBuilder()
                .insert()
                .into(AdsCollectionCategory)
                .values(assignment)
                .orUpdate(['ordinal_number', 'is_active', 'updated_at'], ['collection_id', 'category_id'])
                .execute();
        }
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use assignCategoriesToCollectionWithTW with many-to-many support
     */
    private async assignCategoriesToCollectionLegacy(
        entityManager: EntityManager,
        _provinceId: string,
        collectionId: number,
        categoryIds: number[],
    ): Promise<void> {
        // Validate categories exist
        const categories = await entityManager.getRepository(AdsCategory).findBy({
            id: In(categoryIds),
        });

        if (categories.length !== categoryIds.length) {
            const foundIds = categories.map((c) => c.id);
            const missingIds = categoryIds.filter((id) => !foundIds.includes(id));
            throw new BadRequestException(`Không tìm thấy categories với IDs: ${missingIds.join(', ')}`);
        }

        // Update categories to belong to this collection (legacy one-to-many)
        await entityManager.getRepository(AdsCategory).update(categoryIds, { collection_id: collectionId });
    }

    /**
     * Assign categories to collection
     * @param provinceId - Province ID
     * @param dto - Assign categories DTO
     * @returns Promise<AdsCollection>
     */
    async assignCategoriesToCollection(
        provinceId: string,
        dto: AssignCategoriesToCollectionDto,
    ): Promise<AdsCollection> {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            await this.assignCategoriesToCollectionWithTW(
                entityManager,
                provinceId,
                dto.collection_id,
                dto.category_ids,
            );

            return await this.getCollectionById(provinceId, dto.collection_id);
        });
    }

    /**
     * Remove categories from collection
     * @param provinceId - Province ID
     * @param collectionId - Collection ID
     * @param dto - Remove categories DTO
     * @returns Promise<AdsCollection>
     */
    async removeCategoriesFromCollection(
        provinceId: string,
        collectionId: number,
        dto: RemoveCategoriesFromCollectionDto,
    ): Promise<AdsCollection> {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            // Validate collection exists
            const collection = await entityManager.getRepository(AdsCollection).findOne({
                where: { id: collectionId },
            });

            if (!collection) {
                throw new BadRequestException(`Không tìm thấy collection với ID: ${collectionId}`);
            }

            // Remove specific category assignments from junction table (Many-to-Many)
            await entityManager.getRepository(AdsCollectionCategory).delete({
                collection_id: collectionId,
                category_id: In(dto.category_ids),
            });

            return await this.getCollectionById(provinceId, collectionId);
        });
    }

    /**
     * Get categories assigned to a specific collection (Many-to-Many)
     * @param provinceId - Province ID
     * @param collectionId - Collection ID
     * @param activeOnly - Whether to return only active assignments (default: true)
     * @returns Promise<AdsCategory[]>
     */
    async getCategoriesByCollection(
        provinceId: string,
        collectionId: number,
        activeOnly: boolean = true,
    ): Promise<AdsCategory[]> {
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsCollectionCategory, provinceId)
            .createQueryBuilder('acc')
            .innerJoinAndSelect('acc.ads_category', 'category')
            .where('acc.collection_id = :collectionId', { collectionId })
            .orderBy('acc.ordinal_number', 'ASC')
            .addOrderBy('category.name', 'ASC');

        if (activeOnly) {
            queryBuilder.andWhere('acc.is_active = 1').andWhere('category.is_active = 1');
        }

        const assignments = await queryBuilder.getMany();
        return assignments.map((assignment) => assignment.ads_category);
    }

    /**
     * Get collections that contain a specific category (Many-to-Many)
     * @param provinceId - Province ID
     * @param categoryId - Category ID
     * @param activeOnly - Whether to return only active assignments (default: true)
     * @returns Promise<AdsCollection[]>
     */
    async getCollectionsByCategory(
        provinceId: string,
        categoryId: number,
        activeOnly: boolean = true,
    ): Promise<AdsCollection[]> {
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsCollectionCategory, provinceId)
            .createQueryBuilder('acc')
            .innerJoinAndSelect('acc.ads_collection', 'collection')
            .where('acc.category_id = :categoryId', { categoryId })
            .orderBy('collection.ordinal_number', 'ASC')
            .addOrderBy('collection.name', 'ASC');

        if (activeOnly) {
            queryBuilder.andWhere('acc.is_active = 1').andWhere('collection.is_active = 1');
        }

        const assignments = await queryBuilder.getMany();
        return assignments.map((assignment) => assignment.ads_collection);
    }

    /**
     * Get categories not in any collection
     * @param provinceId - Province ID
     * @returns Promise<AdsCategory[]>
     */
    async getUnassignedCategories(provinceId: string): Promise<AdsCategory[]> {
        return await DatabaseService.getRepositoryByProvinceId(AdsCategory, provinceId)
            .createQueryBuilder('category')
            .where('category.collection_id IS NULL')
            .andWhere('category.is_active = 1')
            .orderBy('category.name', 'ASC')
            .getMany();
    }

    /**
     * Get categories by collection ID (Legacy - deprecated)
     * @deprecated Use getCategoriesByCollection with many-to-many support
     * @param provinceId - Province ID
     * @param collectionId - Collection ID
     * @returns Promise<AdsCategory[]>
     */
    async getCategoriesByCollectionLegacy(provinceId: string, collectionId: number): Promise<AdsCategory[]> {
        return await DatabaseService.getRepositoryByProvinceId(AdsCategory, provinceId)
            .createQueryBuilder('category')
            .where('category.collection_id = :collectionId', { collectionId })
            .orderBy('category.name', 'ASC')
            .getMany();
    }

    /**
     * Reorder collections
     * @param provinceId - Province ID
     * @param collectionOrders - Array of collection orders
     * @returns Promise<void>
     */
    async reorderCollections(
        provinceId: string,
        collectionOrders: Array<{ collection_id: number; ordinal_number: number }>,
    ): Promise<void> {
        return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
            for (const order of collectionOrders) {
                await entityManager
                    .getRepository(AdsCollection)
                    .update(order.collection_id, { ordinal_number: order.ordinal_number });
            }
        });
    }

    /**
     * Get collection statistics
     * @param provinceId - Province ID
     * @param collectionId - Collection ID (optional)
     * @returns Promise<CollectionStats>
     */
    async getCollectionStats(
        provinceId: string,
        collectionId?: number,
    ): Promise<{
        total_collections: number;
        active_collections: number;
        total_categories: number;
        assigned_categories: number;
        unassigned_categories: number;
        collection_details?: {
            id: number;
            name: string;
            categories_count: number;
            active_categories_count: number;
        };
    }> {
        const connection = DatabaseService.getConnectionByProvinceId(provinceId);

        // Get basic stats with more precise queries
        const [totalCollections, activeCollections, totalCategories, assignedCategories, unassignedCategories] =
            await Promise.all([
                // Total collections
                connection.getRepository(AdsCollection).count(),

                // Active collections
                connection.getRepository(AdsCollection).count({ where: { is_active: 1 } }),

                // Total categories
                connection.getRepository(AdsCategory).count(),

                // Categories that have collection_id (assigned)
                connection
                    .getRepository(AdsCategory)
                    .createQueryBuilder('category')
                    .where('category.collection_id IS NOT NULL')
                    .getCount(),

                // Categories that don't have collection_id (unassigned)
                connection
                    .getRepository(AdsCategory)
                    .createQueryBuilder('category')
                    .where('category.collection_id IS NULL')
                    .getCount(),
            ]);

        // Verify the calculation
        const calculatedUnassigned = totalCategories - assignedCategories;
        console.log('Collection Stats Debug:', {
            totalCategories,
            assignedCategories,
            unassignedCategories,
            calculatedUnassigned,
            match: unassignedCategories === calculatedUnassigned,
        });

        const result = {
            total_collections: totalCollections,
            active_collections: activeCollections,
            total_categories: totalCategories,
            assigned_categories: assignedCategories,
            unassigned_categories: unassignedCategories,
        };

        // Get specific collection details if requested
        if (collectionId) {
            const collection = await connection.getRepository(AdsCollection).findOne({
                where: { id: collectionId },
            });

            if (collection) {
                const [categoriesCount, activeCategoriesCount] = await Promise.all([
                    connection.getRepository(AdsCategory).count({ where: { collection_id: collectionId } }),
                    connection.getRepository(AdsCategory).count({
                        where: { collection_id: collectionId, is_active: 1 },
                    }),
                ]);

                result['collection_details'] = {
                    id: collection.id,
                    name: collection.name,
                    categories_count: categoriesCount,
                    active_categories_count: activeCategoriesCount,
                };
            }
        }

        return result;
    }

    /**
     * Get collections with categories for display (public API)
     * @param provinceId - Province ID
     * @param activeOnly - Only return active collections and categories
     * @returns Promise<CollectionWithCategories[]>
     */
    async getCollectionsForDisplay(
        provinceId: string,
        activeOnly = true,
    ): Promise<
        Array<{
            id: number;
            name: string;
            code: string;
            description: string;
            image: string;
            icon: string;
            display_type: string;
            metadata: Record<string, any>;
            categories: Array<{
                id: number;
                code: string;
                name: string;
                desc: string;
                subtitle: string;
                image: string;
                thumbnail: string;
                frame_id: number;
                cate_limit: number;
                used: number;
                type: string;
            }>;
        }>
    > {
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdsCollection, provinceId)
            .createQueryBuilder('collection')
            .leftJoinAndSelect('collection.ads_categories', 'categories')
            .orderBy('collection.ordinal_number', 'ASC')
            .addOrderBy('categories.name', 'ASC');

        if (activeOnly) {
            queryBuilder
                .andWhere('collection.is_active = 1')
                .andWhere('(categories.is_active = 1 OR categories.id IS NULL)');
        }

        const collections = await queryBuilder.getMany();

        return collections.map((collection) => ({
            id: collection.id,
            name: collection.name,
            code: collection.code,
            description: collection.description,
            image: collection.image,
            icon: collection.icon,
            display_type: collection.display_type,
            metadata: collection.metadata,
            categories: (collection.ads_categories || []).map((category) => ({
                id: category.id,
                code: category.code,
                name: category.name,
                desc: category.desc,
                subtitle: category.subtitle,
                image: category.image,
                thumbnail: category.thumbnail,
                frame_id: category.frame_id,
                cate_limit: category.cate_limit,
                used: category.used,
                type: category.type,
            })),
        }));
    }

    /**
     * Search collections and categories
     * @param provinceId - Province ID
     * @param searchTerm - Search term
     * @returns Promise<SearchResult>
     */
    async searchCollectionsAndCategories(
        provinceId: string,
        searchTerm: string,
    ): Promise<{
        collections: AdsCollection[];
        categories: AdsCategory[];
    }> {
        const [collections, categories] = await Promise.all([
            // Search collections
            DatabaseService.getRepositoryByProvinceId(AdsCollection, provinceId)
                .createQueryBuilder('collection')
                .where('collection.name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .orWhere('collection.code LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .orWhere('collection.description LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .andWhere('collection.is_active = 1')
                .orderBy('collection.ordinal_number', 'ASC')
                .getMany(),

            // Search categories
            DatabaseService.getRepositoryByProvinceId(AdsCategory, provinceId)
                .createQueryBuilder('category')
                .where('category.name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .orWhere('category.code LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .orWhere('category.desc LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
                .andWhere('category.is_active = 1')
                .orderBy('category.name', 'ASC')
                .getMany(),
        ]);

        return { collections, categories };
    }
}
