import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
import { AdsSettings } from 'src/entities/adsSettings.entity';

@Injectable()
export class AdsSettingsService {
    private readonly logger = new Logger(AdsSettingsService.name);
    constructor() {}
    async getAdsSettings(provinceId: string): Promise<AdsSettings> {
        const adsSettings = await DatabaseService.getRepositoryByProvinceId(AdsSettings, provinceId).find();
        return adsSettings[0];
    }

    async updateAdsSettings(provinceId: string, data: Partial<AdsSettings>) {
        const adsSettings = await this.getAdsSettings(provinceId);
        if (!adsSettings) {
            return await DatabaseService.getRepositoryByProvinceId(AdsSettings, provinceId).save(data);
        }
        await DatabaseService.getRepositoryByProvinceId(AdsSettings, provinceId).update(adsSettings.id, data);
        return await this.getAdsSettings(provinceId);
    }
}
