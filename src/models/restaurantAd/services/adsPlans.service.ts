import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
import { AdsPlan } from 'src/entities/adsPlans.entity';
import { AdsPlanItem } from 'src/entities/adsPlanItem.entity';
import { IAdsPlansResponse } from '../interface/adsPlans.interface';
import { GetAdsPlansDto, ActionAdsPlanDto } from '../dto/adsPlans.dto';
import { CreateAdsPlanDto } from '../dto/adsPlans.dto';
import { CreateAdsFoodCategoryDto } from '../dto/adsCampaignItem.dto';

import { AdsCampaignsService } from './adsCampaigns.service';
import { KeywordService } from './adsKeyWord.service';
import { AdsSettingsService } from './adsSettings.service';
import { OrderInvoiceService } from 'src/models/order/services/orderInvoice.service';
import { AdsCampaign } from 'src/entities/adsCampaigns.entity';
import { AdsPayment } from 'src/entities/adsPayment.entity';
import { EAdsPaymentMethod, EAdsPaymentStatus } from '../dto/adsPayment.dto';
import { PaymentCodeUtil } from '../utils/paymentCode.util';
import { IBankWebhook } from '../dto/paymentWebhook.dto';
import { TinyInt } from 'src/common/constants';

// Additional imports for enhanced functionality
import { EAdsCategoryCode } from 'src/entities/adsCategoryV2.entity';
import { Restaurant } from 'src/entities/restaurant.entity';
import { DEFAULT_VAT_RATE } from '../constants/vatRate.constants';
import { EAdsCampaignPlatform } from '../dto/adsCampaign.dto';

@Injectable()
export class AdsPlansService {
    private readonly logger = new Logger(AdsPlansService.name);

    constructor(
        private readonly adsCampaignsService: AdsCampaignsService,
        private readonly keywordService: KeywordService,
        private readonly adsSettingsService: AdsSettingsService,
        private readonly orderInvoiceService: OrderInvoiceService,
    ) {}

    async getAdsPlans(provinceId: string, dto: Partial<GetAdsPlansDto>): Promise<IAdsPlansResponse> {
        const { page, limit, orderBy, sortedBy, from_date, to_date } = dto;
        try {
            this.logger.log(`Getting ads plans for province ${provinceId}`, { dto });

            // Base query builder
            const baseQuery = DatabaseService.getRepositoryByProvinceId(AdsPlan, provinceId)
                .createQueryBuilder('AdsPlan')
                .leftJoinAndSelect('AdsPlan.ads_plan_items', 'adsPlanItems')
                .leftJoinAndSelect('adsPlanItems.ads_category', 'ads_category')
                .where('AdsPlan.id IS NOT NULL');

            // Apply filters to base query
            if (from_date && to_date) {
                baseQuery.andWhere('AdsPlan.created_at BETWEEN :from_date AND :to_date', {
                    from_date: from_date.format('YYYY-MM-DD HH:mm:ss'),
                    to_date: to_date.format('YYYY-MM-DD HH:mm:ss'),
                });
            }

            const [items, total] = await baseQuery
                .orderBy({ [`AdsPlan.${orderBy}`]: sortedBy })
                .skip(page * limit)
                .take(limit)
                .getManyAndCount();

            this.logger.log(`Retrieved ${items.length} ads plans out of ${total} total for province ${provinceId}`);
            return { data: items, total };
        } catch (error) {
            this.logger.error(`Failed to get ads plans for province ${provinceId}`, error.stack);
            console.log('error: ', error);
            throw new BadRequestException(`Không thể lấy danh sách gói quảng cáo: ${error.message}`);
        }
    }

    async createAdsPlan(provinceId: string, dto: CreateAdsPlanDto): Promise<AdsPlan> {
        try {
            this.logger.log(`Creating ads plan for province ${provinceId}`, { dto });

            return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
                const { ads_plan_items, ...planData } = dto;
                const adsPlan = new AdsPlan(planData);

                // Save ads plan
                const savedPlan = await entityManager.getRepository(AdsPlan).save(adsPlan);
                this.logger.log(`Ads plan created with ID: ${savedPlan.id}`);

                // Create ads plan items if provided
                if (ads_plan_items && ads_plan_items.length > 0) {
                    const planItems = ads_plan_items.map(
                        (item) =>
                            new AdsPlanItem({
                                ...item,
                                ads_plan_id: savedPlan.id,
                            }),
                    );

                    await entityManager.getRepository(AdsPlanItem).save(planItems);
                    this.logger.log(`Created ${planItems.length} ads plan items`);
                }

                // Load lại ads plan với ads_plan_items để trả về response đầy đủ
                const finalPlan = await entityManager.getRepository(AdsPlan).findOne({
                    where: { id: savedPlan.id },
                    relations: ['ads_plan_items', 'ads_plan_items.ads_category'],
                });

                this.logger.log(`Successfully created ads plan with ID: ${savedPlan.id}`);
                return finalPlan;
            });
        } catch (error) {
            this.logger.error(`Failed to create ads plan for province ${provinceId}`, error.stack);
            console.log('error: ', error);
            throw new BadRequestException(`Không thể tạo gói quảng cáo: ${error.message}`);
        }
    }

    async updateAdsPlan(provinceId: string, planId: number, dto: Partial<CreateAdsPlanDto>): Promise<AdsPlan> {
        try {
            this.logger.log(`Updating ads plan ${planId} for province ${provinceId}`, { dto });

            return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
                const adsPlan = await entityManager.getRepository(AdsPlan).findOne({
                    where: { id: planId },
                    relations: ['ads_plan_items'],
                });

                if (!adsPlan) {
                    throw new BadRequestException('Không tìm thấy gói quảng cáo');
                }

                const { ads_plan_items, ...planData } = dto;
                Object.assign(adsPlan, planData);

                // Save ads plan data changes
                await entityManager.getRepository(AdsPlan).save(adsPlan);

                // Update ads plan items if provided
                if (ads_plan_items && ads_plan_items.length > 0) {
                    // Remove existing items
                    await entityManager.getRepository(AdsPlanItem).delete({ ads_plan_id: planId });

                    // Create new items
                    const planItems = ads_plan_items.map(
                        (item) =>
                            new AdsPlanItem({
                                ...item,
                                ads_plan_id: planId,
                            }),
                    );

                    await entityManager.getRepository(AdsPlanItem).save(planItems);
                    this.logger.log(`Updated ${planItems.length} ads plan items for plan ${planId}`);
                }

                // Load lại ads plan với ads_plan_items mới để trả về response đúng
                const finalPlan = await entityManager.getRepository(AdsPlan).findOne({
                    where: { id: planId },
                    relations: ['ads_plan_items', 'ads_plan_items.ads_category'],
                });

                this.logger.log(`Successfully updated ads plan ${planId}`);
                return finalPlan;
            });
        } catch (error) {
            this.logger.error(`Failed to update ads plan ${planId} for province ${provinceId}`, error.stack);
            console.log('error: ', error);
            throw new BadRequestException(`Không thể cập nhật gói quảng cáo: ${error.message}`);
        }
    }

    async deleteAdsPlan(provinceId: string, planId: number) {
        try {
            this.logger.log(`Deleting ads plan ${planId} for province ${provinceId}`);

            return await DatabaseService.getConnectionByProvinceId(provinceId).transaction(async (entityManager) => {
                const adsPlan = await entityManager.getRepository(AdsPlan).findOne({
                    where: { id: planId },
                    relations: ['ads_plan_items'],
                });

                if (!adsPlan) {
                    throw new BadRequestException('Không tìm thấy gói quảng cáo');
                }

                // Delete ads plan items first (cascade delete)
                const deleteItemsResult = await entityManager.getRepository(AdsPlanItem).delete({
                    ads_plan_id: planId,
                });
                this.logger.log(`Deleted ${deleteItemsResult.affected} ads plan items for plan ${planId}`);

                // Delete ads plan
                const deletePlanResult = await entityManager.getRepository(AdsPlan).delete({ id: planId });

                this.logger.log(`Successfully deleted ads plan ${planId}`);
                return deletePlanResult;
            });
        } catch (error) {
            this.logger.error(`Failed to delete ads plan ${planId} for province ${provinceId}`, error.stack);
            console.log('error: ', error);
            throw new BadRequestException(`Không thể xóa gói quảng cáo: ${error.message}`);
        }
    }

    /**
     * Action an ads plan to automatically create campaign based on plan
     * Detects ad category type and adds food categories when needed
     * @param provinceId - Province ID
     * @param planId - Ads plan ID
     * @param dto - Action plan data
     * @param userId - User ID who performs the action
     * @returns Promise<AdsCampaign>
     */
    async actionAdsPlan(
        provinceId: string,
        planId: number,
        dto: ActionAdsPlanDto,
        userId: number,
    ): Promise<AdsCampaign> {
        try {
            this.logger.log(`Actioning ads plan ${planId} for province ${provinceId}`, { dto });
            if (!planId) {
                throw new BadRequestException('plan_id là bắt buộc');
            }

            const { restaurant_id } = dto;

            if (!restaurant_id) {
                throw new BadRequestException('restaurant_id là bắt buộc');
            }

            // Validate restaurant exists
            const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
                where: { id: restaurant_id },
            });

            if (!restaurant) {
                throw new BadRequestException(`Restaurant ${restaurant_id} not found`);
            }

            // Get ads plan with items and categories
            const adsPlan = await DatabaseService.getRepositoryByProvinceId(AdsPlan, provinceId).findOne({
                where: { id: planId },
                relations: ['ads_plan_items', 'ads_plan_items.ads_category'],
            });

            console.log('adsPlan: ', adsPlan);

            if (!adsPlan) {
                throw new BadRequestException('Không tìm thấy gói quảng cáo');
            }

            if (!adsPlan.is_active) {
                throw new BadRequestException('Gói quảng cáo không hoạt động');
            }

            if (!adsPlan.ads_plan_items || adsPlan.ads_plan_items.length === 0) {
                throw new BadRequestException('Gói quảng cáo không có chi tiết nào');
            }

            // Create campaign using enhanced logic with category detection
            return await this.createCampaignFromAdsPlanEnhanced(provinceId, adsPlan, dto, userId);
        } catch (error) {
            this.logger.error(`Failed to action ads plan ${planId} for province ${provinceId}`, error.stack);
            console.log('error: ', error);
            throw new BadRequestException(`Không thể thực hiện gói quảng cáo: ${error.message}`);
        }
    }

    /**
     * Create campaign from ads plan with enhanced logic
     * Detects category types and adds food categories when needed
     */
    private async createCampaignFromAdsPlanEnhanced(
        provinceId: string,
        adsPlan: AdsPlan,
        dto: ActionAdsPlanDto,
        userId: number,
    ): Promise<AdsCampaign> {
        const {
            restaurant_id,
            seller_id,
            commission,
            platform,
            active_from,
            active_to,
            note,
            food_categories,
            keywords,
        } = dto;

        this.logger.log(`Creating campaign from ads plan ${adsPlan.id} for restaurant ${restaurant_id}`);

        // Pre-process keywords if provided to convert them to keyword IDs
        let keywordIds: number[] = [];
        if (keywords && keywords.length > 0) {
            for (const keyword of keywords) {
                try {
                    const adsKeyword = await this.keywordService.findOrCreate({ keyword }, provinceId);
                    keywordIds.push(adsKeyword.id);
                } catch (error) {
                    this.logger.error(`Failed to find or create keyword "${keyword}":`, error);
                    // Continue with other keywords even if one fails
                }
            }
        }

        // Convert plan items to campaign items with enhanced logic
        const ads_items = adsPlan.ads_plan_items.map((planItem) => {
            const campaignItem: any = {
                ad_cate_id: planItem.ad_cate_id,
                active_from: active_from || new Date(),
                active_to: new Date(Date.now() + planItem.duration * 24 * 60 * 60 * 1000), // duration in days
                is_active: TinyInt.NO, // Use TinyInt enum
                ads_status_code: 1, // pending
                note: note || planItem.note,
                price: planItem.price,
                frame_active_from: active_from || new Date(),
                frame_active_to: new Date(Date.now() + planItem.duration * 24 * 60 * 60 * 1000),
                attached_images: [],
                vat_rate: DEFAULT_VAT_RATE,
            };

            // Add food categories if this is category_ad_page and food_categories provided
            if (
                this.shouldAddFoodCategories(planItem.ads_category?.code) &&
                food_categories &&
                food_categories.length > 0
            ) {
                // Convert food_categories (number[]) to foodCategoryAds (CreateAdsFoodCategoryDto[])
                const foodCategoryAds = food_categories.map((categoryId) => ({
                    category_id: categoryId,
                    active_from: active_from || new Date(),
                    active_to: new Date(Date.now() + planItem.duration * 24 * 60 * 60 * 1000),
                }));

                campaignItem.foodCategoryAds = foodCategoryAds;

                this.logger.log(
                    `Added ${foodCategoryAds.length} food categories to campaign item for category ${planItem.ads_category?.code}:`,
                    foodCategoryAds.map((fc) => fc.category_id),
                );
            }

            // Add keywords if this is search_ad_page and keywordIds available
            if (this.shouldAddKeywords(planItem.ads_category?.code) && keywordIds.length > 0) {
                campaignItem.adsKeywords = keywordIds;

                this.logger.log(
                    `Added ${keywordIds.length} keywords to campaign item for category ${planItem.ads_category?.code}:`,
                    keywords,
                );
            }

            return campaignItem;
        });

        // Create campaign data
        const campaignData = {
            name: adsPlan.name,
            restaurant_id,
            ads_items,
            sub_total_price: adsPlan.sub_total_price,
            discount_price: adsPlan.discount_price,
            total_price: adsPlan.total_price,
            seller_id,
            commission,
            platform,
            ads_payments: [], // Empty payments for now
            description: adsPlan.description || '',
        };

        // Create campaign using AdsCampaignsService (payment code will be generated automatically)
        const campaign = await this.adsCampaignsService.createAdsCampaign(provinceId, campaignData, userId);

        // Get the generated payment code
        const paymentCode = await this.adsCampaignsService.getPaymentCode(provinceId, campaign.id);

        this.logger.log(`Successfully created campaign ${campaign.id} from ads plan ${adsPlan.id}`);
        this.logger.log(`Campaign details:`, {
            campaignId: campaign.id,
            restaurantId: campaign.restaurant_id,
            status: campaign.status,
            totalPrice: campaign.total_price,
            paymentCode: paymentCode,
            itemsCount: campaign.ads_items?.length || 0,
        });

        return campaign;
    }

    /**
     * Check if food categories should be added for this ad category
     */
    private shouldAddFoodCategories(categoryCode: string): boolean {
        return categoryCode === EAdsCategoryCode.category_ad_page;
    }

    /**
     * Check if keywords should be added for this ad category
     */
    private shouldAddKeywords(categoryCode: string): boolean {
        return categoryCode === EAdsCategoryCode.search_ad_page;
    }

    /**
     * Webhook để nhận sự kiện thanh toán từ bank tracking system (auto-detect province)
     * @param bankWebhookData - Dữ liệu webhook từ bank tracking system
     * @returns Promise<{ success: boolean; message: string; campaign?: AdsCampaign }>
     */
    async handleBankTransactionWebhookAutoDetect(
        bankWebhookData: IBankWebhook,
    ): Promise<{ success: boolean; message: string; campaign?: AdsCampaign }> {
        try {
            this.logger.log(`Processing bank transaction webhook with auto-detect province`, { bankWebhookData });

            const { event, data } = bankWebhookData;
            const {
                transaction_id,
                // bank_code,
                transaction_code,
                transaction_time,
                amount,
                extracted_transaction_code,
                is_verified,
            } = data;

            // Validate webhook event
            if (event !== 'transaction.created') {
                this.logger.warn(`Unsupported webhook event: ${event}`, { bankWebhookData });
                return {
                    success: false,
                    message: `Sự kiện webhook không được hỗ trợ: ${event}`,
                };
            }

            // Log verification status for monitoring (but don't block processing)
            this.logger.log(`Processing transaction ${transaction_id} with verification status: ${is_verified}`);

            // Validate and extract campaign ID from payment code (flexible format support)
            const paymentCodeValidation = PaymentCodeUtil.validatePaymentCodeFlexible(extracted_transaction_code);

            if (!paymentCodeValidation.isValid) {
                this.logger.warn(`Invalid payment code: ${extracted_transaction_code}`, {
                    error: paymentCodeValidation.error,
                });
                return {
                    success: false,
                    message: paymentCodeValidation.error || 'Mã thanh toán không hợp lệ',
                };
            }

            this.logger.log(
                `Payment code validated successfully with format: ${paymentCodeValidation.detectedFormat}`,
                {
                    paymentCode: extracted_transaction_code,
                    campaignId: paymentCodeValidation.campaignId,
                    format: paymentCodeValidation.detectedFormat,
                },
            );

            this.logger.log(`Processing payment for code: ${extracted_transaction_code}`);

            // Use hybrid detection - tries fixed-length first, then auto-detect
            const campaignResult = await this.adsCampaignsService.getCampaignByPaymentCodeHybrid(
                extracted_transaction_code,
            );

            if (!campaignResult) {
                this.logger.warn(`Campaign not found in any province for payment code: ${extracted_transaction_code}`);
                return {
                    success: false,
                    message: `Không tìm thấy campaign với mã thanh toán: ${extracted_transaction_code}`,
                };
            }

            const { campaign, provinceId } = campaignResult;
            this.logger.log(
                `Found campaign ${campaign.id} in province ${provinceId}, proceeding with payment processing`,
            );

            // Continue with existing logic using detected provinceId
            return await this.handleBankTransactionWebhook(provinceId, bankWebhookData);
        } catch (error) {
            this.logger.error(
                `Error processing bank transaction webhook with auto-detect: ${error.message}`,
                error.stack,
            );
            return {
                success: false,
                message: `Lỗi xử lý webhook: ${error.message}`,
            };
        }
    }

    /**
     * Webhook để nhận sự kiện thanh toán từ bank tracking system
     * @param provinceId - Province ID
     * @param bankWebhookData - Dữ liệu webhook từ bank tracking system
     * @returns Promise<{ success: boolean; message: string; campaign?: AdsCampaign }>
     */
    async handleBankTransactionWebhook(
        provinceId: string,
        bankWebhookData: IBankWebhook,
    ): Promise<{ success: boolean; message: string; campaign?: AdsCampaign }> {
        try {
            this.logger.log(`Processing bank transaction webhook for province ${provinceId}`, { bankWebhookData });

            const { event, data } = bankWebhookData;
            const {
                transaction_id,
                // bank_code,
                transaction_code,
                transaction_time,
                amount,
                extracted_transaction_code,
                is_verified,
            } = data;

            // Validate webhook event
            if (event !== 'transaction.created') {
                this.logger.warn(`Unsupported webhook event: ${event}`, { bankWebhookData });
                return {
                    success: false,
                    message: `Sự kiện webhook không được hỗ trợ: ${event}`,
                };
            }

            // Log verification status for monitoring (but don't block processing)
            this.logger.log(`Processing transaction ${transaction_id} with verification status: ${is_verified}`);

            // Validate and extract campaign ID from payment code (flexible format support)
            const paymentCodeValidation = PaymentCodeUtil.validatePaymentCodeFlexible(extracted_transaction_code);

            if (!paymentCodeValidation.isValid) {
                this.logger.warn(`Invalid payment code: ${extracted_transaction_code}`, {
                    error: paymentCodeValidation.error,
                });
                return {
                    success: false,
                    message: paymentCodeValidation.error || 'Mã thanh toán không hợp lệ',
                };
            }

            this.logger.log(
                `Payment code validated successfully with format: ${paymentCodeValidation.detectedFormat}`,
                {
                    paymentCode: extracted_transaction_code,
                    campaignId: paymentCodeValidation.campaignId,
                    format: paymentCodeValidation.detectedFormat,
                },
            );

            this.logger.log(`Processing payment for code: ${extracted_transaction_code}`);

            // Find campaign by payment_code using AdsCampaignsService
            const campaign = await this.adsCampaignsService.getCampaignByPaymentCode(
                provinceId,
                extracted_transaction_code,
            );

            if (!campaign) {
                this.logger.warn(`Campaign not found for payment code: ${extracted_transaction_code}`);
                return {
                    success: false,
                    message: `Không tìm thấy campaign với mã thanh toán: ${extracted_transaction_code}`,
                };
            }

            // Check if payment already processed
            const existingPayment = campaign.ads_payments?.find(
                (payment) => payment.transaction_id_ref === transaction_code,
            );

            if (existingPayment) {
                this.logger.warn(`Payment already processed for transaction ${transaction_code}`, {
                    campaign_id: campaign.id,
                });
                return {
                    success: true,
                    message: 'Thanh toán đã được xử lý trước đó',
                    campaign,
                };
            }

            // Validate payment amount matches campaign total price
            // Campaign uses standard unit (VND), webhook amount is also in VND
            const expectedAmount = campaign.total_price;
            const receivedAmount = amount;

            this.logger.log(`Payment amount validation:`, {
                campaign_id: campaign.id,
                expected_amount: expectedAmount,
                received_amount: receivedAmount,
                payment_code: extracted_transaction_code,
            });

            // Check if received amount is less than expected amount
            if (receivedAmount < expectedAmount) {
                this.logger.warn(`Payment amount insufficient for campaign ${campaign.id}`, {
                    expected_amount: expectedAmount,
                    received_amount: receivedAmount,
                    shortage: expectedAmount - receivedAmount,
                    payment_code: extracted_transaction_code,
                });

                this.logger.warn(
                    `[handleBankTransactionWebhook] - Insufficient payment detected - manual review may be needed: ${JSON.stringify(
                        {
                            campaign_id: campaign.id,
                            expected_amount: expectedAmount,
                            received_amount: receivedAmount,
                            shortage: expectedAmount - receivedAmount,
                            payment_code: extracted_transaction_code,
                        },
                    )}`,
                );

                return {
                    success: false,
                    message: `Số tiền thanh toán không đủ. Cần thanh toán: ${expectedAmount.toLocaleString(
                        'vi-VN',
                    )} VND, đã nhận: ${receivedAmount.toLocaleString('vi-VN')} VND`,
                };
            }

            // Allow some tolerance for rounding differences when amount is equal or higher (±1 VND)
            const amountDifference = Math.abs(receivedAmount - expectedAmount);
            const tolerance = 1;

            if (receivedAmount > expectedAmount && amountDifference > tolerance) {
                this.logger.warn(
                    `Payment amount higher than expected for campaign ${campaign.id}`,
                    JSON.stringify({
                        expected_amount: expectedAmount,
                        received_amount: receivedAmount,
                        overpayment: receivedAmount - expectedAmount,
                        payment_code: extracted_transaction_code,
                    }),
                );

                // Log warning but continue processing for overpayment
                this.logger.warn(`Processing payment with overpayment - manual review may be needed`);
            }

            // Update payment status using AdsCampaignsService
            const updatedPayment = await this.adsCampaignsService.updatePaymentByCode(
                provinceId,
                extracted_transaction_code,
                EAdsPaymentStatus.PAID,
                transaction_code,
                EAdsPaymentMethod.VIET_BANK_TRANSFER,
            );

            if (!updatedPayment) {
                this.logger.error(`Failed to update payment for code: ${extracted_transaction_code}`);
                return {
                    success: false,
                    message: 'Không thể cập nhật trạng thái thanh toán',
                };
            }

            // Approve campaign and activate all items when payment is successful
            const approvedCampaign = await this.adsCampaignsService.approveCampaignAndActivateItems(
                provinceId,
                campaign.id,
            );

            if (!approvedCampaign) {
                this.logger.error(`Failed to approve campaign ${campaign.id} after successful payment`);
                // Don't return error here as payment was successful, just log the issue
            }

            // Get updated campaign with latest status
            const updatedCampaign = await this.adsCampaignsService.getCampaignByPaymentCode(
                provinceId,
                extracted_transaction_code,
            );

            // Create invoice for approved campaign by ads settings
            const adsSettings = await this.adsSettingsService.getAdsSettings(provinceId);
            const { is_auto_create_ads_invoice_by_merchant, is_auto_create_ads_invoice_by_admin } = adsSettings;
            if (
                campaign.sellerManagement?.platform === EAdsCampaignPlatform.MERCHANT_APP &&
                is_auto_create_ads_invoice_by_merchant === TinyInt.YES
            ) {
                await this.orderInvoiceService.createAdsCampaignInvoice(updatedCampaign, provinceId);
            }

            if (
                campaign.sellerManagement?.platform === EAdsCampaignPlatform.OFFLINE &&
                is_auto_create_ads_invoice_by_admin === TinyInt.YES
            ) {
                await this.orderInvoiceService.createAdsCampaignInvoice(updatedCampaign, provinceId);
            }

            this.logger.log(`Successfully processed bank transaction webhook for campaign ${campaign.id}`, JSON.stringify({
                transaction_id,
                transaction_code,
                amount,
                // bank_code,
                campaign_id: campaign.id,
                payment_code: extracted_transaction_code,
                campaign_status: updatedCampaign?.status,
                items_activated: updatedCampaign?.ads_items?.filter((item) => item.is_active === 1).length || 0,
                expected_amount: campaign.total_price,
                received_amount: amount,
                amount_match: Math.abs(amount - campaign.total_price) <= 1,
            }));

            return {
                success: true,
                message: 'Thanh toán đã được cập nhật thành công và chiến dịch đã được kích hoạt',
                campaign: updatedCampaign,
            };
        } catch (error) {
            this.logger.error(`Failed to process bank transaction webhook for province ${provinceId}`, error.stack);
            console.log('Bank transaction webhook error: ', error);
            throw new BadRequestException(`Không thể xử lý webhook giao dịch ngân hàng: ${error.message}`);
        }
    }

    /**
     * Verify payment status for a campaign
     * @param provinceId - Province ID
     * @param campaignId - Campaign ID
     * @returns Promise<{ is_paid: boolean; total_paid: number; payments: any[] }>
     */
    async verifyPaymentStatus(
        provinceId: string,
        campaignId: number,
    ): Promise<{ is_paid: boolean; total_paid: number; payments: any[] }> {
        try {
            this.logger.log(`Verifying payment status for campaign ${campaignId} in province ${provinceId}`);

            const campaign = await DatabaseService.getRepositoryByProvinceId(AdsCampaign, provinceId).findOne({
                where: { id: campaignId },
                relations: ['ads_payments'],
            });

            if (!campaign) {
                throw new BadRequestException('Không tìm thấy campaign');
            }

            // Get payment records using repository
            const payments = await DatabaseService.getRepositoryByProvinceId(AdsPayment, provinceId).find({
                where: {
                    ads_campaign_id: campaignId,
                    status: EAdsPaymentStatus.PAID,
                },
                order: { created_at: 'DESC' },
            });

            const totalPaid = payments.reduce((sum: number, payment: AdsPayment) => sum + payment.amount, 0);
            const isPaid = totalPaid >= campaign.total_price;

            this.logger.log(`Payment verification for campaign ${campaignId}:`, {
                total_price: campaign.total_price,
                total_paid: totalPaid,
                is_paid: isPaid,
                payments_count: payments.length,
            });

            return {
                is_paid: isPaid,
                total_paid: totalPaid,
                payments,
            };
        } catch (error) {
            this.logger.error(`Failed to verify payment status for campaign ${campaignId}`, error.stack);
            throw new BadRequestException(`Không thể kiểm tra trạng thái thanh toán: ${error.message}`);
        }
    }
}
