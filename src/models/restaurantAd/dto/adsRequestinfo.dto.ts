import { IsNotE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { ERequestInfoRangeTime } from 'src/entities/adsRequestInfo.entity';

export class AdsRequestInfoDto {
    @IsNotEmpty()
    @IsString()
    phone: string;

    @IsOptional()
    @IsString()
    name: string;

    @IsOptional()
    @IsString()
    email: string;

    @IsOptional()
    @IsString()
    message: string;

    @IsNotEmpty()
    @IsString()
    visit_time_type: ERequestInfoRangeTime;
}

export class CreateAdsRequestInfoDto extends AdsRequestInfoDto {
    @IsNotEmpty()
    @IsNumber()
    ads_campaign_id: number;
}
