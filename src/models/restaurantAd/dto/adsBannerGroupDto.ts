import { IsOptional } from 'class-validator';
import { EPageDisplay } from 'src/entities/types/EPayDisplay.enum';

export enum EPosition {
    TOP = 'top',
    MIDDLE = 'mid',
    BOTTOM = 'bottom',
    WAITING_SHIPPER_SCREEN = 'waiting_shipper_screen',
}

export class GetAdsBannerGroupDto {
    @IsOptional()
    adsCampaignItemId: number;

    @IsOptional()
    position: EPosition;

    @IsOptional()
    page_display: EPageDisplay;
}
