import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { toNumber } from 'lodash';

// Interface for Bank Transaction Webhook Data
export interface IBankTransactionData {
    transaction_id: number;
    // bank_code: string;
    transaction_code: string;
    transaction_time: Date;
    transaction_type: string;
    amount: number;
    current_balance: number;
    raw_data: string;
    is_verified: number;
    tracking_bank_account_id: number;
    created_at: Date;
    extracted_transaction_code: string;
}

// Interface for Bank Webhook
export interface IBankWebhook {
    event: string;
    timestamp: Date;
    data: IBankTransactionData;
}

// Interface for Payment Code Generation
export interface IPaymentCodeConfig {
    prefix: string;
    length: number;
    includeTimestamp: boolean;
    includeRandom: boolean;
}

export enum EPaymentWebhookStatus {
    SUCCESS = 'success',
    FAILED = 'failed',
    PENDING = 'pending',
}

export class BankTransactionDataDto {
    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    transaction_id: number;

    @IsOptional()
    @IsString()
    bank_code: string;

    @IsNotEmpty()
    @IsString()
    transaction_code: string;

    @IsNotEmpty()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    transaction_time: Date;

    @IsNotEmpty()
    @IsString()
    transaction_type: string;

    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    amount: number;

    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    current_balance: number;

    @IsNotEmpty()
    @IsString()
    raw_data: string;

    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    is_verified: number;

    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    tracking_bank_account_id: number;

    @IsNotEmpty()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    created_at: Date;

    @IsNotEmpty()
    @IsString()
    extracted_transaction_code: string;
}

export class BankWebhookDto {
    @IsNotEmpty()
    @IsString()
    event: string;

    @IsNotEmpty()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    timestamp: Date;

    @IsNotEmpty()
    @ValidateNested()
    @Type(() => BankTransactionDataDto)
    data: BankTransactionDataDto;
}



export class PaymentWebhookResponseDto {
    success: boolean;
    message: string;
    campaign?: any;
}

export class PaymentStatusResponseDto {
    is_paid: boolean;
    total_paid: number;
    payments: any[];
}
