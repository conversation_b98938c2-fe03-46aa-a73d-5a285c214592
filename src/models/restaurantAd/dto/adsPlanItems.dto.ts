import { IsNotEmpty, <PERSON>N<PERSON><PERSON>, IsOptional } from 'class-validator';
import { toNumber } from 'lodash';
import { Transform } from 'class-transformer';

export class CreateAdsPlanItemDto {
    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ad_cate_id: number;

    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    duration: number;

    @IsOptional()
    note?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    price?: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ads_plan_id?: number;
}

export class UpdateAdsPlanItemDto {
    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ad_cate_id?: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    duration?: number;

    @IsOptional()
    note?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    price?: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ads_plan_id?: number;
}
