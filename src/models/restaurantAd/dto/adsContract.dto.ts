

import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class UpdateAdsContractDto {
    @IsNotEmpty()
    @IsNumber()
    campaignId: number;

    @IsNotEmpty(
        { message: 'Province ID is required' }
    )
    @IsString()
    provinceId: string;

    @IsOptional()
    @IsNumber()
    contractId: number;

    @IsOptional()
    @IsNumber()
    contractStatus: number;

    @IsOptional()
    @IsString()
    message: string;
}