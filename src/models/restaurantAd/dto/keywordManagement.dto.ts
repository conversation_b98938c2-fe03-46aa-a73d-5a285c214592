import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Min, IsArray, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import * as _ from 'lodash';

export class UpdateKeywordDto {
    @ApiProperty({ description: 'Keyword text', required: false })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    keyword?: string;

    @ApiProperty({ description: 'Maximum usage limit', required: false })
    @IsOptional()
    @IsNumber()
    @Min(1)
    limit?: number;
}

export class BulkUpdateKeywordDto {
    @ApiProperty({ description: 'Array of keyword updates' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => KeywordUpdateItem)
    updates: KeywordUpdateItem[];
}

export class KeywordUpdateItem {
    @ApiProperty({ description: 'Keyword ID' })
    @IsNumber()
    id: number;

    @ApiProperty({ description: 'Keyword text', required: false })
    @IsOptional()
    @IsString()
    @IsNotEmpty()
    keyword?: string;

    @ApiProperty({ description: 'Maximum usage limit', required: false })
    @IsOptional()
    @IsNumber()
    @Min(1)
    limit?: number;
}

export class KeywordUsageDetailDto {
    @ApiProperty({ description: 'Campaign item ID' })
    id: number;

    @ApiProperty({ description: 'Campaign ID' })
    ads_campaign_id: number;

    @ApiProperty({ description: 'Restaurant ID' })
    restaurant_id: number;

    @ApiProperty({ description: 'Restaurant name' })
    restaurant_name: string;

    @ApiProperty({ description: 'Campaign status' })
    ads_status_code: number;

    @ApiProperty({ description: 'Active from date' })
    active_from: Date;

    @ApiProperty({ description: 'Active to date' })
    active_to: Date;

    @ApiProperty({ description: 'Is active flag' })
    is_active: number;
}

export class KeywordAvailabilityDto {
    @ApiProperty({ description: 'Keyword ID' })
    id: number;

    @ApiProperty({ description: 'Keyword text' })
    keyword: string;

    @ApiProperty({ description: 'Maximum limit' })
    limit: number;

    @ApiProperty({ description: 'Current restaurant usage count' })
    usage_count: number;

    @ApiProperty({ description: 'Available slots' })
    available_slots: number;

    @ApiProperty({ description: 'Is available for use' })
    is_available: boolean;
}

export class CheckKeywordAvailabilityDto {
    @ApiProperty({ description: 'Keyword IDs to check' })
    @IsArray()
    @IsNumber({}, { each: true })
    keyword_ids: number[];
}

export class BulkDeleteKeywordDto {
    @ApiProperty({ description: 'Keyword IDs to delete' })
    @IsArray()
    @IsNumber({}, { each: true })
    keyword_ids: number[];
}

export class KeywordStatisticsDto {
    @ApiProperty({ description: 'Total keywords' })
    total_keywords: number;

    @ApiProperty({ description: 'Active keywords (in use)' })
    active_keywords: number;

    @ApiProperty({ description: 'Available keywords' })
    available_keywords: number;

    @ApiProperty({ description: 'Fully utilized keywords' })
    fully_utilized_keywords: number;

    @ApiProperty({ description: 'Average usage rate' })
    average_usage_rate: number;
}

export class EnhancedQueryFilterDto {
    @ApiProperty({ description: 'Page number', required: false, default: 1 })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    page = 1;

    @ApiProperty({ description: 'Items per page', required: false, default: 20 })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    limit = 20;

    @ApiProperty({ description: 'Search keyword', required: false })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiProperty({ description: 'Sort by field', required: false, default: 'keyword' })
    @IsOptional()
    @IsString()
    sort_by = 'keyword';

    @ApiProperty({ description: 'Sort order', required: false, default: 'ASC' })
    @IsOptional()
    @IsString()
    sort_order: 'ASC' | 'DESC' = 'ASC';

    @ApiProperty({ description: 'Filter by availability', required: false })
    @IsOptional()
    @Transform(({ value }) => value === 'true')
    available_only?: boolean;

    @ApiProperty({ description: 'Filter by usage status', required: false })
    @IsOptional()
    @IsString()
    usage_status?: 'unused' | 'partial' | 'full';

    @ApiProperty({ description: 'Minimum usage count', required: false })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    min_usage?: number;

    @ApiProperty({ description: 'Maximum usage count', required: false })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    max_usage?: number;
}

export class KeywordHttpQueryDto {
    @ApiProperty({ description: 'Page number', required: false, default: 1 })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    page?: number;

    @ApiProperty({ description: 'Items per page', required: false, default: 20 })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    limit?: number;

    @ApiProperty({ description: 'Search keyword text', required: false })
    @IsOptional()
    @IsString()
    keyword?: string;

    @ApiProperty({ description: 'Filter by restaurant ID', required: false })
    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    restaurantId?: number;
}
