import { Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { toNumber } from 'lodash';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { EAdsCollectionDisplayType } from 'src/entities/adsCollections.entity';

export class CreateAdsCollectionDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsNotEmpty()
    @IsString()
    code: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsString()
    image?: string;

    @IsOptional()
    @IsString()
    icon?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ordinal_number?: number;

    @IsOptional()
    @IsEnum(EAdsCollectionDisplayType)
    display_type?: EAdsCollectionDisplayType;

    @IsOptional()
    metadata?: Record<string, any>;

    @IsOptional()
    @Transform(({ value }) => (Array.isArray(value) ? value.map((v) => toNumber(v)) : []))
    @IsArray()
    @IsNumber({}, { each: true })
    category_ids?: number[];
}

export class UpdateAdsCollectionDto {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsString()
    code?: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsString()
    image?: string;

    @IsOptional()
    @IsString()
    icon?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    ordinal_number?: number;

    @IsOptional()
    @IsEnum(EAdsCollectionDisplayType)
    display_type?: EAdsCollectionDisplayType;

    @IsOptional()
    @Transform(({ value }) => (typeof value === 'boolean' ? (value ? 1 : 0) : toNumber(value)))
    @IsNumber()
    is_active?: number;

    @IsOptional()
    metadata?: Record<string, any>;

    @IsOptional()
    @Transform(({ value }) => (Array.isArray(value) ? value.map((v) => toNumber(v)) : []))
    @IsArray()
    @IsNumber({}, { each: true })
    category_ids?: number[];
}

export class GetAdsCollectionsDto extends BaseQueryFilterDto {
    @IsOptional()
    @IsString()
    code?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    is_active?: number;

    @IsOptional()
    @IsEnum(EAdsCollectionDisplayType)
    display_type?: EAdsCollectionDisplayType;

    @IsOptional()
    @Transform(({ value }) => value === 'true' || value === true)
    @IsBoolean()
    include_categories?: boolean;
}

export class AssignCategoriesToCollectionDto {
    @IsNotEmpty()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    collection_id: number;

    @IsNotEmpty()
    @Transform(({ value }) => (Array.isArray(value) ? value.map((v) => toNumber(v)) : [toNumber(value)]))
    @IsArray()
    @IsNumber({}, { each: true })
    category_ids: number[];
}

export class RemoveCategoriesFromCollectionDto {
    @IsNotEmpty()
    @Transform(({ value }) => (Array.isArray(value) ? value.map((v) => toNumber(v)) : [toNumber(value)]))
    @IsArray()
    @IsNumber({}, { each: true })
    category_ids: number[];
}

export class ReorderCategoriesInCollectionDto {
    @IsNotEmpty()
    @IsArray()
    category_orders: Array<{
        category_id: number;
        ordinal_number: number;
    }>;
}
