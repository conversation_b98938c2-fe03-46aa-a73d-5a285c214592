import { IsArray, IsDate, IsIn, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { toNumber } from 'lodash';
import { Transform, Type } from 'class-transformer';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { CreateAdsPlanItemDto, UpdateAdsPlanItemDto } from './adsPlanItems.dto';
import { EAdsCampaignPlatform } from './adsCampaign.dto';

export class GetAdsPlansDto extends BaseQueryFilterDto {
    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    vill_ad_id: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsIn([0, 1])
    is_active: number;

    @IsOptional()
    @Transform(({ value }) => {
        if (value == null) return undefined;
        return Array.isArray(value) ? value : [value];
    })
    ads_plan_items: number[];

    @IsOptional()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    active_from: Date;

    @IsOptional()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    active_to: Date;
}

export class CreateAdsPlanDto {
    @IsString()
    name: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    is_active: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    sub_total_price: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    discount_price: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    total_price: number;

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => CreateAdsPlanItemDto)
    ads_plan_items: CreateAdsPlanItemDto[];
}

export class UpdateAdsPlanDto {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    is_active: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    sub_total_price: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    discount_price: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    total_price: number;

    @IsOptional()
    @ValidateNested({ each: true })
    @Type(() => UpdateAdsPlanItemDto)
    ads_plan_items: UpdateAdsPlanItemDto[];
}

export class ActionAdsPlanDto {

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    restaurant_id: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    seller_id?: number;

    @IsOptional()
    @Transform(({ value }) => toNumber(value))
    @IsNumber()
    commission?: number;

    @IsOptional()
    platform?: EAdsCampaignPlatform;

    @IsOptional()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    active_from?: Date;

    @IsOptional()
    @Transform(({ value }) => (value ? new Date(value) : null))
    @IsDate()
    active_to?: Date;

    @IsOptional()
    note?: string;

    /**
     * Food categories for direct campaign creation (category_ad_page mode)
     * When provided, will create campaign directly without using ads plan
     */
    @IsOptional()
    @Transform(({ value }) => {
        if (value == null) return undefined;
        return Array.isArray(value) ? value.map((v) => toNumber(v)) : [toNumber(value)];
    })
    @IsNumber({}, { each: true })
    food_categories?: number[];

    /**
     * Keyword for direct campaign creation (category_ad_page mode)
     * When provided, will create campaign directly without using ads plan
     */
    @IsOptional()
    @Transform(({ value }) => {
        if (value == null) return undefined;
        return Array.isArray(value) ? value : [value];
    })
    @IsString({ each: true })
    keywords?: string[];
}

export class ActionAdsPlanV2TcpDto {
    restaurant_id: number;

    @IsNotEmpty()
    @IsNumber()
    plan_id: number;
    seller_id?: number;
    commission?: number;
    platform?: string;
    active_from?: Date;
    active_to?: Date;
    note?: string;
    food_categories: number[];
}

export class GetCampaignsHTTPDto {
    page?: number;
    limit?: number;
    orderBy?: string;
    sortedBy?: string;
    status?: string;
}