import { IsNotEmpty, IsOptional } from 'class-validator';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { EBannerPositionSlot } from 'src/entities/adsBannerTemplate.entity';

export class CreateAdsBannerTemplateDto {
    @IsNotEmpty()
    title: string;

    @IsOptional()
    sub_title: string;

    @IsNotEmpty()
    url: string;

    @IsNotEmpty()
    is_active: number;

    @IsNotEmpty()
    position_slot: EBannerPositionSlot;
}

export class GetAdsBannerTemplatesDto extends BaseQueryFilterDto {
    @IsOptional()
    @IsNotEmpty()
    position_slot: EBannerPositionSlot;

    @IsOptional()
    @IsNotEmpty()
    is_active: number;

    @IsOptional()
    @IsNotEmpty()
    title: string;
}
