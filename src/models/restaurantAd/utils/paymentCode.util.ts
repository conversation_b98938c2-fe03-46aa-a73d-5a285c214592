import { IPaymentCodeConfig } from '../dto/paymentWebhook.dto';
import {
    PAYMENT_CODE_CONSTANTS,
    getDefaultPaymentCodeConfig,
    getPaymentCodePrefix,
    FIXED_LENGTH_CONFIG,
} from '../constants/paymentCode.constants';

export class PaymentCodeUtil {
    /**
     * Generate fixed-length payment code with province encoding (VILLADS-only prefix)
     *
     * Compact Format (15 chars): "VILLADS{8_encoded_chars}"
     * - Encoding: base36(provinceId * 1,000,000 + campaignId)
     * - Capacity: 2.8 trillion combinations (base36^8)
     * - Supports: provinces 1-99, campaigns 0-999999 per province
     *
     * @param campaignId - Campaign ID (max 999999 for compact format)
     * @param provinceId - Province ID (max 99 for compact format)
     * @returns Fixed-length payment code (15 characters)
     */
    static generateFixedLengthPaymentCode(
        campaignId: number,
        provinceId: string | number
    ): string {
        // Input validation
        if (campaignId == null || isNaN(campaignId) || campaignId < 0) {
            throw new Error(`Invalid campaign ID: ${campaignId}. Must be a positive number.`);
        }

        const numericProvinceId = parseInt(provinceId.toString(), 10);
        if (isNaN(numericProvinceId) || numericProvinceId < 1 || numericProvinceId > FIXED_LENGTH_CONFIG.COMPACT_MAX_PROVINCE_ID) {
            throw new Error(`Invalid province ID: ${provinceId}. Must be between 1 and ${FIXED_LENGTH_CONFIG.COMPACT_MAX_PROVINCE_ID} for compact format.`);
        }

        if (campaignId > FIXED_LENGTH_CONFIG.COMPACT_MAX_CAMPAIGN_ID) {
            throw new Error(`Campaign ID too large: ${campaignId}. Must be <= ${FIXED_LENGTH_CONFIG.COMPACT_MAX_CAMPAIGN_ID} for compact format.`);
        }

        const prefix = getPaymentCodePrefix();

        // Compact encoding: province * 1,000,000 + campaign
        const encodedValue = numericProvinceId * 1000000 + campaignId;

        // Validate encoded value fits in available space
        if (encodedValue > FIXED_LENGTH_CONFIG.MAX_ENCODED_VALUE) {
            throw new Error(`Encoded value ${encodedValue} exceeds maximum ${FIXED_LENGTH_CONFIG.MAX_ENCODED_VALUE}`);
        }

        // Convert to base36 and pad to 8 characters
        const encodedString = encodedValue.toString(36).toUpperCase().padStart(FIXED_LENGTH_CONFIG.ENCODED_DIGITS, '0');

        // Construct compact code: "VILLADS" + 8 encoded chars
        const compactCode = prefix + encodedString;

        // Verify exact length (should always be 15 chars with "VILLADS" prefix)
        if (compactCode.length !== FIXED_LENGTH_CONFIG.TOTAL_LENGTH) {
            throw new Error(`Generated code length ${compactCode.length} doesn't match expected ${FIXED_LENGTH_CONFIG.TOTAL_LENGTH}`);
        }

        return compactCode.toUpperCase();
    }

    /**
     * Extract province and campaign from compact fixed-length payment code
     * Supports both new format (15 chars) and legacy format (21 chars)
     * @param paymentCode - Compact fixed-length payment code (15 or 21 chars)
     * @param prefix - Expected prefix (optional, will auto-detect)
     * @returns Decoded information
     */
    static extractFromFixedLengthCode(
        paymentCode: string,
        prefix?: string
    ): {
        provinceId: string | null;
        campaignId: number | null;
        isValid: boolean;
        isFixedLength: boolean;
        format?: 'new' | 'legacy';
        error?: string;
    } {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return {
                provinceId: null,
                campaignId: null,
                isValid: false,
                isFixedLength: false,
                error: 'Invalid payment code format'
            };
        }

        // Auto-detect format based on length and content
        let detectedPrefix: string;
        let expectedLength: number;
        let format: 'new' | 'legacy';

        if (paymentCode.length === FIXED_LENGTH_CONFIG.TOTAL_LENGTH && paymentCode.startsWith('VILLADS')) {
            // New format: 15 chars, "VILLADS" prefix
            detectedPrefix = 'VILLADS';
            expectedLength = FIXED_LENGTH_CONFIG.TOTAL_LENGTH;
            format = 'new';
        } else if (paymentCode.length === FIXED_LENGTH_CONFIG.LEGACY_TOTAL_LENGTH && paymentCode.startsWith('SEVQR VILLADS')) {
            // Legacy format: 21 chars, "SEVQR VILLADS" prefix
            detectedPrefix = 'SEVQR VILLADS';
            expectedLength = FIXED_LENGTH_CONFIG.LEGACY_TOTAL_LENGTH;
            format = 'legacy';
        } else {
            return {
                provinceId: null,
                campaignId: null,
                isValid: false,
                isFixedLength: false,
                error: `Invalid format. Expected 15 chars (VILLADS) or 21 chars (SEVQR VILLADS), got ${paymentCode.length} chars`
            };
        }

        // Use provided prefix or detected prefix
        const finalPrefix = prefix || detectedPrefix;

        try {
            // Extract encoded string after detected prefix
            const afterPrefix = paymentCode.substring(detectedPrefix.length);

            // For compact format, expect 8 encoded characters
            if (afterPrefix.length < FIXED_LENGTH_CONFIG.ENCODED_DIGITS) {
                throw new Error(`Missing encoded data after prefix. Expected ${FIXED_LENGTH_CONFIG.ENCODED_DIGITS} chars, got ${afterPrefix.length}`);
            }

            // Get the encoded string (8 characters)
            const encodedString = afterPrefix.substring(0, FIXED_LENGTH_CONFIG.ENCODED_DIGITS);

            // Decode from base36
            const encodedValue = parseInt(encodedString, 36);
            if (isNaN(encodedValue)) {
                throw new Error(`Invalid encoded string: ${encodedString}`);
            }

            // Validate encoded value is within range
            if (encodedValue > FIXED_LENGTH_CONFIG.MAX_ENCODED_VALUE) {
                throw new Error(`Encoded value ${encodedValue} exceeds maximum ${FIXED_LENGTH_CONFIG.MAX_ENCODED_VALUE}`);
            }

            // Decode: province = floor(value / 1,000,000), campaign = value % 1,000,000
            const provinceId = Math.floor(encodedValue / 1000000);
            const campaignId = encodedValue % 1000000;

            // Validate extracted values for compact format
            if (provinceId < 1 || provinceId > FIXED_LENGTH_CONFIG.COMPACT_MAX_PROVINCE_ID) {
                throw new Error(`Invalid province ID: ${provinceId}. Must be 1-${FIXED_LENGTH_CONFIG.COMPACT_MAX_PROVINCE_ID}`);
            }

            if (campaignId < 0 || campaignId > FIXED_LENGTH_CONFIG.COMPACT_MAX_CAMPAIGN_ID) {
                throw new Error(`Invalid campaign ID: ${campaignId}. Must be 0-${FIXED_LENGTH_CONFIG.COMPACT_MAX_CAMPAIGN_ID}`);
            }

            return {
                provinceId: provinceId.toString(),
                campaignId,
                isValid: true,
                isFixedLength: true,
                format
            };

        } catch (error) {
            return {
                provinceId: null,
                campaignId: null,
                isValid: false,
                isFixedLength: true,
                format,
                error: error.message
            };
        }
    }

    /**
     * Test method to verify province detection works correctly
     * @param provinceId - Province ID to test
     * @param campaignId - Campaign ID to test
     * @returns Test result with generated code and detected province
     */
    static testProvinceDetection(provinceId: number, campaignId: number): {
        generatedCode: string;
        detectedProvince: string | null;
        isWorking: boolean;
        error?: string;
    } {
        try {
            // Generate code
            const generatedCode = this.generateFixedLengthPaymentCode(campaignId, provinceId);

            // Detect province
            const detectedProvince = this.detectProvinceOptimized(generatedCode);

            // Check if detection worked
            const isWorking = detectedProvince === provinceId.toString();

            return {
                generatedCode,
                detectedProvince,
                isWorking,
                error: isWorking ? undefined : `Expected ${provinceId}, got ${detectedProvince}`
            };
        } catch (error) {
            return {
                generatedCode: '',
                detectedProvince: null,
                isWorking: false,
                error: error.message
            };
        }
    }

    // Note: Compact format doesn't use checksum due to space constraints
    // Validation is done through base36 encoding limits and range checks

    /**
     * Fast province detection from fixed-length code - O(1)
     * @param paymentCode - Payment code
     * @returns Province ID or null
     */
    static detectProvinceFromFixedCode(paymentCode: string): string | null {
        const result = this.extractFromFixedLengthCode(paymentCode);
        return result.isValid ? result.provinceId : null;
    }

    /**
     * Generate payment code for campaign (legacy method for backward compatibility)
     * @param campaignId - Campaign ID
     * @param config - Configuration for code generation
     * @returns Generated payment code
     */
    static generatePaymentCode(campaignId: number, config: Partial<IPaymentCodeConfig> = {}): string {
        // Input validation
        if (campaignId == null || isNaN(campaignId) || campaignId < 0) {
            throw new Error(`Invalid campaign ID: ${campaignId}. Must be a positive number.`);
        }

        // Convert to integer to handle float inputs
        const validCampaignId = Math.floor(Math.abs(campaignId));

        const defaultConfig = getDefaultPaymentCodeConfig();
        const finalConfig = { ...defaultConfig, ...config };
        const { prefix, length, includeTimestamp, includeRandom } = finalConfig;

        let code = prefix;

        // Add campaign ID
        code += validCampaignId.toString();

        // Add timestamp component (last 4 digits of timestamp)
        if (includeTimestamp) {
            const timestamp = Date.now().toString();
            const timestampSuffix = timestamp.slice(-4);
            code += timestampSuffix;
        }

        // Add random component
        if (includeRandom) {
            const randomLength = Math.max(2, length - code.length + prefix.length);
            const randomString = this.generateRandomString(randomLength);
            code += randomString;
        }

        // Ensure minimum length
        while (code.length < prefix.length + length) {
            code += this.generateRandomString(1);
        }

        // Truncate if too long
        if (code.length > prefix.length + length) {
            code = code.substring(0, prefix.length + length);
        }

        return code.toUpperCase();
    }

    /**
     * Extract campaign ID from payment code (flexible version - no pattern validation)
     * @param paymentCode - Payment code (e.g., "VILLADS123456" or "SEVQR VILLADS123456")
     * @param prefix - Expected prefix
     * @returns Campaign ID or null if invalid
     */
    static extractCampaignIdFlexible(paymentCode: string, prefix: string): number | null {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return null;
        }

        // Validate prefix
        if (!paymentCode.startsWith(prefix)) {
            return null;
        }

        // Remove prefix
        const codeWithoutPrefix = paymentCode.substring(prefix.length);

        // Extract numeric part from the beginning
        const numericMatch = codeWithoutPrefix.match(/^(\d+)/);
        if (!numericMatch) {
            return null;
        }

        const fullNumericPart = numericMatch[1];

        // Parse the campaign ID directly
        const campaignId = parseInt(fullNumericPart, 10);
        if (isNaN(campaignId)) {
            return null;
        }

        return campaignId;
    }

    /**
     * Extract campaign ID from payment code (legacy version with pattern validation)
     * @param paymentCode - Payment code (e.g., "VILLADS123456")
     * @param prefix - Expected prefix (default: from constants)
     * @returns Campaign ID or null if invalid
     */
    static extractCampaignId(paymentCode: string, prefix: string = getPaymentCodePrefix()): number | null {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return null;
        }

        // Validate prefix
        if (!paymentCode.startsWith(prefix)) {
            return null;
        }

        // Additional security: Check against full pattern
        if (!PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(paymentCode)) {
            return null;
        }

        // Remove prefix
        const codeWithoutPrefix = paymentCode.substring(prefix.length);

        // Extract numeric part from the beginning - be more precise
        const numericMatch = codeWithoutPrefix.match(/^(\d+)/);
        if (!numericMatch) {
            return null;
        }

        const fullNumericPart = numericMatch[1];

        // For payment codes generated with unique IDs (campaignId * 100 + index),
        // we need to extract the original campaign ID
        const uniqueCampaignId = parseInt(fullNumericPart, 10);
        if (isNaN(uniqueCampaignId) || uniqueCampaignId < 0) {
            return null;
        }

        // Enhanced logic for multiple payments
        // Multiple payment IDs are generated as: originalId * 1000 + index (where index is 1-999)
        // So they will be at least 1001 (1*1000+1) and have specific patterns
        if (uniqueCampaignId >= 1001) {
            const lastThreeDigits = uniqueCampaignId % 1000;
            const potentialOriginalId = Math.floor(uniqueCampaignId / 1000);

            // Multiple payment criteria:
            // 1. Last three digits must be 001-999 (payment index)
            // 2. Original ID must be reasonable (1-9999)
            // 3. The number must be >= 1001 (minimum: 1*1000+1)
            if (
                lastThreeDigits >= 1 &&
                lastThreeDigits <= 999 &&
                potentialOriginalId >= 1 &&
                potentialOriginalId <= 9999
            ) {
                return potentialOriginalId;
            }
        }

        // For single payments or campaign IDs that don't match multiple payment pattern
        return uniqueCampaignId;
    }

    /**
     * Extract unique campaign ID from payment code (used for payment lookup)
     * @param paymentCode - Payment code
     * @param prefix - Expected prefix (default: from constants)
     * @returns Unique campaign ID or null if invalid
     */
    static extractUniqueCampaignId(paymentCode: string, prefix: string = getPaymentCodePrefix()): number | null {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return null;
        }

        // Validate prefix
        if (!paymentCode.startsWith(prefix)) {
            return null;
        }

        // Additional security: Check against full pattern
        if (!PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(paymentCode)) {
            return null;
        }

        // Remove prefix
        const codeWithoutPrefix = paymentCode.substring(prefix.length);

        // Extract numeric part from the beginning - be more precise
        const numericMatch = codeWithoutPrefix.match(/^(\d+)/);
        if (!numericMatch) {
            return null;
        }

        const uniqueCampaignId = parseInt(numericMatch[1], 10);
        return isNaN(uniqueCampaignId) || uniqueCampaignId < 0 ? null : uniqueCampaignId;
    }

    /**
     * Validate payment code format with flexible prefix support
     * Supports both formats:
     * - With SUB_PREFIX: "SEVQR VILLADS0000LGK9"
     * - Without SUB_PREFIX: "VILLADS0000LGK9"
     * @param paymentCode - Payment code to validate
     * @returns Validation result
     */
    static validatePaymentCodeFlexible(
        paymentCode: string,
    ): {
        isValid: boolean;
        campaignId: number | null;
        error?: string;
        detectedFormat?: 'with_sub_prefix' | 'without_sub_prefix';
    } {
        // Enhanced input validation
        if (!paymentCode || typeof paymentCode !== 'string') {
            return {
                isValid: false,
                campaignId: null,
                error: PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.REQUIRED,
            };
        }

        // Security: Check for dangerous characters (allow space for new format)
        if (paymentCode.includes('_') || /[^A-Z0-9\s]/.test(paymentCode)) {
            return {
                isValid: false,
                campaignId: null,
                error: 'Payment code contains invalid characters. Only A-Z, 0-9, and spaces allowed.',
            };
        }

        // Try with legacy SUB_PREFIX first: "SEVQR VILLADS..." (for backward compatibility)
        const legacyFullPrefix = 'SEVQR VILLADS'; // Legacy format
        if (paymentCode.startsWith(legacyFullPrefix)) {
            // Validate against full pattern
            if (PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(paymentCode)) {
                const campaignId = this.extractCampaignIdFlexible(paymentCode, legacyFullPrefix);
                if (campaignId !== null) {
                    return {
                        isValid: true,
                        campaignId,
                        detectedFormat: 'with_sub_prefix',
                    };
                }
            }
        }

        // Try with new format: "VILLADS..." (current format)
        const currentPrefix = getPaymentCodePrefix(); // "VILLADS"
        if (paymentCode.startsWith(currentPrefix)) {
            // Create pattern for VILLADS only (without SEVQR)
            const villadsPrefixPattern = /^VILLADS(STG|DEV|TEST)?\d+[A-Z0-9]*$/;
            if (villadsPrefixPattern.test(paymentCode)) {
                const campaignId = this.extractCampaignIdFlexible(paymentCode, currentPrefix);
                if (campaignId !== null) {
                    return {
                        isValid: true,
                        campaignId,
                        detectedFormat: 'without_sub_prefix',
                    };
                }
            }
        }

        return {
            isValid: false,
            campaignId: null,
            error: 'Payment code must start with either "SEVQR VILLADS" or "VILLADS"',
        };
    }

    /**
     * Validate payment code format (legacy method - kept for backward compatibility)
     * @param paymentCode - Payment code to validate
     * @param prefix - Expected prefix (default: from constants)
     * @returns Validation result
     */
    static validatePaymentCode(
        paymentCode: string,
        prefix: string = getPaymentCodePrefix(),
    ): {
        isValid: boolean;
        campaignId: number | null;
        error?: string;
    } {
        // Enhanced input validation
        if (!paymentCode || typeof paymentCode !== 'string') {
            return {
                isValid: false,
                campaignId: null,
                error: PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.REQUIRED,
            };
        }

        // Security: Check for dangerous characters (allow space for new format)
        if (paymentCode.includes('_') || /[^A-Z0-9\s]/.test(paymentCode)) {
            return {
                isValid: false,
                campaignId: null,
                error: 'Payment code contains invalid characters. Only A-Z, 0-9, and spaces allowed.',
            };
        }

        // Validate prefix
        if (!paymentCode.startsWith(prefix)) {
            return {
                isValid: false,
                campaignId: null,
                error: PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.INVALID_PREFIX,
            };
        }

        // Validate against full pattern
        if (!PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(paymentCode)) {
            return {
                isValid: false,
                campaignId: null,
                error: 'Payment code format is invalid.',
            };
        }

        const campaignId = this.extractCampaignId(paymentCode, prefix);
        if (campaignId === null) {
            return {
                isValid: false,
                campaignId: null,
                error: PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.INVALID_CAMPAIGN_ID,
            };
        }

        return {
            isValid: true,
            campaignId,
        };
    }

    /**
     * Generate random alphanumeric string
     * @param length - Length of random string
     * @returns Random string
     */
    private static generateRandomString(length: number): string {
        const chars = PAYMENT_CODE_CONSTANTS.RANDOM_CHARS;
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * Generate multiple payment codes for testing
     * @param campaignIds - Array of campaign IDs
     * @param config - Configuration for code generation
     * @returns Array of payment codes
     */
    static generateMultiplePaymentCodes(
        campaignIds: number[],
        config: Partial<IPaymentCodeConfig> = {},
    ): { campaignId: number; paymentCode: string }[] {
        return campaignIds.map((campaignId) => ({
            campaignId,
            paymentCode: this.generatePaymentCode(campaignId, config),
        }));
    }

    /**
     * Hybrid payment code processor - tries fixed-length first, then legacy
     * This is the main method that should be used for all payment code processing
     * @param paymentCode - Payment code to process
     * @returns Processing result with format detection
     */
    static processPaymentCodeHybrid(
        paymentCode: string
    ): {
        provinceId: string | null;
        campaignId: number | null;
        format: 'fixed' | 'legacy' | 'invalid';
        isValid: boolean;
        error?: string;
    } {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return {
                provinceId: null,
                campaignId: null,
                format: 'invalid',
                isValid: false,
                error: 'Invalid payment code format'
            };
        }

        // Step 1: Try fixed-length format first (supports both new and legacy)
        if (paymentCode.length === FIXED_LENGTH_CONFIG.TOTAL_LENGTH ||
            paymentCode.length === FIXED_LENGTH_CONFIG.LEGACY_TOTAL_LENGTH) {
            const fixedResult = this.extractFromFixedLengthCode(paymentCode);
            if (fixedResult.isValid) {
                return {
                    provinceId: fixedResult.provinceId,
                    campaignId: fixedResult.campaignId,
                    format: 'fixed',
                    isValid: true
                };
            }
        }

        // Step 2: Fallback to legacy format
        const legacyResult = this.validatePaymentCode(paymentCode);
        if (legacyResult.isValid) {
            return {
                provinceId: null, // Legacy format doesn't contain province info
                campaignId: legacyResult.campaignId,
                format: 'legacy',
                isValid: true
            };
        }

        return {
            provinceId: null,
            campaignId: null,
            format: 'invalid',
            isValid: false,
            error: 'Invalid payment code format'
        };
    }

    /**
     * Fast province detection - tries fixed-length first, returns null for legacy
     * @param paymentCode - Payment code
     * @returns Province ID or null if not detectable
     */
    static detectProvinceOptimized(paymentCode: string): string | null {
        if (!paymentCode || typeof paymentCode !== 'string') {
            return null;
        }

        // Quick check: if length matches fixed format, try direct extraction
        if (paymentCode.length === FIXED_LENGTH_CONFIG.TOTAL_LENGTH) {
            const fixedResult = this.extractFromFixedLengthCode(paymentCode);
            if (fixedResult.isValid && fixedResult.provinceId) {
                return fixedResult.provinceId;
            }
        }

        // For legacy format or invalid codes, return null
        return null;
    }

    /**
     * Parse payment code to get detailed information (enhanced version)
     * @param paymentCode - Payment code to parse
     * @param prefix - Expected prefix (default: from constants)
     * @returns Enhanced parsed information
     */
    static parsePaymentCodeEnhanced(
        paymentCode: string,
        prefix: string = getPaymentCodePrefix(),
    ): {
        isValid: boolean;
        format: 'fixed' | 'legacy' | 'invalid';
        prefix: string;
        provinceId: string | null;
        campaignId: number | null;
        suffix: string;
        fullCode: string;
        error?: string;
    } {
        const hybridResult = this.processPaymentCodeHybrid(paymentCode);

        if (!hybridResult.isValid) {
            return {
                isValid: false,
                format: 'invalid',
                prefix: '',
                provinceId: null,
                campaignId: null,
                suffix: '',
                fullCode: paymentCode,
                error: hybridResult.error,
            };
        }

        if (hybridResult.format === 'fixed') {
            return {
                isValid: true,
                format: 'fixed',
                prefix,
                provinceId: hybridResult.provinceId,
                campaignId: hybridResult.campaignId,
                suffix: '', // Fixed format doesn't have suffix
                fullCode: paymentCode,
            };
        }

        // Legacy format parsing
        const codeWithoutPrefix = paymentCode.substring(prefix.length);
        const numericMatch = codeWithoutPrefix.match(/^(\d+)(.*)/);

        return {
            isValid: true,
            format: 'legacy',
            prefix,
            provinceId: null,
            campaignId: hybridResult.campaignId,
            suffix: numericMatch ? numericMatch[2] : '',
            fullCode: paymentCode,
        };
    }

    /**
     * Legacy parse method for backward compatibility
     */
    static parsePaymentCode(
        paymentCode: string,
        prefix: string = getPaymentCodePrefix(),
    ): {
        isValid: boolean;
        prefix: string;
        campaignId: number | null;
        suffix: string;
        fullCode: string;
        error?: string;
    } {
        const enhanced = this.parsePaymentCodeEnhanced(paymentCode, prefix);
        return {
            isValid: enhanced.isValid,
            prefix: enhanced.prefix,
            campaignId: enhanced.campaignId,
            suffix: enhanced.suffix,
            fullCode: enhanced.fullCode,
            error: enhanced.error,
        };
    }
}

// Export utility functions for convenience
export const generatePaymentCode = PaymentCodeUtil.generatePaymentCode;
export const extractCampaignId = PaymentCodeUtil.extractCampaignId;
export const extractUniqueCampaignId = PaymentCodeUtil.extractUniqueCampaignId;
export const validatePaymentCode = PaymentCodeUtil.validatePaymentCode;
export const validatePaymentCodeFlexible = PaymentCodeUtil.validatePaymentCodeFlexible;
export const parsePaymentCode = PaymentCodeUtil.parsePaymentCode;
