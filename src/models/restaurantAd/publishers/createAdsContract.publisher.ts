import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import { AdsCampaign } from 'src/entities/adsCampaigns.entity';
import { RABBIT_MQ_EXCHANGE } from 'src/rabbitMQ/rabbitMQ.constant';
import { CreateAdsContractMessage } from '../messages/createAdsContract.message';

@Injectable()
export class AdsContractPublisher {
    private logger = new Logger(AdsContractPublisher.name);
    constructor(private readonly amqpConnection: AmqpConnection) {}

    async publishCreateContractEvent(
        campaign: AdsCampaign,
        provinceId: string,
    ): Promise<void> {
        try {
            if (!campaign || !provinceId) {
                return;
            }

            this.logger.log(`Publish created ads campaign: ${campaign.id} | provinceId: ${provinceId}`);

            const msg = new CreateAdsContractMessage(campaign, parseInt(provinceId));

            await this.amqpConnection.publish(
                RABBIT_MQ_EXCHANGE.ADS_V2_CONTRACT.name,
                RABBIT_MQ_EXCHANGE.ADS_V2_CONTRACT.routingKeys.created,
                msg,
            );
        } catch (error) {
            this.logger.error(
                `[${provinceId}] Publish order driver expense failed: ${error.message} | stack: ${
                    error.stack
                } | args: ${JSON.stringify(campaign)}`,
            );
        }
    }
}
