/**
 * Payment Code Constants
 * Centralized constants for payment code generation and validation
 */

export const PAYMENT_CODE_CONSTANTS = {
    // Payment code sub prefix (added before main prefix)
    SUB_PREFIX: 'SEVQR',

    // Payment code prefix
    PREFIX: 'VILLADS',

    // Payment code configuration
    DEFAULT_LENGTH: 8,
    MIN_LENGTH: 6,
    MAX_LENGTH: 20,

    // Code generation settings
    INCLUDE_TIMESTAMP: true,
    INCLUDE_RANDOM: true,

    // Validation patterns (updated to support sub prefix + main prefix)
    SUB_PREFIX_PATTERN: /^SEVQR\s+/,
    PREFIX_PATTERN: /^SEVQR\s+VILLADS(STG|DEV|TEST)?/,
    CAMPAIGN_ID_PATTERN: /^(\d+)/,
    FULL_CODE_PATTERN: /^SEVQR\s+VILLADS(STG|DEV|TEST)?\d+[A-Z0-9]*$/,

    // Error messages (updated for new format)
    ERROR_MESSAGES: {
        REQUIRED: 'Payment code is required',
        INVALID_SUB_PREFIX: 'Payment code must start with SEVQR (legacy format)',
        INVALID_PREFIX: 'Payment code must start with VILLADS or SEVQR VILLADS (legacy)',
        INVALID_CAMPAIGN_ID: 'Invalid campaign ID in payment code',
        INVALID_FORMAT: 'Invalid payment code format',
        NOT_FOUND: 'Payment code not found',
        ALREADY_PROCESSED: 'Payment already processed',
    },

    // Character sets for random generation
    RANDOM_CHARS: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
    NUMERIC_CHARS: '0123456789',
    ALPHA_CHARS: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
} as const;

/**
 * Payment code prefix - updated to use only VILLADS (no SEVQR sub prefix)
 * Note: Changed from 'SEVQR VILLADS' to 'VILLADS' for new payment codes
 * Format: "VILLADS..." (total prefix: 7 chars)
 * Legacy format "SEVQR VILLADS..." still accepted for webhook processing
 */
export const PAYMENT_CODE_PREFIXES = {
    PRODUCTION: 'VILLADS',
    STAGING: 'VILLADS',
    DEVELOPMENT: 'VILLADS',
    TEST: 'VILLADS',
} as const;

/**
 * Fixed-length payment code configuration (updated for VILLADS-only prefix)
 *
 * Format Analysis:
 * - Full prefix = "VILLADS" = 7 chars (FIXED for all environments)
 * - Available data space = TOTAL_LENGTH - 7 = 8 chars
 *
 * COMPACT Format (15 chars): "VILLADS" + 8 encoded chars
 * - Data space: 8 chars = base36^8 = 2.8 trillion combinations
 * - Encoding: provinceId * 1,000,000 + campaignId
 * - Supports: 99 provinces × 1M campaigns = 99M combinations
 *
 * Legacy Format (21 chars): "SEVQR VILLADS" + 8 encoded chars (still accepted for webhooks)
 */
export const FIXED_LENGTH_CONFIG = {
    // === CORE CONFIGURATION ===
    TOTAL_LENGTH: 15, // Total payment code length ("VILLADS" + 8 data chars)

    // === COMPACT FORMAT (current: 15-char) ===
    ENCODED_DIGITS: 8, // Number of chars for encoding (15 - 7 = 8)
    MAX_ENCODED_VALUE: 2821109907455, // base36^8 - 1 (ZZZZZZZZ in base36)

    // Compact format capacity calculation:
    // provinceId * 1,000,000 + campaignId <= 2,821,109,907,455
    // Required: 99 * 1,000,000 + 999,999 = 99,999,999 (easily fits!)
    COMPACT_MAX_PROVINCE_ID: 99, // 1-99 provinces for compact
    COMPACT_MAX_CAMPAIGN_ID: 999999, // 0-999999 campaigns per province

    // === LEGACY FORMAT (21-char, still accepted for webhooks) ===
    LEGACY_TOTAL_LENGTH: 21, // Legacy format: "SEVQR VILLADS" + 8 data chars
    LEGACY_PREFIX_LENGTH: 13, // "SEVQR VILLADS" = 13 chars

    // === VALIDATION PATTERNS ===
    // New format: "VILLADS" + 8 base36 chars
    FIXED_FORMAT_PATTERN: /^VILLADS[0-9A-Z]{8}$/,
    // Legacy format: "SEVQR VILLADS" + 8 base36 chars (for webhook compatibility)
    LEGACY_FORMAT_PATTERN: /^SEVQR\s+VILLADS[0-9A-Z]{8}$/,
} as const;

/**
 * Payment code configuration presets (legacy - for backward compatibility)
 */
export const PAYMENT_CODE_CONFIGS = {
    DEFAULT: {
        prefix: PAYMENT_CODE_CONSTANTS.PREFIX,
        length: PAYMENT_CODE_CONSTANTS.DEFAULT_LENGTH,
        includeTimestamp: PAYMENT_CODE_CONSTANTS.INCLUDE_TIMESTAMP,
        includeRandom: PAYMENT_CODE_CONSTANTS.INCLUDE_RANDOM,
    },
    SHORT: {
        prefix: PAYMENT_CODE_CONSTANTS.PREFIX,
        length: 6,
        includeTimestamp: false,
        includeRandom: true,
    },
    LONG: {
        prefix: PAYMENT_CODE_CONSTANTS.PREFIX,
        length: 12,
        includeTimestamp: true,
        includeRandom: true,
    },
    TIMESTAMP_ONLY: {
        prefix: PAYMENT_CODE_CONSTANTS.PREFIX,
        length: 8,
        includeTimestamp: true,
        includeRandom: false,
    },
    // New fixed-length config as default
    FIXED_LENGTH: {
        prefix: PAYMENT_CODE_CONSTANTS.PREFIX,
        length: FIXED_LENGTH_CONFIG.TOTAL_LENGTH,
        includeTimestamp: false,
        includeRandom: false,
        useFixedLength: true,
    },
} as const;

/**
 * Environment-specific prefix getter
 */
export const getPaymentCodePrefix = (): string => {
    const env = process.env.NODE_ENV || 'development';

    switch (env.toLowerCase()) {
        case 'production':
            return PAYMENT_CODE_PREFIXES.PRODUCTION;
        case 'staging':
            return PAYMENT_CODE_PREFIXES.STAGING;
        case 'test':
            return PAYMENT_CODE_PREFIXES.TEST;
        default:
            return PAYMENT_CODE_PREFIXES.DEVELOPMENT;
    }
};

/**
 * Get default payment code configuration (now uses fixed-length by default)
 */
export const getDefaultPaymentCodeConfig = () => ({
    ...PAYMENT_CODE_CONFIGS.FIXED_LENGTH,
    prefix: getPaymentCodePrefix(),
});

/**
 * Get legacy payment code configuration (for backward compatibility)
 */
export const getLegacyPaymentCodeConfig = () => ({
    ...PAYMENT_CODE_CONFIGS.DEFAULT,
    prefix: getPaymentCodePrefix(),
});

// Export individual constants for convenience
export const {
    PREFIX,
    DEFAULT_LENGTH,
    MIN_LENGTH,
    MAX_LENGTH,
    INCLUDE_TIMESTAMP,
    INCLUDE_RANDOM,
    PREFIX_PATTERN,
    CAMPAIGN_ID_PATTERN,
    FULL_CODE_PATTERN,
    ERROR_MESSAGES,
    RANDOM_CHARS,
    NUMERIC_CHARS,
    ALPHA_CHARS,
} = PAYMENT_CODE_CONSTANTS;
