import {
    PAYMENT_CODE_CONSTANTS,
    PAYMENT_CODE_PREFIXES,
    PAYMENT_CODE_CONFIGS,
    getPaymentCodePrefix,
    getDefaultPaymentCodeConfig,
} from '../constants/paymentCode.constants';
import { PaymentCodeUtil } from '../utils/paymentCode.util';

describe('Payment Code Constants', () => {
    describe('Environment-specific prefixes', () => {
        const originalEnv = process.env.NODE_ENV;

        afterEach(() => {
            process.env.NODE_ENV = originalEnv;
        });

        it('should return production prefix for production environment', () => {
            process.env.NODE_ENV = 'production';
            expect(getPaymentCodePrefix()).toBe(PAYMENT_CODE_PREFIXES.PRODUCTION);
            expect(getPaymentCodePrefix()).toBe('VILLADS');
        });

        it('should return staging prefix for staging environment', () => {
            process.env.NODE_ENV = 'staging';
            expect(getPaymentCodePrefix()).toBe(PAYMENT_CODE_PREFIXES.STAGING);
            expect(getPaymentCodePrefix()).toBe('VILLADS_STG');
        });

        it('should return test prefix for test environment', () => {
            process.env.NODE_ENV = 'test';
            expect(getPaymentCodePrefix()).toBe(PAYMENT_CODE_PREFIXES.TEST);
            expect(getPaymentCodePrefix()).toBe('VILLADS_TEST');
        });

        it('should return development prefix for development environment', () => {
            process.env.NODE_ENV = 'development';
            expect(getPaymentCodePrefix()).toBe(PAYMENT_CODE_PREFIXES.DEVELOPMENT);
            expect(getPaymentCodePrefix()).toBe('VILLADS_DEV');
        });

        it('should return development prefix for unknown environment', () => {
            process.env.NODE_ENV = 'unknown';
            expect(getPaymentCodePrefix()).toBe(PAYMENT_CODE_PREFIXES.DEVELOPMENT);
            expect(getPaymentCodePrefix()).toBe('VILLADS_DEV');
        });
    });

    describe('Default configuration', () => {
        it('should return correct default configuration', () => {
            const config = getDefaultPaymentCodeConfig();
            
            expect(config.prefix).toBe(getPaymentCodePrefix());
            expect(config.length).toBe(PAYMENT_CODE_CONSTANTS.DEFAULT_LENGTH);
            expect(config.includeTimestamp).toBe(PAYMENT_CODE_CONSTANTS.INCLUDE_TIMESTAMP);
            expect(config.includeRandom).toBe(PAYMENT_CODE_CONSTANTS.INCLUDE_RANDOM);
        });
    });

    describe('Validation patterns', () => {
        it('should validate prefix pattern correctly', () => {
            expect(PAYMENT_CODE_CONSTANTS.PREFIX_PATTERN.test('VILLADS123')).toBe(true);
            expect(PAYMENT_CODE_CONSTANTS.PREFIX_PATTERN.test('VILLADS_STG123')).toBe(true);
            expect(PAYMENT_CODE_CONSTANTS.PREFIX_PATTERN.test('INVALID123')).toBe(false);
            expect(PAYMENT_CODE_CONSTANTS.PREFIX_PATTERN.test('villads123')).toBe(false);
        });

        it('should extract campaign ID using pattern', () => {
            const testCases = [
                { input: '123ABC', expected: '123' },
                { input: '456XYZ789', expected: '456' },
                { input: '789', expected: '789' },
                { input: 'ABC123', expected: null },
            ];

            testCases.forEach(({ input, expected }) => {
                const match = input.match(PAYMENT_CODE_CONSTANTS.CAMPAIGN_ID_PATTERN);
                if (expected) {
                    expect(match).toBeTruthy();
                    expect(match[1]).toBe(expected);
                } else {
                    expect(match).toBeFalsy();
                }
            });
        });

        it('should validate full code pattern correctly', () => {
            const validCodes = [
                'VILLADS123',
                'VILLADS123ABC',
                'VILLADS4567890XYZ',
                'VILLADS1A2B3C',
            ];

            const invalidCodes = [
                'INVALID123',
                'villads123',
                'VILLADS',
                'VILLADSABC',
                'VILLADS123abc',
                'VILLADS123-ABC',
            ];

            validCodes.forEach(code => {
                expect(PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(code)).toBe(true);
            });

            invalidCodes.forEach(code => {
                expect(PAYMENT_CODE_CONSTANTS.FULL_CODE_PATTERN.test(code)).toBe(false);
            });
        });
    });

    describe('Error messages', () => {
        it('should have all required error messages', () => {
            const requiredMessages = [
                'REQUIRED',
                'INVALID_PREFIX',
                'INVALID_CAMPAIGN_ID',
                'INVALID_FORMAT',
                'NOT_FOUND',
                'ALREADY_PROCESSED',
            ];

            requiredMessages.forEach(key => {
                expect(PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES[key]).toBeDefined();
                expect(typeof PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES[key]).toBe('string');
                expect(PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES[key].length).toBeGreaterThan(0);
            });
        });
    });

    describe('Character sets', () => {
        it('should have valid character sets', () => {
            expect(PAYMENT_CODE_CONSTANTS.RANDOM_CHARS).toMatch(/^[A-Z0-9]+$/);
            expect(PAYMENT_CODE_CONSTANTS.NUMERIC_CHARS).toMatch(/^[0-9]+$/);
            expect(PAYMENT_CODE_CONSTANTS.ALPHA_CHARS).toMatch(/^[A-Z]+$/);
            
            expect(PAYMENT_CODE_CONSTANTS.RANDOM_CHARS.length).toBe(36); // 26 letters + 10 digits
            expect(PAYMENT_CODE_CONSTANTS.NUMERIC_CHARS.length).toBe(10);
            expect(PAYMENT_CODE_CONSTANTS.ALPHA_CHARS.length).toBe(26);
        });
    });

    describe('Configuration presets', () => {
        it('should have valid default configuration', () => {
            const config = PAYMENT_CODE_CONFIGS.DEFAULT;
            
            expect(config.prefix).toBe(PAYMENT_CODE_CONSTANTS.PREFIX);
            expect(config.length).toBe(PAYMENT_CODE_CONSTANTS.DEFAULT_LENGTH);
            expect(config.includeTimestamp).toBe(true);
            expect(config.includeRandom).toBe(true);
        });

        it('should have valid short configuration', () => {
            const config = PAYMENT_CODE_CONFIGS.SHORT;
            
            expect(config.prefix).toBe(PAYMENT_CODE_CONSTANTS.PREFIX);
            expect(config.length).toBe(6);
            expect(config.includeTimestamp).toBe(false);
            expect(config.includeRandom).toBe(true);
        });

        it('should have valid long configuration', () => {
            const config = PAYMENT_CODE_CONFIGS.LONG;
            
            expect(config.prefix).toBe(PAYMENT_CODE_CONSTANTS.PREFIX);
            expect(config.length).toBe(12);
            expect(config.includeTimestamp).toBe(true);
            expect(config.includeRandom).toBe(true);
        });

        it('should have valid timestamp-only configuration', () => {
            const config = PAYMENT_CODE_CONFIGS.TIMESTAMP_ONLY;
            
            expect(config.prefix).toBe(PAYMENT_CODE_CONSTANTS.PREFIX);
            expect(config.length).toBe(8);
            expect(config.includeTimestamp).toBe(true);
            expect(config.includeRandom).toBe(false);
        });
    });
});

describe('PaymentCodeUtil with Constants', () => {
    describe('Generation with constants', () => {
        it('should generate code with default configuration', () => {
            const code = PaymentCodeUtil.generatePaymentCode(123);
            const expectedPrefix = getPaymentCodePrefix();
            
            expect(code.startsWith(expectedPrefix)).toBe(true);
            expect(code.length).toBeGreaterThan(expectedPrefix.length + 3); // prefix + campaign ID + extras
        });

        it('should generate code with preset configurations', () => {
            const shortCode = PaymentCodeUtil.generatePaymentCode(123, PAYMENT_CODE_CONFIGS.SHORT);
            const longCode = PaymentCodeUtil.generatePaymentCode(123, PAYMENT_CODE_CONFIGS.LONG);
            
            expect(shortCode.length).toBeLessThan(longCode.length);
            expect(shortCode.startsWith(PAYMENT_CODE_CONSTANTS.PREFIX)).toBe(true);
            expect(longCode.startsWith(PAYMENT_CODE_CONSTANTS.PREFIX)).toBe(true);
        });
    });

    describe('Validation with constants', () => {
        it('should validate using constants error messages', () => {
            const emptyValidation = PaymentCodeUtil.validatePaymentCode('');
            expect(emptyValidation.isValid).toBe(false);
            expect(emptyValidation.error).toBe(PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.REQUIRED);

            const invalidPrefixValidation = PaymentCodeUtil.validatePaymentCode('INVALID123');
            expect(invalidPrefixValidation.isValid).toBe(false);
            expect(invalidPrefixValidation.error).toBe(PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.INVALID_PREFIX);

            const invalidCampaignValidation = PaymentCodeUtil.validatePaymentCode('VILLADSABC');
            expect(invalidCampaignValidation.isValid).toBe(false);
            expect(invalidCampaignValidation.error).toBe(PAYMENT_CODE_CONSTANTS.ERROR_MESSAGES.INVALID_CAMPAIGN_ID);
        });

        it('should validate generated codes successfully', () => {
            const code = PaymentCodeUtil.generatePaymentCode(123);
            const validation = PaymentCodeUtil.validatePaymentCode(code);
            
            expect(validation.isValid).toBe(true);
            expect(validation.campaignId).toBe(123);
            expect(validation.error).toBeUndefined();
        });
    });

    describe('Extraction with constants', () => {
        it('should extract campaign ID using constants', () => {
            const testCodes = [
                { code: 'VILLADS123ABC', expected: 123 },
                { code: 'VILLADS456XYZ789', expected: 456 },
                { code: 'VILLADS789', expected: 789 },
            ];

            testCodes.forEach(({ code, expected }) => {
                const campaignId = PaymentCodeUtil.extractCampaignId(code);
                expect(campaignId).toBe(expected);
            });
        });

        it('should return null for invalid codes', () => {
            const invalidCodes = [
                'INVALID123',
                'VILLADSABC',
                'villads123',
                '',
            ];

            invalidCodes.forEach(code => {
                const campaignId = PaymentCodeUtil.extractCampaignId(code);
                expect(campaignId).toBeNull();
            });
        });
    });
});
