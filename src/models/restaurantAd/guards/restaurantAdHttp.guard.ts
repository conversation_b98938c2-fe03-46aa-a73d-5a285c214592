import {
    CanActivate,
    ExecutionContext,
    Injectable,
    BadRequestException,
    Logger,
} from '@nestjs/common';
import { Request } from 'express';

/**
 * Guard để validate headers cho Restaurant Ads HTTP API
 * Kiểm tra các headers bắt buộc: x-province và x-user-id (tùy endpoint)
 */
@Injectable()
export class RestaurantAdHttpGuard implements CanActivate {
    private readonly logger = new Logger(RestaurantAdHttpGuard.name);

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest() as Request;
        const handler = context.getHandler();
        const className = context.getClass().name;
        const methodName = handler.name;

        // Lấy headers
        const provinceId = request.headers['x-province'] as string;
        const userId = request.headers['x-user-id'] as string;
        const isMerchant = request.headers['x-is-merchant'] as string;

        this.logger.log(`[${className}.${methodName}] Validating headers - Province: ${provinceId}, User: ${userId}, isMerchant: ${isMerchant}`);

        if (isMerchant !== 'true') {
            this.logger.error(`[${className}.${methodName}] Invalid x-is-merchant header: ${isMerchant}`);
            throw new BadRequestException('You are not a merchant');
        }
        if (isMerchant) {
            this.logger.log(`[${className}.${methodName}] Request from merchant`);
        }

        // Validate x-province header (bắt buộc cho tất cả endpoints trừ health check)
        if (methodName !== 'healthCheck') {
            if (!provinceId) {
                this.logger.error(`[${className}.${methodName}] Missing x-province header`);
                throw new BadRequestException('Header x-province là bắt buộc');
            }

            // Validate province ID format (phải là số)
            if (!/^\d+$/.test(provinceId)) {
                this.logger.error(`[${className}.${methodName}] Invalid x-province format: ${provinceId}`);
                throw new BadRequestException('Header x-province phải là số');
            }

            // Validate province ID range (1-99)
            const provinceIdNum = parseInt(provinceId);
            if (provinceIdNum < 1 || provinceIdNum > 99) {
                this.logger.error(`[${className}.${methodName}] Invalid x-province range: ${provinceIdNum}`);
                throw new BadRequestException('Header x-province phải trong khoảng 1-99');
            }
        }

        // Validate x-user-id header (bắt buộc cho một số endpoints cụ thể)
        // const methodsRequireUserId = [
        //     'actionAdsPlanV2',
        //     'requestNewCampaign',
        // ];

        // if (methodsRequireUserId.includes(methodName)) {
        //     if (!userId) {
        //         this.logger.error(`[${className}.${methodName}] Missing x-user-id header`);
        //         throw new BadRequestException('Header x-user-id là bắt buộc cho endpoint này');
        //     }

        //     // Validate user ID format (phải là số)
        //     if (!/^\d+$/.test(userId)) {
        //         this.logger.error(`[${className}.${methodName}] Invalid x-user-id format: ${userId}`);
        //         throw new BadRequestException('Header x-user-id phải là số');
        //     }

        //     // Validate user ID range (phải > 0)
        //     const userIdNum = parseInt(userId);
        //     if (userIdNum <= 0) {
        //         this.logger.error(`[${className}.${methodName}] Invalid x-user-id value: ${userIdNum}`);
        //         throw new BadRequestException('Header x-user-id phải lớn hơn 0');
        //     }
        // }

        // Validate Content-Type cho POST requests
        if (request.method === 'POST') {
            const contentType = request.headers['content-type'];
            if (!contentType || !contentType.includes('application/json')) {
                this.logger.error(`[${className}.${methodName}] Invalid Content-Type: ${contentType}`);
                throw new BadRequestException('Content-Type phải là application/json');
            }
        }

        this.logger.log(`[${className}.${methodName}] Headers validation passed`);
        return true;
    }
}
