import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import { Payload } from '@nestjs/microservices';
import { RABBIT_MQ_EXCHANGE } from 'src/rabbitMQ/rabbitMQ.constant';
import { AdsContractInfoService } from '../services/adsContractInfo.service';
import { ContractInfoUpdatedMessage } from '../messages/contractInfo.message';
import { Channel, ConsumeMessage } from 'amqplib';
import { UpdateAdsContractDto } from '../dto/adsContract.dto';

const { ADS_V2_CONTRACT } = RABBIT_MQ_EXCHANGE;
@Injectable()
export class ContractInfoUpdatedSubscriber {
    private readonly logger = new Logger(ContractInfoUpdatedSubscriber.name);

    constructor(private adsContractInfoService: AdsContractInfoService) {}

    @RabbitSubscribe({
        exchange: ADS_V2_CONTRACT.name,
        routingKey: ADS_V2_CONTRACT.routingKeys.updated,
        queue: 'admin-api-service/ads-contract-info-updated',
        createQueueIfNotExists: true,
        errorHandler: (channel: Channel, msg: ConsumeMessage, error: any) => {
            console.error(msg);
            console.error(error);
            channel.ack(msg);
        },
    })
    public async updatedHandler(@Payload() payload: ContractInfoUpdatedMessage) {
        try {
            const updatePayload = new UpdateAdsContractDto();
            Object.assign(updatePayload, payload);
            await this.adsContractInfoService.updateAdsContractInfo(updatePayload);
        } catch (error) {
            this.logger.error(`[updatedAdsContractHandler] error: ${error.message} | stack: ${error.stack}`);
        }
    }

    // @RabbitSubscribe({
    //     exchange: ADS_V2_CONTRACT.name,
    //     routingKey: ADS_V2_CONTRACT.routingKeys.updated,
    //     queue: 'admin-api-service/ads-contract-info-updated',
    //     createQueueIfNotExists: true,
    // })
    // public async createdHandler(@Payload() payload: ContractInfoUpdatedMessage) {
    //     try {
    //         await this.adsContractInfoService.updateAdsContractInfo(payload.provinceId.toString(), payload.campaignId, payload.contractId, payload.contractStatus);
    //     } catch (error) {
    //         this.logger.error(`[UpdateRestaurantPromotionsToEls] error: ${error.message} | stack: ${error.stack}`);
    //     }
    // }
}
