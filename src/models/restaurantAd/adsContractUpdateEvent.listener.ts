import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EAdsCampaignEventNames } from 'src/events/constant';
import { AdsCampaignsService } from './services/adsCampaigns.service';
import { AdsCampaignUpdateDto } from 'src/events/dto/adsCampaignUpdate.dto';

@Injectable()
export class AdsCampaignUpdatedEventListener {
    private logger = new Logger(AdsCampaignUpdatedEventListener.name);
    constructor(private adsCampaignsService: AdsCampaignsService) {}

    @OnEvent(EAdsCampaignEventNames.ADS_CAMPAIGN_UPDATE_STATUS, { async: true })
    async handleAdsCampaignUpdateStatusEvent(eventData: AdsCampaignUpdateDto) {
        try {
            const { adsCampaignId, provinceId } = eventData;
            this.logger.log(`handleAdsCampaignUpdateStatusEvent: adsCampaignId-${adsCampaignId}; provinceId-${provinceId}`);
            await this.adsCampaignsService.approveCampaignAndActivateItems(provinceId.toString(), adsCampaignId);
        } catch (error) {
            this.logger.error(`[handleAdsCampaignUpdateStatusEvent] event data: ${JSON.stringify(eventData)}`);
        }
    }
}
