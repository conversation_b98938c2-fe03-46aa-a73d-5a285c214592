import { Controller, Post, Get, Body, Param, Query, Headers, Logger, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiParam, ApiSecurity } from '@nestjs/swagger';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor, SkipLoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { RestaurantAdHttpGuard } from './guards/restaurantAdHttp.guard';
import { GetCampaignsHTTPDto } from './dto/adsPlans.dto';
import { AdsRequestInfoDto } from './dto/adsRequestinfo.dto';
import { ActionAdsPlanDto } from './dto/adsPlans.dto';
import { KeywordHttpQueryDto } from './dto/keywordManagement.dto';
import { GetAdsCollectionsDto } from './dto/adsCollections.dto';
import { GetAdsBannerTemplatesDto } from './dto/adsBannerTemplate.dto';

import { AdsCampaignsService } from './services/adsCampaigns.service';
import { KeywordService } from './services/adsKeyWord.service';
import { AdsPlansService } from './services/adsPlans.service';
import { AdsFoodCategoryService } from './services/adsFoodCategory.service';
import { AdsCollectionsService } from './services/adsCollections.service';
import { AdsBannerService } from './services/adsBanner.service';

@ApiTags('Restaurant Ads HTTP API')
@Controller('restaurant-ads-v3')
@UseGuards(RestaurantAdHttpGuard)
@UseInterceptors(LoggingInterceptor)
@SkipLoggingInterceptor()
@ApiHeader({
    name: 'x-province',
    description: 'Province ID (1-99) - Required for all endpoints except health check',
    required: true,
    example: '1',
})
@ApiHeader({
    name: 'x-user-id',
    description: 'User ID - Required for specific endpoints (actionAdsPlanV2, requestNewCampaign)',
    required: false,
    example: '123',
})
export class RestaurantAdHttpController {
    private readonly logger = new Logger(RestaurantAdHttpController.name);

    constructor(
        private readonly adsCampaignsService: AdsCampaignsService,
        private readonly adsPlansService: AdsPlansService,
        private readonly keywordService: KeywordService,
        private readonly adsFoodCategoryService: AdsFoodCategoryService,
        private readonly adsCollectionsService: AdsCollectionsService,
        private readonly adsBannerService: AdsBannerService,
    ) {}

    @Post('action-ads-plan-v2')
    @ApiOperation({
        summary: 'Kích hoạt ads plan v2 - tạo campaign category_ad_page',
        description: 'HTTP endpoint để tạo campaign category_ad_page từ merchant app',
    })
    @ApiResponse({ status: 201, description: 'Campaign created successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    @ApiResponse({ status: 500, description: 'Internal server error' })
    async actionAdsPlanV2(
        @HeaderProvince() provinceId: string,
        @Headers('x-user-id') userId: string,
        @Body()
        body: {
            planId: number;
            dto: ActionAdsPlanDto;
        },
    ) {
        this.logger.log(
            `HTTP Request: action-ads-plan-v2 for restaurant ${body.dto.restaurant_id}, plan ${body.planId}`,
        );

        try {
            const result = await this.adsPlansService.actionAdsPlan(
                provinceId,
                body.planId,
                body.dto,
                parseInt(userId),
            );

            this.logger.log(`HTTP Response: action-ads-plan-v2 success for campaign ${result.id}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: action-ads-plan-v2 failed: ${error.message}`);
            throw error;
        }
    }

    @Get('campaigns/:campaignId')
    @ApiOperation({
        summary: 'Lấy chi tiết campaign theo ID',
        description: 'HTTP endpoint để lấy thông tin chi tiết của campaign',
    })
    @ApiParam({ name: 'campaignId', description: 'Campaign ID', type: 'number' })
    @ApiResponse({ status: 200, description: 'Campaign details retrieved successfully' })
    @ApiResponse({ status: 404, description: 'Campaign not found' })
    async getCampaignById(@HeaderProvince() provinceId: string, @Param('campaignId') campaignId: string) {
        this.logger.log(`HTTP Request: get-campaign-by-id for campaign ${campaignId}`);

        try {
            const result = await this.adsCampaignsService.getCampaignById(provinceId, parseInt(campaignId));

            this.logger.log(`HTTP Response: get-campaign-by-id success for campaign ${campaignId}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: get-campaign-by-id failed: ${error.message}`);
            throw error;
        }
    }

    @Get('campaigns/restaurant/:restaurantId')
    @ApiOperation({
        summary: 'Lấy danh sách campaigns theo restaurant',
        description: 'HTTP endpoint để lấy danh sách campaigns của một restaurant',
    })
    @ApiParam({ name: 'restaurantId', description: 'Restaurant ID', type: 'number' })
    @ApiResponse({ status: 200, description: 'Campaigns list retrieved successfully' })
    async getCampaignsByRestaurant(
        @HeaderProvince() provinceId: string,
        @Param('restaurantId') restaurantId: string,
        @Query() query: GetCampaignsHTTPDto,
    ) {
        this.logger.log(`HTTP Request: get-campaigns-by-restaurant for restaurant ${restaurantId}`);

        try {
            const result = await this.adsCampaignsService.getCampaignsByRestaurant(
                provinceId,
                parseInt(restaurantId),
                query,
            );

            this.logger.log(
                `HTTP Response: get-campaigns-by-restaurant success, found ${result.data?.length || 0} campaigns`,
            );
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: get-campaigns-by-restaurant failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/keywords')
    @ApiOperation({
        summary: 'Lấy danh sách keywords với thông tin sử dụng theo nhà hàng',
        description: 'HTTP endpoint để lấy keywords kèm số lượng nhà hàng đã sử dụng và thông tin nhà hàng',
    })
    @ApiResponse({ status: 200, description: 'Keywords retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async getKeywords(@HeaderProvince() provinceId: string, @Body() body: KeywordHttpQueryDto) {
        this.logger.log(`HTTP Request: get-keywords with filters`, body);

        try {
            const result = await this.keywordService.getKeywordsWithUsageCount(provinceId, {
                limit: body.limit,
                page: body.page,
                search: body.keyword,
                // restaurant_id: body.restaurantId,
            });

            this.logger.log(`HTTP Response: get-keywords success, found ${result.data?.length || 0} keywords`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: get-keywords failed: ${error.message}`);
            throw error;
        }
    }

    @Get('health')
    @ApiOperation({
        summary: 'Health check endpoint',
        description: 'Kiểm tra trạng thái service',
    })
    @ApiResponse({ status: 200, description: 'Service is healthy' })
    async healthCheck() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'restaurant-ads-http',
        };
    }

    @Post('campaigns/food-categories')
    @ApiOperation({
        summary: 'Lấy danh sách food categories kèm theo số lượng nhà hàng đang sử dụng quảng cáo với category đó',
        description:
            'HTTP endpoint để lấy danh sách food categories kèm theo số lượng nhà hàng đang sử dụng quảng cáo với category đó',
    })
    @ApiResponse({ status: 200, description: 'Food categories retrieved successfully' })
    async getFoodCategories(
        @HeaderProvince() provinceId: string,
        @Body() body: { limit?: number; page?: number; search?: string; restaurant_id?: number },
    ) {
        this.logger.log(`HTTP Request: get-food-categories for province ${provinceId}`);

        try {
            const result = await this.adsFoodCategoryService.getFoodCategoriesWithUsageCount(provinceId, body);

            this.logger.log(`HTTP Response: get-food-categories success for province ${provinceId}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: get-food-categories failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/request')
    @ApiOperation({
        summary: 'Tạo ticket chiến dịch quảng cáo',
        description: 'HTTP endpoint để tạo ticket chiến dịch quảng cáo',
    })
    @ApiResponse({ status: 200, description: 'Campaign make ticket successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async makeRequestedCampaign(
        @HeaderProvince() provinceId: string,
        @Body() body: { campaignId: number; userId: number },
    ) {
        this.logger.log(`HTTP Request: ticket-campaign for campaign ${body.campaignId}`);

        try {
            const result = await this.adsCampaignsService.makeRequestedCampaignById(
                provinceId,
                body.campaignId,
                body.userId,
            );

            this.logger.log(`HTTP Response: ticket-campaign success for campaign ${body.campaignId}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: ticket-campaign failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/request-new')
    @ApiOperation({
        summary: 'Tạo mới chiến dịch quảng cáo',
        description: 'HTTP endpoint để tạo mới chiến dịch quảng cáo',
    })
    @ApiResponse({ status: 201, description: 'Campaign created successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async requestNewCampaign(
        @HeaderProvince() provinceId: string,
        @Headers('x-user-id') userId: string,
        @Body() body: { restaurantId: number; adCateId: number; requestinfo: AdsRequestInfoDto },
    ) {
        this.logger.log(`HTTP Request: request-new-campaign for restaurant ${body.restaurantId}`);

        try {
            const result = await this.adsCampaignsService.requestNewCampaign(
                provinceId,
                parseInt(userId),
                body.restaurantId,
                body.adCateId,
                body.requestinfo,
            );

            this.logger.log(`HTTP Response: request-new-campaign success for restaurant ${body.restaurantId}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: request-new-campaign failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/cancel')
    @ApiOperation({
        summary: 'Huỷ chiến dịch quảng cáo',
        description: 'HTTP endpoint để huỷ chiến dịch quảng cáo',
    })
    @ApiResponse({ status: 200, description: 'Campaign cancelled successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async cancelCampaign(@HeaderProvince() provinceId: string, @Body() body: { campaignId: number; userId: number }) {
        this.logger.log(`HTTP Request: cancel-campaign for campaign ${body.campaignId}`);

        try {
            const result = await this.adsCampaignsService.cancelCampaignById(provinceId, body.campaignId, body.userId);

            this.logger.log(`HTTP Response: cancel-campaign success for campaign ${body.campaignId}`);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: cancel-campaign failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/collections')
    @ApiOperation({
        summary: 'Lấy danh sách collections',
        description: 'HTTP endpoint để lấy danh sách collections',
    })
    @ApiResponse({ status: 201, description: 'Collections retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async createCampaignCollection(@HeaderProvince() provinceId: string, @Body() body: GetAdsCollectionsDto) {
        try {
            const result = await this.adsCollectionsService.getCollections(provinceId, body);
            console.log('result: ', result);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: create-campaign-collection failed: ${error.message}`);
            throw error;
        }
    }

    @Post('campaigns/banner-templates/get-all')
    @ApiOperation({
        summary: 'Lấy danh sách templates',
        description: 'HTTP endpoint để lấy danh sách templates',
    })
    @ApiResponse({ status: 201, description: 'Templates retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Bad request' })
    async getAdsBannerTemplates(@HeaderProvince() provinceId: string, @Body() body: GetAdsBannerTemplatesDto) {
        try {
            const result = await this.adsBannerService.getAdsBannerTemplates(provinceId, body);
            return result;
        } catch (error) {
            this.logger.error(`HTTP Error: get-ads-banner-templates failed: ${error.message}`);
            throw error;
        }
    }
}
