import { AdsCampaign } from "src/entities/adsCampaigns.entity";


export class CreateAdsContractMessage {
    ads_campaign_id: number;
    province_id: number;
    restaurant_id: number;
    contract_type_id?: number;
    campaign: AdsCampaign;

    constructor(
        adsCampaign: AdsCampaign,
        provinceId: number,
        contractTypeId?: number
    ) {
        this.ads_campaign_id = adsCampaign.id;
        this.province_id = provinceId;
        this.restaurant_id = adsCampaign.restaurant_id;
        this.contract_type_id = contractTypeId;
        this.campaign = adsCampaign;
    }
}