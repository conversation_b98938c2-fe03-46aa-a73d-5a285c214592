import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { TinyInt } from 'src/common/constants';
import { Request } from 'express';
import { AdBannerMerchant } from 'src/entities/adBannerMerchant.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { ProvinceService } from '../province/province.service';
import { CreateAdBannerMerchantDto, UpdateAdBannerMerchantDto } from './adBannerMerchant.dto';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';

@Injectable()
export class AdBannerMerchantService {
    constructor(private provinceService: ProvinceService) {}
    async create(createAdBannerMerchantDto: CreateAdBannerMerchantDto, req: Request) {
        const {
            title,
            image,
            description,
            is_activated,
            start_time,
            end_time,
            video_thumbnail_url,
            video_url,
            event_type,
            metadata,
            external_data,
            provinceIds,
        } = createAdBannerMerchantDto;
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            let newAdBannerMerchant = new AdBannerMerchant({
                title,
                image,
                is_activated,
                start_time,
                description,
                end_time,
                video_thumbnail_url,
                video_url,
                event_type,
                metadata,
                external_data,
                is_global: provinceIds.length === 0 ? TinyInt.YES : TinyInt.NO,
            });
            newAdBannerMerchant = await entityManager.getRepository(AdBannerMerchant).save(newAdBannerMerchant);
            if (provinceIds.length > 0) {
                const provinceExistings = await this.provinceService.findProvincesByIdsWithTransactionWrapper(
                    entityManager,
                    provinceIds,
                );
                newAdBannerMerchant.provinces = provinceExistings;
                return await entityManager.getRepository(AdBannerMerchant).save(newAdBannerMerchant);
            } else {
                return newAdBannerMerchant;
            }
        });
    }

    async update(req: Request, id: number, updateAdBannerMerchantDto: UpdateAdBannerMerchantDto) {
        const {
            title,
            image,
            is_activated,
            start_time,
            end_time,
            video_thumbnail_url,
            video_url,
            event_type,
            description,
            metadata,
            external_data,
            provinceIds,
        } = updateAdBannerMerchantDto;
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            const adBannerExisting = await entityManager
                .getRepository(AdBannerMerchant)
                .createQueryBuilder('adBannerMerchant')
                .leftJoinAndSelect('adBannerMerchant.provinces', 'provinces')
                .where('adBannerMerchant.id = :id', { id })
                .setLock('pessimistic_write')
                .getOne();
            if (!adBannerExisting) {
                throw new HttpException('Không tìm thấy banner', HttpStatus.NOT_FOUND);
            }
            const updateResult = await entityManager
                .getRepository(AdBannerMerchant)
                .createQueryBuilder('adBannerMerchant')
                .update()
                .set({
                    title,
                    image,
                    is_activated,
                    start_time,
                    end_time,
                    description,
                    video_thumbnail_url,
                    video_url,
                    event_type,
                    metadata,
                    external_data,
                    is_global: provinceIds.length === 0 ? TinyInt.YES : TinyInt.NO,
                })
                .where('id = :id', { id })
                .execute();
            if (!updateResult || updateResult.affected === 0) {
                throw new HttpException('Cập nhật banner thất bại', HttpStatus.INTERNAL_SERVER_ERROR);
            } else {
                const adBannerUpdated = await entityManager
                    .getRepository(AdBannerMerchant)
                    .createQueryBuilder('adBannerMerchant')
                    .leftJoinAndSelect('adBannerMerchant.provinces', 'provinces')
                    .where('adBannerMerchant.id = :id', { id })
                    .getOne();
                if (!adBannerUpdated.is_global) {
                    const provinceExistings = await this.provinceService.findProvincesByIdsWithTransactionWrapper(
                        entityManager,
                        provinceIds,
                    );
                    adBannerUpdated.provinces = provinceExistings;
                    return await entityManager.getRepository(AdBannerMerchant).save(adBannerUpdated);
                } else {
                    adBannerUpdated.provinces = [];
                    return await entityManager.getRepository(AdBannerMerchant).save(adBannerUpdated);
                }
            }
        });
    }

    async find(baseQueryFilterDto: BaseQueryFilterDto, provinceId: string) {
        const { sortedBy, orderBy, limit, page } = baseQueryFilterDto;
        return await DatabaseService.getRepositoryByProvinceId(AdBannerMerchant, provinceId).findAndCount({
            order: {
                [orderBy]: sortedBy,
            },
            skip: page * limit,
            take: limit,
        });
    }

    async findOne(req: Request, id: number) {
        return await DatabaseService.getRepository(AdBannerMerchant, req).findOne({
            relations: ['provinces'],
            where: {
                id,
            },
        });
    }

    async delete(id: number, req: Request) {
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            const adBannerExisting = await entityManager
                .getRepository(AdBannerMerchant)
                .createQueryBuilder('adBannerMerchant')
                .leftJoinAndSelect('adBannerMerchant.provinces', 'provinces')
                .where('adBannerMerchant.id = :id', { id })
                .getOne();
            if (!adBannerExisting) {
                throw new HttpException('Không tìm thấy banner', HttpStatus.NOT_FOUND);
            } else {
                if (adBannerExisting.provinces.length > 0) {
                    adBannerExisting.provinces = [];
                    await entityManager.getRepository(AdBannerMerchant).save(adBannerExisting);
                }
                await entityManager.getRepository(AdBannerMerchant).delete({ id });
                return adBannerExisting;
            }
        });
    }
}
