import { Transform, Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    IsUrl,
    ValidateIf,
    ValidateNested,
} from 'class-validator';
import { EBannerEvent, ExternalDataDto, MetadataDto } from 'src/entities/adBannerMerchant.entity';

export class CreateAdBannerMerchantDto {
    @IsNotEmpty()
    @IsString()
    title: string;

    @ValidateIf((obj, value) => !!value)
    @IsDateString()
    start_time: Date;

    @ValidateIf((obj, value) => !!value)
    @IsDateString()
    end_time: Date;

    @IsOptional()
    @IsString()
    description: string;

    @IsOptional()
    @IsString()
    image: string;

    @IsOptional()
    @IsString()
    video_thumbnail_url: string;

    @IsOptional()
    @IsString()
    video_url: string;

    @IsNotEmpty()
    @IsBoolean()
    is_activated: boolean;

    @IsOptional()
    @IsEnum(EBannerEvent)
    event_type: EBannerEvent;

    @IsNotEmpty()
    @IsObject()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata: MetadataDto;

    @IsNotEmpty()
    @IsObject()
    @ValidateNested()
    @Type(() => ExternalDataDto)
    external_data: ExternalDataDto;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    provinceIds: number[];

    @IsOptional()
    @IsNumber()
    promotion_market_id: number;
}

export class UpdateAdBannerMerchantDto extends CreateAdBannerMerchantDto {}
