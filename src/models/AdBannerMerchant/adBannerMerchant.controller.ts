import { Controller, Get, Put, Query, Body, Post, Delete, Req, Param, ParseIntPipe } from '@nestjs/common';
import { Request } from 'express';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { CreateAdBannerMerchantDto, UpdateAdBannerMerchantDto } from './adBannerMerchant.dto';
import { AdBannerMerchantService } from './adBannerMerchant.service';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('ad-banner-merchants')
export class AdBannerMerchantController {
    constructor(private adBannerMerchantService: AdBannerMerchantService) {}

    @Post()
    async create(
        @Body(new HttpValidationPipe()) createAdBannerMerchantDto: CreateAdBannerMerchantDto,
        @Req() req: Request,
    ) {
        return await this.adBannerMerchantService.create(createAdBannerMerchantDto, req);
    }

    @Get()
    async find(@Query(new HttpValidationPipe()) query: BaseQueryFilterDto, @HeaderProvince() provinceId: string) {
        const result = await this.adBannerMerchantService.find(query, provinceId);
        return {
            items: result[0],
            total: result[1],
        };
    }

    @Get('/:id')
    async findOne(@Param('id', new ParseIntPipe()) id: number, @Req() req: Request) {
        return this.adBannerMerchantService.findOne(req, id);
    }

    @Put('/:id')
    async update(
        @Param('id', new ParseIntPipe()) id: number,
        @Body(new HttpValidationPipe()) updateAdBannerMerchantDto: UpdateAdBannerMerchantDto,
        @Req() req: Request,
    ) {
        return await this.adBannerMerchantService.update(req, id, updateAdBannerMerchantDto);
    }

    @Delete('/:id')
    async deleteById(@Param('id', new ParseIntPipe()) id: number, @Req() req: Request) {
        return await this.adBannerMerchantService.delete(id, req);
    }
}
