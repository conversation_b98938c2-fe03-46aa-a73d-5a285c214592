import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail, IsOptional, IsNumber, IsEnum, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { TinyInt } from 'src/common/constants';

export class UserCompanyProfileQueryDto {
    @ApiPropertyOptional({ description: 'Page number', minimum: 1, default: 1 })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({ description: 'Items per page', minimum: 1, maximum: 100, default: 20 })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @ApiPropertyOptional({ description: 'Search by company name, tax code, buyer name, or user info' })
    @IsOptional()
    @IsString()
    search?: string;

    @ApiPropertyOptional({ description: 'Filter by user ID' })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    user_id?: number;

    @ApiPropertyOptional({ description: 'Filter by active status', enum: TinyInt })
    @IsOptional()
    @Type(() => Number)
    @IsEnum(TinyInt)
    is_active?: TinyInt;

    @ApiPropertyOptional({ description: 'Filter by default status', enum: TinyInt })
    @IsOptional()
    @Type(() => Number)
    @IsEnum(TinyInt)
    is_default?: TinyInt;
}

export class CreateUserCompanyProfileDto {
    @ApiProperty({ description: 'User ID' })
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    user_id: number;

    @ApiProperty({ description: 'Company name', maxLength: 255 })
    @IsNotEmpty()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_name: string;

    @ApiProperty({ description: 'Company tax code', maxLength: 50 })
    @IsNotEmpty()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_tax_code: string;

    @ApiProperty({ description: 'Company address' })
    @IsNotEmpty()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_address: string;

    @ApiPropertyOptional({ description: 'Invoice email address' })
    @IsOptional()
    @IsEmail()
    @Transform(({ value }) => value?.trim())
    invoice_email?: string;

    @ApiPropertyOptional({ description: 'Buyer name', maxLength: 255 })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.trim())
    buyer_name?: string;

    @ApiPropertyOptional({ description: 'Set as default profile', enum: TinyInt, default: TinyInt.NO })
    @IsOptional()
    @Type(() => Number)
    @IsEnum(TinyInt)
    is_default?: TinyInt = TinyInt.NO;
}

export class UpdateUserCompanyProfileDto {
    @ApiPropertyOptional({ description: 'Company name', maxLength: 255 })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_name?: string;

    @ApiPropertyOptional({ description: 'Company tax code', maxLength: 50 })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_tax_code?: string;

    @ApiPropertyOptional({ description: 'Company address' })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.trim())
    company_address?: string;

    @ApiPropertyOptional({ description: 'Invoice email address' })
    @IsOptional()
    @IsEmail()
    @Transform(({ value }) => value?.trim())
    invoice_email?: string;

    @ApiPropertyOptional({ description: 'Buyer name', maxLength: 255 })
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value?.trim())
    buyer_name?: string;

    @ApiPropertyOptional({ description: 'Set as default profile', enum: TinyInt })
    @IsOptional()
    @Type(() => Number)
    @IsEnum(TinyInt)
    is_default?: TinyInt;

    @ApiPropertyOptional({ description: 'Active status', enum: TinyInt })
    @IsOptional()
    @Type(() => Number)
    @IsEnum(TinyInt)
    is_active?: TinyInt;
}

export class SetDefaultCompanyProfileDto {
    @ApiProperty({ description: 'Company profile ID to set as default' })
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    id: number;
}
