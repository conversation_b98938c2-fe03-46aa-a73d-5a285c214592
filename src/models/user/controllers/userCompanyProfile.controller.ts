import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    UseInterceptors,
    ParseIntPipe,
    HttpStatus,
    NotFoundException,
} from '@nestjs/common';
import { UserCompanyProfileService } from '../services/userCompanyProfile.service';
import {
    UserCompanyProfileQueryDto,
    CreateUserCompanyProfileDto,
    UpdateUserCompanyProfileDto,
    SetDefaultCompanyProfileDto,
} from '../dto/userCompanyProfile.dto';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import AuthGuard from 'src/common/middlewares/auth.gaurd';

@UseInterceptors(LoggingInterceptor)
@Controller('user-company-profiles')
@UseGuards(AuthGuard)
export class UserCompanyProfileController {
    constructor(private readonly userCompanyProfileService: UserCompanyProfileService) {}

    @Get()
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_FIND_LIST)
    async getCompanyProfiles(@Query() query: UserCompanyProfileQueryDto, @HeaderProvince() provinceId: string) {
        return this.userCompanyProfileService.getList(query, provinceId);
    }

    @Get(':id')
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_FIND_ONE)
    async getCompanyProfile(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        const profile = await this.userCompanyProfileService.findById(id, provinceId);
        if (!profile) {
            throw new NotFoundException('Company profile not found');
        }
        return profile;
    }

    @Post()
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_CREATE)
    async createCompanyProfile(@Body() createDto: CreateUserCompanyProfileDto, @HeaderProvince() provinceId: string) {
        return this.userCompanyProfileService.create(createDto, provinceId);
    }

    @Put(':id')
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_UPDATE)
    async updateCompanyProfile(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateDto: UpdateUserCompanyProfileDto,
        @HeaderProvince() provinceId: string,
    ) {
        return this.userCompanyProfileService.update(id, updateDto, provinceId);
    }

    @Delete(':id')
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_DELETE)
    async deleteCompanyProfile(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        await this.userCompanyProfileService.delete(id, provinceId);
        return { message: 'Company profile deleted successfully' };
    }

    @Put(':id/set-default')
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_UPDATE)
    async setDefaultCompanyProfile(@Param('id', ParseIntPipe) id: number, @HeaderProvince() provinceId: string) {
        return this.userCompanyProfileService.setDefault(id, provinceId);
    }

    @Get('user/:userId')
    // @RequirePermissions(PermissionsAccessAction.USER_COMPANY_PROFILE_FIND_LIST)
    async getCompanyProfilesByUser(
        @Param('userId', ParseIntPipe) userId: number,
        @HeaderProvince() provinceId: string,
    ) {
        return this.userCompanyProfileService.findByUserId(userId, provinceId);
    }
}
