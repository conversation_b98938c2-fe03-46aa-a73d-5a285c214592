import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { UserCompanyProfile } from 'src/entities/userCompanyProfile.entity';
import { DatabaseService, DEFAULT_PROVINCE_ID, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { TinyInt } from 'src/common/constants';
import { SelectQueryBuilder } from 'typeorm';

export interface UserCompanyProfileQueryDto {
    page?: number;
    limit?: number;
    search?: string;
    user_id?: number;
    is_active?: TinyInt;
    is_default?: TinyInt;
}

export interface CreateUserCompanyProfileDto {
    user_id: number;
    company_name: string;
    company_tax_code: string;
    company_address: string;
    invoice_email?: string;
    buyer_name?: string;
    is_default?: TinyInt;
}

export interface UpdateUserCompanyProfileDto {
    company_name?: string;
    company_tax_code?: string;
    company_address?: string;
    invoice_email?: string;
    buyer_name?: string;
    is_default?: TinyInt;
    is_active?: TinyInt;
}

@Injectable()
export class UserCompanyProfileService {
    private readonly logger = new Logger(UserCompanyProfileService.name);

    getUserProvinceId(provinceId: string) {
        if (provinceId === VUNGTAU_PROVINCE_ID.toString()) {
            return VUNGTAU_PROVINCE_ID.toString();
        }
        return DEFAULT_PROVINCE_ID.toString();
    }

    async findById(id: number, provinceId: string): Promise<UserCompanyProfile | null> {
        if (!id) {
            return null;
        }
        return await DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        ).findOne({
            where: {
                id,
                is_active: TinyInt.YES,
            },
            relations: ['user'],
        });
    }

    async findByUserId(userId: number, provinceId: string): Promise<UserCompanyProfile[]> {
        if (!userId) {
            return [];
        }
        return await DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        ).find({
            where: {
                user_id: userId,
                is_active: TinyInt.YES,
            },
            order: { is_default: 'DESC', created_at: 'DESC' },
        });
    }

    async findDefaultByUserId(userId: number, provinceId: string): Promise<UserCompanyProfile | null> {
        if (!userId) {
            return null;
        }
        return await DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        ).findOne({
            where: {
                user_id: userId,
                is_default: TinyInt.YES,
                is_active: TinyInt.YES,
            },
        });
    }

    async validateCompanyProfileForUser(
        companyProfileId: number,
        userId: number,
        provinceId: string,
    ): Promise<UserCompanyProfile> {
        const companyProfile = await DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        ).findOne({
            where: {
                id: companyProfileId,
                user_id: userId,
                is_active: TinyInt.YES,
            },
        });

        if (!companyProfile) {
            throw new NotFoundException('Company profile not found or does not belong to user');
        }

        return companyProfile;
    }

    async getList(query: UserCompanyProfileQueryDto, provinceId: string) {
        const { page = 1, limit = 20, search, user_id, is_active, is_default } = query;

        const queryBuilder = DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        )
            .createQueryBuilder('profile')
            .leftJoinAndSelect('profile.user', 'user');

        this.applyFilters(queryBuilder, { search, user_id, is_active, is_default });

        queryBuilder
            .orderBy('profile.created_at', 'DESC')
            .skip((page - 1) * limit)
            .take(limit);

        const [data, total] = await queryBuilder.getManyAndCount();

        return {
            items: data,
            total,
            page,
            limit,
        };
    }

    private applyFilters(queryBuilder: SelectQueryBuilder<UserCompanyProfile>, filters: any) {
        const { search, user_id, is_active, is_default } = filters;

        if (search) {
            queryBuilder.andWhere(
                '(profile.company_name LIKE :search OR profile.company_tax_code LIKE :search OR profile.buyer_name LIKE :search OR user.name LIKE :search OR user.email LIKE :search)',
                { search: `%${search}%` },
            );
        }

        if (user_id) {
            queryBuilder.andWhere('profile.user_id = :user_id', { user_id });
        }

        if (is_active !== undefined) {
            queryBuilder.andWhere('profile.is_active = :is_active', { is_active });
        }

        if (is_default !== undefined) {
            queryBuilder.andWhere('profile.is_default = :is_default', { is_default });
        }
    }

    async create(data: CreateUserCompanyProfileDto, provinceId: string): Promise<UserCompanyProfile> {
        const repository = DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        );

        // If this is set as default, unset other defaults for the user
        if (data.is_default === TinyInt.YES) {
            await repository.update({ user_id: data.user_id, is_active: TinyInt.YES }, { is_default: TinyInt.NO });
        }

        // Check for duplicate tax code
        const existingProfile = await repository.findOne({
            where: {
                company_tax_code: data.company_tax_code,
                is_active: TinyInt.YES,
            },
        });

        if (existingProfile) {
            throw new BadRequestException('Company tax code already exists');
        }

        const profile = repository.create({
            ...data,
            is_active: TinyInt.YES,
            is_default: data.is_default || TinyInt.NO,
        });

        const savedProfile = await repository.save(profile);

        return await this.findById(savedProfile.id, provinceId);
    }

    async update(id: number, data: UpdateUserCompanyProfileDto, provinceId: string): Promise<UserCompanyProfile> {
        const repository = DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        );

        const profile = await this.findById(id, provinceId);
        if (!profile) {
            throw new NotFoundException('Company profile not found');
        }

        // If setting as default, unset other defaults for the user
        if (data.is_default === TinyInt.YES) {
            await repository.update({ user_id: profile.user_id, is_active: TinyInt.YES }, { is_default: TinyInt.NO });
        }

        // Check for duplicate tax code if it's being changed
        if (data.company_tax_code && data.company_tax_code !== profile.company_tax_code) {
            const existingProfile = await repository.findOne({
                where: {
                    company_tax_code: data.company_tax_code,
                    is_active: TinyInt.YES,
                    id: { $ne: id } as any,
                },
            });

            if (existingProfile) {
                throw new BadRequestException('Company tax code already exists');
            }
        }

        await repository.update(id, data);

        return await this.findById(id, provinceId);
    }

    async delete(id: number, provinceId: string): Promise<void> {
        const repository = DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        );

        const profile = await this.findById(id, provinceId);
        if (!profile) {
            throw new NotFoundException('Company profile not found');
        }

        // Soft delete by setting is_active to NO
        await repository.update(id, { is_active: TinyInt.NO });

        this.logger.log(`Company profile ${id} deleted successfully`);
    }

    async setDefault(id: number, provinceId: string): Promise<UserCompanyProfile> {
        const repository = DatabaseService.getRepositoryByProvinceId(
            UserCompanyProfile,
            this.getUserProvinceId(provinceId),
        );

        const profile = await this.findById(id, provinceId);
        if (!profile) {
            throw new NotFoundException('Company profile not found');
        }

        // Unset other defaults for the user
        await repository.update({ user_id: profile.user_id, is_active: TinyInt.YES }, { is_default: TinyInt.NO });

        // Set this profile as default
        await repository.update(id, { is_default: TinyInt.YES });

        return await this.findById(id, provinceId);
    }
}
