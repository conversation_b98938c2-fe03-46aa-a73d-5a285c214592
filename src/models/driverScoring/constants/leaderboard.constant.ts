import { EVehicleType } from "src/entities/vehicleType.entity";

export const LEADERBOARD_REDIS = 'LEADERBOARD_REDIS';

export enum LeaderboardType {
    DAILY = 'DAILY',
    WEEKLY = 'WEEKLY',
    MONTHLY = 'MONTHLY',
}

export const LEADERBOARD_DATE_KEY_FORMAT = '{provinceId}_DRIVER_ORDERS_{date}';
export const LEADERBOARD_WEEK_KEY_FORMAT = '{provinceId}_DRIVER_ORDERS_{week}';
export const LEADERBOARD_MONTH_KEY_FORMAT = '{provinceId}_DRIVER_ORDERS_{month}';

export const METADATA_KEY_FORMAT = '{provinceId}:driver_leaderboard:metadata:{memberId}';

// Thời gian mặc định để cache (30 ngày)
export const DEFAULT_LEADERBOARD_EXPIRY = 30 * 24 * 60 * 60;
export const DEFAULT_WEEKLY_LEADERBOARD_EXPIRY = 7 * 24 * 60 * 60;

// Tham số mặc định cho các API
export const DEFAULT_TOP_DRIVERS_LIMIT = 10;
export const DEFAULT_AROUND_RANGE = 3;
export const DEFAULT_INCREMENT = 1;

// Cache-related constants
export const LEADERBOARD_CACHE_TTL = 60; // 1 minute in seconds
export const LEADERBOARD_CACHE_PREFIX = 'leaderboard';

// Cache key formats
export const CACHE_KEY_FORMATS = {
    RANKING: (
        provinceId: number | string,
        type: string,
        periodKey: string,
        startRank: number,
        endRank: number,
        rankId?: number,
        vehicleType?: EVehicleType,
    ) =>
        `${LEADERBOARD_CACHE_PREFIX}:${provinceId}:${type}:${periodKey}:${startRank}:${endRank}${
            rankId ? `:rank:${rankId}` : ''
        }${
            vehicleType ? `:vehicle:${vehicleType}` : ''
        }`,
    DRIVER_SCORE: (provinceId: number | string, driverId: number, type: string, periodKey: string) =>
        `${LEADERBOARD_CACHE_PREFIX}:driver:${provinceId}:${driverId}:${type}:${periodKey}`,
    PROVINCE_STATS: (provinceId: number | string, type: string, periodKey: string) =>
        `${LEADERBOARD_CACHE_PREFIX}:stats:${provinceId}:${type}:${periodKey}`,
};
