import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

// Import services from consolidated module
import { ScoringHistoryService } from "./services/scoringHistory.service";
import { ScoringRuleService } from "./services/scoringRule.service";
// import { DriverScoreService } from './services/driverScore.service';
import { DriverScoresService } from "./services/driverScores.service";
import { LeaderboardCoreService } from "./services/leaderboardCore.service";
import { ManualScoringService } from "./services/manualScoring.service";

// Import controllers from consolidated module
import { DriverScoresController } from "./controllers/driverScores.controller";
import { LeaderboardController } from "./controllers/leaderboard.controller";
import { ManualScoringController } from "./controllers/manualScoring.controller";
import { ScoringHistoryController } from "./controllers/scoringAnalytics.controller";
import { ScoringRuleController } from "./controllers/scoringRule.controller";

// Import appSetting module for driver ranking enabled check
import { UserActivityModule } from "../adminUserActivity/userActivity.module";
import { AppSettingModule } from "../appSetting/appSetting.module";
import { AppSettingService } from "../appSetting/appSetting.service";
import { DriverModule } from "../driver/driver.module";
import { UserModule } from "../user/user.module";

@Module({
  imports: [
    ConfigModule,
    AppSettingModule,
    UserActivityModule,
    UserModule,
    DriverModule,
  ],
  providers: [
    AppSettingService,
    DriverScoresService,

    // Core scoring services in dependency order
    ScoringRuleService,
    ScoringHistoryService,

    LeaderboardCoreService,

    // Manual scoring service
    ManualScoringService,

    // Driver scores service

    // Order scoring integration removed to avoid circular dependency
  ],
  controllers: [
    // Scoring rule management
    ScoringRuleController,
    ScoringHistoryController,
    // Leaderboard management
    LeaderboardController,
    // Manual scoring management
    ManualScoringController,
    // Driver scores management
    DriverScoresController,
  ],
  exports: [
    DriverScoresService,

    // Export all services for use in other modules
    ScoringRuleService,
    ScoringHistoryService,
    LeaderboardCoreService,
    // DriverScoreService,
    AppSettingService,
  ],
})
export class DriverScoringModule {}
