import { ApiProperty } from '@nestjs/swagger';
import {
    IsOptional,
    IsInt,
    IsString,
    IsDateString,
    IsEnum,
    IsArray,
    Min,
    Max,
    IsNumber,
    IsBoolean,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { EScoringSourceType, EPointType, EScoringHistoryStatus } from 'src/entities/types/EScoringRule.enum';

export class GetScoringHistoryDto {
    @ApiProperty({ description: 'Page number', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @ApiProperty({ description: 'Records per page', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @ApiProperty({ description: 'Sort field', required: false })
    @IsOptional()
    @IsString()
    @IsEnum(['created_at', 'points_awarded', 'rule_name'])
    sortBy?: 'created_at' | 'points_awarded' | 'rule_name' = 'created_at';

    @ApiProperty({ description: 'Sort order', required: false })
    @IsOptional()
    @IsString()
    @IsEnum(['ASC', 'DESC'])
    sortOrder?: 'ASC' | 'DESC' = 'DESC';

    @ApiProperty({ description: 'Start date', required: false })
    @IsOptional()
    @IsDateString()
    dateFrom?: string;

    @ApiProperty({ description: 'End date', required: false })
    @IsOptional()
    @IsDateString()
    dateTo?: string;

    @ApiProperty({ description: 'Driver ID', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    driverId?: number;

    @ApiProperty({ description: 'Source ID', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    sourceId?: number;

    @ApiProperty({ description: 'Rule ID', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    ruleId?: number;

    @ApiProperty({ description: 'Source types', required: false })
    @IsOptional()
    @IsArray()
    @IsEnum(EScoringSourceType, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    sourceTypes?: EScoringSourceType[];

    @ApiProperty({ description: 'Source IDs', required: false })
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    sourceIds?: number[];

    @ApiProperty({ description: 'Search term', required: false })
    @IsOptional()
    @IsString()
    searchTerm?: string;

    @ApiProperty({ description: 'Min points', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(0)
    minPoints?: number;

    @ApiProperty({ description: 'Max points', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(0)
    maxPoints?: number;

    @ApiProperty({ description: 'Points type (ADD/SUBTRACT)', required: false })
    @IsOptional()
    @IsArray()
    @IsEnum(EPointType, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    pointsTypes?: EPointType[];

    @ApiProperty({
        description: 'Scoring history statuses to filter by',
        required: false,
        enum: EScoringHistoryStatus,
        isArray: true,
    })
    @IsOptional()
    @IsArray()
    @IsEnum(EScoringHistoryStatus, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    statuses?: EScoringHistoryStatus[];

    @ApiProperty({
        description: 'Include deleted records in results',
        required: false,
        default: false,
    })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === 'true' || value === true)
    includeDeleted?: boolean;

    @ApiProperty({
        description: 'Include refund rows (records with original_scoring_history_id) in results. Only for admin use.',
        required: false,
        default: false,
    })
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === 'true' || value === true)
    includeRefundRows?: boolean;

    @ApiProperty({ description: 'Source type', required: false })
    @IsOptional()
    @IsEnum(EScoringSourceType)
    sourceType?: EScoringSourceType;
}
