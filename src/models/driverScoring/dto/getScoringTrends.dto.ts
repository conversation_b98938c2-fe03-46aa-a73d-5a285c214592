import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsIn } from 'class-validator';

export class GetScoringTrendsDto {
    @ApiProperty({ description: 'Time period', required: false })
    @IsOptional()
    @IsString()
    @IsIn(['daily', 'weekly', 'monthly'])
    period?: string = 'daily';

    @ApiProperty({ description: 'Start date', required: false })
    @IsOptional()
    @IsDateString()
    dateFrom?: string;

    @ApiProperty({ description: 'End date', required: false })
    @IsOptional()
    @IsDateString()
    dateTo?: string;
}
