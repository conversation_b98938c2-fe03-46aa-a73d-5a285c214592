import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString, Min, Max, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';
import { EVehicleType } from 'src/entities/vehicleType.entity';

// Interface for driver information returned by services
export interface DriverInfo {
    id: number;
    rank: number;
    score: number;
    name?: string;
    avatar_url?: string;
    last_updated?: Date;
}

// Interfaces for cache services
export interface CacheMetrics {
    hits: number;
    misses: number;
    hitRate: number;
}

export interface LeaderboardCacheData {
    data: DriverRankingItemDto[];
    timestamp: number;
    ttl: number;
}

export interface DriverScoreCacheData {
    score: number;
    rank?: number;
    timestamp: number;
    ttl: number;
}

// Interfaces for core services
export interface UpsertDriverScoreData {
    driver_id: number;
    leaderboard_type: any; // Using any to avoid circular import, will be typed in service
    period_key: string;
    new_points: number;
    vehicle_type: EVehicleType;
}

export interface UpsertDriverScoreResult {
    driverScore: any; // DriverScore entity
    points_before: number;
    points_after: number;
    was_created: boolean; // true if new record was created, false if updated
}

export interface BatchUpsertData {
    scores: UpsertDriverScoreData[];
    province_id: number;
}

export class IncrementLeaderboardScoreDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;

    @ApiProperty({ description: 'The score to increment by', example: 1 })
    @IsNumber()
    score: number;

    @ApiPropertyOptional({
        description: 'Additional metadata about the member',
        example: { name: 'John Doe', avatar: 'https://example.com/avatar.jpg' },
        required: false,
    })
    @IsOptional()
    metadata?: Record<string, any>;
}

export class GetLeaderboardScoreDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;
}

export class GetLeaderboardRankDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;

    @ApiPropertyOptional({
        description: 'Whether to use reverse ranking (lower scores are better)',
        example: false,
        default: false,
    })
    @IsOptional()
    isReversed?: boolean;
}

export class GetTopMembersDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiPropertyOptional({
        description: 'The starting position (0-based)',
        example: 0,
        default: 0,
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    start?: number;

    @ApiPropertyOptional({
        description: 'The ending position (inclusive)',
        example: 9,
        default: 9,
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    end?: number;

    @ApiPropertyOptional({
        description: 'Whether to include scores',
        example: true,
        default: true,
    })
    @IsOptional()
    withScores?: boolean;

    @ApiPropertyOptional({
        description: 'Whether to use reverse ranking (lower scores are better)',
        example: false,
        default: false,
    })
    @IsOptional()
    isReversed?: boolean;
}

export class GetAroundMemberDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;

    @ApiPropertyOptional({
        description: 'The number of members to include before and after',
        example: 5,
        default: 5,
    })
    @IsOptional()
    @IsNumber()
    @Min(0)
    range?: number;

    @ApiPropertyOptional({
        description: 'Whether to use reverse ranking (lower scores are better)',
        example: false,
        default: false,
    })
    @IsOptional()
    isReversed?: boolean;
}

export class SetLeaderboardExpiryDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({
        description: 'Number of seconds until expiration',
        example: 86400, // 24 hours
    })
    @IsNumber()
    @Min(1)
    seconds: number;
}

export class RemoveMemberDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;
}

export class ResetLeaderboardDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;
}

export class GetMemberCountDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;
}

export class GetMemberMetadataDto {
    @ApiProperty({ description: 'The name of the leaderboard', example: 'daily_deliveries' })
    @IsString()
    leaderboardName: string;

    @ApiProperty({ description: 'The member ID', example: '123456' })
    @IsString()
    memberId: string;
}

// New DTOs for database-based leaderboard
export class GetDriverRankingsDto {
    @ApiProperty({
        description: 'Leaderboard type',
        enum: ['daily', 'weekly', 'monthly'],
        example: 'daily',
    })
    @IsString()
    @IsIn(['daily', 'weekly', 'monthly'])
    leaderboard_type: string;

    @ApiProperty({
        description: 'Start date for the leaderboard period',
        example: '2024-01-15',
        required: false,
    })
    @IsOptional()
    @IsString()
    start_date?: string;

    @ApiProperty({
        description: 'End date for the leaderboard period',
        example: '2024-01-15',
        required: false,
    })
    @IsOptional()
    @IsString()
    end_date?: string;

    @ApiProperty({
        description: 'Starting rank position (0-based)',
        example: 0,
        minimum: 0,
    })
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(0)
    start_rank: number;

    @ApiProperty({
        description: 'Ending rank position',
        example: 100,
        minimum: 1,
        maximum: 1000,
    })
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(1)
    @Max(1000)
    end_rank: number;

    @ApiProperty({
        description: 'Interval type for custom leaderboard',
        example: 'custom',
        required: false,
    })
    @IsOptional()
    @IsString()
    interval_type?: string;

    @ApiProperty({
        description: 'Filter by driver rank ID (shipper ranking)',
        example: 1,
        required: false,
    })
    @IsOptional()
    @Transform(({ value }) => (value ? parseInt(value) : undefined))
    @IsNumber()
    @Min(1)
    rank_id?: number;

    @ApiProperty({
        description: 'Vehicle type filter for leaderboard',
        enum: EVehicleType,
        example: EVehicleType.bike,
        required: false,
    })
    @IsOptional()
    @IsEnum(EVehicleType)
    vehicle_type?: EVehicleType;

    @ApiProperty({
        description: 'Filter by sub province ID',
        example: 1,
        required: false,
    })
    @IsOptional()
    @Transform(({ value }) => (value ? parseInt(value) : undefined))
    @IsNumber()
    @Min(1)
    sub_province_id?: number;
}

export class DriverRankingItemDto {
    @ApiProperty({
        description: 'Driver ID',
        example: '12345',
    })
    id: number;

    @ApiProperty({
        description: 'Driver rank position',
        example: 1,
    })
    rank: number;

    @ApiProperty({
        description: 'Driver total score/points',
        example: 150.5,
    })
    score: number;

    @ApiProperty({
        description: 'Driver avatar URL',
        example: 'https://example.com/avatar.jpg',
        required: false,
    })
    avatar_url?: string;

    @ApiProperty({
        description: 'Driver name',
        example: 'Nguyễn Văn A',
    })
    name: string;

    @ApiProperty({
        description: 'Last updated timestamp',
        example: '2024-01-15T10:30:00.000Z',
    })
    last_updated: string;

    @ApiProperty({
        description: 'Driver rank tier information',
        example: {
            id: 1,
            name: 'Vàng',
            code: 'GOLD',
            level: 3,
            image: 'https://example.com/gold.png',
            tier: 'gold',
        },
        required: false,
    })
    rank_tier?: {
        id: number;
        name: string;
        code: string;
        level: number;
        image: string;
    };
}

export class DriverRankingsResponseDto {
    @ApiProperty({
        description: 'List of driver rankings',
        type: [DriverRankingItemDto],
    })
    data: DriverRankingItemDto[];

    @ApiProperty({
        description: 'Total number of drivers in leaderboard',
        example: 500,
    })
    total: number;

    @ApiProperty({
        description: 'Cache information',
        example: {
            cached: true,
            cache_key: 'leaderboard:1:daily:2024-01-15:0:100',
            ttl: 60,
        },
    })
    cache_info?: {
        cached: boolean;
        cache_key?: string;
        ttl: number;
    };
}
