import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDateString, IsEnum, IsNumber, IsOptional, IsString, Max, Min } from 'class-validator';
import { EPointType, EScoringSourceType } from 'src/entities/types/EScoringRule.enum';

export class GetScoringRulesDto {
    @ApiProperty({ description: 'Page number', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @ApiProperty({ description: 'Items per page', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @ApiProperty({ description: 'Sort field', required: false })
    @IsOptional()
    @IsString()
    sortBy?: string = 'created_at';

    @ApiProperty({ description: 'Sort order', required: false })
    @IsOptional()
    @IsString()
    sortOrder?: 'ASC' | 'DESC' = 'DESC';

    @ApiProperty({ description: 'Source types', required: false })
    @IsOptional()
    @IsArray()
    @IsEnum(EScoringSourceType, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    sourceTypes?: EScoringSourceType[];

    @ApiProperty({ description: 'Source IDs', required: false })
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    sourceIds?: number[];

    @ApiProperty({ description: 'Province ID', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    provinceId?: number;

    @ApiProperty({ description: 'Active status', required: false })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    is_active?: boolean;

    @ApiProperty({ description: 'Search term', required: false })
    @IsOptional()
    @IsString()
    searchTerm?: string;

    @ApiProperty({ description: 'Min priority', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(0)
    @Max(100)
    minPriority?: number;

    @ApiProperty({ description: 'Max priority', required: false })
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(0)
    @Max(100)
    maxPriority?: number;

    @ApiProperty({ description: 'Valid from date', required: false })
    @IsOptional()
    @IsDateString()
    validFrom?: Date;

    @ApiProperty({ description: 'Valid to date', required: false })
    @IsOptional()
    @IsDateString()
    validTo?: Date;

    @ApiProperty({ description: 'Include conditions', required: false })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    includeConditions?: boolean = false;

    @ApiProperty({ description: 'Include actions', required: false })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    includeActions?: boolean = false;

    @ApiProperty({ description: 'Include stats', required: false })
    @IsOptional()
    @Transform(({ value }) => {
        if (value === 'true') return true;
        if (value === 'false') return false;
        return value;
    })
    @IsBoolean()
    includeStats?: boolean = false;

    @ApiProperty({ description: 'Scoring type', required: false })
    @IsOptional()
    scoring_type?: EPointType;

    @ApiProperty({ description: 'Name', required: false })
    @IsOptional()
    @IsString()
    name?: string;
}
