import { ScoringRule } from 'src/entities/driverScoringRule.entity';
import { EScoringSourceType } from 'src/entities/types/EScoringRule.enum';

export interface FilterScoringRulesDto {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    sourceTypes?: EScoringSourceType[];
    provinceId?: number;
    is_active?: boolean;
    searchTerm?: string;
    minPriority?: number;
    maxPriority?: number;
    validFrom?: Date;
    validTo?: Date;
    includeConditions?: boolean;
    includeActions?: boolean;
    includeStats?: boolean;
}

export interface PaginatedScoringRulesResult {
    data: ScoringRule[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
