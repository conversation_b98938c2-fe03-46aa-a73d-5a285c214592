import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
    ArrayMinSize,
    IsArray,
    IsBoolean,
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Max,
    Min,
    Validate,
    ValidateNested,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import {
    ECalculationType,
    EConditionOperator,
    EPointType,
    EScoringFieldName,
    EScoringSourceType,
} from 'src/entities/types/EScoringRule.enum';
import { transformDeliveryDayOfWeek } from '../validators/deliveryDayOfWeek.validator';

@ValidatorConstraint({ name: 'operatorRequired', async: false })
export class OperatorRequiredConstraint implements ValidatorConstraintInterface {
    validate(operator: any, args: any) {
        const object = args.object as CreateScoringRuleConditionDto;
        if (
            object.field_name === 'order_type' ||
            object.field_name === 'delivery_hour' ||
            object.field_name === 'payment_method_code'
        ) {
            return true;
        }
        return operator !== undefined && operator !== null;
    }

    defaultMessage() {
        return 'Operator is required for non-order_type, non-delivery_hour, and non-payment_method_code conditions';
    }
}

export class TierConfigDto {
    @ApiProperty({ description: 'Minimum value for this tier' })
    @IsNumber()
    @Min(0)
    min_value: number;

    @ApiProperty({ description: 'Maximum value for this tier' })
    @IsNumber()
    @Min(0)
    max_value: number;

    @ApiProperty({ description: 'Points awarded for this tier' })
    @IsNumber()
    points: number;
}

export class CreateScoringRuleConditionDto {
    @ApiProperty({
        description: 'Source type for the condition data',
        enum: EScoringSourceType,
        default: EScoringSourceType.ORDER,
        required: false,
    })
    @IsOptional()
    @IsEnum(EScoringSourceType)
    source_type?: EScoringSourceType;

    @ApiProperty({ description: 'Field name' })
    @IsString()
    @IsNotEmpty()
    field_name:  EScoringFieldName;

    @ApiProperty({ description: 'Comparison operator', required: false })
    @IsOptional()
    @IsEnum(EConditionOperator)
    @Validate(OperatorRequiredConstraint)
    operator?: EConditionOperator;

    @ApiProperty({ description: 'Comparison value' })
    @IsNotEmpty()
    @Transform(({ value, obj }) => {
        if (obj.field_name === EScoringFieldName.DELIVERY_DAY_OF_WEEK) {
            return transformDeliveryDayOfWeek(value);
        }
        return value;
    })
    value: any;

    @ApiProperty({ description: 'Logical operator', required: false })
    @IsOptional()
    @IsString()
    logical_operator?: string;

    @ApiProperty({ description: 'Order index', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    order_index?: number;
}

export class CreateScoringRuleActionDto {
    @ApiProperty({ description: 'Calculation type' })
    @IsEnum(ECalculationType)
    calculation_type: ECalculationType;

    @IsOptional()
    @ApiProperty({ description: 'Scoring type - ADD or SUBTRACT' })
    @IsEnum(EPointType)
    point_type: EPointType;

    @ApiProperty({ description: 'Base points', required: false })
    @IsOptional()
    @IsNumber()
    base_points?: number;

    @ApiProperty({ description: 'Multiplier', required: false })
    @IsOptional()
    @IsNumber()
    multiplier?: number;

    @ApiProperty({ description: 'Tier config', required: false, type: [TierConfigDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TierConfigDto)
    tiers?: TierConfigDto[];

    @ApiProperty({ description: 'Target field', required: false })
    @IsOptional()
    @IsString()
    target_field?: string;
}

export class CreateScoringRuleDto {
    @ApiProperty({ description: 'Rule name' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: 'Rule description', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Rule type' })
    @IsEnum(EScoringSourceType)
    source_type: EScoringSourceType;

    @ApiProperty({
        description: 'Scoring type - ADD or SUBTRACT',
        enum: EPointType,
        required: false,
    })
    @IsOptional()
    @IsEnum(EPointType)
    @Transform(({ value }) => value?.toUpperCase())
    scoring_type?: EPointType;

    @ApiProperty({ description: 'Priority', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    priority?: number;

    @ApiProperty({ description: 'Valid from', required: false })
    @IsOptional()
    @IsDateString()
    valid_from?: Date;

    @ApiProperty({ description: 'Valid to', required: false })
    @IsOptional()
    @IsDateString()
    valid_to?: Date;

    @ApiProperty({ description: 'Rule conditions' })
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => CreateScoringRuleConditionDto)
    conditions: CreateScoringRuleConditionDto[];

    @ApiProperty({ description: 'Rule actions' })
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => CreateScoringRuleActionDto)
    actions: CreateScoringRuleActionDto[];

    @ApiProperty({ description: 'Is active', required: false })
    @IsOptional()
    @IsBoolean()
    is_active?: boolean;
}
