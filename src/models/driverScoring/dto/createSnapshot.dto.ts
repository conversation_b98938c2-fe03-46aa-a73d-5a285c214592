import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { IntervalType } from 'src/entities/deliveryRevenue.entity';

export class CreateSnapshotDto {
    @ApiProperty({ 
        enum: IntervalType, 
        description: 'Type of interval (date, week, month)' 
    })
    @IsEnum(IntervalType)
    interval_type: IntervalType;
    
    @ApiPropertyOptional({ 
        description: 'ISO date string (YYYY-MM-DD)', 
        example: '2023-10-15' 
    })
    @IsOptional()
    @IsString()
    date?: string;
} 