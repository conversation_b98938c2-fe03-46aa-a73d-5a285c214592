import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class RefundScoringHistoryDto {
    @ApiProperty({
        description: 'Reason for refunding the scoring history record',
        required: false,
    })
    @IsOptional()
    @IsString()
    reason?: string;
}

export class DeleteScoringHistoryDto {
    @ApiProperty({
        description: 'Reason for deleting the scoring history record',
        required: false,
    })
    @IsOptional()
    @IsString()
    reason?: string;
}

export class GetRefundHistoryDto {
    @ApiProperty({
        description: 'Original scoring history ID to get refund records for',
    })
    @Type(() => Number)
    @IsInt()
    @Min(1)
    originalHistoryId: number;
}

export class GetStatusStatisticsDto {
    @ApiProperty({
        description: 'Start date for statistics (ISO string)',
        required: false,
    })
    @IsOptional()
    @IsString()
    dateFrom?: string;

    @ApiProperty({
        description: 'End date for statistics (ISO string)',
        required: false,
    })
    @IsOptional()
    @IsString()
    dateTo?: string;
}

// Response DTOs
export class ScoringHistoryActionResponseDto {
    @ApiProperty({ description: 'Whether the action was successful' })
    success: boolean;

    @ApiProperty({ description: 'Response message' })
    message: string;

    @ApiProperty({
        description: 'ID of the refund record (only for refund action)',
        required: false,
    })
    refundHistoryId?: number;
}

export class StatusStatisticsResponseDto {
    @ApiProperty({ description: 'Statistics for ACTIVE records' })
    SUCCESS: {
        count: number;
        total_points: number;
    };

    @ApiProperty({ description: 'Statistics for REFUNDED records' })
    REFUNDED: {
        count: number;
        total_points: number;
    };

    @ApiProperty({ description: 'Statistics for DELETED records' })
    DELETED: {
        count: number;
        total_points: number;
    };
}
