import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
    IsNumber,
    IsString,
    IsOptional,
    IsEnum,
    IsDateString,
    Min,
    Max,
    IsArray,
    ValidateNested,
    IsNotEmpty,
    Length
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ELeaderboardType } from 'src/entities/driverScore.entity';

/**
 * DTO for manual scoring input
 */
export class ManualScoringDto {
    @ApiProperty({
        description: 'Driver ID to add/update points',
        example: 123,
        minimum: 1
    })
    @IsNumber()
    @Min(1)
    driver_id: number;

    @ApiPropertyOptional({
        description: 'Scoring rule ID to reference',
        example: 456,
        minimum: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    rule_id?: number;

    @ApiProperty({
        description: 'Points to add (positive) or subtract (negative). Always required for manual scoring.',
        example: 50,
        minimum: -10000,
        maximum: 10000
    })
    @IsNumber()
    @Min(-10000)
    @Max(10000)
    points: number;

    @ApiProperty({
        description: 'Leaderboard type',
        enum: ELeaderboardType,
        example: ELeaderboardType.DAILY
    })
    @IsEnum(ELeaderboardType)
    leaderboard_type: ELeaderboardType;

    @ApiPropertyOptional({
        description: 'Specific date for the scoring (YYYY-MM-DD format). If not provided, uses current date',
        example: '2024-01-15'
    })
    @IsOptional()
    @IsDateString()
    target_date?: string;

    @ApiProperty({
        description: 'Reason for manual scoring adjustment',
        example: 'Bonus points for exceptional service',
        minLength: 5,
        maxLength: 500
    })
    @IsString()
    @IsNotEmpty()
    @Length(5, 500)
    reason: string;

    @ApiPropertyOptional({
        description: 'Additional notes',
        example: 'Customer feedback: excellent delivery service',
        maxLength: 1000
    })
    @IsOptional()
    @IsString()
    @Length(0, 1000)
    notes?: string;
}

/**
 * DTO for batch manual scoring
 */
export class BatchManualScoringDto {
    @ApiProperty({
        description: 'Array of manual scoring entries',
        type: [ManualScoringDto]
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ManualScoringDto)
    entries: ManualScoringDto[];

    @ApiPropertyOptional({
        description: 'Batch operation reason',
        example: 'Monthly performance bonus',
        maxLength: 500
    })
    @IsOptional()
    @IsString()
    @Length(0, 500)
    batch_reason?: string;
}

/**
 * DTO for updating existing manual scoring
 */
export class UpdateManualScoringDto {
    @ApiPropertyOptional({
        description: 'Updated scoring rule ID',
        example: 456,
        minimum: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    rule_id?: number;

    @ApiPropertyOptional({
        description: 'New points value',
        minimum: -10000,
        maximum: 10000
    })
    @IsOptional()
    @IsNumber()
    @Min(-10000)
    @Max(10000)
    points?: number;

    @ApiPropertyOptional({
        description: 'Updated reason',
        minLength: 5,
        maxLength: 500
    })
    @IsOptional()
    @IsString()
    @Length(5, 500)
    reason?: string;

    @ApiPropertyOptional({
        description: 'Updated notes',
        maxLength: 1000
    })
    @IsOptional()
    @IsString()
    @Length(0, 1000)
    notes?: string;
}

/**
 * DTO for querying manual scoring history
 */
export class GetManualScoringHistoryDto {
    @ApiPropertyOptional({
        description: 'Page number',
        example: 1,
        minimum: 1
    })
    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Items per page',
        example: 20,
        minimum: 1,
        maximum: 100
    })
    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @ApiPropertyOptional({
        description: 'Filter by driver ID',
        minimum: 1
    })
    @IsOptional()
    @Transform(({ value }) => value ? parseInt(value) : undefined)
    @IsNumber()
    @Min(1)
    driver_id?: number;

    @ApiPropertyOptional({
        description: 'Filter by leaderboard type',
        enum: ELeaderboardType
    })
    @IsOptional()
    @IsEnum(ELeaderboardType)
    leaderboard_type?: ELeaderboardType;

    @ApiPropertyOptional({
        description: 'Filter from date (YYYY-MM-DD)',
        example: '2024-01-01'
    })
    @IsOptional()
    @IsDateString()
    from_date?: string;

    @ApiPropertyOptional({
        description: 'Filter to date (YYYY-MM-DD)',
        example: '2024-01-31'
    })
    @IsOptional()
    @IsDateString()
    to_date?: string;

    @ApiPropertyOptional({
        description: 'Sort by field',
        example: 'created_at',
        enum: ['created_at', 'points_awarded', 'driver_id']
    })
    @IsOptional()
    @IsString()
    sort_by?: string = 'created_at';

    @ApiPropertyOptional({
        description: 'Sort order',
        example: 'DESC',
        enum: ['ASC', 'DESC']
    })
    @IsOptional()
    @IsString()
    sort_order?: 'ASC' | 'DESC' = 'DESC';
}

/**
 * Response DTO for manual scoring operation
 */
export class ManualScoringResponseDto {
    @ApiProperty({
        description: 'Operation success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Driver ID',
        example: 123
    })
    driver_id: number;

    @ApiProperty({
        description: 'Points awarded/deducted',
        example: 50
    })
    points_awarded: number;

    @ApiProperty({
        description: 'Points before the operation',
        example: 100
    })
    points_before: number;

    @ApiProperty({
        description: 'Points after the operation',
        example: 150
    })
    points_after: number;

    @ApiProperty({
        description: 'Leaderboard type',
        enum: ELeaderboardType
    })
    leaderboard_type: ELeaderboardType;

    @ApiProperty({
        description: 'Period key (YYYY-MM-DD, YYYY-WW, YYYY-MM)',
        example: '2024-01-15'
    })
    period_key: string;

    @ApiProperty({
        description: 'History record ID',
        example: 456
    })
    history_id: number;

    @ApiProperty({
        description: 'Operation timestamp',
        example: '2024-01-15T10:30:00Z'
    })
    timestamp: Date;
}

/**
 * Response DTO for batch manual scoring
 */
export class BatchManualScoringResponseDto {
    @ApiProperty({
        description: 'Overall operation success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Number of successful operations',
        example: 8
    })
    successful_count: number;

    @ApiProperty({
        description: 'Number of failed operations',
        example: 2
    })
    failed_count: number;

    @ApiProperty({
        description: 'Individual operation results',
        type: [ManualScoringResponseDto]
    })
    results: ManualScoringResponseDto[];

    @ApiProperty({
        description: 'Error details for failed operations',
        example: [{ driver_id: 999, error: 'Driver not found' }]
    })
    errors: Array<{
        driver_id: number;
        error: string;
    }>;
}

/**
 * DTO for manual scoring history item
 */
export class ManualScoringHistoryDto {
    @ApiProperty({
        description: 'History record ID',
        example: 123
    })
    id: number;

    @ApiProperty({
        description: 'Driver ID',
        example: 456
    })
    driver_id: number;

    @ApiProperty({
        description: 'Points awarded/deducted',
        example: 50
    })
    points_awarded: number;

    @ApiProperty({
        description: 'Leaderboard type',
        enum: ELeaderboardType
    })
    leaderboard_type: ELeaderboardType;

    @ApiProperty({
        description: 'Period key',
        example: '2024-01-15'
    })
    period_key: string;

    @ApiProperty({
        description: 'Reason for manual scoring',
        example: 'Bonus points for exceptional service'
    })
    reason: string;

    @ApiPropertyOptional({
        description: 'Additional notes',
        example: 'Customer feedback: excellent delivery service'
    })
    notes?: string;

    @ApiProperty({
        description: 'Points before operation',
        example: 100
    })
    points_before: number;

    @ApiProperty({
        description: 'Points after operation',
        example: 150
    })
    points_after: number;

    @ApiProperty({
        description: 'Admin user who performed the operation',
        example: 789
    })
    processed_by: number;

    @ApiProperty({
        description: 'Creation timestamp',
        example: '2024-01-15T10:30:00Z'
    })
    created_at: Date;
}

/**
 * Response DTO for manual scoring history list
 */
export class ManualScoringHistoryResponseDto {
    @ApiProperty({
        description: 'List of manual scoring history records',
        type: [ManualScoringHistoryDto]
    })
    items: ManualScoringHistoryDto[];

    @ApiProperty({
        description: 'Total number of records',
        example: 150
    })
    total: number;

    @ApiProperty({
        description: 'Current page number',
        example: 1
    })
    page: number;

    @ApiProperty({
        description: 'Number of items per page',
        example: 20
    })
    limit: number;
}

/**
 * DTO for deleting manual scoring history
 */
export class DeleteManualScoringHistoryDto {
    @ApiPropertyOptional({
        description: 'Specific history record IDs to delete',
        example: [1, 2, 3],
        type: [Number]
    })
    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    @Min(1, { each: true })
    history_ids?: number[];

    @ApiPropertyOptional({
        description: 'Delete all records for specific driver ID',
        example: 123,
        minimum: 1
    })
    @IsOptional()
    @IsNumber()
    @Min(1)
    driver_id?: number;

    @ApiPropertyOptional({
        description: 'Delete records from this date (YYYY-MM-DD format)',
        example: '2024-01-01'
    })
    @IsOptional()
    @IsDateString()
    from_date?: string;

    @ApiPropertyOptional({
        description: 'Delete records until this date (YYYY-MM-DD format)',
        example: '2024-01-31'
    })
    @IsOptional()
    @IsDateString()
    to_date?: string;

    @ApiPropertyOptional({
        description: 'Delete records for specific leaderboard type',
        enum: ELeaderboardType,
        example: ELeaderboardType.DAILY
    })
    @IsOptional()
    @IsEnum(ELeaderboardType)
    leaderboard_type?: ELeaderboardType;

    @ApiProperty({
        description: 'Reason for deleting history records',
        example: 'Data cleanup - removing test records',
        minLength: 5,
        maxLength: 500
    })
    @IsString()
    @IsNotEmpty()
    @Length(5, 500)
    reason: string;
}

/**
 * Response DTO for delete manual scoring history operation
 */
export class DeleteManualScoringHistoryResponseDto {
    @ApiProperty({
        description: 'Operation success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Number of records deleted',
        example: 5
    })
    deleted_count: number;

    @ApiProperty({
        description: 'Reason for deletion',
        example: 'Data cleanup - removing test records'
    })
    reason: string;

    @ApiProperty({
        description: 'Admin user who performed the deletion',
        example: 789
    })
    deleted_by: number;

    @ApiProperty({
        description: 'Deletion timestamp',
        example: '2024-01-15T10:30:00Z'
    })
    deleted_at: Date;
}
