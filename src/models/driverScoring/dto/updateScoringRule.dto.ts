import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsEnum,
    IsNumber,
    IsOptional,
    IsString,
    Max,
    Min,
    ValidateNested
} from 'class-validator';
import {
    ECalculationType,
    EConditionOperator,
    EPointType,
    EScoringFieldName,
    EScoringSourceType,
} from 'src/entities/types/EScoringRule.enum';


export class UpdateScoringRuleConditionDto {
    @ApiProperty({ description: 'Condition ID', required: false })
    @IsOptional()
    @IsNumber()
    id?: number;

    @ApiProperty({ description: 'Field name', required: false })
    @IsOptional()
    @IsEnum(EScoringFieldName)
    field_name?: EScoringFieldName;

    @ApiProperty({ description: 'Operator', required: false })
    @IsOptional()
    @IsEnum(EConditionOperator)
    operator?: EConditionOperator;

    @ApiProperty({ description: 'Value', required: false })
    @IsOptional()
    value?: any;

    @ApiProperty({ description: 'Logical operator', required: false })
    @IsOptional()
    @IsString()
    logical_operator?: string;

    @ApiProperty({ description: 'Order index', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    order_index?: number;
}

export class UpdateScoringRuleActionDto {
    @ApiProperty({ description: 'Action ID', required: false })
    @IsOptional()
    @IsNumber()
    id?: number;

    @ApiProperty({ description: 'Calculation type', required: false })
    @IsOptional()
    @IsEnum(ECalculationType)
    calculation_type?: ECalculationType;

    @ApiProperty({ description: 'Point type - ADD or SUBTRACT', required: false })
    @IsOptional()
    @IsEnum(EPointType)
    point_type?: EPointType;

    @ApiProperty({ description: 'Base points', required: false })
    @IsOptional()
    @IsNumber()
    base_points?: number;

    @ApiProperty({ description: 'Multiplier', required: false })
    @IsOptional()
    @IsNumber()
    multiplier?: number;

    @ApiProperty({ description: 'Tiers', required: false })
    @IsOptional()
    @IsArray()
    tiers?: any[];

    @ApiProperty({ description: 'Target field', required: false })
    @IsOptional()
    @IsString()
    target_field?: string;
}

export class UpdateScoringRuleDto {
    @ApiProperty({ description: 'Rule name', required: false })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ description: 'Description', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Source type', required: false })
    @IsOptional()
    @IsEnum(EScoringSourceType)
    source_type?: EScoringSourceType;

    @ApiProperty({ description: 'Scoring type - ADD or SUBTRACT', required: false })
    @IsOptional()
    @IsEnum(EPointType)
    scoring_type?: EPointType;

    @ApiProperty({ description: 'Province ID', required: false })
    @IsOptional()
    @IsNumber()
    @Min(1)
    province_id?: number;

    @ApiProperty({ description: 'Priority', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    @Max(100)
    priority?: number;

    @ApiProperty({ description: 'Valid from', required: false })
    @IsOptional()
    @IsDateString()
    valid_from?: Date;

    @ApiProperty({ description: 'Valid to', required: false })
    @IsOptional()
    @IsDateString()
    valid_to?: Date;

    @ApiProperty({ description: 'Is active', required: false })
    @IsOptional()
    @IsBoolean()
    is_active?: boolean;

    @ApiProperty({ description: 'Conditions', required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpdateScoringRuleConditionDto)
    conditions?: UpdateScoringRuleConditionDto[];

    @ApiProperty({ description: 'Actions', required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => UpdateScoringRuleActionDto)
    actions?: UpdateScoringRuleActionDto[];
}
