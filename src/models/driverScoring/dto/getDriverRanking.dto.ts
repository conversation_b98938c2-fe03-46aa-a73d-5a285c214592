import { Transform } from 'class-transformer';
import { IsIn, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import * as _ from 'lodash';
import { BaseQueryFilterV2Dto } from 'src/common/pipes/global.dto';
import { IntervalType } from 'src/entities/deliveryRevenue.entity';
import { LeaderboardType } from '../constants/leaderboard.constant';

export enum ELeaderboardTypeDTO {
    DAILY = 'daily',
    WEEKLY = 'weekly',
    MONTHLY = 'monthly',
    CUSTOM = 'custom',
}

export class GetDriverRankingDto extends BaseQueryFilterV2Dto {
    @IsOptional()
    @IsString()
    @IsIn(Object.values(ELeaderboardTypeDTO))
    leaderboard_type?: ELeaderboardTypeDTO = ELeaderboardTypeDTO.DAILY;


    //if leaderboard_type is custom, then interval_type is required
    @IsOptional()
    @IsString()
    @IsIn(Object.values(IntervalType))
    interval_type?: IntervalType;

    @IsOptional()
    @IsString()
    start_date?: string;

    @IsOptional()
    @IsString()
    end_date?: string;

    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    min_delivered_orders?: number;

    //start and end rank
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    start_rank?: number;

    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    end_rank?: number;
}
