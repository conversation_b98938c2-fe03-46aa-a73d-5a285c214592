import { Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

export class GetDriverScoresDto {
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    page?: number = 1;

    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    limit?: number = 20;

    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    driver_id?: number;

    @IsOptional()
    @IsString()
    driver_name?: string;

    @IsOptional()
    @IsString()
    driver_phone?: string;

    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    province_id?: number;

    @IsOptional()
    @IsString()
    period_type?: string;

    @IsOptional()
    @IsString()
    period_key?: string;

    @IsOptional()
    @IsString()
    from_date?: string;

    @IsOptional()
    @IsString()
    to_date?: string;

    @IsOptional()
    @IsString()
    sort_by?: string = 'total_points';

    @IsOptional()
    @IsEnum(['asc', 'desc'])
    sort_order?: 'asc' | 'desc' = 'desc';

    @IsOptional()
    @IsBoolean()
    @Type(() => Boolean)
    include_driver?: boolean = true;
}

export class DriverScoreResponseDto {
    id: number;
    driver_id: number;
    period_type: string;
    period_key: string;
    total_score: number;
    current_rank?: number;
    previous_rank?: number;
    scoring_events_count?: number;
    period_start?: string;
    period_end?: string;
    last_updated: Date;
    created_at: Date;
    updated_at: Date;
    driver?: {
        id: number;
        name: string;
        phone: string;
        avatar?: string;
        province?: {
            id: number;
            name: string;
        };
    };
}

export class DriverScoresSummaryDto {
    total_drivers: number;
    total_periods: number;
    average_score: number;
    highest_score: number;
    lowest_score: number;
    by_period_type: Record<
        string,
        {
            count: number;
            average_score: number;
            highest_score: number;
            lowest_score: number;
        }
    >;
}
