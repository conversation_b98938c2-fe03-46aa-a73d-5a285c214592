import { ApiProperty } from '@nestjs/swagger';
import {
    ECalculationType,
    EConditionOperator,
    EPointType,
    EScoringSourceType,
} from 'src/entities/types/EScoringRule.enum';

export class ScoringRuleConditionResponseDto {
    @ApiProperty({ description: 'ID' })
    id: number;

    @ApiProperty({ description: 'Field name' })
    field_name: string;

    @ApiProperty({ description: 'Operator' })
    operator: EConditionOperator;

    @ApiProperty({ description: 'Value' })
    value: any;

    @ApiProperty({ description: 'Logical operator', required: false })
    logical_operator?: string;

    @ApiProperty({ description: 'Order index' })
    order_index: number;
}

export class ScoringRuleActionResponseDto {
    @ApiProperty({ description: 'ID' })
    id: number;

    @ApiProperty({ description: 'Calculation type' })
    calculation_type: ECalculationType;

    @ApiProperty({ description: 'Point type - ADD or SUBTRACT' })
    point_type: EPointType;

    @ApiProperty({ description: 'Base points' })
    base_points: number;

    @ApiProperty({ description: 'Multiplier' })
    multiplier: number;

    @ApiProperty({ description: 'Tiers', required: false })
    tiers?: any[];

    @ApiProperty({ description: 'Target field', required: false })
    target_field?: string;

    @ApiProperty({ description: 'Max points', required: false })
    max_points?: number;

    @ApiProperty({ description: 'Min points', required: false })
    min_points?: number;
}

export class ScoringRuleUsageStatsDto {
    @ApiProperty({ description: 'Total usage' })
    total_usage: number;

    @ApiProperty({ description: 'Total points awarded' })
    total_points_awarded: number;

    @ApiProperty({ description: 'Avg points per usage' })
    average_points_per_usage: number;

    @ApiProperty({ description: 'Unique drivers' })
    unique_drivers: number;

    @ApiProperty({ description: 'Unique orders' })
    unique_orders: number;

    @ApiProperty({ description: 'Last used' })
    last_used: Date;

    @ApiProperty({ description: 'Usage trend' })
    usage_trend: string;
}

export class ScoringRuleResponseDto {
    @ApiProperty({ description: 'ID' })
    id: number;

    @ApiProperty({ description: 'Name' })
    name: string;

    @ApiProperty({ description: 'Description', required: false })
    description?: string;

    @ApiProperty({ description: 'Source type' })
    source_type: EScoringSourceType;

    @ApiProperty({ description: 'Province ID' })
    province_id: number;

    @ApiProperty({ description: 'Priority' })
    priority: number;

    @ApiProperty({ description: 'Is active' })
    is_active: boolean;

    @ApiProperty({ description: 'Valid from', required: false })
    valid_from?: Date;

    @ApiProperty({ description: 'Valid to', required: false })
    valid_to?: Date;

    @ApiProperty({ description: 'Created at' })
    created_at: Date;

    @ApiProperty({ description: 'Updated at' })
    updated_at: Date;

    @ApiProperty({ description: 'Conditions', required: false })
    conditions?: ScoringRuleConditionResponseDto[];

    @ApiProperty({ description: 'Actions', required: false })
    actions?: ScoringRuleActionResponseDto[];

    @ApiProperty({ description: 'Usage stats', required: false })
    usage_stats?: ScoringRuleUsageStatsDto;
}

export class PaginatedScoringRulesResponseDto {
    @ApiProperty({ description: 'Data' })
    data: ScoringRuleResponseDto[];

    @ApiProperty({ description: 'Pagination' })
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };

    @ApiProperty({ description: 'Filters' })
    filters: {
        sourceTypes?: EScoringSourceType[];
        provinceId?: number;
        is_active?: boolean;
        searchTerm?: string;
        priorityRange?: { min: number; max: number };
        dateRange?: { from: Date; to: Date };
    };
}
