import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsString,
    IsNumber,
    ValidateNested,
    ArrayMinSize,
    IsOptional
} from 'class-validator';
import { CreateScoringRuleConditionDto, CreateScoringRuleActionDto } from './createScoringRule.dto';

export class GenerateRuleSummaryDto {
    @ApiProperty({ description: 'Tên quy tắc', required: false })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ description: 'Mô tả quy tắc', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Điều kiện' })
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => CreateScoringRuleConditionDto)
    conditions: CreateScoringRuleConditionDto[];

    @ApiProperty({ description: 'Hành động' })
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => CreateScoringRuleActionDto)
    actions: CreateScoringRuleActionDto[];
}

export class RuleSummaryExampleDto {
    @ApiProperty({ description: 'Tình huống' })
    scenario: string;

    @ApiProperty({ description: 'Cách tính' })
    calculation: string;

    @ApiProperty({ description: 'Kết quả' })
    result: number;

    @ApiProperty({ description: 'Ghi chú', required: false })
    notes?: string;
}

export class RuleSummaryResponseDto {
    @ApiProperty({ description: 'Mô tả quy tắc' })
    rule_description: string;

    @ApiProperty({ description: 'Tóm tắt điều kiện' })
    conditions_summary: string;

    @ApiProperty({ description: 'Tóm tắt tính điểm' })
    calculation_summary: string;

    @ApiProperty({ description: 'Ví dụ' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => RuleSummaryExampleDto)
    examples: RuleSummaryExampleDto[];

    @ApiProperty({ description: 'Cảnh báo', required: false })
    warnings?: string[];
}
