import { IsIn, IsNotEmpty, <PERSON>N<PERSON>ber, <PERSON>O<PERSON>al, IsString, Min } from 'class-validator';
import { IntervalType } from 'src/entities/deliveryRevenue.entity';
import { GetDriverRankingDto } from './getDriverRanking.dto';
import _ from 'lodash';
import { Transform } from 'stream';

export class GetRankingsDto extends GetDriverRankingDto {
    @IsNotEmpty()
    @IsString()
    @IsIn(Object.values(IntervalType))
    interval_type: IntervalType;

    @IsOptional()
    @IsNumber()
    @Min(0)
    start_rank?: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    end_rank?: number;
}   
