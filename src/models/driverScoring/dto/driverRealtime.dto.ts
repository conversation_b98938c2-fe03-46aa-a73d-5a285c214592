import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsNumber, IsOptional, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class DriverOrderUpdateDto {
    @ApiProperty({ description: 'ID của tài xế', example: 123 })
    @IsNumber()
    @Type(() => Number)
    driverId: number;

    @ApiProperty({ description: 'ID của tỉnh/thành phố', example: 1 })
    @IsNumber()
    @Type(() => Number)
    provinceId: number;

    @ApiPropertyOptional({ description: 'Số lượng đơn tăng thêm', example: 1, default: 1 })
    @IsOptional()
    @IsNumber()
    @Type(() => Number)
    increment?: number;
}

export class GetTopDriversDto {
    @ApiProperty({ description: 'ID của tỉnh/thành phố', example: 1 })
    @IsNumber()
    @Type(() => Number)
    provinceId: number;

    @ApiPropertyOptional({ description: '<PERSON><PERSON><PERSON> c<PERSON>n lấy dữ liệu (YYYY-MM-DD)', example: '2023-06-01' })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => value ? new Date(value) : undefined)
    date?: Date;

    @ApiPropertyOptional({ description: 'Số lượng tài xế cần lấy', example: 10, default: 10 })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    limit?: number;
}

export class GetDriverRankingRealtimeDto {
    @ApiProperty({ description: 'ID của tỉnh/thành phố', example: 1 })
    @IsNumber()
    @Type(() => Number)
    provinceId: number;

    @ApiPropertyOptional({ description: 'Ngày cần lấy dữ liệu (YYYY-MM-DD)', example: '2023-06-01' })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => value ? new Date(value) : undefined)
    date?: Date;
}

export class GetDriversAroundRankDto {
    @ApiProperty({ description: 'ID của tỉnh/thành phố', example: 1 })
    @IsNumber()
    @Type(() => Number)
    provinceId: number;

    @ApiPropertyOptional({ description: 'Ngày cần lấy dữ liệu (YYYY-MM-DD)', example: '2023-06-01' })
    @IsOptional()
    @IsDate()
    @Transform(({ value }) => value ? new Date(value) : undefined)
    date?: Date;

    @ApiPropertyOptional({ description: 'Số lượng tài xế trước và sau cần lấy', example: 3, default: 3 })
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    range?: number;
} 