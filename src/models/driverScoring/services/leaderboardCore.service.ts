import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Driver } from 'src/entities/driver.entity';
import { DriverScore, ELeaderboardType } from 'src/entities/driverScore.entity';
import { DriverVehicle } from 'src/entities/driverVehicle.entity';
import { EVehicleType } from 'src/entities/vehicleType.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { Repository } from 'typeorm';
import {
    DriverRankingItemDto,
    GetDriverRankingsDto,
    UpsertDriverScoreData,
    UpsertDriverScoreResult,
} from '../dto/leaderboard.dto';
import { toISODateString } from '../helpers/leaderboard.helper';
import { getPeriodKey, mapLeaderboardType } from '../helpers/period.helper';

@Injectable()
export class LeaderboardCoreService {
    private readonly logger = new Logger(LeaderboardCoreService.name);

    async upsertDriverScore(data: UpsertDriverScoreData, provinceId: string | number): Promise<DriverScore> {
        try {
            this.validateUpsertData(data);

            const repository = this.getRepository(provinceId);

            // Get driver's vehicle type if not provided
            const vehicleType = data.vehicle_type || (await this.getDriverVehicleType(data.driver_id, provinceId));

            // Try to find existing record
            const existingScore = await repository.findOne({
                where: {
                    driver_id: data.driver_id,
                    leaderboard_type: data.leaderboard_type,
                    period_key: data.period_key,
                    vehicle_type: vehicleType,
                },
            });

            let result: DriverScore;
            const snapshotData = await this.captureRankingSnapshot(data.driver_id, provinceId);

            if (existingScore) {
                // Update existing record - keep snapshot data unchanged
                const new_total_points = existingScore.total_points + data.new_points;
                existingScore.total_points = new_total_points;
                if (snapshotData.snapshot_rank_id) {
                    existingScore.snapshot_rank_id = snapshotData.snapshot_rank_id;
                    existingScore.snapshot_rank_name = snapshotData.snapshot_rank_name;
                    existingScore.snapshot_rank_code = snapshotData.snapshot_rank_code;
                    existingScore.snapshot_rank_level = snapshotData.snapshot_rank_level;
                    existingScore.snapshot_rank_image = snapshotData.snapshot_rank_image;
                }
                result = await repository.save(existingScore);
            } else {
                // Create new record - capture current ranking as snapshot

                const newScore = repository.create({
                    driver_id: data.driver_id,
                    leaderboard_type: data.leaderboard_type,
                    period_key: data.period_key,
                    vehicle_type: vehicleType,
                    total_points: data.new_points,
                    ...snapshotData,
                });

                result = await repository.save(newScore);
            }

            //  related caches

            return result;
        } catch (error) {
            this.logger.error(`Error upserting driver score: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Upsert driver score with points tracking (insert or update if exists)
     * Returns detailed information including points before and after
     */
    async upsertDriverScoreWithTracking(
        data: UpsertDriverScoreData,
        province_id: number,
    ): Promise<UpsertDriverScoreResult> {
        try {
            this.validateUpsertData(data);

            const repository = this.getRepository(province_id);

            // Get driver's vehicle type if not provided
            const vehicleType = data.vehicle_type || (await this.getDriverVehicleType(data.driver_id, province_id));

            // Try to find existing record
            const existingScore = await repository.findOne({
                where: {
                    driver_id: data.driver_id,
                    leaderboard_type: data.leaderboard_type,
                    period_key: data.period_key,
                    vehicle_type: vehicleType,
                },
            });

            let result: DriverScore;
            let points_before: number;
            let points_after: number;
            let was_created: boolean;

            if (existingScore) {
                // Update existing record - keep snapshot data unchanged
                points_before = Number(existingScore.total_points);
                points_after = points_before + data.new_points;
                existingScore.total_points = points_after;
                was_created = false;

                result = await repository.save(existingScore);
            } else {
                // Create new record - capture current ranking as snapshot
                points_before = 0;
                points_after = data.new_points;
                was_created = true;

                const snapshotData = await this.captureRankingSnapshot(data.driver_id, province_id);

                const newScore = repository.create({
                    driver_id: data.driver_id,
                    leaderboard_type: data.leaderboard_type,
                    period_key: data.period_key,
                    vehicle_type: vehicleType,
                    total_points: points_after,
                    ...snapshotData,
                });
                result = await repository.save(newScore);
            }

            return {
                driverScore: result,
                points_before,
                points_after,
                was_created,
            };
        } catch (error) {
            this.logger.error(`Error upserting driver score with tracking: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get driver's primary vehicle type (bike or car)
     * Returns bike as default if no vehicle found
     */
    private async getDriverVehicleType(driverId: number, provinceId: string | number): Promise<EVehicleType> {
        try {
            const driverVehicleRepository = DatabaseService.getRepositoryByProvinceId(DriverVehicle, provinceId);
            const driverVehicle = await driverVehicleRepository.findOne({
                where: {
                    driver_id: driverId,
                    in_use: 1, // Only get the vehicle currently in use
                },
                relations: ['vehicleType'],
                order: { created_at: 'DESC' }, // Get the most recent one if multiple
            });

            if (driverVehicle && driverVehicle.vehicleType) {
                return driverVehicle.vehicleType.type;
            }

            // Default to bike if no vehicle found
            return EVehicleType.bike;
        } catch (error) {
            this.logger.error(
                `Error getting driver vehicle type for driver ${driverId}: ${error.message}`,
                error.stack,
            );
            return EVehicleType.bike; // Default fallback
        }
    }

    /**
     * Capture current driver ranking as snapshot
     */
    private async captureRankingSnapshot(
        driverId: number,
        provinceId: string | number,
    ): Promise<{
        snapshot_rank_id?: number;
        snapshot_rank_name?: string;
        snapshot_rank_code?: string;
        snapshot_rank_level?: number;
        snapshot_rank_image?: string;
    }> {
        try {
            const driverRepository = DatabaseService.getRepositoryByProvinceId(Driver, provinceId);
            const driver = await driverRepository.findOne({
                where: { user_id: driverId },
                relations: ['ranking'],
            });

            if (!driver || !driver.ranking) {
                this.logger.warn(`No ranking found for driver ${driverId} in province ${provinceId}`);
                return {};
            }

            return {
                snapshot_rank_id: driver.ranking.id,
                snapshot_rank_name: driver.ranking.name,
                snapshot_rank_code: driver.ranking.code,
                snapshot_rank_level: driver.ranking.level,
                snapshot_rank_image: driver.ranking.image,
            };
        } catch (error) {
            this.logger.error(`Error capturing ranking snapshot for driver ${driverId}: ${error.message}`, error.stack);
            return {};
        }
    }

    /**
     * Get driver ranking for a specific leaderboard type and period with caching
     */
    async getDriverRanking(
        province_id: number | string,
        params: GetDriverRankingsDto,
    ): Promise<DriverRankingItemDto[]> {
        try {
            const leaderboardType = mapLeaderboardType(params.leaderboard_type);
            const periodKey = getPeriodKey(
                leaderboardType,
                params.start_date ? new Date(params.start_date) : new Date(),
            );

            if (params.start_rank >= params.end_rank) {
                throw new BadRequestException('start_rank must be less than end_rank');
            }

            const maxRange = params.end_rank - params.start_rank;
            if (maxRange > 1000) {
                throw new BadRequestException('Maximum range is 1000 records');
            }
            this.validateLeaderboardType(leaderboardType);
            this.validateRankRange(params.start_rank, params.end_rank);

            // Cache miss - fetch from database
            const repository = this.getRepository(province_id);
            console.log('getDriverRanking', params);
            console.log('province_id', periodKey);
            console.log('province_id', leaderboardType);
            const queryBuilder = repository
                .createQueryBuilder('ds')
                .leftJoinAndSelect('ds.driver', 'driver')
                .leftJoinAndSelect('driver.user', 'user')
                .andWhere('ds.leaderboard_type = :leaderboard_type', { leaderboard_type: leaderboardType })
                .andWhere('ds.period_key = :period_key', { period_key: periodKey });

            if (params.vehicle_type) {
                queryBuilder.andWhere('ds.vehicle_type = :vehicle_type', { vehicle_type: params.vehicle_type });
            }

            if (params.rank_id) {
                queryBuilder.andWhere('ds.snapshot_rank_id = :rank_id', { rank_id: params.rank_id });
            }

            if (params.sub_province_id) {
                console.log('params.sub_province_id', params.sub_province_id);
                queryBuilder.andWhere('driver.province_id = :province_id', {
                    province_id: params.sub_province_id,
                });
            }

            const scores = await queryBuilder
                .orderBy('ds.total_points', 'DESC')
                .addOrderBy('ds.last_updated', 'ASC') // Tie-breaker: earlier update wins
                .skip(params.start_rank)
                .take(params.end_rank - params.start_rank + 1)
                .getMany();

            const result: DriverRankingItemDto[] = scores.map((score, index) => ({
                id: score.driver_id,
                rank: params.start_rank + index + 1,
                score: Number(score.total_points),
                name: score.driver?.user?.name || `Tài xế ${score.driver_id}`,
                avatar_url: score.driver?.user?.avatar || '',
                last_updated: toISODateString(score.last_updated),
                rank_tier: score.snapshot_rank_id
                    ? {
                          id: score.snapshot_rank_id,
                          name: score.snapshot_rank_name,
                          code: score.snapshot_rank_code,
                          level: score.snapshot_rank_level,
                          image: score.snapshot_rank_image,
                      }
                    : undefined,
            }));

            return result;
        } catch (error) {
            this.logger.error(`Error getting driver ranking: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get repository for specific province
     */
    private getRepository(province_id: string | number): Repository<DriverScore> {
        return DatabaseService.getRepositoryByProvinceId(DriverScore, province_id);
    }

    /**
     * Validation methods
     */
    private validateUpsertData(data: UpsertDriverScoreData): void {
        if (!data.driver_id || data.driver_id <= 0) {
            throw new BadRequestException('Valid driver_id is required');
        }

        if (!data.leaderboard_type) {
            throw new BadRequestException('Leaderboard type is required');
        }
        if (!data.period_key?.trim()) {
            throw new BadRequestException('Period key is required');
        }
        if (typeof data.new_points !== 'number') {
            throw new BadRequestException('Valid new_points is required');
        }
    }

    private validateProvinceId(province_id: number): void {
        if (!province_id || province_id <= 0) {
            throw new BadRequestException('Valid province_id is required');
        }
    }

    private validateDriverId(driver_id: number): void {
        if (!driver_id || driver_id <= 0) {
            throw new BadRequestException('Valid driver_id is required');
        }
    }

    private validateLeaderboardType(leaderboard_type: ELeaderboardType): void {
        if (!Object.values(ELeaderboardType).includes(leaderboard_type)) {
            throw new BadRequestException('Invalid leaderboard type');
        }
    }

    private validateRankRange(start: number, end: number): void {
        if (start < 0 || end < 0 || start > end) {
            throw new BadRequestException('Invalid rank range');
        }
    }
}
