import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Request } from 'express';
import { DriverScore, ELeaderboardType } from 'src/entities/driverScore.entity';
import { ScoringHistory } from 'src/entities/driverScoringHistory.entity';
import { EPointType, EScoringHistoryStatus, EScoringSourceType } from 'src/entities/types/EScoringRule.enum';

import { DatabaseService } from 'src/providers/database/database.service';
import { Repository } from 'typeorm';
import { GetScoringHistoryDto } from '../dto';
import { getPeriodKey } from '../helpers/period.helper';
import { LeaderboardCoreService } from './leaderboardCore.service';

@Injectable()
export class ScoringHistoryService {
    private readonly logger = new Logger(ScoringHistoryService.name);

    constructor(private readonly leaderboardCoreService: LeaderboardCoreService) {}

    /**
     * Repository helper methods
     */
    private getHistoryRepository(req: Request): Repository<ScoringHistory> {
        return DatabaseService.getRepository(ScoringHistory, req);
    }

    async getSourceScoringTotal(req: Request, sourceId: number): Promise<number> {
        try {
            const historyRepository = this.getHistoryRepository(req);
            const result = await historyRepository
                .createQueryBuilder('history')
                .select('SUM(history.points_awarded)', 'total')
                .where('history.source_id = :sourceId', { sourceId })
                .andWhere('history.status = :status', { status: EScoringHistoryStatus.SUCCESS })
                .getRawOne();

            const total = Number(result?.total || 0);
            this.logger.debug(`Source ${sourceId} total scoring points (ACTIVE only): ${total}`);
            return total;
        } catch (error) {
            this.logger.error(`Error getting scoring total for source ${sourceId}: ${error.message}`, error.stack);
            return 0;
        }
    }

    async getAllHistory(
        provinceId: string,
        getScoringHistoryDto: GetScoringHistoryDto,
    ): Promise<{
        items: ScoringHistory[];
        total: number;
        pagination: {
            page: number;
            limit: number;
            total_pages: number;
            has_next: boolean;
            has_prev: boolean;
        };
        date_range: {
            from: Date;
            to: Date;
        };
    }> {
        try {
            // Process date range with defaults
            const dateRange = {
                from: getScoringHistoryDto.dateFrom
                    ? new Date(getScoringHistoryDto.dateFrom)
                    : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                to: getScoringHistoryDto.dateTo ? new Date(getScoringHistoryDto.dateTo) : new Date(),
            };

            // Extract options from DTO
            const {
                page = 1,
                limit = 20,

                driverId,
                sourceId,
                ruleId,
                minPoints,
                maxPoints,
                sourceTypes,
                sourceIds,
                searchTerm,
                pointsTypes,
                statuses,
                sourceType,
            } = getScoringHistoryDto;

            const dateFrom = dateRange.from;
            const dateTo = dateRange.to;

            const historyRepository = DatabaseService.getRepositoryByProvinceId(ScoringHistory, provinceId);
            const queryBuilder = historyRepository
                .createQueryBuilder('history')
                .leftJoinAndSelect('history.rule', 'rule')
                .leftJoinAndSelect('history.driver', 'driver')
                .leftJoinAndSelect('driver.user', 'user')
                .leftJoinAndSelect('history.driver_scores', 'many_to_many_driver_scores')
                .leftJoinAndSelect('many_to_many_driver_scores.driver', 'many_to_many_drivers');

            // Apply date filters
            if (dateFrom) {
                queryBuilder.andWhere('history.created_at >= :dateFrom', { dateFrom });
            }
            if (dateTo) {
                queryBuilder.andWhere('history.created_at <= :dateTo', { dateTo });
            }

            // Apply specific filters
            if (driverId) {
                queryBuilder.andWhere('history.driver_id = :driverId', { driverId });
            }
            if (sourceId) {
                queryBuilder.andWhere('history.source_id = :sourceId', { sourceId });
            }
            if (ruleId) {
                queryBuilder.andWhere('history.rule_id = :ruleId', { ruleId });
            }

            // Apply points range filters
            if (minPoints !== undefined) {
                queryBuilder.andWhere('history.points_awarded >= :minPoints', { minPoints });
            }
            if (maxPoints !== undefined) {
                queryBuilder.andWhere('history.points_awarded <= :maxPoints', { maxPoints });
            }

            // Apply source type filters
            if (sourceType) {
                queryBuilder.andWhere('history.source_type = :sourceType', { sourceType });
            }

            // Apply source IDs filters
            if (sourceIds && sourceIds.length > 0) {
                queryBuilder.andWhere('history.source_id IN (:...sourceIds)', { sourceIds });
            }

            // Apply search term
            if (searchTerm) {
                queryBuilder.andWhere('(history.rule_name ILIKE :searchTerm OR rule.name ILIKE :searchTerm)', {
                    searchTerm: `%${searchTerm}%`,
                });
            }

            // Apply points type filters
            if (pointsTypes && pointsTypes.length > 0) {
                queryBuilder.andWhere('history.points_type IN (:...pointsTypes)', { pointsTypes });
            }

            // Apply status filters
            if (statuses && statuses.length > 0) {
                queryBuilder.andWhere('history.status IN (:...statuses)', { statuses });
            }

            queryBuilder.orderBy(`history.id`, 'DESC');

            // Get total count for pagination
            const total = await queryBuilder.getCount();

            // Apply pagination
            queryBuilder.skip((page - 1) * limit).take(limit);

            const items = await queryBuilder.getMany();

            // Calculate pagination info
            const totalPages = Math.ceil(total / limit);
            const currentPage = page;

            return {
                items,
                total,
                pagination: {
                    page: currentPage,
                    limit: limit,
                    total_pages: totalPages,
                    has_next: currentPage < totalPages,
                    has_prev: currentPage > 1,
                },
                date_range: dateRange,
            };
        } catch (error) {
            this.logger.error(`Error getting all history: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Refund points from scoring history record and reverse the points in driver_scores
     * FIXED: Now properly reverses points across all affected leaderboards (daily, weekly, monthly)
     * Uses database transactions for data consistency
     */
    async refundScoringHistory(
        provinceId: number,
        historyId: number,
        adminUserId?: number,
        reason?: string,
    ): Promise<{ success: boolean; message: string; refundHistoryId?: number }> {
        // Create a connection and query runner for transaction
        const connection = DatabaseService.getConnectionByProvinceId(provinceId.toString());
        const queryRunner = connection.createQueryRunner();

        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // Find the history record with its related driver_scores within transaction
            // Use FOR UPDATE to lock the row and prevent concurrent modifications
            const existingRecord = await queryRunner.manager
                .createQueryBuilder(ScoringHistory, 'sh')
                .leftJoinAndSelect('sh.driver_scores', 'ds')
                .where('sh.id = :historyId', { historyId })
                .setLock('pessimistic_write')
                .getOne();

            if (!existingRecord) {
                throw new NotFoundException(`Scoring history record with ID ${historyId} not found`);
            }

            // Check if already refunded or deleted
            if (existingRecord.status === EScoringHistoryStatus.REFUNDED) {
                throw new BadRequestException(`Scoring history record ${historyId} has already been refunded`);
            }
            if (existingRecord.status === EScoringHistoryStatus.DELETED) {
                throw new BadRequestException(`Cannot refund deleted scoring history record ${historyId}`);
            }

            // Calculate points to reverse (opposite of original points_awarded)
            const pointsToReverse = -existingRecord.points_awarded;

            // Extract the scoring date from the original record to determine which leaderboards should be affected
            const scoringDate = existingRecord.created_at;

            // Generate period keys for all leaderboard types based on the scoring date
            const dailyPeriodKey = getPeriodKey(ELeaderboardType.DAILY, scoringDate);
            const weeklyPeriodKey = getPeriodKey(ELeaderboardType.WEEKLY, scoringDate);
            const monthlyPeriodKey = getPeriodKey(ELeaderboardType.MONTHLY, scoringDate);

            // Get all driver scores that should be affected (daily, weekly, monthly for the scoring date) within transaction
            // Use FOR UPDATE to lock the driver score rows and prevent concurrent modifications
            const affectedDriverScores = await queryRunner.manager
                .createQueryBuilder(DriverScore, 'ds')
                .where(
                    '(ds.driver_id = :driverId AND ds.leaderboard_type = :dailyType AND ds.period_key = :dailyKey) OR ' +
                        '(ds.driver_id = :driverId AND ds.leaderboard_type = :weeklyType AND ds.period_key = :weeklyKey) OR ' +
                        '(ds.driver_id = :driverId AND ds.leaderboard_type = :monthlyType AND ds.period_key = :monthlyKey)',
                    {
                        driverId: existingRecord.driver_id,
                        dailyType: ELeaderboardType.DAILY,
                        dailyKey: dailyPeriodKey,
                        weeklyType: ELeaderboardType.WEEKLY,
                        weeklyKey: weeklyPeriodKey,
                        monthlyType: ELeaderboardType.MONTHLY,
                        monthlyKey: monthlyPeriodKey,
                    },
                )
                .setLock('pessimistic_write')
                .getMany();

            // Update points across all affected leaderboards within transaction
            const updatedScores = [];
            const refundDriverScores = [];

            for (const scoreRecord of affectedDriverScores) {
                // Track points before update
                const pointsBefore = Number(scoreRecord.total_points);
                const pointsAfter = pointsBefore + pointsToReverse;

                // Update the driver score within transaction
                scoreRecord.total_points = pointsAfter;
                const updatedDriverScore = await queryRunner.manager.save(DriverScore, scoreRecord);

                // Track the update for logging
                const updatedScore = {
                    driverScore: updatedDriverScore,
                    points_before: pointsBefore,
                    points_after: pointsAfter,
                    was_created: false,
                };

                updatedScores.push(updatedScore);
                refundDriverScores.push(updatedDriverScore);
            }

            // Build complete points_before and points_after objects from all affected leaderboards
            const pointsBefore: Record<string, number> = {};
            const pointsAfter: Record<string, number> = {};

            updatedScores.forEach((updated) => {
                const key = updated.driverScore.leaderboard_type;
                pointsBefore[key] = updated.points_before;
                pointsAfter[key] = updated.points_after;
            });

            // Update the original record status and metadata within transaction
            existingRecord.status = EScoringHistoryStatus.REFUNDED;
            existingRecord.refunded_at = new Date();
            // existingRecord.refunded_by = adminUserId;
            existingRecord.refund_reason = reason || 'Admin refund';
            existingRecord.notes = (existingRecord.notes || '') + `on ${new Date().toISOString()}`;

            await queryRunner.manager.save(ScoringHistory, existingRecord);

            // Create a new refund record (negative points) within transaction
            const refundRecord = new ScoringHistory({
                source_id: existingRecord.id,
                driver_id: existingRecord.driver_id,
                rule_id: existingRecord.rule_id,
                rule_name: `REFUND: ${existingRecord.rule_name}`,
                points_type: EPointType.REFUND,
                source_type: EScoringSourceType.REFUND,
                points_awarded: pointsToReverse,
                points_before: pointsBefore,
                points_after: pointsAfter,
                calculation_details: `Refund of scoring history ID ${historyId}`,
                notes: `Refund record created by admin ${adminUserId}` + (reason ? `. Reason: ${reason}` : ''),
                processed_by: adminUserId,
                status: EScoringHistoryStatus.SUCCESS,
                driver_scores: refundDriverScores, // Initialize Many-to-Many relationship
            });

            const savedRefundRecord = await queryRunner.manager.save(ScoringHistory, refundRecord);

            // Ensure bidirectional Many-to-Many relationship is properly established within transaction
            for (const driverScore of refundDriverScores) {
                const driverScoreWithRelations = await queryRunner.manager.findOne(DriverScore, {
                    where: { id: driverScore.id },
                    relations: ['scoring_histories'],
                });

                if (driverScoreWithRelations) {
                    if (!driverScoreWithRelations.scoring_histories) {
                        driverScoreWithRelations.scoring_histories = [];
                    }
                    // Add the new refund record to the driver score's collection
                    driverScoreWithRelations.scoring_histories.push(savedRefundRecord);
                    await queryRunner.manager.save(DriverScore, driverScoreWithRelations);
                }
            }

            // Commit transaction if everything is successful
            await queryRunner.commitTransaction();

            // Log detailed information about all affected leaderboards
            const affectedLeaderboards = Object.keys(pointsAfter).join(', ');
            const pointsChangesLog = Object.entries(pointsBefore)
                .map(([type, before]) => `${type}: ${before} → ${pointsAfter[type]}`)
                .join(', ');

            this.logger.log(
                `Refunded ${Math.abs(
                    existingRecord.points_awarded,
                )} points from scoring history record ${historyId} for driver ${
                    existingRecord.driver_id
                } by admin ${adminUserId}. Created refund record ${savedRefundRecord.id}. ` +
                    `Affected leaderboards: ${affectedLeaderboards}. Points changes: ${pointsChangesLog}`,
            );

            return {
                success: true,
                message:
                    `Successfully refunded ${Math.abs(
                        existingRecord.points_awarded,
                    )} points from scoring history record ${historyId}. ` +
                    `Affected leaderboards: ${affectedLeaderboards}. Points changes: ${pointsChangesLog}`,
                refundHistoryId: savedRefundRecord.id,
            };
        } catch (error) {
            // Rollback transaction on error
            await queryRunner.rollbackTransaction();
            this.logger.error(`Error refunding scoring history record ${historyId}: ${error.message}`, error.stack);
            throw error;
        } finally {
            // Release query runner
            await queryRunner.release();
        }
    }

    async refundOrderScoringPoints(
        provinceId: number,
        orderId: number,
        reason?: string,
    ): Promise<{ success: boolean; refundedCount: number; message: string }> {
        try {
            // Create a DTO for the query
            const queryDto = new GetScoringHistoryDto();
            queryDto.sourceId = orderId;
            queryDto.includeDeleted = false;
            queryDto.includeRefundRows = false;
            queryDto.page = 1;
            queryDto.limit = 100;

            const scoringHistories = await this.getAllHistory(provinceId.toString(), queryDto);

            let refundedCount = 0;
            for (const history of scoringHistories.items) {
                if (history.status === EScoringHistoryStatus.SUCCESS) {
                    await this.refundScoringHistory(
                        provinceId,
                        history.id,
                        undefined,
                        reason || `Order cancelled - ID: ${orderId}`,
                    );
                    refundedCount++;
                }
            }

            return {
                success: true,
                refundedCount,
                message: `Refunded ${refundedCount} scoring records for order ${orderId}`,
            };
        } catch (error) {
            this.logger.error(`Failed to refund scoring points for order ${orderId}: ${error.message}`);
            return {
                success: false,
                refundedCount: 0,
                message: `Failed to refund scoring points: ${error.message}`,
            };
        }
    }
}
