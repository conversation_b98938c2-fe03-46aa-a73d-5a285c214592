import { Injectable, Logger } from '@nestjs/common';
import { DriverScore, ELeaderboardType } from 'src/entities/driverScore.entity';
import { ScoringHistory } from 'src/entities/driverScoringHistory.entity';
import { EPointType, EScoringHistoryStatus, EScoringSourceType } from 'src/entities/types/EScoringRule.enum';
import { DatabaseService } from 'src/providers/database/database.service';
import { LeaderboardCoreService } from './leaderboardCore.service';
import { getPeriodKey } from '../helpers/period.helper';

@Injectable()
export class ScoringRefundService {
    private readonly logger = new Logger(ScoringRefundService.name);

    constructor(private readonly leaderboardCoreService: LeaderboardCoreService) {}

    /**
     * Refund points from scoring history records for a specific source and province
     * This function processes all scoring records for the given sourceId and sourceType
     */
    async refundScoringPoints(
        provinceId: number,
        sourceId: number,
        sourceType: EScoringSourceType,
        adminUserId?: number,
        reason?: string,
    ): Promise<{ success: boolean; refundedCount: number; message: string; refundHistoryIds: number[] }> {
        // Create a connection and query runner for transaction
        const connection = DatabaseService.getConnectionByProvinceId(provinceId.toString());
        const queryRunner = connection.createQueryRunner();

        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // Find all scoring history records for the given source
            const scoringHistories = await queryRunner.manager
                .createQueryBuilder(ScoringHistory, 'sh')
                .leftJoinAndSelect('sh.driver_scores', 'ds')
                .where('sh.source_id = :sourceId', { sourceId })
                .andWhere('sh.source_type = :sourceType', { sourceType })
                .andWhere('sh.status = :status', { status: EScoringHistoryStatus.SUCCESS })
                .setLock('pessimistic_write')
                .getMany();

            if (!scoringHistories || scoringHistories.length === 0) {
                await queryRunner.rollbackTransaction();
                return {
                    success: true,
                    refundedCount: 0,
                    message: `No scoring records found for source ${sourceId} with type ${sourceType}`,
                    refundHistoryIds: [],
                };
            }

            let refundedCount = 0;
            const refundHistoryIds: number[] = [];

            // Process each scoring history record
            for (const existingRecord of scoringHistories) {
                // Check if already refunded
                if (existingRecord.status === EScoringHistoryStatus.REFUNDED) {
                    this.logger.warn(`Scoring history record ${existingRecord.id} has already been refunded`);
                    continue;
                }

                // Calculate points to reverse (opposite of original points_awarded)
                const pointsToReverse = -existingRecord.points_awarded;

                // Extract the scoring date from the original record
                const scoringDate = existingRecord.created_at;

                // Generate period keys for all leaderboard types based on the scoring date
                const dailyPeriodKey = getPeriodKey(ELeaderboardType.DAILY, scoringDate);
                const weeklyPeriodKey = getPeriodKey(ELeaderboardType.WEEKLY, scoringDate);
                const monthlyPeriodKey = getPeriodKey(ELeaderboardType.MONTHLY, scoringDate);

                // Get all driver scores that should be affected
                const affectedDriverScores = await queryRunner.manager
                    .createQueryBuilder(DriverScore, 'ds')
                    .where(
                        '(ds.driver_id = :driverId AND ds.leaderboard_type = :dailyType AND ds.period_key = :dailyKey) OR ' +
                            '(ds.driver_id = :driverId AND ds.leaderboard_type = :weeklyType AND ds.period_key = :weeklyKey) OR ' +
                            '(ds.driver_id = :driverId AND ds.leaderboard_type = :monthlyType AND ds.period_key = :monthlyKey)',
                        {
                            driverId: existingRecord.driver_id,
                            dailyType: ELeaderboardType.DAILY,
                            dailyKey: dailyPeriodKey,
                            weeklyType: ELeaderboardType.WEEKLY,
                            weeklyKey: weeklyPeriodKey,
                            monthlyType: ELeaderboardType.MONTHLY,
                            monthlyKey: monthlyPeriodKey,
                        },
                    )
                    .setLock('pessimistic_write')
                    .getMany();

                // Update points across all affected leaderboards
                const updatedScores = [];
                const refundDriverScores = [];

                for (const scoreRecord of affectedDriverScores) {
                    // Track points before update
                    const pointsBefore = Number(scoreRecord.total_points);
                    const pointsAfter = pointsBefore + pointsToReverse;

                    // Update the driver score
                    scoreRecord.total_points = pointsAfter;
                    const updatedDriverScore = await queryRunner.manager.save(DriverScore, scoreRecord);

                    // Track the update for logging
                    const updatedScore = {
                        driverScore: updatedDriverScore,
                        points_before: pointsBefore,
                        points_after: pointsAfter,
                        was_created: false,
                    };

                    updatedScores.push(updatedScore);
                    refundDriverScores.push(updatedDriverScore);
                }

                // Build complete points_before and points_after objects
                const pointsBefore: Record<string, number> = {};
                const pointsAfter: Record<string, number> = {};

                updatedScores.forEach((updated) => {
                    const key = updated.driverScore.leaderboard_type;
                    pointsBefore[key] = updated.points_before;
                    pointsAfter[key] = updated.points_after;
                });

                // Update the original record status and metadata
                existingRecord.status = EScoringHistoryStatus.REFUNDED;
                existingRecord.refunded_at = new Date();
                existingRecord.refund_reason = reason || 'Admin refund';
                existingRecord.notes = (existingRecord.notes || '') + ` Refunded on ${new Date().toISOString()}`;

                await queryRunner.manager.save(ScoringHistory, existingRecord);

                // Create a new refund record (negative points)
                const refundRecord = new ScoringHistory({
                    source_id: existingRecord.id,
                    driver_id: existingRecord.driver_id,
                    rule_id: existingRecord.rule_id,
                    rule_name: `REFUND: ${existingRecord.rule_name}`,
                    points_type: EPointType.REFUND,
                    source_type: EScoringSourceType.REFUND,
                    points_awarded: pointsToReverse,
                    points_before: pointsBefore,
                    points_after: pointsAfter,
                    calculation_details: `Refund of scoring history ID ${existingRecord.id}`,
                    notes: `Refund record created by admin ${adminUserId}` + (reason ? `. Reason: ${reason}` : ''),
                    processed_by: adminUserId,
                    status: EScoringHistoryStatus.SUCCESS,
                    driver_scores: refundDriverScores,
                });

                const savedRefundRecord = await queryRunner.manager.save(ScoringHistory, refundRecord);

                // Ensure bidirectional Many-to-Many relationship is properly established
                for (const driverScore of refundDriverScores) {
                    const driverScoreWithRelations = await queryRunner.manager.findOne(DriverScore, {
                        where: { id: driverScore.id },
                        relations: ['scoring_histories'],
                    });

                    if (driverScoreWithRelations) {
                        if (!driverScoreWithRelations.scoring_histories) {
                            driverScoreWithRelations.scoring_histories = [];
                        }
                        driverScoreWithRelations.scoring_histories.push(savedRefundRecord);
                        await queryRunner.manager.save(DriverScore, driverScoreWithRelations);
                    }
                }

                refundedCount++;
                refundHistoryIds.push(savedRefundRecord.id);

                // Log detailed information about the refund
                const affectedLeaderboards = Object.keys(pointsAfter).join(', ');
                const pointsChangesLog = Object.entries(pointsBefore)
                    .map(([type, before]) => `${type}: ${before} → ${pointsAfter[type]}`)
                    .join(', ');

                this.logger.log(
                    `Refunded ${Math.abs(existingRecord.points_awarded)} points from scoring history record ${
                        existingRecord.id
                    } for driver ${existingRecord.driver_id} by admin ${adminUserId}. Created refund record ${
                        savedRefundRecord.id
                    }. ` + `Affected leaderboards: ${affectedLeaderboards}. Points changes: ${pointsChangesLog}`,
                );
            }

            // Commit transaction if everything is successful
            await queryRunner.commitTransaction();

            this.logger.log(
                `Successfully refunded ${refundedCount} scoring records for source ${sourceId} with type ${sourceType} by admin ${adminUserId}`,
            );

            return {
                success: true,
                refundedCount,
                message: `Successfully refunded ${refundedCount} scoring records for source ${sourceId} with type ${sourceType}`,
                refundHistoryIds,
            };
        } catch (error) {
            // Rollback transaction on error
            await queryRunner.rollbackTransaction();
            this.logger.error(
                `Error refunding scoring points for source ${sourceId} with type ${sourceType}: ${error.message}`,
                error.stack,
            );
            throw error;
        } finally {
            // Release query runner
            await queryRunner.release();
        }
    }

    /**
     * Get refund history for a specific source
     */
    async getRefundHistoryBySource(provinceId: number, sourceId: number, sourceType: any): Promise<ScoringHistory[]> {
        try {
            const historyRepository = DatabaseService.getRepositoryByProvinceId(ScoringHistory, provinceId);

            const refundRecords = await historyRepository.find({
                where: {
                    source_id: sourceId,
                    source_type: sourceType,
                    points_type: EPointType.REFUND,
                },
                relations: ['driver', 'rule'],
                order: { created_at: 'DESC' },
            });

            return refundRecords;
        } catch (error) {
            this.logger.error(
                `Error getting refund history for source ${sourceId} with type ${sourceType}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }

    /**
     * Check if a source has any refunded scoring records
     */
    async hasRefundedRecords(provinceId: number, sourceId: number, sourceType: any): Promise<boolean> {
        try {
            const historyRepository = DatabaseService.getRepositoryByProvinceId(ScoringHistory, provinceId);

            const count = await historyRepository.count({
                where: {
                    source_id: sourceId,
                    source_type: sourceType,
                    status: EScoringHistoryStatus.REFUNDED,
                },
            });

            return count > 0;
        } catch (error) {
            this.logger.error(
                `Error checking refunded records for source ${sourceId} with type ${sourceType}: ${error.message}`,
                error.stack,
            );
            return false;
        }
    }
}
