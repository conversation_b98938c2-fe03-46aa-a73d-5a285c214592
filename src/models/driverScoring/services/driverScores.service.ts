import { Injectable } from "@nestjs/common";
import { DatabaseService } from "src/providers/database/database.service";
import { SelectQueryBuilder } from "typeorm";
import {
  DriverScore,
  ELeaderboardType,
} from "../../../entities/driverScore.entity";
import {
  DriverScoreResponseDto,
  GetDriverScoresDto,
} from "../dto/driverScores.dto";

@Injectable()
export class DriverScoresService {
  async getDriverScores(params: GetDriverScoresDto, province_id: number) {
    const {
      page = 1,
      limit = 20,
      driver_id,
      driver_name,
      driver_phone,
      period_type,
      period_key,
      from_date,
      to_date,
      sort_by = "total_points",
      sort_order = "desc",
      include_driver = true,
    } = params;

    const queryBuilder = this.createBaseQueryBuilder(
      province_id,
      include_driver
    );

    // Apply filters
    this.applyFilters(queryBuilder, {
      driver_id,
      driver_name,
      driver_phone,
      province_id,
      period_type,
      period_key,
      from_date,
      to_date,
    });

    // Apply sorting
    this.applySorting(queryBuilder, sort_by, sort_order);

    // Get total count
    const totalCount = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const items = await queryBuilder.getMany();

    // Transform to response DTOs
    const transformedItems = await this.transformToResponseDto(
      items,
      include_driver
    );

    return {
      items: transformedItems,
      total: totalCount,
    };
  }

  private createBaseQueryBuilder(
    province_id: number,
    includeDriver: boolean
  ): SelectQueryBuilder<DriverScore> {
    const queryBuilder = DatabaseService.getRepositoryByProvinceId(
      DriverScore,
      province_id
    ).createQueryBuilder("ds");

    if (includeDriver) {
      queryBuilder
        .leftJoinAndSelect("ds.driver", "driver")
        .leftJoinAndSelect("driver.user", "user")
        .leftJoinAndSelect("ds.scoring_histories", "scoring_histories")
        .leftJoinAndSelect("scoring_histories.rule", "scoring_rule");
    }

    return queryBuilder;
  }

  private applyFilters(
    queryBuilder: SelectQueryBuilder<DriverScore>,
    filters: {
      driver_id?: number;
      driver_name?: string;
      driver_phone?: string;
      province_id?: number;
      period_type?: string;
      period_key?: string;
      from_date?: string;
      to_date?: string;
    }
  ): void {
    const {
      driver_id,
      driver_name,
      driver_phone,
      period_type,
      period_key,
      from_date,
      to_date,
    } = filters;

    // If we need to filter by driver name or phone, we need to join the tables
    if (driver_name || driver_phone) {
      // Check if driver join already exists
      const existingJoins = queryBuilder.expressionMap.joinAttributes;
      const hasDriverJoin = existingJoins.some(
        (join) => join.alias?.name === "driver"
      );
      const hasUserJoin = existingJoins.some(
        (join) => join.alias?.name === "user"
      );

      if (!hasDriverJoin) {
        queryBuilder.leftJoin("ds.driver", "driver");
      }
      if (!hasUserJoin) {
        queryBuilder.leftJoin("driver.user", "user");
      }
    }

    if (driver_id) {
      queryBuilder.andWhere("ds.driver_id = :driver_id", { driver_id });
    }

    if (period_type) {
      queryBuilder.andWhere("ds.leaderboard_type = :period_type", {
        period_type,
      });
    }

    if (period_key) {
      queryBuilder.andWhere("ds.period_key = :period_key", { period_key });
    }

    if (driver_name) {
      queryBuilder.andWhere("user.name LIKE :driver_name", {
        driver_name: `%${driver_name}%`,
      });
    }

    if (driver_phone) {
      queryBuilder.andWhere("user.phone LIKE :driver_phone", {
        driver_phone: `%${driver_phone}%`,
      });
    }

    if (from_date) {
      queryBuilder.andWhere("ds.created_at >= :from_date", { from_date });
    }

    if (to_date) {
      queryBuilder.andWhere("ds.created_at <= :to_date", {
        to_date: to_date + " 23:59:59",
      });
    }
  }

  private applySorting(
    queryBuilder: SelectQueryBuilder<DriverScore>,
    sortBy: string,
    sortOrder: "asc" | "desc"
  ): void {
    // Map frontend sort fields to database fields
    const sortFieldMap: Record<string, string> = {
      total_score: "ds.total_points",
      current_rank: "ds.total_points", // Sort by points for ranking
      events_count: "ds.total_points", // Fallback to points
      last_updated: "ds.last_updated",
      created_at: "ds.created_at",
      driver_name: "user.name",
      period_key: "ds.period_key",
    };

    const dbField = sortFieldMap[sortBy] || "ds.total_points";

    // If sorting by driver name, ensure we have the necessary joins
    if (sortBy === "driver_name") {
      const existingJoins = queryBuilder.expressionMap.joinAttributes;
      const hasDriverJoin = existingJoins.some(
        (join) => join.alias?.name === "driver"
      );
      const hasUserJoin = existingJoins.some(
        (join) => join.alias?.name === "user"
      );

      if (!hasDriverJoin) {
        queryBuilder.leftJoin("ds.driver", "driver");
      }
      if (!hasUserJoin) {
        queryBuilder.leftJoin("driver.user", "user");
      }
    }

    queryBuilder.orderBy(dbField, sortOrder.toUpperCase() as "ASC" | "DESC");

    // Add secondary sort by driver name for consistency
    if (sortBy !== "driver_name") {
      // Only add secondary sort if we have the joins available
      const existingJoins = queryBuilder.expressionMap.joinAttributes;
      const hasUserJoin = existingJoins.some(
        (join) => join.alias?.name === "user"
      );
      if (hasUserJoin) {
        queryBuilder.addOrderBy("user.name", "ASC");
      }
    }
  }

  private async transformToResponseDto(
    items: DriverScore[],
    includeDriver: boolean
  ): Promise<DriverScoreResponseDto[]> {
    // Calculate rankings and additional fields
    return items.map((item, index) => {
      const transformed: DriverScoreResponseDto = {
        id: item.id,
        driver_id: item.driver_id,
        period_type: item.leaderboard_type,
        period_key: item.period_key,
        total_score: Number(item.total_points),
        current_rank: index + 1, // Simple ranking based on order
        scoring_events_count: 0, // This would need to be calculated from scoring history
        last_updated: item.last_updated,
        created_at: item.created_at,
        updated_at: item.last_updated,
        ...this.calculatePeriodDates(item.leaderboard_type, item.period_key),
      };

      if (includeDriver && item.driver && item.driver.user) {
        transformed.driver = {
          id: item.driver.user.id,
          name: item.driver.user.name,
          phone: item.driver.user.phone,
          avatar: item.driver.user.avatar,
        };
      }

      return transformed;
    });
  }

  private calculatePeriodDates(
    periodType: ELeaderboardType,
    periodKey: string
  ): {
    period_start?: string;
    period_end?: string;
  } {
    try {
      switch (periodType) {
        case ELeaderboardType.DAILY:
          return {
            period_start: periodKey,
            period_end: periodKey,
          };
        case ELeaderboardType.WEEKLY:
          // Assuming format YYYY-WW
          const [year, week] = periodKey.split("-W");
          const startDate = this.getDateOfWeek(parseInt(year), parseInt(week));
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6);
          return {
            period_start: startDate.toISOString().split("T")[0],
            period_end: endDate.toISOString().split("T")[0],
          };
        case ELeaderboardType.MONTHLY:
          // Assuming format YYYY-MM
          const [monthYear, month] = periodKey.split("-");
          const monthStart = new Date(
            parseInt(monthYear),
            parseInt(month) - 1,
            1
          );
          const monthEnd = new Date(parseInt(monthYear), parseInt(month), 0);
          return {
            period_start: monthStart.toISOString().split("T")[0],
            period_end: monthEnd.toISOString().split("T")[0],
          };
        default:
          return {};
      }
    } catch (error) {
      return {};
    }
  }

  private getDateOfWeek(year: number, week: number): Date {
    const simple = new Date(year, 0, 1 + (week - 1) * 7);
    const dow = simple.getDay();
    const isoWeekStart = simple;
    if (dow <= 4) {
      isoWeekStart.setDate(simple.getDate() - simple.getDay() + 1);
    } else {
      isoWeekStart.setDate(simple.getDate() + 8 - simple.getDay());
    }
    return isoWeekStart;
  }
}
