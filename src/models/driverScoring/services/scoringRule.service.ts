import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { ScoringRule } from 'src/entities/driverScoringRule.entity';
import { ScoringRuleAction } from 'src/entities/driverScoringRuleAction.entity';
import { ScoringRuleCondition } from 'src/entities/driverScoringRuleCondition.entity';

import {
    ECalculationType,
    EConditionOperator,
    ELogicalOperator,
    EScoringFieldName,
    EScoringSourceType,
} from 'src/entities/types/EScoringRule.enum';
import { DatabaseService } from 'src/providers/database/database.service';
import { Repository } from 'typeorm';
import { CreateScoringRuleDto, GetScoringRulesDto, UpdateScoringRuleDto } from '../dto';
import { FilterScoringRulesDto, PaginatedScoringRulesResult } from '../dto/scoringRuleService.dto';
import { processDeliveryDayOfWeekCondition } from '../validators/deliveryDayOfWeek.validator';
import { processOrderDateCondition } from '../validators/orderDate.validator';

@Injectable()
export class ScoringRuleService {
    private readonly logger = new Logger(ScoringRuleService.name);
    private readonly CACHE_PREFIX = 'scoring_rules';

    constructor(private readonly configService: ConfigService) {}

    /**
     * Repository helper methods - simplified without province_id
     */
    private getRuleRepository(provinceId: number | string): Repository<ScoringRule> {
        return DatabaseService.getRepositoryByProvinceId(ScoringRule, provinceId);
    }

    private getConditionRepository(provinceId: number | string): Repository<ScoringRuleCondition> {
        return DatabaseService.getRepositoryByProvinceId(ScoringRuleCondition, provinceId);
    }

    private getActionRepository(provinceId: number | string): Repository<ScoringRuleAction> {
        return DatabaseService.getRepositoryByProvinceId(ScoringRuleAction, provinceId);
    }

    /**
     * Validation methods
     */
    private validateCreateRuleDto(dto: CreateScoringRuleDto): void {
        if (!dto.name?.trim()) {
            throw new BadRequestException('Rule name is required');
        }

        if (!dto.conditions || dto.conditions.length === 0) {
            throw new BadRequestException('At least one condition is required');
        }
        if (!dto.actions || dto.actions.length === 0) {
            throw new BadRequestException('At least one action is required');
        }

        // Validate conditions
        dto.conditions.forEach((condition, index) => {
            if (!condition.field_name?.trim()) {
                throw new BadRequestException(`Condition ${index + 1}: field_name is required`);
            }

            if (condition.value === undefined || condition.value === null) {
                throw new BadRequestException(`Condition ${index + 1}: value is required`);
            }
        });

        // Validate actions
        dto.actions.forEach((action, index) => {
            if (!action.calculation_type) {
                throw new BadRequestException(`Action ${index + 1}: calculation_type is required`);
            }
            if (action.calculation_type === ECalculationType.TIERED && !action.target_field) {
                throw new BadRequestException(`Action ${index + 1}: target_field is required for TIERED calculation`);
            }
            if (action.calculation_type === ECalculationType.TIERED && (!action.tiers || action.tiers.length === 0)) {
                throw new BadRequestException(`Action ${index + 1}: tiers are required for TIERED calculation`);
            }
        });
    }

    private validateDateRange(validFrom?: Date, validTo?: Date): void {
        if (validFrom && validTo && validFrom >= validTo) {
            throw new BadRequestException('valid_from must be before valid_to');
        }
    }

    /**
     * Cache management methods
     */
    private getCacheKey(type?: EScoringSourceType, provinceId?: number): string {
        const typeKey = type ? `_${type}` : '';
        const provinceKey = provinceId ? `_p${provinceId}` : '';
        return `${this.CACHE_PREFIX}${typeKey}${provinceKey}`;
    }

    /**
     * Parse and format condition value from database
     * Handles the transformer logic that was problematic in the entity
     */
    private parseConditionValue(value: any, fieldName: string): any {
        if (value === null || value === undefined) {
            return null;
        }

        try {
            // If value is already parsed (object/array), return as is
            if (typeof value === 'object') {
                // For DELIVERY_DAY_OF_WEEK, ensure it's always an array
                if (fieldName === EScoringFieldName.DELIVERY_DAY_OF_WEEK) {
                    return Array.isArray(value) ? value : [value];
                }
                // For ORDER_DATE with IN operator, ensure it's always an array
                if (fieldName === EScoringFieldName.ORDER_DATE && Array.isArray(value)) {
                    return value;
                }
                return value;
            }

            // If value is a string, try to parse as JSON
            if (typeof value === 'string') {
                const parsed = JSON.parse(value);

                // For DELIVERY_DAY_OF_WEEK, ensure it's always an array
                if (fieldName === EScoringFieldName.DELIVERY_DAY_OF_WEEK) {
                    return Array.isArray(parsed) ? parsed : [parsed];
                }

                // For ORDER_DATE with IN operator, ensure it's always an array
                if (fieldName === EScoringFieldName.ORDER_DATE && Array.isArray(parsed)) {
                    return parsed;
                }

                return parsed;
            }

            // For primitive values, return as is
            return value;
        } catch (error) {
            this.logger.warn(`Failed to parse condition value: ${value}`, error);
            return value;
        }
    }

    /**
     * Format condition value for database storage
     * Handles the transformer logic for saving to database
     */
    private formatConditionValueForStorage(value: any): string | null {
        if (value === null || value === undefined) {
            return null;
        }

        try {
            // If it's already a string, validate it's proper JSON
            if (typeof value === 'string') {
                JSON.parse(value); // Validate JSON
                return value;
            }

            // Convert to JSON string
            return JSON.stringify(value);
        } catch (error) {
            this.logger.warn(`Failed to format condition value for storage: ${value}`, error);
            // If it's a string that's not valid JSON, stringify it
            return JSON.stringify(value);
        }
    }

    /**
     * Process conditions after fetching from database
     * Applies proper parsing to all condition values
     */
    private processConditionsForResponse(conditions: ScoringRuleCondition[]): ScoringRuleCondition[] {
        if (!conditions || conditions.length === 0) {
            return conditions;
        }

        return conditions.map((condition) => {
            const processedCondition = { ...condition };
            processedCondition.value = this.parseConditionValue(condition.value, condition.field_name);
            return processedCondition;
        });
    }

    /**
     * Process a single rule with its conditions for API response
     */
    private processRuleForResponse(rule: ScoringRule): ScoringRule {
        if (!rule) {
            return rule;
        }

        const processedRule = { ...rule };

        if (rule.conditions && rule.conditions.length > 0) {
            processedRule.conditions = this.processConditionsForResponse(rule.conditions);
        }

        return processedRule;
    }

    /**
     * Create a new scoring rule
     */
    async createRule(
        req: Request,
        dto: CreateScoringRuleDto,
        createdBy?: number,
        provinceId?: number,
    ): Promise<ScoringRule> {
        try {
            this.validateCreateRuleDto(dto);
            this.validateDateRange(dto.valid_from, dto.valid_to);

            const ruleRepository = this.getRuleRepository(provinceId);
            const queryRunner = ruleRepository.manager.connection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // Create main rule
                const rule = ruleRepository.create({
                    name: dto.name.trim(),
                    description: dto.description?.trim(),
                    source_type: dto.source_type,
                    scoring_type: dto.scoring_type,
                    priority: dto.priority || 0,
                    valid_from: dto.valid_from,
                    valid_to: dto.valid_to,
                    created_by: createdBy,
                    version: 1,
                    is_active: dto.is_active || true,
                });

                const savedRule = await queryRunner.manager.save(rule);

                // Create conditions
                const conditionRepository = this.getConditionRepository(provinceId);
                const conditions = dto.conditions.map((conditionDto, index) => {
                    // Process value for special fields
                    let processedValue = processDeliveryDayOfWeekCondition(conditionDto.field_name, conditionDto.value);

                    // Also process order_date field
                    processedValue = processOrderDateCondition(conditionDto.field_name, processedValue);

                    // Format value for storage using our custom formatter
                    const formattedValue = this.formatConditionValueForStorage(processedValue);

                    return conditionRepository.create({
                        rule_id: savedRule.id,
                        field_name: conditionDto.field_name,
                        operator: this.operatorHandler(conditionDto.operator, conditionDto.field_name),
                        value: formattedValue,
                        logical_operator: (conditionDto.logical_operator as ELogicalOperator) || ELogicalOperator.AND,
                        order_index: conditionDto.order_index || index,
                    });
                });

                await queryRunner.manager.save(conditions);

                // Create actions
                console.log('Creating actions for rule:', savedRule.id);
                console.log('Actions data:', JSON.stringify(dto.actions, null, 2));

                const actions = dto.actions.map((actionDto) => {
                    const actionData = {
                        rule_id: savedRule.id,
                        calculation_type: actionDto.calculation_type,
                        point_type: actionDto.point_type,
                        base_points: actionDto.base_points || 0,
                        multiplier: actionDto.multiplier || 1,
                        tiers: actionDto.tiers,
                        target_field: actionDto.target_field,
                    };
                    console.log('Creating action with data:', JSON.stringify(actionData, null, 2));

                    // Tạo action entity trực tiếp thay vì dùng repository.create
                    const action = new ScoringRuleAction(actionData);
                    console.log('Created action entity:', action);
                    return action;
                });

                try {
                    // Thử save từng action một để debug
                    const savedActions = [];
                    for (let i = 0; i < actions.length; i++) {
                        console.log(`Saving action ${i + 1}:`, actions[i]);
                        try {
                            const savedAction = await queryRunner.manager.save(actions[i]);
                            console.log(`Action ${i + 1} saved successfully:`, savedAction);
                            savedActions.push(savedAction);
                        } catch (singleActionError) {
                            console.error(`Error saving action ${i + 1}:`, singleActionError);
                            throw singleActionError;
                        }
                    }
                } catch (actionError) {
                    console.error('Error saving actions:', actionError);
                    console.error('Action error details:', {
                        message: actionError.message,
                        code: actionError.code,
                        errno: actionError.errno,
                        sqlState: actionError.sqlState,
                        sqlMessage: actionError.sqlMessage,
                    });
                    throw actionError;
                }

                await queryRunner.commitTransaction();

                this.logger.log(
                    `Created scoring rule: ${savedRule.name} (ID: ${savedRule.id}) for province: ${provinceId}`,
                );

                // Lấy lại rule để kiểm tra
                const finalRule = await this.getRuleById(provinceId, savedRule.id);
                console.log('Final rule with relations:', {
                    id: finalRule?.id,
                    name: finalRule?.name,
                    conditions_count: finalRule?.conditions?.length || 0,
                    actions_count: finalRule?.actions?.length || 0,
                    actions: finalRule?.actions?.map((a) => ({
                        id: a.id,
                        calculation_type: a.calculation_type,
                        tiers: a.tiers,
                        target_field: a.target_field,
                    })),
                });

                return finalRule;
            } catch (error) {
                await queryRunner.rollbackTransaction();
                throw error;
            } finally {
                await queryRunner.release();
            }
        } catch (error) {
            this.logger.error(`Error creating scoring rule: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Update an existing scoring rule
     */
    async updateRule(
        provinceId: number | string,
        id: number,
        dto: UpdateScoringRuleDto,
        updatedBy?: number,
    ): Promise<ScoringRule> {
        try {
            const existingRule = await this.getRuleById(provinceId, id);
            if (!existingRule) {
                throw new NotFoundException(`Scoring rule with ID ${id} not found`);
            }

            if (dto.valid_from || dto.valid_to) {
                this.validateDateRange(
                    dto.valid_from || existingRule.valid_from,
                    dto.valid_to || existingRule.valid_to,
                );
            }

            const ruleRepository = this.getRuleRepository(provinceId);
            const queryRunner = ruleRepository.manager.connection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // Create new version of the rule
                const newVersion = existingRule.version + 1;

                // Update main rule
                await queryRunner.manager.update(ScoringRule, id, {
                    name: dto.name?.trim() || existingRule.name,
                    description: dto.description?.trim() || existingRule.description,
                    source_type: dto.source_type || existingRule.source_type,
                    scoring_type: dto.scoring_type || existingRule.scoring_type,
                    is_active: dto.is_active !== undefined ? dto.is_active : existingRule.is_active,
                    priority: dto.priority !== undefined ? dto.priority : existingRule.priority,
                    valid_from: dto.valid_from || existingRule.valid_from,
                    valid_to: dto.valid_to || existingRule.valid_to,
                    updated_by: updatedBy,
                    version: newVersion,
                });

                // Update conditions if provided
                if (dto.conditions) {
                    await queryRunner.manager.delete(ScoringRuleCondition, { rule_id: id });

                    const conditionRepository = this.getConditionRepository(provinceId);
                    const conditions = dto.conditions.map((conditionDto, index) => {
                        // Process value for special fields
                        let processedValue = processDeliveryDayOfWeekCondition(
                            conditionDto.field_name,
                            conditionDto.value,
                        );

                        // Also process order_date field
                        processedValue = processOrderDateCondition(conditionDto.field_name, processedValue);

                        // Format value for storage using our custom formatter
                        const formattedValue = this.formatConditionValueForStorage(processedValue);

                        return conditionRepository.create({
                            rule_id: id,
                            field_name: conditionDto.field_name,
                            operator: this.operatorHandler(conditionDto.operator, conditionDto.field_name),
                            value: formattedValue,
                            logical_operator:
                                (conditionDto.logical_operator as ELogicalOperator) || ELogicalOperator.AND,
                            order_index: conditionDto.order_index || index,
                        });
                    });

                    await queryRunner.manager.save(conditions);
                }

                // Update actions if provided
                if (dto.actions) {
                    await queryRunner.manager.delete(ScoringRuleAction, { rule_id: id });

                    const actionRepository = this.getActionRepository(provinceId);
                    const actions = dto.actions.map((actionDto) =>
                        actionRepository.create({
                            rule_id: id,
                            calculation_type: actionDto.calculation_type,
                            point_type: actionDto.point_type,
                            base_points: actionDto.base_points || 0,
                            multiplier: actionDto.multiplier || 1,
                            tiers: actionDto.tiers,
                            target_field: actionDto.target_field,
                        }),
                    );

                    await queryRunner.manager.save(actions);
                }

                await queryRunner.commitTransaction();

                this.logger.log(`Updated scoring rule: ${existingRule.name} (ID: ${id}) to version ${newVersion}`);
                return await this.getRuleById(provinceId, id);
            } catch (error) {
                await queryRunner.rollbackTransaction();
                throw error;
            } finally {
                await queryRunner.release();
            }
        } catch (error) {
            this.logger.error(`Error updating scoring rule ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Hard delete a scoring rule
     */
    async deleteRule(provinceId: number | string, id: number): Promise<boolean> {
        try {
            const rule = await this.getRuleById(provinceId, id);
            if (!rule) {
                throw new NotFoundException(`Scoring rule with ID ${id} not found`);
            }

            const ruleRepository = this.getRuleRepository(provinceId);
            const queryRunner = ruleRepository.manager.connection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // Delete related conditions first
                const conditionRepository = this.getConditionRepository(provinceId);
                await queryRunner.manager.delete(conditionRepository.target, { rule_id: id });

                // Delete related actions
                const actionRepository = this.getActionRepository(provinceId);
                await queryRunner.manager.delete(actionRepository.target, { rule_id: id });

                // Delete the main rule
                await queryRunner.manager.delete(ruleRepository.target, { id });

                await queryRunner.commitTransaction();

                this.logger.log(`Hard deleted scoring rule: ${rule.name} (ID: ${id})`);
                return true;
            } catch (error) {
                await queryRunner.rollbackTransaction();
                throw error;
            } finally {
                await queryRunner.release();
            }
        } catch (error) {
            this.logger.error(`Error deleting scoring rule ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get active rules with caching
     */
    async getActiveRules(type?: EScoringSourceType, provinceId?: number | string): Promise<ScoringRule[]> {
        try {
            // const cacheKey = this.getCacheKey(type, provinceId);

            // // Try to get from cache first
            // const cached = await this.redis.get(cacheKey);
            // if (cached) {
            //     this.logger.debug(`Cache hit for ${cacheKey}`);
            //     return JSON.parse(cached);
            // }

            // Add time-based filtering
            const now = new Date();

            // Use query builder for complex time-based conditions
            const ruleRepository = this.getRuleRepository(provinceId);
            // console.log('ruleRepository', ruleRepository);

            const queryBuilder = ruleRepository
                .createQueryBuilder('rule')
                .leftJoinAndSelect('rule.conditions', 'conditions')
                .leftJoinAndSelect('rule.actions', 'actions')
                .where('rule.is_active = :isActive', { isActive: true })
                .andWhere('(rule.valid_from IS NULL OR rule.valid_from <= :now)', { now })
                .andWhere('(rule.valid_to IS NULL OR rule.valid_to >= :now)', { now });

            if (type) {
                queryBuilder.andWhere('rule.rule_type = :type', { type });
            }

            const rules = await queryBuilder
                .orderBy('rule.priority', 'ASC')
                .addOrderBy('rule.created_at', 'ASC')
                .getMany();

            // Process rules to properly parse condition values
            const processedRules = rules.map((rule) => this.processRuleForResponse(rule));

            // Cache the results
            // await this.redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(processedRules));

            this.logger.log(
                `Retrieved ${processedRules.length} active rules, type: ${type || 'all'}, province: ${
                    provinceId || 'all'
                }`,
            );
            return processedRules;
        } catch (error) {
            this.logger.error(`Error getting active rules: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get rule by ID with relations
     */
    async getRuleById(provinceId: number | string, id: number): Promise<ScoringRule | null> {
        try {
            if (!id || id <= 0) {
                throw new BadRequestException('Valid rule ID is required');
            }

            const ruleRepository = this.getRuleRepository(provinceId);
            const rule = await ruleRepository.findOne({
                where: { id },
                relations: ['conditions', 'actions'],
            });

            if (!rule) {
                return null;
            }

            // Process conditions to properly parse values using our custom transformer
            return this.processRuleForResponse(rule);
        } catch (error) {
            this.logger.error(`Error getting rule by ID ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get rules by type
     */
    async getRulesByType(type: EScoringSourceType, provinceId?: number): Promise<ScoringRule[]> {
        try {
            return await this.getActiveRules(type, provinceId);
        } catch (error) {
            this.logger.error(`Error getting rules by type ${type}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Activate a rule
     */
    async activateRule(provinceId: number | string, id: number, activatedBy?: number): Promise<boolean> {
        try {
            const rule = await this.getRuleById(provinceId, id);
            if (!rule) {
                throw new NotFoundException(`Scoring rule with ID ${id} not found`);
            }

            const ruleRepository = this.getRuleRepository(provinceId);
            await ruleRepository.update(id, {
                is_active: true,
                updated_by: activatedBy,
                updated_at: new Date(),
            });

            return true;
        } catch (error) {
            this.logger.error(`Error activating rule ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Deactivate a rule
     */
    async deactivateRule(provinceId: number | string, id: number, deactivatedBy?: number): Promise<boolean> {
        try {
            const rule = await this.getRuleById(provinceId, id);
            if (!rule) {
                throw new NotFoundException(`Scoring rule with ID ${id} not found`);
            }

            const ruleRepository = this.getRuleRepository(provinceId);
            await ruleRepository.update(id, {
                is_active: false,
                updated_by: deactivatedBy,
                updated_at: new Date(),
            });

            this.logger.log(`Deactivated scoring rule: ${rule.name} (ID: ${id})`);
            return true;
        } catch (error) {
            this.logger.error(`Error deactivating rule ${id}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get all rules with pagination and filtering
     */
    async getAllRules(
        provinceId: number | string,
        filters: FilterScoringRulesDto,
    ): Promise<PaginatedScoringRulesResult> {
        try {
            const ruleRepository = this.getRuleRepository(provinceId);
            const queryBuilder = ruleRepository
                .createQueryBuilder('rule')
                .leftJoinAndSelect('rule.conditions', 'conditions')
                .leftJoinAndSelect('rule.actions', 'actions');

            // Apply filters
            if (filters.sourceTypes && filters.sourceTypes.length > 0) {
                queryBuilder.andWhere('rule.source_type IN (:...types)', { types: filters.sourceTypes });
            }

            if (filters.is_active !== undefined) {
                queryBuilder.andWhere('rule.is_active = :isActive', { isActive: filters.is_active });
            }

            if (filters.searchTerm) {
                queryBuilder.andWhere('(rule.name LIKE :search OR rule.description LIKE :search)', {
                    search: `%${filters.searchTerm}%`,
                });
            }
            // Apply sorting
            const sortField = filters.sortBy || 'created_at';
            const sortOrder = filters.sortOrder || 'DESC';
            queryBuilder.orderBy(`rule.${sortField}`, sortOrder as 'ASC' | 'DESC');

            // Apply pagination
            const page = filters.page || 1;
            const limit = filters.limit || 10;
            const offset = (page - 1) * limit;

            queryBuilder.skip(offset).take(limit);

            const [rules, total] = await queryBuilder.getManyAndCount();

            return {
                data: rules,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1,
                },
            };
        } catch (error) {
            this.logger.error(`Error getting all rules: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Find rules with filters (used by controller)
     */
    async findWithFilters(
        provinceId: number | string,
        filters: GetScoringRulesDto,
    ): Promise<PaginatedScoringRulesResult> {
        try {
            const ruleRepository = this.getRuleRepository(provinceId);
            const queryBuilder = ruleRepository.createQueryBuilder('rule');

            // Include relations if requested
            if (filters.includeConditions) {
                queryBuilder.leftJoinAndSelect('rule.conditions', 'conditions');
            }
            if (filters.includeActions) {
                queryBuilder.leftJoinAndSelect('rule.actions', 'actions');
            }
            if (filters.name) {
                queryBuilder.andWhere('rule.name LIKE :name', { name: `%${filters.name}%` });
            }

            // Always include actions for scoring_type calculation
            if (!filters.includeActions) {
                queryBuilder.leftJoinAndSelect('rule.actions', 'actions');
            }

            // Apply other filters
            if (filters.sourceTypes && filters.sourceTypes.length > 0) {
                queryBuilder.andWhere('rule.source_type IN (:...types)', { types: filters.sourceTypes });
            }

            if (filters.is_active !== undefined) {
                queryBuilder.andWhere('rule.is_active = :isActive', { isActive: filters.is_active });
            }

            if (filters.searchTerm) {
                queryBuilder.andWhere('(rule.name LIKE :search OR rule.description LIKE :search)', {
                    search: `%${filters.searchTerm}%`,
                });
            }

            if (filters.minPriority !== undefined) {
                queryBuilder.andWhere('rule.priority >= :minPriority', { minPriority: filters.minPriority });
            }

            if (filters.maxPriority !== undefined) {
                queryBuilder.andWhere('rule.priority <= :maxPriority', { maxPriority: filters.maxPriority });
            }

            if (filters.validFrom) {
                queryBuilder.andWhere('(rule.valid_from IS NULL OR rule.valid_from >= :validFrom)', {
                    validFrom: filters.validFrom,
                });
            }

            if (filters.validTo) {
                queryBuilder.andWhere('(rule.valid_to IS NULL OR rule.valid_to <= :validTo)', {
                    validTo: filters.validTo,
                });
            }

            // Apply scoring types filter
            if (filters.scoring_type) {
                queryBuilder.andWhere('rule.scoring_type = :scoringType', { scoringType: filters.scoring_type });
            }

            // Apply sorting
            const sortField = filters.sortBy || 'created_at';
            const sortOrder = filters.sortOrder || 'DESC';
            queryBuilder.orderBy(`rule.${sortField}`, sortOrder as 'ASC' | 'DESC');

            // Apply pagination
            const page = filters.page || 1;
            const limit = filters.limit || 10;
            const offset = (page - 1) * limit;

            queryBuilder.skip(offset).take(limit);

            const [rules, total] = await queryBuilder.getManyAndCount();

            // Process rules to properly parse condition values if conditions are included
            const processedRules = filters.includeConditions
                ? rules.map((rule) => this.processRuleForResponse(rule))
                : rules;

            return {
                data: processedRules,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1,
                },
            };
        } catch (error) {
            this.logger.error(`Error finding rules with filters: ${error.message}`, error.stack);
            throw error;
        }
    }
    private operatorHandler(operator: EConditionOperator, scoringFieldName: EScoringFieldName): EConditionOperator {
        switch (scoringFieldName) {
            case EScoringFieldName.ORDER_TYPE:
                return EConditionOperator.IN;
            case EScoringFieldName.PAYMENT_METHOD_CODE:
                return EConditionOperator.IN;
            // Allow all operators for ORDER_DATE and SURCHARGE
            case EScoringFieldName.ORDER_DATE:
            case EScoringFieldName.SURCHARGE:
                return operator;
            default:
                return operator;
        }
    }
}
