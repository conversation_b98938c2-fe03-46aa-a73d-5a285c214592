import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { Driver } from "src/entities/driver.entity";
import { DriverScore, ELeaderboardType } from "src/entities/driverScore.entity";
import { ScoringHistory } from "src/entities/driverScoringHistory.entity";
import { ScoringRule } from "src/entities/driverScoringRule.entity";
import { DriverVehicle } from "src/entities/driverVehicle.entity";
import {
  EPointType,
  EScoringHistoryStatus,
  EScoringSourceType,
} from "src/entities/types/EScoringRule.enum";
import { DatabaseService } from "src/providers/database/database.service";
import { IsNull, Not } from "typeorm";
import {
  BatchManualScoringDto,
  BatchManualScoringResponseDto,
  ManualScoringDto,
  ManualScoringResponseDto,
  UpdateManualScoringDto,
} from "../dto/manualScoring.dto";
import { LeaderboardCoreService } from "./leaderboardCore.service";
import { getPeriodKey } from "../helpers/period.helper";

@Injectable()
export class ManualScoringService {
  private readonly logger = new Logger(ManualScoringService.name);

  constructor(
    private readonly leaderboardCoreService: LeaderboardCoreService
  ) {}

  async addManualPoints(
    provinceId: number,
    data: ManualScoringDto,
    adminUserId: number
  ): Promise<ManualScoringResponseDto> {
    const connection = DatabaseService.getConnectionByProvinceId(
      provinceId.toString()
    );
    const queryRunner = connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(
        `Adding manual points for driver ${data.driver_id}: ${data.points} points`
      );

      const driver = await queryRunner.manager.findOne(Driver, {
        where: { user_id: data.driver_id },
        select: {
          id: true,
          user_id: true,
          is_active: true,
        },
        lock: { mode: "pessimistic_write" },
      });

      if (!driver) {
        throw new NotFoundException(
          `Driver with ID ${data.driver_id} not found`
        );
      }

      const pointsToAward = data.points;
      let ruleName = "Cộng điểm thủ công";

      if (data.rule_id) {
        const rule = await this.getRuleById(data.rule_id, provinceId);
        if (rule) {
          ruleName = rule.name;
        }
      }
      const targetDate = data.target_date
        ? new Date(data.target_date)
        : new Date();
      const periodKey = getPeriodKey(data.leaderboard_type, targetDate);

      const result = await this.upsertDriverScoreWithinTransaction(
        queryRunner,
        {
          driver_id: driver.id,
          leaderboard_type: data.leaderboard_type,
          period_key: periodKey,
          new_points: pointsToAward,
          vehicle_type: undefined,
        }
      );

      const historyRecord = new ScoringHistory({
        source_id: result.driverScore.id,
        source_type: EScoringSourceType.MANUAL,
        driver_id: driver.id,
        rule_id: data.rule_id || null,
        rule_name: ruleName,
        points_type: pointsToAward >= 0 ? EPointType.ADD : EPointType.SUBTRACT,
        points_awarded: pointsToAward,
        points_before: { [data.leaderboard_type]: result.points_before },
        points_after: { [data.leaderboard_type]: result.points_after },
        calculation_details: data.rule_id
          ? `Cộng điểm thủ công với quy tắc #${data.rule_id}: ${data.reason}`
          : `Cộng điểm thủ công: ${data.reason}`,
        notes: data.notes || null,
        processed_by: adminUserId,
        status: EScoringHistoryStatus.SUCCESS,
        driver_scores: [result.driverScore],
      });

      const savedHistory = await queryRunner.manager.save(
        ScoringHistory,
        historyRecord
      );

      const driverScore = await queryRunner.manager.findOne(DriverScore, {
        where: { id: result.driverScore.id },
        relations: ["scoring_histories"],
      });

      if (driverScore) {
        if (!driverScore.scoring_histories) {
          driverScore.scoring_histories = [];
        }

        driverScore.scoring_histories.push(savedHistory);
        await queryRunner.manager.save(DriverScore, driverScore);
      }

      await queryRunner.commitTransaction();

      return {
        success: true,
        driver_id: driver.id,
        points_awarded: data.points,
        points_before: result.points_before,
        points_after: result.points_after,
        leaderboard_type: data.leaderboard_type,
        period_key: periodKey,
        history_id: savedHistory.id,
        timestamp: new Date(),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error adding manual points for driver ${data.driver_id}: ${error.message}`,
        error.stack
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Batch add manual scoring points
   */
  async batchAddManualPoints(
    provinceId: number,
    data: BatchManualScoringDto,
    adminUserId: number
  ): Promise<BatchManualScoringResponseDto> {
    const results: ManualScoringResponseDto[] = [];
    const errors: Array<{ driver_id: number; error: string }> = [];

    this.logger.log(
      `Processing batch manual scoring for ${data.entries.length} entries`
    );

    for (const entry of data.entries) {
      try {
        const result = await this.addManualPoints(
          provinceId,
          entry,
          adminUserId
        );
        results.push(result);
      } catch (error) {
        this.logger.error(
          `Error processing manual scoring for driver ${entry.driver_id}: ${error.message}`
        );
        errors.push({
          driver_id: entry.driver_id,
          error: error.message,
        });
      }
    }

    return {
      success: errors.length === 0,
      successful_count: results.length,
      failed_count: errors.length,
      results,
      errors,
    };
  }

  /**
   * Update existing manual scoring record
   */
  async updateManualScoring(
    provinceId: number,
    historyId: number,
    data: UpdateManualScoringDto,
    adminUserId: number
  ): Promise<ManualScoringResponseDto> {
    try {
      const historyRepository = DatabaseService.getRepositoryByProvinceId(
        ScoringHistory,
        provinceId
      );

      const existingRecord = await historyRepository.findOne({
        where: {
          id: historyId,
          notes: Not(IsNull()),
        },
      });

      if (!existingRecord) {
        throw new NotFoundException(
          `Bản ghi cộng điểm thủ công với ID ${historyId} không tồn tại`
        );
      }

      let pointsDifference = 0;
      if (data.points !== undefined) {
        pointsDifference = data.points - existingRecord.points_awarded;
      }

      if (data.rule_id !== undefined) {
        existingRecord.rule_id = data.rule_id;
        existingRecord.rule_name = data.rule_id
          ? `Quy tắc #${data.rule_id}`
          : "Cộng điểm thủ công";
      }
      if (data.points !== undefined) {
        existingRecord.points_awarded = data.points;
        existingRecord.points_type =
          data.points >= 0 ? EPointType.ADD : EPointType.SUBTRACT;
      }
      if (data.reason !== undefined) {
        existingRecord.calculation_details = data.rule_id
          ? `Cộng điểm thủ công với quy tắc #${data.rule_id}: ${data.reason}`
          : `Cộng điểm thủ công : ${data.reason}`;
      }
      if (data.notes !== undefined) {
        existingRecord.notes = data.notes;
      }
      existingRecord.processed_by = adminUserId;

      await historyRepository.save(existingRecord);

      let updatedDriverScore = null;
      if (pointsDifference !== 0) {
        const historyWithRelations = await historyRepository.findOne({
          where: { id: existingRecord.id },
          relations: ["driver_scores"],
        });

        const driverScore = historyWithRelations?.driver_scores?.[0];

        if (driverScore) {
          updatedDriverScore =
            await this.leaderboardCoreService.upsertDriverScoreWithTracking(
              {
                driver_id: existingRecord.driver_id,
                leaderboard_type: driverScore.leaderboard_type,
                period_key: driverScore.period_key,
                new_points: pointsDifference,
                vehicle_type: undefined,
              },
              provinceId
            );

          existingRecord.points_after = {
            [driverScore.leaderboard_type]: updatedDriverScore.points_after,
          };
          await historyRepository.save(existingRecord);
        }
      }

      return {
        success: true,
        driver_id: existingRecord.driver_id,
        points_awarded: existingRecord.points_awarded,
        points_before: updatedDriverScore
          ? updatedDriverScore.points_before
          : 0,
        points_after: updatedDriverScore ? updatedDriverScore.points_after : 0,
        leaderboard_type: updatedDriverScore
          ? updatedDriverScore.driverScore.leaderboard_type
          : ELeaderboardType.DAILY,
        period_key: updatedDriverScore
          ? updatedDriverScore.driverScore.period_key
          : "",
        history_id: existingRecord.id,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Error updating manual scoring record ${historyId}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Get scoring rule by ID
   */
  private async getRuleById(
    ruleId: number,
    provinceId: number
  ): Promise<ScoringRule | null> {
    try {
      const ruleRepository = DatabaseService.getRepositoryByProvinceId(
        ScoringRule,
        provinceId
      );
      const rule = await ruleRepository.findOne({
        where: { id: ruleId, is_active: true },
        relations: ["actions"],
      });
      return rule;
    } catch (error) {
      this.logger.error(`Error fetching rule ${ruleId}:`, error);
      return null;
    }
  }

  /**
   * Upsert driver score within transaction
   * This is a copy of leaderboardCoreService.upsertDriverScoreWithTracking but using queryRunner
   */
  private async upsertDriverScoreWithinTransaction(
    queryRunner: any,
    data: {
      driver_id: number;
      leaderboard_type: any;
      period_key: string;
      new_points: number;
      vehicle_type?: any;
    }
  ): Promise<{
    driverScore: DriverScore;
    points_before: number;
    points_after: number;
    was_created: boolean;
  }> {
    const vehicleType =
      data.vehicle_type ||
      (await this.getDriverVehicleTypeWithinTransaction(
        queryRunner,
        data.driver_id
      ));

    const existingScore = await queryRunner.manager.findOne(DriverScore, {
      where: {
        driver_id: data.driver_id,
        leaderboard_type: data.leaderboard_type,
        period_key: data.period_key,
        vehicle_type: vehicleType,
      },
    });

    let result: DriverScore;
    let points_before: number;
    let points_after: number;
    let was_created: boolean;

    if (existingScore) {
      points_before = Number(existingScore.total_points);
      points_after = points_before + data.new_points;
      existingScore.total_points = points_after;
      was_created = false;

      result = await queryRunner.manager.save(DriverScore, existingScore);
    } else {
      points_before = 0;
      points_after = data.new_points;
      was_created = true;

      const snapshotData = await this.captureRankingSnapshotWithinTransaction(
        queryRunner,
        data.driver_id
      );

      const newScore = queryRunner.manager.create(DriverScore, {
        driver_id: data.driver_id,
        leaderboard_type: data.leaderboard_type,
        period_key: data.period_key,
        vehicle_type: vehicleType,
        total_points: points_after,
        ...snapshotData,
      });

      result = await queryRunner.manager.save(DriverScore, newScore);
    }

    return {
      driverScore: result,
      points_before,
      points_after,
      was_created,
    };
  }

  private async getDriverVehicleTypeWithinTransaction(
    queryRunner: any,
    driverId: number
  ): Promise<any> {
    try {
      const driverVehicle = await queryRunner.manager.findOne(DriverVehicle, {
        where: {
          driver_id: driverId,
          in_use: 1,
        },
        relations: ["vehicleType"],
        order: { created_at: "DESC" },
      });

      if (driverVehicle && driverVehicle.vehicleType) {
        return driverVehicle.vehicleType.type;
      }

      throw new BadRequestException("Không tìm thấy loại xe");
    } catch (error) {
      throw error;
    }
  }

  private async captureRankingSnapshotWithinTransaction(
    queryRunner: any,
    driverId: number
  ): Promise<{
    snapshot_rank_id?: number;
    snapshot_rank_name?: string;
    snapshot_rank_code?: string;
    snapshot_rank_level?: number;
    snapshot_rank_image?: string;
  }> {
    try {
      const driver = await queryRunner.manager.findOne(Driver, {
        where: { user_id: driverId },
        relations: ["ranking"],
      });

      if (!driver || !driver.ranking) {
        this.logger.warn(`No ranking found for driver ${driverId}`);
        return {};
      }

      return {
        snapshot_rank_id: driver.ranking.id,
        snapshot_rank_name: driver.ranking.name,
        snapshot_rank_code: driver.ranking.code,
        snapshot_rank_level: driver.ranking.level,
        snapshot_rank_image: driver.ranking.image,
      };
    } catch (error) {
      this.logger.warn(
        `Error capturing ranking snapshot for driver ${driverId}: ${error.message}`
      );
      return {};
    }
  }
}
