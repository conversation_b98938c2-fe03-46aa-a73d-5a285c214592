import * as moment from 'moment';
import { ELeaderboardType } from 'src/entities/driverScore.entity';

/**
 * Generate period key based on leaderboard type and date
 */
export const getPeriodKey = (leaderboard_type: ELeaderboardType, date: Date = new Date()): string => {
    const momentDate = moment(date).utcOffset(7); // Vietnam timezone

    switch (leaderboard_type) {
        case ELeaderboardType.DAILY:
            return momentDate.format('YYYY-MM-DD');
        case ELeaderboardType.WEEKLY:
            return momentDate.format('YYYY-[W]WW');
        case ELeaderboardType.MONTHLY:
            return momentDate.format('YYYY-MM');
        default:
            throw new Error(`Unsupported leaderboard type: ${leaderboard_type}`);
    }
};

export const mapLeaderboardType = (type: string): ELeaderboardType => {
    switch (type.toLowerCase()) {
        case 'daily':
            return ELeaderboardType.DAILY;
        case 'weekly':
            return ELeaderboardType.WEEKLY;
        case 'monthly':
            return ELeaderboardType.MONTHLY;
        default:
            throw new Error(`Invalid leaderboard type: ${type}`);
    }
};
