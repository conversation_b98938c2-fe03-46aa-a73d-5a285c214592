import * as moment from 'moment';
import {
    LEADERBOARD_DATE_KEY_FORMAT,
    LEADERBOARD_MONTH_KEY_FORMAT,
    LEADERBOARD_WEEK_KEY_FORMAT,
    METADATA_KEY_FORMAT,
} from '../constants/leaderboard.constant';

/**
 * Tạo key cho leaderboard dựa trên ngày và provinceId
 * @param type Loại leaderboard
 * @param date Ngày tạo leaderboard
 * @param provinceId ID của tỉnh/thành phố
 * @returns Key của leaderboard
 */
export const generateLeaderboardDailyKey = (date: Date, provinceId: number): string => {
    const dateStr = moment(date).format('YYYY-MM-DD');
    return LEADERBOARD_DATE_KEY_FORMAT.replace('{date}', dateStr).replace('{provinceId}', provinceId.toString());
};

//generate weekly leaderboard key
export const generateWeeklyLeaderboardKey = (provinceId: number, weekNumber: number): string => {
    return LEADERBOARD_WEEK_KEY_FORMAT.replace('{week}', weekNumber.toString()).replace(
        '{provinceId}',
        provinceId.toString(),
    );
};
//METADATA_KEY_FORMAT
export const generateMetadataKey = (provinceId: number, driverId: string): string => {
    return METADATA_KEY_FORMAT.replace('{provinceId}', provinceId.toString()).replace('{memberId}', driverId);
};

//generate monthly leaderboard key
export const generateMonthlyLeaderboardKey = (date: Date, provinceId: number): string => {
    const dateStr = moment(date).format('YYYY-MM');
    return LEADERBOARD_MONTH_KEY_FORMAT.replace('{month}', dateStr).replace('{provinceId}', provinceId.toString());
};

/**
 * Kiểm tra xem ngày được cung cấp có phải là ngày hôm nay không
 * @param date Ngày cần kiểm tra
 * @returns Boolean cho biết có phải ngày hôm nay không
 */
export const isToday = (date: Date): boolean => {
    const today = new Date();
    return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    );
};

/**
 * Chuyển đổi chuỗi ngày thành đối tượng Date
 * @param dateStr Chuỗi ngày theo định dạng YYYY-MM-DD
 * @returns Đối tượng Date hoặc null nếu định dạng không hợp lệ
 */
export const parseDate = (dateStr: string): Date | null => {
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return null;
        }
        return date;
    } catch (error) {
        return null;
    }
};

/**
 * Format số để hiển thị dễ đọc hơn
 * @param number Số cần format
 * @returns Chuỗi đã được format
 */
export const formatNumber = (number: number): string => {
    return new Intl.NumberFormat('vi-VN').format(number);
};

/**
 * Lấy số tuần hiện tại trong năm theo ISO week
 * @param date Ngày cần lấy số tuần (mặc định là ngày hiện tại)
 * @returns Số tuần trong năm (1-53)
 */
export const getCurrentWeekOfYear = (date: Date = new Date()): number => {
    // Sử dụng moment để tính ISO week number để đồng nhất với logic khác
    const weekNumber = moment(date).utcOffset(7).isoWeek();
    return weekNumber;
};

// Added utility: safely convert a date value (Date instance or string) to ISO string.
export const toISODateString = (value?: Date | string | null): string => {
    if (!value) {
        return new Date().toISOString();
    }

    const dateInstance = value instanceof Date ? value : new Date(value);

    // If the date is invalid, fallback to current timestamp
    if (isNaN(dateInstance.getTime())) {
        return new Date().toISOString();
    }

    return dateInstance.toISOString();
};
