import { EScoringSourceType } from 'src/entities/types/EScoringRule.enum';

export interface IScoringHistoryRecord {
    source_id: number;
    driver_id: number;
    rule_id: number;
    rule_name: string;
    source_type?: EScoringSourceType;
    points_awarded: number;
    points_before?: Record<string, number>; // JSON object: {"daily": 0, "weekly": 0, "monthly": 0}
    points_after?: Record<string, number>; // JSON object: {"daily": 10, "weekly": 10, "monthly": 10}
    calculation_details?: Record<string, any>;
    processed_by?: number;
    notes?: string;
}

export interface IHistoryQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: 'created_at' | 'points_awarded' | 'rule_name';
    sortOrder?: 'ASC' | 'DESC';
    dateFrom?: Date;
    dateTo?: Date;
    ruleTypes?: EScoringSourceType[];
    searchTerm?: string;
}

export interface IDriverHistoryStats {
    driver_id: number;
    total_points: number;
    total_records: number;
    average_points_per_order: number;
    most_used_rule: {
        rule_id: number;
        rule_name: string;
        usage_count: number;
    };
    date_range: {
        from: Date;
        to: Date;
    };
}

export interface IProvinceStatistics {
    province_id: number;
    total_points_awarded: number;
    total_scoring_events: number;
    unique_drivers: number;
    unique_sources: number;
    rule_breakdown: Array<{
        rule_type: EScoringSourceType;
        count: number;
        points: number;
    }>;
    date_range: {
        from: Date;
        to: Date;
    };
}
