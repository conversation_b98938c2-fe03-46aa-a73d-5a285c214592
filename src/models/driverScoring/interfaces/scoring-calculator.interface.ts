import { ScoringRuleAction } from 'src/entities/driverScoringRuleAction.entity';
import {
    ECalculationType,
    ICalculationDetails
} from 'src/entities/types/EScoringRule.enum';

export interface ICalculationInput {
    action: ScoringRuleAction;
    value: number;
    context?: any; // Additional context for calculations
}

export interface ICalculationResult {
    points: number;
    formula: string;
    details: ICalculationDetails;
}

export interface ICalculationPreview {
    calculation_type: ECalculationType;
    formula_template: string;
    sample_calculations: Array<{
        input_value: number;
        expected_output: number;
        formula_used: string;
    }>;
    constraints: Record<string, never>;
}

export interface IValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Driver order plan registration context for scoring
 */
export interface IDriverOrderPlanRegistrationContext {
    registration_id: number;
    driver_id: number;
    plan_id: number;
    bonus_amount: number;
    week_start_date: string;
    week_end_date: string;
    total_planned_orders: number;
    registration_time: string;
    deadline: string;
    // Plan data
    plan_name: string;
    plan_min_orders: number;
    plan_active: boolean;
    plan_required_rank_ids: number[];
    // Driver rank info
    driver_rank_id: number;
}

/**
 * Driver order plan completion context for scoring
 */
export interface IDriverOrderPlanCompletionContext extends IDriverOrderPlanRegistrationContext {
    total_completed_orders: number;
    is_completed: boolean;
    reward_id: number | null;
    completion_rate: number;
}
