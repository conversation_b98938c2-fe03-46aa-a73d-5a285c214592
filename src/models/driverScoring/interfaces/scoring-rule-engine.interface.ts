import { Order } from 'src/entities/order.entity';
import { ScoringRule } from 'src/entities/driverScoringRule.entity';
import { EPointType } from 'src/entities/types/EScoringRule.enum';

export interface IOrderScoringContext {
    order: Order;
    driver_id: number;
    province_id: number;
}

export interface IRuleEvaluationResult {
    rule: ScoringRule;
    conditions_met: boolean;
    points_awarded: number;
    calculation_details: string;
    execution_time_ms: number;
    points_type: EPointType;
}

export interface IOrderScoringResult {
    order_id: number;
    driver_id: number;
    total_points: number;
    points_type: EPointType;
    rules_evaluated: IRuleEvaluationResult[];
    execution_time_ms: number;
    timestamp: Date;
}
