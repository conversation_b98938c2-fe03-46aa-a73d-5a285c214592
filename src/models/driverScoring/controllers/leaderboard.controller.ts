import { Body, Controller, Logger, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { DriverRankingsResponseDto, GetDriverRankingsDto } from '../dto/leaderboard.dto';
import { LeaderboardCoreService } from '../services/leaderboardCore.service';

@ApiTags('Leaderboard')
@UseInterceptors(LoggingInterceptor)
@Controller('leaderboard')
@UseGuards(AuthGuard)
export class LeaderboardController {
    private readonly logger = new Logger(LeaderboardController.name);

    constructor(private readonly leaderboardCoreService: LeaderboardCoreService) {}

    @Post('rankings')
    @ApiOperation({
        summary: 'Get driver rankings',
        description: 'Retrieve driver rankings from database with 1-minute caching',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully retrieved driver rankings',
        type: DriverRankingsResponseDto,
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid request parameters',
    })
    async getDriverRankings(
        @HeaderProvince(new ParseIntPipe()) provinceId: number,
        @Body(new HttpValidationPipe()) query: GetDriverRankingsDto,
    ) {
        return await this.leaderboardCoreService.getDriverRanking(provinceId, query);
    }
}
