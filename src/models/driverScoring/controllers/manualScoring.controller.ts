import { Body, Controller, Logger, Param, ParseIntPipe, Post, Put, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { IRequestUser, RequestUser } from 'src/common/decorators/user.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { ManualScoringDto, ManualScoringResponseDto, UpdateManualScoringDto } from '../dto/manualScoring.dto';
import { ManualScoringService } from '../services/manualScoring.service';

@ApiTags('Manual Scoring')
@UseInterceptors(LoggingInterceptor)
@Controller('manual-scoring')
@UseGuards(AuthGuard)
export class ManualScoringController {
    private readonly logger = new Logger(ManualScoringController.name);

    constructor(private readonly manualScoringService: ManualScoringService) {}

    @Post('add-points')
    @ApiOperation({
        summary: 'Add manual scoring points',
        description: 'Add manual scoring points for a specific driver',
    })
    @ApiResponse({
        status: 200,
        description: 'Successfully added manual points',
        type: ManualScoringResponseDto,
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid request parameters',
    })
    async addManualPoints(
        @HeaderProvince(new ParseIntPipe()) provinceId: number,
        @Body(new HttpValidationPipe()) data: ManualScoringDto,
        @RequestUser() user: IRequestUser,
    ): Promise<ManualScoringResponseDto> {
        try {
            this.logger.log(
                `Admin ${user.id} adding manual points for driver ${data.driver_id}: ${data.points} points`,
            );

            const result = await this.manualScoringService.addManualPoints(provinceId, data, user.id);

            this.logger.log(`Successfully added manual points for driver ${data.driver_id}`);
            return result;
        } catch (error) {
            this.logger.error(`Error adding manual points: ${error.message}`, error.stack);
            throw error;
        }
    }

    @Put('update/:historyId')
    @ApiOperation({
        summary: 'Update manual scoring record',
        description: 'Update an existing manual scoring record',
    })
    @ApiParam({ name: 'historyId', description: 'Manual scoring history record ID' })
    @ApiResponse({
        status: 200,
        description: 'Successfully updated manual scoring record',
        type: ManualScoringResponseDto,
    })
    @ApiResponse({
        status: 404,
        description: 'Manual scoring record not found',
    })
    async updateManualScoring(
        @HeaderProvince(new ParseIntPipe()) provinceId: number,
        @Param('historyId', ParseIntPipe) historyId: number,
        @Body(new HttpValidationPipe()) data: UpdateManualScoringDto,
        @RequestUser() user: IRequestUser,
    ) {
        return await this.manualScoringService.updateManualScoring(provinceId, historyId, data, user.id);
    }
}
