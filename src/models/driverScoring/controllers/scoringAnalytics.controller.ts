import { Body, Controller, Param, ParseIntPipe, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { IRequestUser, RequestUser } from 'src/common/decorators/user.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { GetScoringHistoryDto } from '../dto';
import { RefundScoringHistoryDto, ScoringHistoryActionResponseDto } from '../dto/scoringHistoryActions.dto';
import { ScoringHistoryService } from '../services/scoringHistory.service';

@ApiTags('Scoring History')
@UseInterceptors(LoggingInterceptor)
@Controller('scoring-history')
@UseGuards(AuthGuard)
export class ScoringHistoryController {
    constructor(private readonly scoringHistoryService: ScoringHistoryService) {}

    @Post('statistics/history')
    @RequirePermissions(PermissionsAccessAction.SCORING_RULES_READ)
    async getScoringHistory(
        @Body(new HttpValidationPipe()) getScoringHistoryDto: GetScoringHistoryDto,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.scoringHistoryService.getAllHistory(provinceId, getScoringHistoryDto);
    }

    @Post('history/:historyId/refund')
    async refundScoringHistory(
        @HeaderProvince(new ParseIntPipe()) provinceId: number,
        @Param('historyId', ParseIntPipe) historyId: number,
        @Body(new HttpValidationPipe()) refundDto: RefundScoringHistoryDto,
        @RequestUser() user: IRequestUser,
    ): Promise<ScoringHistoryActionResponseDto> {
        try {
            const result = await this.scoringHistoryService.refundScoringHistory(
                provinceId,
                historyId,
                user.id,
                refundDto.reason,
            );
            return result;
        } catch (error) {
            throw error;
        }
    }
}
