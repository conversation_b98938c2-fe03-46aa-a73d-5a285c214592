import { Body, Controller, ParseIntPipe, Post, UseGuards } from '@nestjs/common';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { GetDriverScoresDto } from '../dto/driverScores.dto';
import { DriverScoresService } from '../services/driverScores.service';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { UseInterceptors } from '@nestjs/common';

@Controller('driver-scores')
@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
export class DriverScoresController {
    constructor(private readonly driverScoresService: DriverScoresService) {}

    @Post()
    async getDriverScores(@Body() params: GetDriverScoresDto, @HeaderProvince(new ParseIntPipe()) provinceId: number) {
        return await this.driverScoresService.getDriverScores(params, provinceId);
    }
}
