import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";
import { HeaderProvince } from "src/common/decorators/province.decorator";
import {
  IRequestUser,
  RequestUser,
} from "src/common/decorators/user.decorator";
import { LoggingInterceptor } from "src/common/interceptors/logging.interceptor";
import AuthGuard from "src/common/middlewares/auth.gaurd";
import { RequirePermissions } from "src/common/middlewares/permissions.decorator";
import { HttpValidationPipe } from "src/common/pipes/httpValidation.pipe";
import { PermissionsAccessAction } from "src/entities/permission.entity";
import {
  CreateScoringRuleDto,
  GetScoringRulesDto,
  ScoringRuleResponseDto,
  UpdateScoringRuleDto,
} from "../dto";
import { ScoringRuleService } from "../services/scoringRule.service";

@ApiTags("Scoring Rules")
@UseInterceptors(LoggingInterceptor)
@Controller("scoring-rules")
@UseGuards(AuthGuard)
export class ScoringRuleController {
  constructor(private readonly scoringRuleService: ScoringRuleService) {}

  @Post("list")
  @ApiOperation({
    summary: "Get scoring rules list",
    description:
      "Retrieve scoring rules with filtering, sorting and pagination",
  })
  @ApiResponse({
    status: 200,
    description: "Successfully retrieved scoring rules",
  })
  @RequirePermissions(PermissionsAccessAction.SCORING_RULES_READ)
  async getScoringRules(
    @Body(new HttpValidationPipe()) query: GetScoringRulesDto,
    @HeaderProvince(new ParseIntPipe()) provinceId: number
  ) {
    const result = await this.scoringRuleService.findWithFilters(
      provinceId,
      query
    );

    return {
      items: result.data,
      total: result.pagination.total,
      pagination: result.pagination,
      filters: {
        sourceTypes: query.sourceTypes,
        sourceIds: query.sourceIds,
        is_active: query.is_active,
        searchTerm: query.searchTerm,
        scoring_type: query.scoring_type,
        priorityRange:
          query.minPriority || query.maxPriority
            ? {
                min: query.minPriority || 0,
                max: query.maxPriority || 100,
              }
            : undefined,
        dateRange:
          query.validFrom || query.validTo
            ? {
                from: query.validFrom,
                to: query.validTo,
              }
            : undefined,
      },
    };
  }

  @Get(":id")
  @ApiOperation({
    summary: "Get scoring rule by ID",
    description: "Retrieve detailed information about a specific scoring rule",
  })
  @ApiParam({ name: "id", description: "Scoring rule ID" })
  @ApiResponse({
    status: 200,
    description: "Successfully retrieved scoring rule",
    type: ScoringRuleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Scoring rule not found" })
  @RequirePermissions(PermissionsAccessAction.SCORING_RULES_READ)
  async getScoringRuleById(
    @Param("id", ParseIntPipe) id: number,
    @HeaderProvince(new ParseIntPipe()) provinceId: number
  ) {
    const rule = await this.scoringRuleService.getRuleById(provinceId, id);

    if (!rule) {
      throw new NotFoundException(`Scoring rule with ID ${id} not found`);
    }

    return rule;
  }

  @Post()
  @ApiOperation({
    summary: "Create new scoring rule",
    description: "Create a new scoring rule with conditions and actions",
  })
  @ApiResponse({
    status: 201,
    description: "Successfully created scoring rule",
    type: ScoringRuleResponseDto,
  })
  @RequirePermissions(PermissionsAccessAction.SCORING_RULES_CREATE)
  async createScoringRule(
    @Req() ctx: Request,
    @HeaderProvince(new ParseIntPipe()) provinceId: number,
    @Body(new HttpValidationPipe()) createDto: CreateScoringRuleDto,
    @RequestUser() user: IRequestUser
  ) {
    await this.validateRuleConfiguration(createDto);

    const ruleData = {
      ...createDto,
      created_by: user.id,
    };
    const createdRule = await this.scoringRuleService.createRule(
      ctx,
      ruleData,
      user.id,
      provinceId
    );
    return createdRule;
  }

  @Put(":id")
  @ApiOperation({
    summary: "Update scoring rule",
    description: "Update an existing scoring rule",
  })
  @ApiParam({ name: "id", description: "Scoring rule ID" })
  @ApiResponse({
    status: 200,
    description: "Successfully updated scoring rule",
    type: ScoringRuleResponseDto,
  })
  @RequirePermissions(PermissionsAccessAction.SCORING_RULES_UPDATE)
  async updateScoringRule(
    @Param("id", ParseIntPipe) id: number,
    @Body(new HttpValidationPipe()) updateDto: UpdateScoringRuleDto,
    @HeaderProvince(new ParseIntPipe()) provinceId: number,
    @RequestUser() user: IRequestUser
  ) {
    const existingRule = await this.scoringRuleService.getRuleById(
      provinceId,
      id
    );
    if (!existingRule) {
      throw new NotFoundException(`Scoring rule with ID ${id} not found`);
    }

    await this.validateRuleConfiguration(updateDto);
    const updatedRule = await this.scoringRuleService.updateRule(
      provinceId,
      id,
      updateDto,
      user.id
    );
    return updatedRule;
  }

  @Delete(":id")
  @ApiOperation({
    summary: "Delete scoring rule",
    description: "Delete an existing scoring rule",
  })
  @ApiParam({ name: "id", description: "Scoring rule ID" })
  @ApiResponse({
    status: 200,
    description: "Successfully deleted scoring rule",
  })
  @RequirePermissions(PermissionsAccessAction.SCORING_RULES_DELETE)
  async deleteScoringRule(
    @Param("id", ParseIntPipe) id: number,
    @HeaderProvince(new ParseIntPipe()) provinceId: number
  ) {
    const existingRule = await this.scoringRuleService.getRuleById(
      provinceId,
      id
    );
    if (!existingRule) {
      throw new NotFoundException(`Scoring rule with ID ${id} not found`);
    }

    await this.scoringRuleService.deleteRule(provinceId, id);
    return { message: "Scoring rule deleted successfully" };
  }

  // Private helper methods
  private async validateRuleConfiguration(
    ruleDto: CreateScoringRuleDto | UpdateScoringRuleDto
  ): Promise<void> {
    // Validate conditions
    if (ruleDto.conditions && ruleDto.conditions.length > 0) {
      for (const condition of ruleDto.conditions) {
        if (!this.isValidFieldName(condition.field_name)) {
          throw new BadRequestException(
            `Invalid field name: ${condition.field_name}`
          );
        }
      }
    }

    // No action validation needed
  }

  private isValidFieldName(fieldName: string): boolean {
    const validFields = [
      // Order fields
      "distance",
      "total_price",
      "type",
      "order_type",
      "province_id",
      "delivery_fee",
      "sub_total_price",
      "order_status_id",
      "types_of_good_id",
      "vehicle_type_id",
      "long_distance_bonus",
      "holiday_bonus",
      "large_order_bonus",
      "delivery_hour",
      "delivery_day_of_week",
      "order_date",
      "surcharge",
      "payment_method_code",

      // Driver Order Plan Registration fields
      "id",
      "driver_id",
      "plan_id",
      "bonus_amount",
      "week_start_date",
      "week_end_date",
      "total_planned_orders",
      "total_completed_orders",
      "completion_rate",
      "is_completed",
      "reward_id",
      "registration_time",
      "deadline",

      // Plan fields
      "name",
      "min_orders",
      "active",
      "required_rank_ids",
      "image",
      "desc",

      //rating
      "rate",
    ];
    return validFields.includes(fieldName);
  }
}
