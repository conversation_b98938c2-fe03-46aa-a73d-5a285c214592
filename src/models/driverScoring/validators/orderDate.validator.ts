import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { EScoringFieldName } from 'src/entities/types/EScoringRule.enum';

/**
 * Helper function to parse and validate order date values
 * Ensures values are always valid date strings in YYYY-MM-DD format
 */
export function parseOrderDate(value: any): string[] | string {
    if (value === null || value === undefined) {
        return [];
    }

    let parsedValues: string[] = [];

    try {
        // If it's already an array
        if (Array.isArray(value)) {
            parsedValues = value.map(val => {
                if (typeof val === 'string') {
                    if (!isValidDateString(val)) {
                        throw new Error(`Invalid date format: ${val}. Expected YYYY-MM-DD`);
                    }
                    return val;
                } else {
                    throw new Error(`Invalid date value type: ${typeof val}`);
                }
            });
        }
        // If it's a single value
        else if (typeof value === 'string') {
            if (!isValidDateString(value)) {
                throw new Error(`Invalid date format: ${value}. Expected YYYY-MM-DD`);
            }
            return value; // Return single string for non-array operators
        }
        // If it's a JSON string
        else if (typeof value === 'string' && (value.startsWith('[') || value.startsWith('{'))) {
            const parsed = JSON.parse(value);
            return parseOrderDate(parsed); // Recursive call
        }
        else {
            throw new Error(`Unsupported value type: ${typeof value}`);
        }

        // Remove duplicates and sort
        return [...new Set(parsedValues)].sort();

    } catch (error) {
        throw new Error(`Failed to parse order_date: ${error.message}`);
    }
}

/**
 * Validate if a string is a valid date in YYYY-MM-DD format
 */
function isValidDateString(dateString: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        return false;
    }

    const date = new Date(dateString);
    const [year, month, day] = dateString.split('-').map(Number);
    
    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
}

/**
 * Validator decorator for order date fields
 */
export function IsValidOrderDate(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'isValidOrderDate',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value: any, args: ValidationArguments) {
                    try {
                        parseOrderDate(value);
                        return true;
                    } catch (error) {
                        return false;
                    }
                },
                defaultMessage(args: ValidationArguments) {
                    return `${args.property} must be valid date(s) in YYYY-MM-DD format`;
                }
            }
        });
    };
}

/**
 * Transform function to automatically parse order_date values
 * Use this in DTOs with @Transform decorator
 */
export function transformOrderDate(value: any): string[] | string {
    try {
        return parseOrderDate(value);
    } catch (error) {
        // Return original value if parsing fails, let validator handle the error
        return value;
    }
}

/**
 * Utility to check if a field is order_date and needs special handling
 */
export function isOrderDateField(fieldName: string): boolean {
    return fieldName === EScoringFieldName.ORDER_DATE;
}

/**
 * Process condition value for order_date field
 * This should be called before saving to database
 */
export function processOrderDateCondition(fieldName: string, value: any): any {
    if (isOrderDateField(fieldName)) {
        return parseOrderDate(value);
    }
    return value;
}
