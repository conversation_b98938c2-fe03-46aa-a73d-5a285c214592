import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { EScoringFieldName } from 'src/entities/types/EScoringRule.enum';

/**
 * Helper function to parse and validate delivery day of week values
 * Ensures values are always numbers between 0-6
 */
export function parseDeliveryDayOfWeek(value: any): number[] {
    if (value === null || value === undefined) {
        return [];
    }

    let parsedValues: number[] = [];

    try {
        // If it's already an array
        if (Array.isArray(value)) {
            parsedValues = value.map(val => {
                if (typeof val === 'string') {
                    const num = parseInt(val, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid day value: ${val}`);
                    }
                    return num;
                } else if (typeof val === 'number') {
                    return val;
                } else {
                    throw new Error(`Invalid day value type: ${typeof val}`);
                }
            });
        }
        // If it's a single value
        else if (typeof value === 'string') {
            const num = parseInt(value, 10);
            if (isNaN(num)) {
                throw new Error(`Invalid day value: ${value}`);
            }
            parsedValues = [num];
        }
        else if (typeof value === 'number') {
            parsedValues = [value];
        }
        // If it's a JSON string
        else if (typeof value === 'string' && (value.startsWith('[') || value.startsWith('{'))) {
            const parsed = JSON.parse(value);
            return parseDeliveryDayOfWeek(parsed); // Recursive call
        }
        else {
            throw new Error(`Unsupported value type: ${typeof value}`);
        }

        // Validate each day is between 0-6
        for (const day of parsedValues) {
            if (!Number.isInteger(day) || day < 0 || day > 6) {
                throw new Error(`Day of week must be between 0-6, got: ${day}`);
            }
        }

        // Remove duplicates and sort
        return [...new Set(parsedValues)].sort();

    } catch (error) {
        throw new Error(`Failed to parse delivery_day_of_week: ${error.message}`);
    }
}

/**
 * Validator decorator for delivery day of week fields
 */
export function IsValidDeliveryDayOfWeek(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'isValidDeliveryDayOfWeek',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            validator: {
                validate(value: any, args: ValidationArguments) {
                    try {
                        parseDeliveryDayOfWeek(value);
                        return true;
                    } catch (error) {
                        return false;
                    }
                },
                defaultMessage(args: ValidationArguments) {
                    return `${args.property} must be valid day(s) of week (0-6). Use 0=Sunday, 1=Monday, ..., 6=Saturday`;
                }
            }
        });
    };
}

/**
 * Transform function to automatically parse delivery_day_of_week values
 * Use this in DTOs with @Transform decorator
 */
export function transformDeliveryDayOfWeek(value: any): number[] {
    try {
        return parseDeliveryDayOfWeek(value);
    } catch (error) {
        // Return original value if parsing fails, let validator handle the error
        return value;
    }
}


/**
 * Utility to check if a field is delivery_day_of_week and needs special handling
 */
export function isDeliveryDayOfWeekField(fieldName: string): boolean {
    return fieldName === EScoringFieldName.DELIVERY_DAY_OF_WEEK;
}

/**
 * Process condition value for delivery_day_of_week field
 * This should be called before saving to database
 */
export function processDeliveryDayOfWeekCondition(fieldName: string, value: any): any {
    if (isDeliveryDayOfWeekField(fieldName)) {
        return parseDeliveryDayOfWeek(value);
    }
    return value;
}
