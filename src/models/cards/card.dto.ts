import { Transform } from 'class-transformer';
import { IsEnum } from 'class-validator';
import * as _ from 'lodash';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';

export class getCardsDto extends BaseQueryFilterDto {
    @Transform(({ value }) => (!_.isEmpty(value) ? value : 'created_at'), { toClassOnly: true })
    @IsEnum({
        user_id: 'user_id',
    })
    orderBy = 'user_id';
}
