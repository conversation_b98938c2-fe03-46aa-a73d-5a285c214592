import { Inject, Injectable, NotFoundException, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { DEFAULT_PROVINCE_ID, DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { EntityManager, Repository } from 'typeorm';
import { Request } from 'express';
import * as _ from 'lodash';

import { MgpayIcPaymentToken } from 'src/entities/mgpayIcPaymentToken.entity';
import { getCardsDto } from './card.dto';

@Injectable()
export class CardService {
    constructor() {}

    private getRepositoryByProvinceId(provinceId: number): Repository<MgpayIcPaymentToken> {
        if (provinceId === VUNGTAU_PROVINCE_ID) {
            return DatabaseService.getRepositoryByProvinceId(MgpayIcPaymentToken, VUNGTAU_PROVINCE_ID);
        }
        return DatabaseService.getRepositoryByProvinceId(MgpayIcPaymentToken, DEFAULT_PROVINCE_ID);
    }

    public list({ sortedBy, orderBy, limit, page }: getCardsDto, provinceId: string) {
        return this.getRepositoryByProvinceId(+provinceId).findAndCount({
            relations: ['paymentMethod', 'user'],
            order: {
                [orderBy]: sortedBy,
            },
            skip: page * limit,
            take: limit,
        });
    }

    public async getById(id: number, provinceId: string) {
        return await this.getRepositoryByProvinceId(+provinceId).findOne({ where: { user_id: id } });
    }
}
