import { Controller, Get, NotFoundException, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { getCardsDto } from './card.dto';
import { CardService } from './card.service';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('cards')
@UseGuards(AuthGuard)
export class CardController {
    constructor(private readonly cardService: CardService) {}
    // @Post()
    // @RequirePermissions(PermissionsAccessAction.GALLERY_CREATE)
    // async create(@Body(new ValidationPipe()) data: newItem) {
    //     return (await this.paymentMethodService.create(data))
    // }

    @Get()
    @RequirePermissions(PermissionsAccessAction.CARD_FIND_LIST)
    async list(@Query(new HttpValidationPipe()) query: getCardsDto, @HeaderProvince() provinceId: string){
        const result = await this.cardService.list(query, provinceId);
        return {
            items: result[0],
            total: result[1],
        };
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.CARD_FIND_ONE)
    async getById(@Param('id', new ParseIntPipe()) id: number, @HeaderProvince() provinceId: string) {
        const result = await this.cardService.getById(id, provinceId);
        if (!result) throw new NotFoundException('category not found');
        return result;
    }
}
