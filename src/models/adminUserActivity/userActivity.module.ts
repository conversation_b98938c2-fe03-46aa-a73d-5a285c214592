import { Module } from '@nestjs/common';
import { UserActivityService } from './userActivity.service';
import { UserActivityController } from './userActivity.controller';
import { UserModule } from '../user/user.module';
import { EmployeeService } from '../employee/employee.service';
@Module({
    controllers: [UserActivityController],
    providers: [UserActivityService, EmployeeService],
    exports: [UserActivityService],
})
export class UserActivityModule {}
