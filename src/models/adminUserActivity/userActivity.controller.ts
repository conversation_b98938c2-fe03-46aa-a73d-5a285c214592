import { Controller, Get, Param, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { GetUserActivityDto } from './userActivity.dto';
import { UserActivityService } from './userActivity.service';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('user-activities')
@UseGuards(AuthGuard)
export class UserActivityController {
    constructor(private userActivityService: UserActivityService) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.USER_ACTIVITY_FIND_LIST)
    async getList(@Query(new HttpValidationPipe()) params: GetUserActivityDto, @HeaderProvince() provinceId: string) {
        return await this.userActivityService.getList(params, provinceId);
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.USER_ACTIVITY_FIND_ONE)
    async getDetails(@Param('id') id: number, @HeaderProvince() provinceId: string) {
        return await this.userActivityService.getDetails(id, provinceId);
    }
}
