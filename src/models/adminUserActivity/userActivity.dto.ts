import { Transform } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { EAdminEmployeeActivityAction, EAdminEmployeeActivityType } from 'src/entities/adminUserActivity.entity';

export class GetUserActivityDto extends BaseQueryFilterDto {
    @IsOptional()
    @IsEnum(EAdminEmployeeActivityType)
    type: EAdminEmployeeActivityType;

    @IsOptional()
    @IsEnum(EAdminEmployeeActivityAction)
    action: EAdminEmployeeActivityAction;

    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    user_id: number;

    @IsOptional()
    @Transform(({ value }) => parseInt(value))
    effected_id: number;
}
