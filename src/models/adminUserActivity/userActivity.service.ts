import { Injectable, Logger } from '@nestjs/common';
import {
    AdminUserActivity,
    EAdminEmployeeActivityAction,
    EAdminEmployeeActivityType,
} from 'src/entities/adminUserActivity.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { isEqual, omit, result } from 'lodash';
import { GetUserActivityDto } from './userActivity.dto';
import { IPagedResult } from 'src/common/types/pagedResult.interface';
import { InsertResult } from 'typeorm';
import { EmployeeService } from '../employee/employee.service';

@Injectable()
export class UserActivityService {
    private logger = new Logger(UserActivityService.name);

    constructor(private readonly employeeService: EmployeeService) {}

    async createEmployeeActivity(
        type: EAdminEmployeeActivityType,
        action: EAdminEmployeeActivityAction,
        old_data: Record<string, any>,
        new_data: Record<string, any>,
        provinceId: string,
        employee_id: number,
    ): Promise<InsertResult> {
        try {
            const isDiff = !isEqual(omit(old_data, 'updated_at'), omit(new_data, 'updated_at'));
            if (isDiff) {
                const id =
                    new_data?.id ||
                    old_data?.id ||
                    new_data?.promotion_id ||
                    old_data?.promotion_id ||
                    old_data?.driver_id ||
                    new_data?.driver_id;

                const employee = await this.employeeService.getEmployeeById(employee_id);

                return await DatabaseService.getRepositoryByProvinceId(AdminUserActivity, provinceId).insert({
                    type,
                    action,
                    user_id: employee_id,
                    old_data,
                    new_data,
                    effected_id: id,
                    user: employee,
                });
            }
        } catch (error) {
            this.logger.error(`[createEmployeeActivity] | message: ${error.message} | stack: ${error.stack}`);
        }
    }

    async getList(
        { limit, page, orderBy, sortedBy, type, user_id, action, effected_id }: GetUserActivityDto,
        provinceId: string,
    ): Promise<IPagedResult<AdminUserActivity>> {
        const qb = DatabaseService.getRepositoryByProvinceId(AdminUserActivity, provinceId).createQueryBuilder(
            'activity',
        );
        if (type) {
            qb.andWhere('activity.type = :type', { type });
        }
        if (user_id) {
            qb.andWhere('activity.user_id = :user_id', { user_id });
        }
        if (action) {
            qb.andWhere('activity.action = :action', { action });
        }
        if (effected_id) {
            qb.andWhere('activity.effected_id = :effected_id', { effected_id });
        }
        const [items, total] = await qb
            .offset(page * limit)
            .limit(limit)
            .orderBy({ [`activity.id`]: 'DESC' })
            .getManyAndCount();

        const result = await this.mapEmployeesToActivities(items, provinceId);

        return {
            items: result,
            total,
        };
    }

    private async mapEmployeesToActivities(
        activities: AdminUserActivity[],
        provinceId: string,
    ): Promise<AdminUserActivity[]> {
        if (!activities || !activities.length) {
            return activities;
        }

        //uniqueEmployeeIds
        const employeeIds =
            activities
                .filter((item) => !!item.user_id)
                .map((item) => item.user_id)
                .filter((item, index, self) => self.indexOf(item) === index) || [];
        const employees = employeeIds.length ? await this.employeeService.getEmployeesByIds(employeeIds) : [];

        activities.map((item) => {
            if (item.user_id && !item.user) {
                const employee = employees.find((employee) => employee.id === item.user_id);
                item.user = employee;
            }

            return item;
        });

        return activities;
    }

    async getDetails(id: number, provinceId: string): Promise<AdminUserActivity> {
        const activity = await DatabaseService.getRepositoryByProvinceId(AdminUserActivity, provinceId).findOne({
            where: { id },
        });

        if (!activity) {
            return null;
        }

        if (activity.user_id) {
            activity.user = await this.employeeService.getEmployeeById(activity.user_id);
        }

        return activity;
    }
}
