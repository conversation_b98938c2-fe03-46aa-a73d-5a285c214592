import { Body, Controller, Inject, Post, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { DELIVERY_SERVICE } from 'src/providers/microservices/deliveryServiceProxy/deliveryService.constant';

@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
@Controller('delivery-invoices')
export class DeliveryInvoiceController {
    constructor(@Inject(DELIVERY_SERVICE) private readonly clientProxy: ClientProxy) {}

    @Post('list/get')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_LIST)
    getDeliveryInvoiceList(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.get-paged-list' }, body);
    }

    @Post('detail/get-by-id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_ONE)
    getOne(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.get-by-id' }, body);
    }

    @Post('detail/get-by-ref-id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_ONE)
    create(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.get-by-ref-id' }, body);
    }

    @Post('retry')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_UPDATE)
    getInvoiceByOrderId(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.retry-publish' }, body);
    }

    @Post('sync')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_UPDATE)
    syncInvoice(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.sync' }, body);
    }

    @Post('download')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_INVOICE_FIND_ONE)
    downloadInvoice(@Body() body: Record<string, any>) {
        return this.clientProxy.send({ cmd: 'invoice.download' }, body);
    }
}
