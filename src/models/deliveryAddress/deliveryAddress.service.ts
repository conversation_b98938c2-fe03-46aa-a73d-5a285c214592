import { GetDeliveryAddressListDto, UpdateDeliveryAddressDto } from './deliveryAddress.dto';
import { Inject, Injectable, NotFoundException, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { DeliveryAddress } from 'src/entities/deliveryAddress.entity';
import { DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { In, Repository } from 'typeorm';
import { BARIA_DELIVERY_ADDRESS_REPOSITORY, GLOBAL_DELIVERY_ADDRESS_REPOSITORY } from './constant';
import { DeliveryAddressLabelService } from '../delivery-address-label/delivery-address-label.service';

@Injectable()
export class DeliveryAddressService {
    constructor(
        @Inject(BARIA_DELIVERY_ADDRESS_REPOSITORY)
        private bariaDeliveryAddressRepository: Repository<DeliveryAddress>,
        @Inject(GLOBAL_DELIVERY_ADDRESS_REPOSITORY)
        private globalDeliveryAddressRepository: Repository<DeliveryAddress>,
        private readonly deliveryAddressLabelService: DeliveryAddressLabelService,
    ) {}

    private getRepositoryByProvinceId(provinceId: string) {
        return +provinceId === VUNGTAU_PROVINCE_ID
            ? this.bariaDeliveryAddressRepository
            : this.globalDeliveryAddressRepository;
    }

    public async list(
        { sortedBy, orderBy, limit, page, name, phoneNumber }: GetDeliveryAddressListDto,
        provinceId: string,
    ) {
        const builder = this.getRepositoryByProvinceId(provinceId).createQueryBuilder('deliveryAddress');
        builder.innerJoinAndSelect('deliveryAddress.user', 'user');
        if (name) builder.where(`user.name LIKE '%${decodeURIComponent(name)}%'`);
        if (phoneNumber) {
            builder.andWhere(`user.phone LIKE '%${phoneNumber}%'`);
        }
        const res = await builder
            .orderBy({ [`deliveryAddress.id`]: sortedBy })
            .offset(page * limit)
            .limit(limit)
            .getManyAndCount();

        const resIds = res[0].map((item) => item.id);
        if (!resIds.length) return [[], 0];

        const deliveryAddresses = await this.getRepositoryByProvinceId(provinceId).find({
            where: { id: In(resIds) },
            relations: ['user'],
            order: { id: 'DESC' },
        });

        const codes = deliveryAddresses.map((item) => item.label_code);
        const labels = await this.deliveryAddressLabelService.getDeliveryAddressLabelByCodes(codes);

        const result = deliveryAddresses.map((item) => ({
            ...item,
            label: labels.find((label) => label.code === item.label_code) || null,
        }));

        return [result, res[1]];
    }

    public async getById(id: number, provinceId: string) {
        const item = await this.getRepositoryByProvinceId(provinceId).findOne({ where: { id }, relations: ['user'] });
        if (!item) throw new NotFoundException();

        const label = item.label_code
            ? await this.deliveryAddressLabelService.getDeliveryAddressLabelByCode(item.label_code)
            : null;
        return { ...item, label };
    }

    public async delete(id: number, provinceId: string): Promise<any> {
        const item = await this.getRepositoryByProvinceId(provinceId).findOne({ where: { id } });
        if (!item) throw new NotFoundException();

        return await this.getRepositoryByProvinceId(provinceId).delete({ id: item.id });
    }

    public async update(
        {
            id,
            address,
            description,
            is_default,
            latitude,
            longitude,
            label_code,
            address_details,
            driver_note,
        }: UpdateDeliveryAddressDto,
        provinceId: string,
    ) {
        const labelExist = await this.deliveryAddressLabelService.checkDeliveryAddressLabelExist(label_code);
        if (!labelExist) throw new NotFoundException('Nhãn không tồn tại');

        return await this.getRepositoryByProvinceId(provinceId)
            .createQueryBuilder()
            .update()
            .set({ address, description, is_default, latitude, longitude, address_details, driver_note })
            .where('id=:id', { id })
            .execute();
    }
}
