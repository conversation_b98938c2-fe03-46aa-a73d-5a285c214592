import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DeliveryAddressController } from './deliveryAddress.controller';
import { DeliveryAddressService } from './deliveryAddress.service';
import { DeliveryAddress } from 'src/entities/deliveryAddress.entity';
import { DEFAULT_PROVINCE_ID, DatabaseService, VUNGTAU_PROVINCE_ID } from 'src/providers/database/database.service';
import { GLOBAL_DELIVERY_ADDRESS_REPOSITORY, BARIA_DELIVERY_ADDRESS_REPOSITORY } from './constant';
import { DeliveryAddressLabelService } from '../delivery-address-label/delivery-address-label.service';

@Module({
    controllers: [DeliveryAddressController],
    providers: [
        DeliveryAddressService,
        DeliveryAddressLabelService,
        {
            provide: GLOBAL_DELIVERY_ADDRESS_REPOSITORY,
            useFactory: () => DatabaseService.getRepositoryByProvinceId(DeliveryAddress, DEFAULT_PROVINCE_ID),
        },
        {
            provide: BARIA_DELIVERY_ADDRESS_REPOSITORY,
            useFactory: () => DatabaseService.getRepositoryByProvinceId(DeliveryAddress, VUNGTAU_PROVINCE_ID),
        },
    ],
})
export class DeliveryAddressModule {}
