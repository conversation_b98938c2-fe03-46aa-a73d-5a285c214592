import { Transform, Type } from 'class-transformer';
import {
    IsEnum,
    IsIn,
    IsLatitude,
    IsLongitude,
    IsNotEmpty,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import * as _ from 'lodash';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';

export class UpdateOrderStatusDto {
    @IsString()
    @IsNotEmpty()
    status: string;
}

export class GetDeliveryAddressListDto extends BaseQueryFilterDto {
    @IsOptional()
    @IsString()
    phoneNumber: string;
}

export class UpdateDeliveryAddressDto {
    @IsNotEmpty()
    @Transform((val) => _.toNumber(val))
    @IsNumber()
    id: number;

    @IsNotEmpty()
    @IsString()
    description: string;

    @IsNotEmpty()
    @IsString()
    address: string;

    @IsNotEmpty()
    @IsLatitude()
    latitude: string;

    @IsNotEmpty()
    @IsLongitude()
    longitude: string;

    @IsNotEmpty()
    @IsIn([0, 1])
    is_default: 0 | 1;

    @IsNotEmpty()
    @Transform((val) => _.toNumber(val))
    @IsNumber()
    user_id: number;

    @IsNotEmpty()
    @Type(() => Number)
    label_code: number;

    @IsOptional()
    @IsString()
    address_details: string;

    @IsOptional()
    @IsString()
    driver_note: string;
}
