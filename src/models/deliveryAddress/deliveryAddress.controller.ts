import { Body, Controller, Delete, Get, NotFoundException, Param, ParseIntPipe, Post, Query } from '@nestjs/common';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { GetDeliveryAddressListDto, UpdateDeliveryAddressDto } from './deliveryAddress.dto';
import { DeliveryAddressService } from './deliveryAddress.service';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

import { UseInterceptors } from '@nestjs/common';


import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';


@UseInterceptors(LoggingInterceptor)
@Controller('delivery-addresses')
export class DeliveryAddressController {
    constructor(private readonly deliveryAddressService: DeliveryAddressService) {}

    @Get()
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_FIND_LIST)
    async list(@Query(new HttpValidationPipe()) query: GetDeliveryAddressListDto, @HeaderProvince() provinceId: string) {
        const result = await this.deliveryAddressService.list(query, provinceId);
        return {
            items: result[0],
            total: result[1],
        };
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_FIND_ONE)
    async getById(@Param('id', new ParseIntPipe()) id: number, @HeaderProvince() provinceId: string) {
        const result = await this.deliveryAddressService.getById(id, provinceId);
        if (!result) throw new NotFoundException('Review not found');
        return result;
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_REMOVE)
    async deleteById(@Param('id', new ParseIntPipe()) id: number, @HeaderProvince() provinceId: string) {
        return await this.deliveryAddressService.delete(id, provinceId);
    }

    @Post('update')
    @RequirePermissions(PermissionsAccessAction.DELIVERY_ADDRESS_UPDATE)
    update(@Body('params') params: UpdateDeliveryAddressDto, @HeaderProvince() provinceId: string) {
        return this.deliveryAddressService.update(params, provinceId);
    }
}
