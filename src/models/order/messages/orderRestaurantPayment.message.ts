export class OrderRestaurantPaymentDto {
    province_id: string;
    order_id: number;
}

export class UpdateOrderRestaurantPaymentDto {
    order_id: number;
    province_id: string;
    balance_transaction_id: string;
    status: EOrderTransactionStatus;
    order_code: string;
    restaurant_id: number;
    type: EOrderTransactionType;
    reverse_id: string;
}

export enum EOrderTransactionType {
    order = 'order',
    order_reversals = 'order_reversals',
}

export enum EOrderTransactionStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
}
