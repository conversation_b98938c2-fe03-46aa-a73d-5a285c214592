import { Injectable, Logger } from '@nestjs/common';
import { RabbitPayload, RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { RABBIT_MQ_EXCHANGE } from 'src/rabbitMQ/rabbitMQ.constant';
import { OrderRestaurantPaymentDto, UpdateOrderRestaurantPaymentDto } from '../messages/orderRestaurantPayment.message';
import { OrderService } from '../services/order.service';
import { OrderRestaurantPaymentService } from '../services/orderRestaurantPayment.service';
import { RabbitMQValidation } from 'src/common/pipes/rabbitMQValidation.pipe';
import { rabbitMQErrorHandler } from 'src/common/utils/rabbitMQErrorHandler.util';

const { ORDER_RESTAURANT_PAYMENT, MERCHANT_TRANSACTION } = RABBIT_MQ_EXCHANGE;

@Injectable()
export class OrderRestaurantPaymentSubscriber {
    private readonly logger = new Logger(OrderRestaurantPaymentSubscriber.name);
    constructor(private readonly orderService: OrderService, private readonly orderRestaurantPaymentService: OrderRestaurantPaymentService) {}

    // @RabbitSubscribe({
    //     exchange: ORDER_RESTAURANT_PAYMENT.NAME,
    //     routingKey: ORDER_RESTAURANT_PAYMENT.routingKeys.create,
    //     queue: 'admin-api-service/restaurant-order-payment-created',
    //     createQueueIfNotExists: true,
    //     errorHandler: rabbitMQErrorHandler,
    // })
    // public async CreateOrderRestaurantPaymentHandler(@RabbitPayload(new RabbitMQValidation())  payload: OrderRestaurantPaymentDto) {
    //     try {
    //         this.logger.log(`[CreateOrderRestaurantPaymentHandler] payload: ${JSON.stringify(payload)}`);
    //         if (!payload) {
    //             this.logger.warn('Invalid payload received for CreateOrderRestaurantPaymentHandler');
    //             return;
    //         }
    //         if (!payload || !payload.order_id || !payload.province_id) {
    //             this.logger.warn('Invalid payload received for CreateOrderRestaurantPaymentHandler');
    //             return;
    //         }
    //         await this.orderService.updateRestaurantOrderPayment(payload.order_id, payload.province_id)
    //     } catch (error) {
    //         this.logger.error(`[CreateOrderRestaurantPaymentHandler] error: ${error.message} | stack: ${error.stack}`);
    //     }
    // }

    // @RabbitSubscribe({
    //     exchange: ORDER_RESTAURANT_PAYMENT.NAME,
    //     routingKey: ORDER_RESTAURANT_PAYMENT.routingKeys.update,
    //     queue: 'admin-api-service/vill-wallet.updated',
    //     createQueueIfNotExists: true,
    //     errorHandler: rabbitMQErrorHandler,
    // })
    // public async UpdateOrderRestaurantPaymentHandler(@RabbitPayload(new RabbitMQValidation())  payload: UpdateOrderRestaurantPaymentDto) {
    //     try {
    //         if (!payload) {
    //             this.logger.warn('Invalid payload received for UpdateOrderRestaurantPaymentHandler');
    //             return;
    //         }
    //         const { order_id, province_id, } = payload;
    //         if (!order_id || !province_id) {
    //             this.logger.warn('Invalid payload received for UpdateOrderRestaurantPaymentHandler');
    //             return;
    //         }
    //         const order = await this.orderService.getOrderById(order_id,['status', 'restaurant'], province_id);
    //         if (!order) {
    //             this.logger.warn(`Order with id ${order_id} not found`);
    //             return;
    //         }
    //         // console.log('UpdateRestaurantOrderPaymentHandler payload: ', payload);
    //         this.logger.log(`[UpdateOrderRestaurantPaymentHandler] payload: ${JSON.stringify(payload)}`);
    //         await this.orderRestaurantPaymentService.handlePaymentWithVillWallet(order, province_id, payload);
    //     } catch (error) {
    //         this.logger.error(`[UpdateOrderRestaurantPaymentHandler] error: ${error.message} | stack: ${error.stack}`);
    //     }
    // }
}