import { Body, Controller, Post, UseGuards, UseInterceptors } from '@nestjs/common';

import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { OrderDeliveryMethod } from 'src/entities/orderDeliveryMethod.entity';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { GetOrderDriverExpenseListDto } from '../dto';
import { OrderDeliveryMethodService } from '../services/orderDeliveryMethod.service';

@UseInterceptors(LoggingInterceptor)
@Controller('order-delivery-methods')
@UseGuards(AuthGuard)
export class OrderDeliveryMethodController {
    constructor(private readonly orderDeliveryMethodService: OrderDeliveryMethodService) {}

    @Post('list/get-all')
    @RequirePermissions(PermissionsAccessAction.ORDER_DRIVER_EXPENSE_FIND_LIST)
    async getList(
        @Body(new HttpValidationPipe()) query: GetOrderDriverExpenseListDto,
        @HeaderProvince() provinceId: string,
    ): Promise<OrderDeliveryMethod[]> {
        return await this.orderDeliveryMethodService.getAllList(provinceId);
    }
}
