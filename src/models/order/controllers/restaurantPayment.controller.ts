import { Query, Controller, Get, UseGuards, UseInterceptors, Post, Body } from '@nestjs/common';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { OrderRestaurantPaymentService } from '../services/orderRestaurantPayment.service';
import { GetOrderRestaurantPaymentListDto, GetOrderRestaurantPaymentSummaryDto } from '../dto/orderRestaurantPayment.dto';

@UseInterceptors(LoggingInterceptor)
@Controller('restaurant-order-payments')
@UseGuards(AuthGuard)
export class OrderRestaurantPaymentController {
    constructor(private readonly orderRestaurantPaymentService: OrderRestaurantPaymentService) {}

    @Get()
    async getList(
        @Query(new HttpValidationPipe()) query: GetOrderRestaurantPaymentListDto,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.orderRestaurantPaymentService.getList(query, provinceId);
    }

    @Post('export/get-list')
    // @RequirePermissions(PermissionsAccessAction.RESTAURANT_ORDER_PAYMENT_EXPORT)
    async exportOrderDriverExpenseList(
        @HeaderProvince() provinceId: string,
        @Body(new HttpValidationPipe()) exportDto: GetOrderRestaurantPaymentListDto,
    ) {
        return await this.orderRestaurantPaymentService.exportGetList(exportDto, provinceId);
    }

    @Post('summary')
    async getPaymentSummary(
        @HeaderProvince() provinceId: string,
        @Body() query: GetOrderRestaurantPaymentSummaryDto,
    ) {
        const { fromDate, toDate } = query;
        return await this.orderRestaurantPaymentService.paymentSummary(fromDate, toDate, provinceId);
    }

}
