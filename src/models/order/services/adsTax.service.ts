import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from 'src/providers/database/database.service';
import { AdsTax } from 'src/entities/adsTax.entity';

export interface IAdsTaxCalculation {
    total_item: number;
    vat_rate: number;
    total_order: number;
    vat_amount: number;
}

@Injectable()
export class AdsTaxService {
    private readonly logger = new Logger(AdsTaxService.name);

    /**
     * Tính toán thuế cho ads campaign item
     * Input: totalItem theo đơn vị VND thực tế (từ ads_campaign_item.price)
     * Output: Tất cả giá trị theo đơn vị VND thực tế (để lưu vào ads_tax)
     * Công thức: total_order + vat_amount = total_item
     */
    calculateAdsTax(totalItemVND: number, vatRate: number): IAdsTaxCalculation {
        // Tính thành tiền chưa thuế: total_order = total_item / (1 + vat_rate/100)
        const totalOrderVND = Math.round(totalItemVND / (1 + vatRate / 100));

        // Tính tiền thuế: vat_amount = total_item - total_order
        const vatAmountVND = totalItemVND - totalOrderVND;

        return {
            total_item: totalItemVND,     // VND thực tế
            vat_rate: vatRate,
            total_order: totalOrderVND,   // VND thực tế
            vat_amount: vatAmountVND,     // VND thực tế
        };
    }

    /**
     * Tạo hoặc cập nhật record ads tax cho campaign item
     */
    async createOrUpdateAdsTax(
        provinceId: string,
        adsCampaignItemId: number,
        totalItem: number,
        vatRate: number,
    ): Promise<AdsTax> {
        const taxCalculation = this.calculateAdsTax(totalItem, vatRate);

        // Tìm record hiện tại
        const existingAdsTax = await this.getAdsTaxByItemId(provinceId, adsCampaignItemId);

        if (existingAdsTax) {
            // Cập nhật record hiện tại
            existingAdsTax.total_item = taxCalculation.total_item;
            existingAdsTax.vat_rate = taxCalculation.vat_rate;
            existingAdsTax.total_order = taxCalculation.total_order;
            existingAdsTax.vat_amount = taxCalculation.vat_amount;

            return await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId).save(existingAdsTax);
        } else {
            // Tạo record mới
            const adsTax = new AdsTax({
                ads_campaign_item_id: adsCampaignItemId,
                total_item: taxCalculation.total_item,
                vat_rate: taxCalculation.vat_rate,
                total_order: taxCalculation.total_order,
                vat_amount: taxCalculation.vat_amount,
            });

            return await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId).save(adsTax);
        }
    }

    /**
     * Lấy thông tin thuế theo ads campaign item ID
     */
    async getAdsTaxByItemId(provinceId: string, adsCampaignItemId: number): Promise<AdsTax | null> {
        return await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId).findOne({
            where: { ads_campaign_item_id: adsCampaignItemId },
        });
    }

    /**
     * Lấy tất cả thông tin thuế theo campaign ID (từ các items)
     */
    async getAdsTaxesByCampaignId(provinceId: string, adsCampaignId: number): Promise<AdsTax[]> {
        return await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId)
            .createQueryBuilder('ads_tax')
            .innerJoin('ads_campaigns_item', 'item', 'item.id = ads_tax.ads_campaign_item_id')
            .where('item.ads_campaign_id = :adsCampaignId', { adsCampaignId })
            .getMany();
    }

    /**
     * Xóa ads tax theo campaign item ID
     */
    async deleteAdsTaxByItemId(provinceId: string, adsCampaignItemId: number): Promise<boolean> {
        try {
            const result = await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId).delete({
                ads_campaign_item_id: adsCampaignItemId,
            });
            return result.affected > 0;
        } catch (error) {
            this.logger.error(`Failed to delete ads tax for item ${adsCampaignItemId}: ${error.message}`);
            return false;
        }
    }

    /**
     * Xóa tất cả ads tax theo campaign ID
     */
    async deleteAdsTaxesByCampaignId(provinceId: string, adsCampaignId: number): Promise<boolean> {
        try {
            const adsTaxes = await this.getAdsTaxesByCampaignId(provinceId, adsCampaignId);
            if (adsTaxes.length === 0) return true;

            const itemIds = adsTaxes.map(tax => tax.ads_campaign_item_id);
            const result = await DatabaseService.getRepositoryByProvinceId(AdsTax, provinceId)
                .createQueryBuilder()
                .delete()
                .where('ads_campaign_item_id IN (:...itemIds)', { itemIds })
                .execute();

            return result.affected > 0;
        } catch (error) {
            this.logger.error(`Failed to delete ads taxes for campaign ${adsCampaignId}: ${error.message}`);
            return false;
        }
    }

    /**
     * Validate tính toán thuế
     */
    validateTaxCalculation(totalItem: number, totalOrder: number, vatAmount: number): boolean {
        return Math.abs((totalOrder + vatAmount) - totalItem) <= 1; // Cho phép sai số 1 VND do làm tròn
    }
}
