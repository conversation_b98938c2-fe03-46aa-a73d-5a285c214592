import { Injectable, Logger } from '@nestjs/common';
import { Repository } from 'typeorm';

import { EOrderDeliveryMethodCode, OrderDeliveryMethod } from 'src/entities/orderDeliveryMethod.entity';
import { DatabaseService } from 'src/providers/database/database.service';

@Injectable()
export class OrderDeliveryMethodService {
    private logger = new Logger(OrderDeliveryMethodService.name);

    private getRepository(provinceId: string): Repository<OrderDeliveryMethod> {
        return DatabaseService.getRepositoryByProvinceId(OrderDeliveryMethod, provinceId);
    }

    async findOneById(id: number, provinceId: string): Promise<OrderDeliveryMethod> {
        if (!id) {
            return null;
        }

        return await this.getRepository(provinceId).createQueryBuilder().where('id = :id', { id }).getOne();
    }

    async findOneByCode(code: EOrderDeliveryMethodCode, provinceId: string): Promise<OrderDeliveryMethod> {
        if (!code) {
            return null;
        }

        return await this.getRepository(provinceId).createQueryBuilder().where('code = :code', { code }).getOne();
    }

    async getAllActiveList(provinceId: string): Promise<OrderDeliveryMethod[]> {
        return await this.getRepository(provinceId)
            .createQueryBuilder()
            .where('is_active = :isActive', { isActive: 1 })
            .getMany();
    }

    async getAllList(provinceId: string): Promise<OrderDeliveryMethod[]> {
        return await this.getRepository(provinceId).createQueryBuilder().getMany();
    }
}
