import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bull';
import { generateJobIdByProcessName } from 'src/common/helpers/job.helper';
import { JobQueue } from 'src/jobs';
import { EInvoiceJobExportOrderToMisa } from 'src/jobs/invoice/invoice.interface';

@Injectable()
export class OrderInvoiceJobService {
    private readonly logger = new Logger(OrderInvoiceJobService.name);

    constructor(
        @InjectQueue(JobQueue.INVOICE_QUEUE.PROCESSOR)
        private readonly invoiceQueue: Queue,
    ) {}

    async queueOrderInvoiceExport(orderId: number, provinceId: string): Promise<void> {
        try {
            const jobId = generateJobIdByProcessName(
                JobQueue.INVOICE_QUEUE.PROCESS.EXPORT_ORDER_USER_INVOICE_TO_MISA,
                orderId.toString(),
                provinceId,
            );

            const existingJob = await this.invoiceQueue.getJob(jobId);
            if (existingJob) {
                const jobStatus = await existingJob.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await existingJob.remove();
                } else {
                    this.logger.warn(
                        `Invoice export job already exists for order ${orderId} with status: ${jobStatus}`,
                    );
                    return;
                }
            }

            const jobData: EInvoiceJobExportOrderToMisa = {
                order_id: orderId,
                provinceId,
            };

            await this.invoiceQueue.add(JobQueue.INVOICE_QUEUE.PROCESS.EXPORT_ORDER_USER_INVOICE_TO_MISA, jobData, {
                jobId,
                removeOnComplete: 100,
                attempts: 3,
                backoff: 10000,
            });

            this.logger.log(`Successfully queued invoice export job for order ${orderId} in province ${provinceId}`);
        } catch (error) {
            this.logger.error(`Failed to queue invoice export job for order ${orderId}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async queueOrderCompanyInvoiceExport(orderId: number, provinceId: string): Promise<void> {
        try {
            const jobId = generateJobIdByProcessName(
                JobQueue.INVOICE_QUEUE.PROCESS.EXPORT_ORDER_COMPANY_INVOICE_TO_MISA,
                orderId.toString(),
                provinceId,
            );

            const existingJob = await this.invoiceQueue.getJob(jobId);
            if (existingJob) {
                const jobStatus = await existingJob.getState();
                if (['completed', 'failed', 'stuck'].includes(jobStatus)) {
                    await existingJob.remove();
                } else {
                    this.logger.warn(
                        `Company invoice export job already exists for order ${orderId} with status: ${jobStatus}`,
                    );
                    return;
                }
            }

            const jobData: EInvoiceJobExportOrderToMisa = {
                order_id: orderId,
                provinceId,
            };

            await this.invoiceQueue.add(JobQueue.INVOICE_QUEUE.PROCESS.EXPORT_ORDER_COMPANY_INVOICE_TO_MISA, jobData, {
                jobId,
                removeOnComplete: 100,
                attempts: 3,
                backoff: 10000,
            });

            this.logger.log(
                `Successfully queued company invoice export job for order ${orderId} in province ${provinceId}`,
            );
        } catch (error) {
            this.logger.error(
                `Failed to queue company invoice export job for order ${orderId}: ${error.message}`,
                error.stack,
            );
            throw error;
        }
    }
}
