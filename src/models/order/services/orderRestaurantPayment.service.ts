import { isNil, sum, isEmpty } from 'lodash';
import { Logger, Injectable, InternalServerErrorException } from '@nestjs/common';
import { Order } from 'src/entities/order.entity';
import {
    EOrderRestaurantPaymentStatus,
    EOrderRestaurantPaymentType,
    OrderRestaurantPayment,
} from 'src/entities/orderRestaurantPayments.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { CurrencyConvertService } from './currencyConvert.service';
import { RestaurantTradeDiscountPeriodType } from 'src/entities/restaurant.entity';
import { EOrderStatusId, OrderStatus } from 'src/entities/orderStatus.entity';
import { ETransactionType } from 'src/models/merchantNPay/enums';
import { Merchant9PayCommandService } from 'src/providers/microservices/deliveryServiceProxy/service/merchantNPay.service';
import { INPayAccountLinking } from 'src/providers/microservices/deliveryServiceProxy/interfaces';
import { MerchantNPayTransactionService } from 'src/models/merchantNPay/services/merchantNPayTransaction.service';
import {
    ENPayBusinessToWalletTransactionStatus,
    ENPayWalletToBusinessWalletStatus,
    INPayBusinessToWalletTransaction,
    INPayResponse,
    INPayWalletToBusinessTransaction,
} from 'src/providers/nPay/interfaces';
import { NPayTransactionTransferDataService } from 'src/models/merchantNPay/services/nPayTransactionTransferData.service';
import { NPayTransactionIPNDto } from 'src/models/merchantNPay/dto/nPayTransactionIPN.dto';
import { IParsedAppTransIdResult } from 'src/models/merchantNPay/types/parsedAppTransIdResult.interface';
import { ENPayTransactionIPNStatus } from 'src/providers/nPay/interfaces/nPayTransactionIPN.interface';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { GetOrderRestaurantPaymentListDto } from '../dto/orderRestaurantPayment.dto';

import * as moment from 'moment';
import {
    EOrderTransactionStatus,
    EOrderTransactionType,
    UpdateOrderRestaurantPaymentDto,
} from '../messages/orderRestaurantPayment.message';
import { IOrderRestaurantPaymentSummary } from '../interfaces';

@Injectable()
export class OrderRestaurantPaymentService {
    private readonly logger = new Logger(OrderRestaurantPaymentService.name);
    constructor(
        private readonly currencyConvertService: CurrencyConvertService,
        private readonly merchant9PayCommandService: Merchant9PayCommandService,
        private readonly merchantNPayTransactionService: MerchantNPayTransactionService,
        private readonly nPayTransactionTransferDataService: NPayTransactionTransferDataService,
    ) {}

    private mapNPayIPNStatusToOrderRestaurantPaymentStatus(
        status: ENPayTransactionIPNStatus,
    ): EOrderRestaurantPaymentStatus {
        switch (status) {
            case ENPayTransactionIPNStatus.INITIALIZED:
            case ENPayTransactionIPNStatus.PROCESSING:
                return EOrderRestaurantPaymentStatus.PROCESSING;
            case ENPayTransactionIPNStatus.SUCCESS:
                return EOrderRestaurantPaymentStatus.SUCCESS;
            case ENPayTransactionIPNStatus.FAILURE:
                return EOrderRestaurantPaymentStatus.FAILED;
            case ENPayTransactionIPNStatus.CANCEL:
                return EOrderRestaurantPaymentStatus.FAILED;
            default:
                return EOrderRestaurantPaymentStatus.PENDING;
        }
    }

    async updateStatusOrReversalStatusByIPN(
        nPayTransaction: NPayTransactionIPNDto,
        parsedAppTransIdResult: IParsedAppTransIdResult,
    ) {
        const { provinceId, sourceId, transactionType } = parsedAppTransIdResult;
        if (!provinceId || !sourceId || !transactionType) {
            this.logger.error('Invalid parsedAppTransIdResult for updating status');
            throw new Error('Invalid parsedAppTransIdResult for updating status');
        }
        const orderId = Number(sourceId);
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment) {
            this.logger.warn(`No orderRestaurantPayment found for order ID ${sourceId} in province ${provinceId}`);
            return;
        }
        const finalStatus = this.mapNPayIPNStatusToOrderRestaurantPaymentStatus(nPayTransaction.status);

        if (isNil(finalStatus)) {
            this.logger.warn(`Final status is nil for order ID ${sourceId} in province ${provinceId}, skipping update`);
            return;
        }
        const updateValues: QueryDeepPartialEntity<OrderRestaurantPayment> = {};
        if (transactionType === ETransactionType.ORDER) {
            updateValues.status = finalStatus;
        } else if (transactionType === ETransactionType.ORDER_REVERSAL) {
            updateValues.reversal_status = finalStatus;
        }
        if (isEmpty(updateValues)) {
            this.logger.warn(
                `No valid update values for order ID ${sourceId} in province ${provinceId}, skipping update`,
            );
            return;
        }
        const updateResult = await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
            .createQueryBuilder()
            .update()
            .set(updateValues)
            .where('order_id = :orderId', { orderId })
            .execute();
        this.logger.log(
            `[updateStatusByIPN] [${provinceId}] ${JSON.stringify({
                updateResult,
                orderId,
                nPayTransaction,
            })}`,
        );
    }

    async getOneByOrderId(orderId: number, provinceId: string): Promise<OrderRestaurantPayment | null> {
        if (!orderId) {
            return null;
        }
        return await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
            .createQueryBuilder('orderRestaurantPayment')
            .leftJoinAndSelect('orderRestaurantPayment.order', 'order')
            .leftJoinAndSelect('orderRestaurantPayment.restaurant', 'restaurant')
            .where('orderRestaurantPayment.order_id = :orderId', { orderId })
            .getOne();
    }

    mapTradeDiscountTypeToPaymentMethod(
        tradeDiscountPeriodType: RestaurantTradeDiscountPeriodType,
    ): EOrderRestaurantPaymentType {
        switch (tradeDiscountPeriodType) {
            case RestaurantTradeDiscountPeriodType.DIRECT:
            case RestaurantTradeDiscountPeriodType.MONTH:
            case RestaurantTradeDiscountPeriodType.WEEK:
                return EOrderRestaurantPaymentType.VIA_DRIVER;
            case RestaurantTradeDiscountPeriodType.DIRECT_WALLET:
                return EOrderRestaurantPaymentType.VILL_WALLET;
            case RestaurantTradeDiscountPeriodType.NPAY_WALLET:
                return EOrderRestaurantPaymentType.NPAY_WALLET;
            default:
                return null;
        }
    }

    private mapNPayBusinessToWalletTransactionStatusToOrderExpenseStatus(
        status: ENPayBusinessToWalletTransactionStatus,
    ): EOrderRestaurantPaymentStatus {
        switch (status) {
            case ENPayBusinessToWalletTransactionStatus.SUCCESS:
                return EOrderRestaurantPaymentStatus.SUCCESS;
            case ENPayBusinessToWalletTransactionStatus.FAIL:
                return EOrderRestaurantPaymentStatus.FAILED;
            case ENPayBusinessToWalletTransactionStatus.PENDING:
                return EOrderRestaurantPaymentStatus.PENDING;
            case ENPayBusinessToWalletTransactionStatus.PROCESSING:
                return EOrderRestaurantPaymentStatus.PROCESSING;
            default:
                return EOrderRestaurantPaymentStatus.PENDING;
        }
    }

    private mapNPayWalletToBusinessTransactionStatusToOrderExpenseStatus(
        status: ENPayWalletToBusinessWalletStatus,
    ): EOrderRestaurantPaymentStatus {
        switch (status) {
            case ENPayWalletToBusinessWalletStatus.INITIATED:
            case ENPayWalletToBusinessWalletStatus.PROCESSING:
                return EOrderRestaurantPaymentStatus.PROCESSING;
            case ENPayWalletToBusinessWalletStatus.SUCCESS:
                return EOrderRestaurantPaymentStatus.SUCCESS;
            case ENPayWalletToBusinessWalletStatus.FAIL:
            case ENPayWalletToBusinessWalletStatus.CANCELLED:
                return EOrderRestaurantPaymentStatus.FAILED;
            default:
                return EOrderRestaurantPaymentStatus.PENDING;
        }
    }

    buildOrderRestaurantPayment(order: Order): OrderRestaurantPayment {
        const amount = sum([order.trade_discount, order.restaurant_vat, order.restaurant_personal_income_tax]);
        // console.log('buildOrderRestaurantPayment', amount);
        const orderRestaurantPayment = new OrderRestaurantPayment();
        orderRestaurantPayment.amount = Math.round(this.currencyConvertService.kDongToVND(amount));
        orderRestaurantPayment.vat = this.currencyConvertService.kDongToVND(order.restaurant_vat);
        orderRestaurantPayment.trade_discount = this.currencyConvertService.kDongToVND(order.trade_discount);
        orderRestaurantPayment.personal_income_tax = this.currencyConvertService.kDongToVND(
            order.restaurant_personal_income_tax,
        );
        orderRestaurantPayment.payment_method = this.mapTradeDiscountTypeToPaymentMethod(
            order.restaurant_trade_discount_period_type,
        );
        orderRestaurantPayment.order_id = order.id;
        orderRestaurantPayment.restaurant_id = order.restaurant_id;
        if (
            orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.VIA_DRIVER &&
            order.order_status_id === EOrderStatusId.ARRIVED
        ) {
            orderRestaurantPayment.status = EOrderRestaurantPaymentStatus.SUCCESS;
        } else if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.NPAY_WALLET) {
            orderRestaurantPayment.status = EOrderRestaurantPaymentStatus.FAILED;
        } else {
            orderRestaurantPayment.status = EOrderRestaurantPaymentStatus.PENDING;
        }

        orderRestaurantPayment.extra_data = {
            restaurant_trade_discount_period_type: order.restaurant_trade_discount_period_type,
        };
        // orderRestaurantPayment.note = order.note;
        orderRestaurantPayment.payment_log = {
            transaction_info: null,
            reversal_info: null,
        };

        return orderRestaurantPayment;
    }

    async updateNote(
        message: string,
        orderId: number,
        provinceId: string
    ): Promise<void> {
        if (!message) {
            this.logger.error(
                'Message, orderId, and provinceId are required to note error'
            );
            return;
        }
        if (!orderId) {
            this.logger.warn(`No orderId found`);
            return;
        }
        const updateData: QueryDeepPartialEntity<OrderRestaurantPayment> = {
            note: message,
        };
        await DatabaseService.getRepositoryByProvinceId(
            OrderRestaurantPayment,
            provinceId
        )
            .createQueryBuilder('orderRestaurantPayment')
            .update()
            .set(updateData)
            .where('orderRestaurantPayment.order_id = :orderId', { orderId })
            .execute();
    }

    async handlePaymentByBusinessToWalletResponse(
        orderId: number,
        provinceId: string,
        response: INPayResponse<INPayBusinessToWalletTransaction>,
    ) {
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment) {
            this.logger.warn(`No orderRestaurantPayment found for order ID ${orderId} in province ${provinceId}`);
            throw new Error(`No orderRestaurantPayment found for order ID ${orderId} in province ${provinceId}`);
        }

        if (response.success === true) {
            const { status, code } = response.data;
            if (!code) {
                this.logger.error(
                    `No refReversalTransId found in response data for order ID ${orderId} in province ${provinceId}`,
                );
                await this.updateNote(
                    'Không tìm thấy refReversalTransId trong dữ liệu phản hồi từ 9Pay',
                    orderId,
                    provinceId
                );
                throw new Error(
                    `No refReversalTransId found in response data for order ID ${orderId} in province ${provinceId}`,
                );
            }

            orderRestaurantPayment.reversal_status =
                this.mapNPayBusinessToWalletTransactionStatusToOrderExpenseStatus(status);
            orderRestaurantPayment.ref_reversal_trans_id = code;
            orderRestaurantPayment.payment_log.reversal_info = response.data;
            // orderRestaurantPayment.npay_account_info = accountInfo;
            orderRestaurantPayment.reversal_at = new Date();
            orderRestaurantPayment.note = '';
        } else {
            orderRestaurantPayment.reversal_at = new Date();
            orderRestaurantPayment.payment_log.reversal_info = response;
            orderRestaurantPayment.reversal_status = EOrderRestaurantPaymentStatus.FAILED;
            orderRestaurantPayment.note = response.message || 'Lỗi khi hoàn tiền';
        }
        await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    async handlePaymentByWalletToBusinessResponse(
        orderId: number,
        provinceId: string,
        response: INPayResponse<INPayWalletToBusinessTransaction>,
        accountInfo: INPayAccountLinking,
    ) {
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment) {
            this.logger.warn(`No orderRestaurantPayment found for order ID ${orderId} in province ${provinceId}`);
            throw new Error(`No orderRestaurantPayment found for order ID ${orderId} in province ${provinceId}`);
        }

        if (response.success === true) {
            const { status, code } = response.data;
            if (!code) {
                this.logger.error(
                    `No refTransId found in response data for order ID ${orderId} in province ${provinceId}`,
                );
                await this.updateNote(
                    'Không tìm thấy refTransId trong dữ liệu phản hồi từ 9Pay',
                    orderId,
                    provinceId
                );
                throw new Error(
                    `No refTransId found in response data for order ID ${orderId} in province ${provinceId}`,
                );
            }
            orderRestaurantPayment.status = this.mapNPayWalletToBusinessTransactionStatusToOrderExpenseStatus(status);
            orderRestaurantPayment.ref_trans_id = code;
            orderRestaurantPayment.payment_log.transaction_info = response.data;
            orderRestaurantPayment.payment_at = new Date();
            // orderRestaurantPayment.npay_account_info = accountInfo;
            orderRestaurantPayment.note = '';
        } else {
            orderRestaurantPayment.payment_log.transaction_info = {...response, accountInfo};
            orderRestaurantPayment.status = EOrderRestaurantPaymentStatus.FAILED;
            orderRestaurantPayment.payment_at = new Date();
            orderRestaurantPayment.npay_account_info = null;
            orderRestaurantPayment.note = response.message || 'Lỗi khi thanh toán';
        }
        await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    private async generateAppTransIdIfNotExist(orderId: number, provinceId: string): Promise<OrderRestaurantPayment> {
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment) {
            return null;
        }
        if (!orderRestaurantPayment.app_trans_id) {
            orderRestaurantPayment.app_trans_id = this.nPayTransactionTransferDataService.generateAppTransId(
                provinceId,
                ETransactionType.ORDER,
                orderId.toString(),
            );
        }
        return await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    private async generateAppReversalTransIdIfNotExist(
        orderId: number,
        provinceId: string,
    ): Promise<OrderRestaurantPayment> {
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment) {
            return null;
        }
        if (!orderRestaurantPayment.app_reversal_trans_id) {
            orderRestaurantPayment.app_reversal_trans_id = this.nPayTransactionTransferDataService.generateAppTransId(
                provinceId,
                ETransactionType.ORDER_REVERSAL,
                orderId.toString(),
            );
        }
        return await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    async executeWithNPayProvider(
        orderId: number,
        provinceId: string,
        transactionType: ETransactionType,
    ): Promise<void> {
        if (!orderId || !provinceId || !transactionType) {
            this.logger.error('orderId, provinceId, and transactionType are required for NPay execution');
            throw new Error('orderId is required for NPay execution');
        }
        const orderRestaurantPayment = await this.getOneByOrderId(orderId, provinceId);
        if (!orderRestaurantPayment.npay_account_info) {
            this.logger.error(
                `Account info is not valid for payment method ${orderRestaurantPayment.payment_method} in province ${provinceId}`,
            );
            await this.updateNote(
                'Không tìm thấy thông tin liên kết 9Pay',
                orderId,
                provinceId
            );
            throw new Error('Invalid account info for NPay execution');
        }

        if (transactionType === ETransactionType.ORDER_REVERSAL) {
            if (
                [
                    EOrderRestaurantPaymentStatus.PENDING,
                    EOrderRestaurantPaymentStatus.PROCESSING,
                    EOrderRestaurantPaymentStatus.SUCCESS,
                ].includes(orderRestaurantPayment.reversal_status)
            ) {
                this.logger.error(
                    `Reversal status ${orderRestaurantPayment.reversal_status} is not supported for NPay execution in province ${provinceId}`,
                );
                throw new Error('Payment method is not supported for NPay execution');
            }
            const appReversalTransId = await this.generateAppReversalTransIdIfNotExist(
                orderRestaurantPayment.order_id,
                provinceId,
            );
            if (!appReversalTransId) {
                this.logger.error(
                    `Failed to generate appReversalTransId for order ID ${orderRestaurantPayment.order_id} in province ${provinceId}`,
                );
                await this.updateNote(
                    'Tạo thất bại appReversalTransId cho giao dịch hoàn tiền',
                    orderId,
                    provinceId
                );
                throw new Error('Failed to generate appReversalTransId for NPay execution');
            }
            const response = await this.merchantNPayTransactionService.makeBusinessToWalletTransfer({
                amount: orderRestaurantPayment.amount,
                note: `Reversal for order ID ${orderRestaurantPayment.order_id}`,
                transactionType: ETransactionType.ORDER_REVERSAL,
                provinceId,
                orderId: orderRestaurantPayment.order_id,
                accountLinking: orderRestaurantPayment.npay_account_info,
                appTransId: appReversalTransId.app_reversal_trans_id,
                sourceId: orderRestaurantPayment.order_id.toString(),
            });
            this.logger.log(
                `Executing NPay reversal transaction for order ID ${
                    orderRestaurantPayment.order_id
                } in province ${provinceId}: ${JSON.stringify(response)}`,
            );
            await this.handlePaymentByBusinessToWalletResponse(
                orderRestaurantPayment.order_id,
                provinceId,
                response,
            );
        } else if (transactionType === ETransactionType.ORDER) {
            if (
                [
                    EOrderRestaurantPaymentStatus.PENDING,
                    EOrderRestaurantPaymentStatus.PROCESSING,
                    EOrderRestaurantPaymentStatus.SUCCESS,
                ].includes(orderRestaurantPayment.status)
            ) {
                this.logger.error(
                    `Payment status ${orderRestaurantPayment.status} is not supported for NPay execution in province ${provinceId}`,
                );
                throw new Error('Payment method is not supported for NPay execution');
            }
            const appTransId = await this.generateAppTransIdIfNotExist(orderRestaurantPayment.order_id, provinceId);

            if (!appTransId) {
                this.logger.error(
                    `Failed to generate appTransId for order ID ${orderRestaurantPayment.order_id} in province ${provinceId}`,
                );
                await this.updateNote(
                    'Tạo thất bại appTransId cho giao dịch thanh toán',
                    orderId,
                    provinceId
                );
                throw new Error('Failed to generate appTransId for NPay execution');
            }
            const response = await this.merchantNPayTransactionService.makeWalletToBusinessTransaction({
                amount: orderRestaurantPayment.amount,
                note: `Payment for order ID ${orderRestaurantPayment.order_id}`,
                transactionType: ETransactionType.ORDER,
                appTransId: appTransId.app_trans_id,
                sourceId: orderRestaurantPayment.order_id.toString(),
                accountLinking: orderRestaurantPayment.npay_account_info,
                provinceId,
                orderId: orderRestaurantPayment.order_id,
            });
            this.logger.log(
                `Executing NPay transaction for order ID ${
                    orderRestaurantPayment.order_id
                } in province ${provinceId}: ${JSON.stringify(response)}`,
            );
            await this.handlePaymentByWalletToBusinessResponse(
                orderRestaurantPayment.order_id,
                provinceId,
                response,
                orderRestaurantPayment.npay_account_info,
            );
        }

        this.logger.log(
            `Executing NPay transaction for payment method ${orderRestaurantPayment.payment_method} in province ${provinceId} with type ${transactionType}`,
        );
    }

    async handleReversalViaDriver(orderRestaurantPayment: OrderRestaurantPayment, provinceId: string) {
        if (!orderRestaurantPayment || !provinceId) {
            this.logger.error('orderRestaurantPayment and provinceId are required for reversal via driver');
            throw new Error('orderRestaurantPayment is required for reversal via driver');
        }

        orderRestaurantPayment.reversal_status = EOrderRestaurantPaymentStatus.SUCCESS;
        orderRestaurantPayment.reversal_at = new Date();
        await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    async makeReversal(
        orderRestaurantPayment: OrderRestaurantPayment,
        provinceId: string,
        order: Order,
    ): Promise<void> {
        if (!orderRestaurantPayment || !provinceId || !order) {
            this.logger.error('orderRestaurantPayment, provinceId, and order are required to make a reversal');
            throw new Error('orderRestaurantPayment is required to make a reversal');
        }

        if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.NPAY_WALLET) {
            const orderRestaurantPaymentWithAccountInfo = await this.GetAndValidateAccountLinking(
                orderRestaurantPayment,
                provinceId,
                order,
            );
            if (!orderRestaurantPaymentWithAccountInfo || !orderRestaurantPaymentWithAccountInfo.npay_account_info) {
                this.logger.error(
                    `No valid NPay account linking found for restaurant ID ${order.restaurant_id} in province ${provinceId}`,
                );
                throw new Error('Không tìm thấy tài khoản liên kết NPay');
            }
            
            await this.executeWithNPayProvider(
                orderRestaurantPaymentWithAccountInfo.order_id,
                provinceId,
                ETransactionType.ORDER_REVERSAL,
            );
        } else if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.VIA_DRIVER) {
            // Handle other payment methods if needed
            await this.handleReversalViaDriver(orderRestaurantPayment, provinceId);
        } else if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.VILL_WALLET) {
            this.logger.warn(`Reversal for payment method ${orderRestaurantPayment.payment_method}`);
        }
    }

    async handleAfterOrderCancelled(order: Order, provinceId: string): Promise<void> {
        try {
            if (!order || !provinceId) {
                this.logger.error('Order and provinceId are required to handle cancellation');
                return;
            }
            const orderRestaurantPayment = await this.getOneByOrderId(order.id, provinceId);
            if (!orderRestaurantPayment) {
                this.logger.warn(`No orderRestaurantPayment found for order ID ${order.id} in province ${provinceId}`);
                return;
            }
            if (orderRestaurantPayment.status !== EOrderRestaurantPaymentStatus.SUCCESS) {
                this.logger.warn(
                    `OrderRestaurantPayment status is not SUCCESS for order ID ${order.id} in province ${provinceId}, current status: ${orderRestaurantPayment.status}`,
                );
                orderRestaurantPayment.payment_log.reversal_info = {
                    message: `Trạng thái thanh toán chưa thành công, không thể thực hiện hoàn tiền`,
                    success: false,
                };
                orderRestaurantPayment.reversal_status = EOrderRestaurantPaymentStatus.FAILED;
                orderRestaurantPayment.reversal_at = new Date();
                await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
                    orderRestaurantPayment,
                );
                return;
            }
            await this.makeReversal(orderRestaurantPayment, provinceId, order);
        } catch (error) {
            this.logger.error(`Error handling order cancellation: ${error.message} || error.stack: ${error.stack}`);
            return;
        }
    }

    async GetAndValidateAccountLinking(
        orderRestaurantPayment: OrderRestaurantPayment,
        provinceId: string,
        order: Order,
    ): Promise<OrderRestaurantPayment> {
        if (orderRestaurantPayment.npay_account_info && orderRestaurantPayment.npay_account_info.status === 'SUCCESS') {
            this.logger.log(
                `Account info already exists for payment method ${orderRestaurantPayment.payment_method} in province ${provinceId}`,
            );
            return orderRestaurantPayment;
        }
        try {
            const accountInfo = await this.merchant9PayCommandService.getDetailByRestaurantId(
                order.restaurant_id,
                Number(provinceId),
            );
            if (!accountInfo) {
                this.logger.error(
                    `No NPay account linking found for restaurant ID ${order.restaurant_id} in province ${provinceId}`,
                );
                orderRestaurantPayment.note = 'Không tìm thấy tài khoản liên kết NPay';
                await this.updateNote(
                    'Không tìm thấy tài khoản liên kết NPay',
                    order.id,
                    provinceId
                );
                return orderRestaurantPayment;
            }
            if (accountInfo.status !== 'SUCCESS') {
                this.logger.error(
                    `NPay account linking status is not successful for restaurant ID ${order.restaurant_id} in province ${provinceId}`,
                );
                orderRestaurantPayment.note = 'Tài khoản chưa liên kết NPay thành công';
                await this.updateNote(
                    'Tài khoản chưa liên kết NPay thành công',
                    order.id,
                    provinceId
                );
                return orderRestaurantPayment;
            }
            orderRestaurantPayment.npay_account_info = accountInfo;
            orderRestaurantPayment.note = '';
            return DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
                orderRestaurantPayment,
            );
            
        } catch (error) {
            this.logger.error(
                `Error getting account linking for restaurant ID ${order.restaurant_id} in province ${provinceId}: ${error.message}`,
            );
            await this.updateNote(
                'Lỗi khi lấy thông tin liên kết tài khoản NPay',
                order.id,
                provinceId
            );
            throw new Error('Error getting account linking for restaurant');
        }
    }

    // async handleAfterPaymentCreated(
    //     order: Order,
    //     provinceId: string,
    //     restaurantOrderPayment: RestaurantOrderPayment,
    // ): Promise<void> {
    //     if (!order || !provinceId) {
    //         this.logger.error('Order and provinceId are required to handle payment creation');
    //         throw new Error('Order is required to handle payment creation');
    //     }
    //     if (!restaurantOrderPayment) {
    //         this.logger.error('RestaurantOrderPayment is required to handle payment creation');
    //         throw new Error('RestaurantOrderPayment is required to handle payment creation');
    //     }
    //     if (restaurantOrderPayment.payment_method === ERestaurantOrderPaymentType.NPAY_WALLET) {
    //         // Handle NPay wallet payment creation logic here
    //         return;
    //     }
    // }

    async handleAfterOrderCompleted(order: Order, provinceId: string): Promise<void> {
        if (!order || !provinceId) {
            this.logger.error('Order and provinceId are required to handle completion');
            throw new Error('Order is required to handle completion');
        }
        try {
            const orderRestaurantPayment = await this.create(order, provinceId);
            if (!orderRestaurantPayment) {
                this.logger.error(
                    `Failed to create orderRestaurantPayment for order ID ${order.id} in province ${provinceId}`,
                );
                throw new Error('Failed to create orderRestaurantPayment');
            }
            if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.NPAY_WALLET) {
                const orderRestaurantPaymentWithAccountInfo = await this.GetAndValidateAccountLinking(
                    orderRestaurantPayment,
                    provinceId,
                    order,
                );
                if (!orderRestaurantPaymentWithAccountInfo || !orderRestaurantPaymentWithAccountInfo.npay_account_info) {
                    this.logger.error(
                        `No valid NPay account linking found for restaurant ID ${order.restaurant_id} in province ${provinceId}`,
                    );
                    throw new Error('Không tìm thấy tài khoản liên kết NPay');
                }
                // if (
                //     orderRestaurantPaymentWithAccountInfo.npay_account_info.status !== 'SUCCESS'
                // ) {
                //     this.logger.error(
                //         `NPay account linking is not valid for restaurant ID ${order.restaurant_id} in province ${provinceId}`,
                //     );
                //     throw new Error('Tài khoản NPay không hợp lệ');
                // }  
                await this.executeWithNPayProvider(orderRestaurantPayment.order_id, provinceId, ETransactionType.ORDER);
            } else if (orderRestaurantPayment.payment_method === EOrderRestaurantPaymentType.VILL_WALLET) {
                this.logger.warn(
                    `Payment method ${orderRestaurantPayment.payment_method} does not require NPay execution`,
                );
            }
        } catch (error) {
            this.logger.error(`Error handling order completion: ${error.message} || error.stack: ${error.stack}`);
        }
    }

    async create(order: Order, provinceId: string): Promise<OrderRestaurantPayment> {
        if (!order) {
            throw new Error('Order is required to create a orderRestaurantPayment');
        }
        const existingPayment = await this.getOneByOrderId(order.id, provinceId);
        if (existingPayment) {
            return existingPayment;
        }
        const orderRestaurantPayment = this.buildOrderRestaurantPayment(order);
        return await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
            orderRestaurantPayment,
        );
    }

    mapVillWalletStatusToOrderRestaurantPaymentStatus(status: EOrderTransactionStatus): EOrderRestaurantPaymentStatus {
        switch (status) {
            case EOrderTransactionStatus.PENDING:
                return EOrderRestaurantPaymentStatus.PENDING;
            case EOrderTransactionStatus.SUCCESS:
                return EOrderRestaurantPaymentStatus.SUCCESS;
            case EOrderTransactionStatus.FAILED:
                return EOrderRestaurantPaymentStatus.FAILED;
            default:
                return EOrderRestaurantPaymentStatus.PENDING;
        }
    }

    async handlePaymentWithVillWallet(
        order: Order,
        provinceId: string,
        payload: UpdateOrderRestaurantPaymentDto,
    ): Promise<OrderRestaurantPayment> {
        if (!order || !provinceId) {
            this.logger.error('Order and provinceId are required to handle payment with Vill Wallet');
            throw new Error('Order is required to handle payment with Vill Wallet');
        }
        const existingPayment = await this.getOneByOrderId(order.id, provinceId);
        if (!existingPayment) {
            this.logger.error(
                `No existing orderRestaurantPayment found for order ID ${order.id} in province ${provinceId}`,
            );
            throw new Error(
                `No existing orderRestaurantPayment found for order ID ${order.id} in province ${provinceId}`,
            );
        }

        if (existingPayment.payment_method === EOrderRestaurantPaymentType.VILL_WALLET) {
            this.logger.warn(
                `orderRestaurantPayment for order ID ${order.id} in province ${provinceId} is not pending, current status: ${existingPayment.status}`,
            );
            const { status, balance_transaction_id, type, order_id, order_code } = payload;
            if (!type) {
                await this.updateNote('Loại giao dịch là bắt buộc để cập nhật thông tin thanh toán', order.id, provinceId);
                this.logger.error('Transaction type is required to update orderRestaurantPayment');
                return;
            }
            if (type === EOrderTransactionType.order) {
                existingPayment.status = this.mapVillWalletStatusToOrderRestaurantPaymentStatus(status);
                existingPayment.payment_log.transaction_info = {
                    balance_transaction_id,
                    status,
                    type,
                    order_id,
                    order_code,
                    province_id: provinceId,
                };
                existingPayment.note = '';
                existingPayment.payment_at = new Date();
            } else if (type === EOrderTransactionType.order_reversals) {
                existingPayment.reversal_status = this.mapVillWalletStatusToOrderRestaurantPaymentStatus(status);
                existingPayment.payment_log.reversal_info = {
                    balance_transaction_id,
                    status,
                    type,
                    order_id,
                    order_code,
                    province_id: provinceId,
                };
                existingPayment.note = '';
                existingPayment.reversal_at = new Date();
            }
            await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId).save(
                existingPayment,
            );
        }
        return;
    }

    async getList(params: GetOrderRestaurantPaymentListDto, provinceId: string) {
        const {
            restaurantId,
            reversalStatus,
            status,
            limit = 20,
            page = 0,
            fromOrderDate,
            toOrderDate,
            paymentMethod,
        } = params;
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
            .createQueryBuilder('orderRestaurantPayment')
            .leftJoinAndSelect('orderRestaurantPayment.order', 'order')
            .leftJoinAndSelect('orderRestaurantPayment.restaurant', 'restaurant');

        if (restaurantId) {
            queryBuilder.andWhere('orderRestaurantPayment.restaurant_id = :restaurantId', { restaurantId });
        }
        if (reversalStatus) {
            queryBuilder.andWhere('orderRestaurantPayment.reversal_status = :reversalStatus', { reversalStatus });
        }
        if (status) {
            queryBuilder.andWhere('orderRestaurantPayment.status = :status', { status });
        }

        if (fromOrderDate && toOrderDate) {
            console.log('fromOrderDate', fromOrderDate, 'toOrderDate', toOrderDate);
            const fromDateFormatted = moment(fromOrderDate, 'YYYY-MM-DD').startOf('day').format('YYYY-MM-DD');
            const toDateFormatted = moment(toOrderDate, 'YYYY-MM-DD').endOf('day').format('YYYY-MM-DD');
            queryBuilder.andWhere('order.order_date BETWEEN :fromDateFormatted AND :toDateFormatted', {
                fromDateFormatted,
                toDateFormatted,
            });
        } else if (fromOrderDate) {
            const fromDateFormatted = moment(fromOrderDate, 'YYYY-MM-DD').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            queryBuilder.andWhere('order.order_date >= :fromDateFormatted', { fromDateFormatted });
        } else if (toOrderDate) {
            const toDateFormatted = moment(toOrderDate, 'YYYY-MM-DD').endOf('day').format('YYYY-MM-DD HH:mm:ss');
            queryBuilder.andWhere('order.order_date <= :toDateFormatted', { toDateFormatted });
        }

        if (paymentMethod) {
            queryBuilder.andWhere('orderRestaurantPayment.payment_method = :paymentMethod', { paymentMethod });
        }

        queryBuilder
            .orderBy('orderRestaurantPayment.order_id', 'DESC')
            .offset(page * limit)
            .limit(limit);
        const [items, total] = await queryBuilder.getManyAndCount();
        return {
            items,
            total,
        };
    }

    async exportGetList(exportDto: GetOrderRestaurantPaymentListDto, provinceId: string) {
        try {
            const {
                restaurantId,
                reversalStatus,
                status,
                limit = 20,
                page = 1,
                fromOrderDate,
                toOrderDate,
                paymentMethod,
            } = exportDto;
            const queryBuilder = DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
                .createQueryBuilder('orderRestaurantPayment')
                .leftJoinAndSelect('orderRestaurantPayment.order', 'order')
                .leftJoinAndSelect('orderRestaurantPayment.restaurant', 'restaurant');

            if (restaurantId) {
                queryBuilder.andWhere('orderRestaurantPayment.restaurant_id = :restaurantId', { restaurantId });
            }
            if (reversalStatus) {
                queryBuilder.andWhere('orderRestaurantPayment.reversal_status = :reversalStatus', { reversalStatus });
            }
            if (status) {
                queryBuilder.andWhere('orderRestaurantPayment.status = :status', { status });
            }

            if (fromOrderDate && toOrderDate) {
                console.log('fromOrderDate', fromOrderDate, 'toOrderDate', toOrderDate);
                const fromDateFormatted = moment(fromOrderDate, 'YYYY-MM-DD').startOf('day').format('YYYY-MM-DD');
                const toDateFormatted = moment(toOrderDate, 'YYYY-MM-DD').endOf('day').format('YYYY-MM-DD');
                queryBuilder.andWhere('order.order_date BETWEEN :fromDateFormatted AND :toDateFormatted', {
                    fromDateFormatted,
                    toDateFormatted,
                });
            } else if (fromOrderDate) {
                const fromDateFormatted = moment(fromOrderDate, 'YYYY-MM-DD')
                    .startOf('day')
                    .format('YYYY-MM-DD HH:mm:ss');
                queryBuilder.andWhere('order.order_date >= :fromDateFormatted', { fromDateFormatted });
            } else if (toOrderDate) {
                const toDateFormatted = moment(toOrderDate, 'YYYY-MM-DD').endOf('day').format('YYYY-MM-DD HH:mm:ss');
                queryBuilder.andWhere('order.order_date <= :toDateFormatted', { toDateFormatted });
            }

            if (paymentMethod) {
                queryBuilder.andWhere('orderRestaurantPayment.payment_method = :paymentMethod', { paymentMethod });
            }
            // Apply sorting
            queryBuilder.orderBy('orderRestaurantPayment.order_id', 'DESC');

            const offset = (page - 1) * limit || 0;

            queryBuilder.offset(offset).limit(limit);

            // Execute query
            const [items, total] = await queryBuilder.getManyAndCount();

            return {
                items,
                total,
            };
        } catch (error) {
            this.logger.error(
                `[exportGetList] [${provinceId}] ${error.message} | stack: ${error.stack} | args: ${JSON.stringify({
                    exportDto,
                    provinceId,
                })}`,
            );
            throw new InternalServerErrorException('Failed to export order driver expense list');
        }
    }

    parseDataSummary(rawData: any[]): IOrderRestaurantPaymentSummary[] {
        if (!rawData) {
            return [];
        }
        let totalAmount = 0,
            totalVat = 0,
            totalTradeDiscount = 0,
            totalPersonalIncomeTax = 0,
            totalCount = 0;

        const result = rawData.map((item) => {
            totalAmount += item.totalAmount;
            totalVat += item.totalVat;
            totalTradeDiscount += item.totalTradeDiscount;
            totalPersonalIncomeTax += item.totalPersonalIncomeTax;
            totalCount += Number(item.totalCount);
            return {
                paymentMethod: item.paymentMethod,
                totalAmount: item.totalAmount,
                totalVat: item.totalVat,
                totalTradeDiscount: item.totalTradeDiscount,
                totalPersonalIncomeTax: item.totalPersonalIncomeTax,
                totalCount: Number(item.totalCount),
            };
        });
        const total = {
            paymentMethod: 'total',
            totalAmount,
            totalVat,
            totalTradeDiscount,
            totalPersonalIncomeTax,
            totalCount,
        };
        return [total, ...result];
    }

    async paymentSummary(startDate: string, endDate: string, provinceId: string) {
        const _doneData = await DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
            .createQueryBuilder('orderRestaurantPayment')
            .select('orderRestaurantPayment.payment_method', 'paymentMethod')
            .addSelect('SUM(orderRestaurantPayment.amount)', 'totalAmount')
            .addSelect('SUM(orderRestaurantPayment.vat)', 'totalVat')
            .addSelect('SUM(orderRestaurantPayment.trade_discount)', 'totalTradeDiscount')
            .addSelect('SUM(orderRestaurantPayment.personal_income_tax)', 'totalPersonalIncomeTax')
            .addSelect('COUNT(orderRestaurantPayment.order_id)', 'totalCount')
            .innerJoin('orderRestaurantPayment.order', 'order')
            .where('order.order_date >= :startDate', { startDate })
            .andWhere('order.order_date <= :endDate', { endDate })
            .andWhere('orderRestaurantPayment.payment_method IS NOT NULL')
            .andWhere('order.order_status_id = :status', { status: EOrderStatusId.ARRIVED })
            .groupBy('orderRestaurantPayment.payment_method')
            .getRawMany();
        const _cancelledData = DatabaseService.getRepositoryByProvinceId(OrderRestaurantPayment, provinceId)
        .createQueryBuilder('orderRestaurantPayment')
        .select('orderRestaurantPayment.payment_method', 'paymentMethod')
        .addSelect('SUM(orderRestaurantPayment.amount)', 'totalAmount')
        .addSelect('SUM(orderRestaurantPayment.vat)', 'totalVat')
        .addSelect('SUM(orderRestaurantPayment.trade_discount)', 'totalTradeDiscount')
        .addSelect('SUM(orderRestaurantPayment.personal_income_tax)', 'totalPersonalIncomeTax')
        .addSelect('COUNT(orderRestaurantPayment.order_id)', 'totalCount')
        .innerJoin('orderRestaurantPayment.order', 'order')
        .where('order.order_date >= :startDate', { startDate })
        .andWhere('order.order_date <= :endDate', { endDate })
        .andWhere('orderRestaurantPayment.payment_method IS NOT NULL')
        .andWhere('order.order_status_id = :status', { status: EOrderStatusId.CANCELED })
        .groupBy('orderRestaurantPayment.payment_method')
        .getRawMany();
        const [doneData, cancelledData] = await Promise.all([_doneData, _cancelledData]);
        return {
            doneData: this.parseDataSummary(doneData),
            cancelledData: this.parseDataSummary(cancelledData),
        }
    }
}
