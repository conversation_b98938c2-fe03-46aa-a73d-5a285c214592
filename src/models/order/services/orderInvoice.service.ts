import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment';
import { Order, OrderType } from 'src/entities/order.entity';
import {
    Invoice,
    EOrderInvoiceStatus,
    EMisaInvoiceSeries,
    EInvoiceType,
    EInvoiceItemCode,
    IInvoiceItem,
    EInvoiceErrorCode,
} from 'src/entities/invoice.entity';
import { AdsTax } from 'src/entities/adsTax.entity';
import { DatabaseService } from 'src/providers/database/database.service';
import { CreateOrderInvoiceDto } from '../dto/createOrderInvoice.dto';
import { MisaHttpService } from 'src/misaHttp/misaHttp.service';
import { MISA_HTTP } from 'src/misaHttp/misaHttp.constant';
import {
    MisaAioInvoiceRequestDto,
    MisaAioInvoiceDataDto,
    MisaTaxRateInfoDto,
    MisaOriginalInvoiceDetailDto,
} from 'src/misaHttp/interfaces/misaInvoice.interface';
import { UserCompanyProfileService } from 'src/models/user/services/userCompanyProfile.service';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';

import { AdsCampaign } from 'src/entities/adsCampaigns.entity';

import { EAdsCategoryCode } from 'src/entities/adsCategoryV2.entity';
import { AdsTaxService } from './adsTax.service';
import { MerchantCommandService } from 'src/providers/microservices/deliveryServiceProxy/service/merchantCommand.service';
import { MerchantIdCardCommandService } from 'src/providers/microservices/deliveryServiceProxy/service/merchantIdCard.service';
import { lastValueFrom } from 'rxjs';
import { EMerchantRole } from 'src/entities/merchantRole.entity';

import num2words = require('vn-num2words');
import { AppSettingService } from 'src/models/appSetting/appSetting.service';
import { IUserOrderInvoiceConfig } from 'src/entities/appSetting.entity';
import { UserCompanyProfile } from 'src/entities/userCompanyProfile.entity';
import { MattermostService } from 'src/misaHttp/mattermost.service';
import { ProvinceService } from 'src/models/province/province.service';
import { removeEmoji } from 'src/common/helpers/common.helper';
import { AdsSettings } from 'src/entities/adsSettings.entity';

const defaultDeliveryFeeVATRate = 8; // 8% VAT for delivery fee
const defaultServiceFeeVATRate = 8; // 8% VAT for service fee
const defaultSurchargeVATRate = 8; // 8% VAT for surcharge

@Injectable()
export class OrderInvoiceService {
    private readonly logger = new Logger(OrderInvoiceService.name);

    constructor(
        @Inject(MISA_HTTP) private readonly misaHttpService: MisaHttpService,
        private readonly userCompanyProfileService: UserCompanyProfileService,
        private s3Service: AwsS3Service,
        private readonly adsTaxService: AdsTaxService,
        private readonly merchantCommandService: MerchantCommandService,
        private readonly merchantIdCardCommandService: MerchantIdCardCommandService,
        private readonly configService: ConfigService,
        private appSettingService: AppSettingService,
        private readonly mattermostService: MattermostService,
        private provinceService: ProvinceService,
    ) {}

    private validateVietnamTaxCode(taxCode: string): {
        isValid: boolean;
        type?: '10-digit' | '12-digit' | '13-digit-dash' | '13-digit-flat';
        reason?: string;
    } {
        if (!taxCode || typeof taxCode !== 'string') {
            return {
                isValid: false,
                reason: 'Mã số thuế không hợp lệ (trống hoặc không phải chuỗi)',
            };
        }

        const tenDigitRegex = /^\d{10}$/;
        // const twelveDigitRegex = /^\d{12}$/;
        const thirteenDigitWithDashRegex = /^\d{10}-\d{3}$/;
        // const thirteenDigitFlatRegex = /^\d{13}$/;

        if (tenDigitRegex.test(taxCode)) {
            return { isValid: true, type: '10-digit' };
        }

        // if (twelveDigitRegex.test(taxCode)) {
        //     return { isValid: true, type: '12-digit' };
        // }

        if (thirteenDigitWithDashRegex.test(taxCode)) {
            return { isValid: true, type: '13-digit-dash' };
        }

        // if (thirteenDigitFlatRegex.test(taxCode)) {
        //     return { isValid: true, type: '13-digit-flat' };
        // }

        return {
            isValid: false,
            reason: 'Mã số thuế không đúng định dạng!',
        };
    }

    async createDeliveryFeeInvoice(order: Order, provinceId: string): Promise<Invoice[]> {
        try {
            if (!order.delivery_fee || order.delivery_fee <= 0) {
                this.logger.warn(`Order ${order.id} has no delivery fee, skipping invoice generation`);
                return [];
            }

            const orderInvoices: Invoice[] = [];

            const existingInvoices = await this.findExistingInvoices(order.id, provinceId);
            if (existingInvoices?.length) {
                if (existingInvoices.every((invoice) => invoice.status !== EOrderInvoiceStatus.FAILED)) {
                    this.logger.warn(`Invoice already exists for order ${order.id}`);
                    return existingInvoices;
                }

                const failedInvoices = existingInvoices.filter(
                    (invoice) => invoice.status === EOrderInvoiceStatus.FAILED,
                );
                if (failedInvoices.length) {
                    for (const failedInvoice of failedInvoices) {
                        const updatedInvoice = await this.updateInvoiceStatus(
                            failedInvoice,
                            EOrderInvoiceStatus.PENDING,
                            provinceId,
                        );
                        orderInvoices.push(updatedInvoice);
                    }
                }
            } else {
                const config = await this.appSettingService.getUserOrderInvoiceConfig(provinceId);
                const isSplitInvoice = config.split_invoice || false;
                let orderInvoiceDtos: CreateOrderInvoiceDto[] = [];

                if (isSplitInvoice) {
                    orderInvoiceDtos = this.buildSplitedInvoicesDto(order, config, provinceId);
                } else {
                    orderInvoiceDtos = this.buildNotSplitedInvoicesDto(order, config, provinceId);
                }

                for (const orderInvoiceDto of orderInvoiceDtos) {
                    const orderInvoice = await this.createOrderInvoiceRecord(orderInvoiceDto, provinceId);
                    orderInvoices.push(orderInvoice);
                }
            }
            const processedInvoices: Invoice[] = [];

            for (const orderInvoice of orderInvoices) {
                try {
                    const publishedInvoice = await this.publishInvoiceToMisa(orderInvoice, provinceId);

                    // Upload invoice PDF to S3 if transaction_id exists
                    if (publishedInvoice.status === EOrderInvoiceStatus.PUBLISHED) {
                        const invoiceDetail = await this.misaHttpService.downloadInvoice([
                            publishedInvoice.transaction_id,
                        ]);
                        if (invoiceDetail.length > 0) {
                            const fileName = `invoice/${publishedInvoice.name}.pdf`;
                            let pdfUrl;
                            let uploadAttempts = 0;
                            const maxAttempts = 5;

                            while (uploadAttempts < maxAttempts) {
                                try {
                                    pdfUrl = await this.s3Service.uploadPublicFile(
                                        fileName,
                                        'application/pdf',
                                        invoiceDetail[0],
                                        undefined,
                                        false,
                                        false,
                                    );
                                    break;
                                } catch (error) {
                                    uploadAttempts++;
                                    if (uploadAttempts >= maxAttempts) {
                                        this.logger.error(
                                            `[${provinceId}] Upload attempt ${uploadAttempts} failed for invoice ${publishedInvoice.id}: ${error.message}`,
                                        );
                                    }
                                    this.logger.warn(
                                        `[${provinceId}] Upload attempt ${uploadAttempts} failed for invoice ${publishedInvoice.id}: ${error.message}`,
                                    );
                                }
                            }

                            await this.updateInvoicePdfUrl(publishedInvoice.id, pdfUrl.Location, provinceId);
                        }
                    }

                    processedInvoices.push(publishedInvoice);
                } catch (error) {
                    this.logger.error(`[${provinceId}] Failed to process invoice ${orderInvoice.id}: ${error.message}`);
                    continue;
                }
            }

            this.logger.log(`Successfully created delivery fee invoice for order ${order.id}`);
            return processedInvoices;
        } catch (error) {
            this.logger.error(
                `Failed to create delivery fee invoice for order ${order.id}: ${error.message} ${error.stack}`,
            );
            await this.markInvoiceAsFailed(order.id, error.message, provinceId);
            return null;
        }
    }

    private buildSplitedInvoicesDto(
        order: Order,
        config: IUserOrderInvoiceConfig,
        provinceId: string,
    ): CreateOrderInvoiceDto[] {
        const deliveryFeeVATRate = config.delivery_vat_percent || defaultDeliveryFeeVATRate;
        const serviceFeeVATRate = config.service_vat_percent || defaultServiceFeeVATRate;
        const surchargeVATRate = config.surcharge_vat_percent || defaultSurchargeVATRate;

        const villItems: IInvoiceItem[] = [];
        const driverItems: IInvoiceItem[] = [];

        if (order.delivery_fee > 0) {
            const deliveryTradeDiscount = order.driverExpense?.trade_discount || 0;
            const driverDeliveryIncome = order.delivery_fee * 1000 - deliveryTradeDiscount;
            villItems.push({
                code: EInvoiceItemCode.VILLSHIPPER,
                quantity: 1,
                amount_without_vat: Math.round(deliveryTradeDiscount / (1 + deliveryFeeVATRate / 100)),
                amount: deliveryTradeDiscount,
                vat_rate: deliveryFeeVATRate,
                vat_amount: deliveryTradeDiscount - Math.round(deliveryTradeDiscount / (1 + deliveryFeeVATRate / 100)),
                name:
                    ([OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                        ? 'Cước phí giao hàng ID: ' + order.id
                        : 'Cước phí vận chuyển ID: ' + order.id) + ' (Phần doanh thu Vill được chia)',
            });
            driverItems.push({
                code: EInvoiceItemCode.GTGT,
                quantity: 1,
                amount_without_vat: Math.round(driverDeliveryIncome / (1 + deliveryFeeVATRate / 100)),
                amount: driverDeliveryIncome,
                vat_rate: deliveryFeeVATRate,
                vat_amount: driverDeliveryIncome - Math.round(driverDeliveryIncome / (1 + deliveryFeeVATRate / 100)),
                name:
                    ([OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                        ? 'Cước phí giao hàng ID: ' + order.id
                        : 'Cước phí vận chuyển ID: ' + order.id) + ' (Phần doanh thu của đối tác vận tải)',
            });
        }

        if (order.service_fee > 0) {
            villItems.push({
                code: EInvoiceItemCode.VILLDVNT,
                quantity: 1,
                amount_without_vat: Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                amount: order.service_fee * 1000,
                vat_rate: serviceFeeVATRate,
                vat_amount:
                    order.service_fee * 1000 - Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                name: 'Phí dịch vụ và nền tảng ID: ' + order.id,
            });
        }

        if (order.surcharge > 0) {
            driverItems.push({
                code: EInvoiceItemCode.GTGT,
                quantity: 1,
                amount_without_vat: Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                amount: order.surcharge * 1000,
                vat_rate: surchargeVATRate,
                vat_amount:
                    order.surcharge * 1000 - Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                name: 'Phụ phí ID: ' + order.id,
            });
        }

        const villInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: `ORD_VILL-${provinceId}-${order.id}`,
            inv_date: order.order_date,
            buyer_name: removeEmoji(order.user?.name),
            buyer_phone: order.user?.phone,
            buyer_email: order.user?.email,
            buyer_address: order.destination_location?.address,
            metadata: {
                items: villItems,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: villItems.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: villItems.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: villItems.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-vill-' + order.code,
        };

        const driverInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: `ORD_DRIVER-${provinceId}-${order.id}`,
            inv_date: order.order_date,
            buyer_name: removeEmoji(order.user?.name),
            buyer_phone: order.user?.phone,
            buyer_email: order.user?.email,
            buyer_address: order.destination_location?.address,
            metadata: {
                items: driverItems,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: driverItems.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: driverItems.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: driverItems.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-taixe-' + order.code,
        };
        return [villInvoiceDto, driverInvoiceDto];
    }

    private buildNotSplitedInvoicesDto(
        order: Order,
        config: IUserOrderInvoiceConfig,
        provinceId: string,
    ): CreateOrderInvoiceDto[] {
        const deliveryFeeVATRate = config.delivery_vat_percent || defaultDeliveryFeeVATRate;
        const serviceFeeVATRate = config.service_vat_percent || defaultServiceFeeVATRate;
        const surchargeVATRate = config.surcharge_vat_percent || defaultSurchargeVATRate;
        const refId = this.generateRefId(order.id, provinceId);

        const items: IInvoiceItem[] = [
            {
                code: EInvoiceItemCode.DELIVERY_FEE,
                quantity: 1,
                amount_without_vat: Math.round((order.delivery_fee * 1000) / (1 + deliveryFeeVATRate / 100)),
                amount: order.delivery_fee * 1000,
                vat_rate: deliveryFeeVATRate,
                vat_amount:
                    order.delivery_fee * 1000 -
                    Math.round((order.delivery_fee * 1000) / (1 + deliveryFeeVATRate / 100)),
                name: [OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                    ? 'Cước phí giao hàng ID: ' + order.id
                    : 'Cước phí vận chuyển ID: ' + order.id,
            },
            {
                code: EInvoiceItemCode.SERVICE_FEE,
                quantity: 1,
                amount_without_vat: Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                amount: order.service_fee * 1000,
                vat_rate: serviceFeeVATRate,
                vat_amount:
                    order.service_fee * 1000 - Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                name: 'Phí dịch vụ và nền tảng ID: ' + order.id,
            },
        ];

        if (order.surcharge > 0) {
            items.push({
                code: EInvoiceItemCode.SURCHARGE,
                quantity: 1,
                amount_without_vat: Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                amount: order.surcharge * 1000,
                vat_rate: surchargeVATRate,
                vat_amount:
                    order.surcharge * 1000 - Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                name: 'Phụ phí ID: ' + order.id,
            });
        }

        const createInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: refId,
            inv_date: order.order_date,
            buyer_name: removeEmoji(order.user?.name),
            buyer_phone: order.user?.phone,
            buyer_email: order.user?.email,
            buyer_address: order.destination_location?.address,
            metadata: {
                items,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: items.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: items.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: items.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-' + order.code,
        };
        return [createInvoiceDto];
    }

    private buildSplitedCompanyInvoicesDto(
        order: Order,
        config: IUserOrderInvoiceConfig,
        provinceId: string,
        companyProfile: UserCompanyProfile,
    ): CreateOrderInvoiceDto[] {
        const deliveryFeeVATRate = config.delivery_vat_percent || defaultDeliveryFeeVATRate;
        const serviceFeeVATRate = config.service_vat_percent || defaultServiceFeeVATRate;
        const surchargeVATRate = config.surcharge_vat_percent || defaultSurchargeVATRate;

        const villItems: IInvoiceItem[] = [];
        const driverItems: IInvoiceItem[] = [];

        if (order.delivery_fee > 0) {
            const deliveryTradeDiscount = order.driverExpense?.trade_discount || 0;
            const driverDeliveryIncome = order.delivery_fee * 1000 - deliveryTradeDiscount;
            villItems.push({
                code: EInvoiceItemCode.VILLSHIPPER,
                quantity: 1,
                amount_without_vat: Math.round(deliveryTradeDiscount / (1 + deliveryFeeVATRate / 100)),
                amount: deliveryTradeDiscount,
                vat_rate: deliveryFeeVATRate,
                vat_amount: deliveryTradeDiscount - Math.round(deliveryTradeDiscount / (1 + deliveryFeeVATRate / 100)),
                name:
                    ([OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                        ? 'Cước phí giao hàng ID: ' + order.id
                        : 'Cước phí vận chuyển ID: ' + order.id) + ' (Phần doanh thu Vill được chia)',
            });
            driverItems.push({
                code: EInvoiceItemCode.GTGT,
                quantity: 1,
                amount_without_vat: Math.round(driverDeliveryIncome / (1 + deliveryFeeVATRate / 100)),
                amount: driverDeliveryIncome,
                vat_rate: deliveryFeeVATRate,
                vat_amount: driverDeliveryIncome - Math.round(driverDeliveryIncome / (1 + deliveryFeeVATRate / 100)),
                name:
                    ([OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                        ? 'Cước phí giao hàng ID: ' + order.id
                        : 'Cước phí vận chuyển ID: ' + order.id) + ' (Phần doanh thu của đối tác vận tải)',
            });
        }

        if (order.service_fee > 0) {
            villItems.push({
                code: EInvoiceItemCode.VILLDVNT,
                quantity: 1,
                amount_without_vat: Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                amount: order.service_fee * 1000,
                vat_rate: serviceFeeVATRate,
                vat_amount:
                    order.service_fee * 1000 - Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                name: 'Phí dịch vụ và nền tảng ID: ' + order.id,
            });
        }

        if (order.surcharge > 0) {
            driverItems.push({
                code: EInvoiceItemCode.GTGT,
                quantity: 1,
                amount_without_vat: Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                amount: order.surcharge * 1000,
                vat_rate: surchargeVATRate,
                vat_amount:
                    order.surcharge * 1000 - Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                name: 'Phụ phí ID: ' + order.id,
            });
        }

        const villInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: `ORD_VILL-${provinceId}-${order.id}`,
            inv_date: order.order_date,
            buyer_name: removeEmoji(companyProfile.company_name),
            buyer_tax_code: companyProfile.company_tax_code,
            buyer_email: companyProfile.invoice_email,
            buyer_phone: '',
            buyer_address: companyProfile.company_address,
            metadata: {
                items: villItems,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: villItems.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: villItems.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: villItems.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-vill-' + order.code,
        };

        const driverInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: `ORD_DRIVER-${provinceId}-${order.id}`,
            inv_date: order.order_date,
            buyer_name: removeEmoji(companyProfile.company_name),
            buyer_tax_code: companyProfile.company_tax_code,
            buyer_email: companyProfile.invoice_email,
            buyer_phone: '',
            buyer_address: companyProfile.company_address,
            metadata: {
                items: driverItems,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: driverItems.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: driverItems.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: driverItems.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-taixe-' + order.code,
        };
        return [villInvoiceDto, driverInvoiceDto];
    }

    private buildNotSplitedCompanyInvoicesDto(
        order: Order,
        config: IUserOrderInvoiceConfig,
        provinceId: string,
        companyProfile: any,
    ): CreateOrderInvoiceDto[] {
        const deliveryFeeVATRate = config.delivery_vat_percent || defaultDeliveryFeeVATRate;
        const serviceFeeVATRate = config.service_vat_percent || defaultServiceFeeVATRate;
        const surchargeVATRate = config.surcharge_vat_percent || defaultSurchargeVATRate;
        const refId = this.generateRefId(order.id, provinceId);

        const items: IInvoiceItem[] = [
            {
                code: EInvoiceItemCode.DELIVERY_FEE,
                quantity: 1,
                amount_without_vat: Math.round((order.delivery_fee * 1000) / (1 + deliveryFeeVATRate / 100)),
                amount: order.delivery_fee * 1000,
                vat_rate: deliveryFeeVATRate,
                vat_amount:
                    order.delivery_fee * 1000 -
                    Math.round((order.delivery_fee * 1000) / (1 + deliveryFeeVATRate / 100)),
                name: [OrderType.VILLFOOD, OrderType.VILLEXPRESS].includes(order.type)
                    ? 'Cước phí giao hàng ID: ' + order.id
                    : 'Cước phí vận chuyển ID: ' + order.id,
            },
            {
                code: EInvoiceItemCode.SERVICE_FEE,
                quantity: 1,
                amount_without_vat: Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                amount: order.service_fee * 1000,
                vat_rate: serviceFeeVATRate,
                vat_amount:
                    order.service_fee * 1000 - Math.round((order.service_fee * 1000) / (1 + serviceFeeVATRate / 100)),
                name: 'Phí dịch vụ và nền tảng ID: ' + order.id,
            },
        ];

        if (order.surcharge > 0) {
            items.push({
                code: EInvoiceItemCode.SURCHARGE,
                quantity: 1,
                amount_without_vat: Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                amount: order.surcharge * 1000,
                vat_rate: surchargeVATRate,
                vat_amount:
                    order.surcharge * 1000 - Math.round((order.surcharge * 1000) / (1 + surchargeVATRate / 100)),
                name: 'Phụ phí ID: ' + order.id,
            });
        }

        const createInvoiceDto: CreateOrderInvoiceDto = {
            order_id: order.id,
            ref_id: refId,
            inv_date: order.order_date,
            buyer_name: removeEmoji(companyProfile.company_name),
            buyer_tax_code: companyProfile.company_tax_code,
            buyer_email: companyProfile.invoice_email,
            buyer_phone: '',
            buyer_address: companyProfile.company_address,
            metadata: {
                items,
                info: {
                    order_id: order.id,
                    order_code: order.code,
                    order_date: order.order_date,
                    order_type: order.type,
                    total_amount: items.reduce((acc, item) => acc + item.amount, 0),
                    total_amount_without_vat: items.reduce((acc, item) => acc + item.amount_without_vat, 0),
                    total_amount_vat: items.reduce((acc, item) => acc + item.vat_amount, 0),
                },
            },
            name: 'hoa-don-' + order.code,
        };
        return [createInvoiceDto];
    }

    async createCompanyInvoice(order: Order, provinceId: string): Promise<Invoice[]> {
        try {
            // Check invoice_requested flag: if invoice_company_profile_id exists, use company profile details (1)
            // Otherwise, this function should not be called (customer details should use createDeliveryFeeInvoice)
            if (!order.invoice_company_profile_id) {
                this.logger.warn(`Order ${order.id} has no company profile ID, skipping company invoice generation`);
                return [];
            }

            if (!order.delivery_fee || order.delivery_fee <= 0) {
                this.logger.warn(`Order ${order.id} has no delivery fee, skipping company invoice generation`);
                return [];
            }

            const orderInvoices: Invoice[] = [];

            const existingInvoices = await this.findExistingInvoices(order.id, provinceId);
            if (existingInvoices?.length) {
                if (existingInvoices.every((invoice) => invoice.status !== EOrderInvoiceStatus.FAILED)) {
                    this.logger.warn(`Invoice already exists for order ${order.id}`);
                    return existingInvoices;
                }

                const failedInvoices = existingInvoices.filter(
                    (invoice) => invoice.status === EOrderInvoiceStatus.FAILED,
                );
                if (failedInvoices.length) {
                    for (const failedInvoice of failedInvoices) {
                        const updatedInvoice = await this.updateInvoiceStatus(
                            failedInvoice,
                            EOrderInvoiceStatus.PENDING,
                            provinceId,
                        );
                        orderInvoices.push(updatedInvoice);
                    }
                }
            } else {
                // Get company profile details for invoice generation
                const companyProfile = await this.userCompanyProfileService.validateCompanyProfileForUser(
                    order.invoice_company_profile_id,
                    order.user_id,
                    provinceId,
                );

                if (!companyProfile) {
                    this.logger.warn(`[${provinceId}] Company profile not found for order ${order.id}`);
                    return [];
                }

                if (!this.validateVietnamTaxCode(companyProfile.company_tax_code).isValid) {
                    this.logger.warn(
                        `[${provinceId}] Company profile tax code is invalid for order ${order.id} | tax code: ${companyProfile.company_tax_code}`,
                    );
                    return [];
                }

                const config = await this.appSettingService.getUserOrderInvoiceConfig(provinceId);
                const isSplitInvoice = config.split_invoice || false;
                let orderInvoiceDtos: CreateOrderInvoiceDto[] = [];

                if (isSplitInvoice) {
                    orderInvoiceDtos = this.buildSplitedCompanyInvoicesDto(order, config, provinceId, companyProfile);
                } else {
                    orderInvoiceDtos = this.buildNotSplitedCompanyInvoicesDto(
                        order,
                        config,
                        provinceId,
                        companyProfile,
                    );
                }

                for (const orderInvoiceDto of orderInvoiceDtos) {
                    const orderInvoice = await this.createOrderInvoiceRecord(orderInvoiceDto, provinceId);
                    orderInvoices.push(orderInvoice);
                }
            }

            const processedInvoices: Invoice[] = [];

            for (const orderInvoice of orderInvoices) {
                try {
                    const publishedInvoice = await this.publishInvoiceToMisa(orderInvoice, provinceId);

                    // Upload invoice PDF to S3 if transaction_id exists
                    if (publishedInvoice.status === EOrderInvoiceStatus.PUBLISHED) {
                        const invoiceDetail = await this.misaHttpService.downloadInvoice([
                            publishedInvoice.transaction_id,
                        ]);
                        if (invoiceDetail.length > 0) {
                            const fileName = `invoice/${publishedInvoice.name}.pdf`;
                            let pdfUrl;
                            let uploadAttempts = 0;
                            const maxAttempts = 5;

                            while (uploadAttempts < maxAttempts) {
                                try {
                                    pdfUrl = await this.s3Service.uploadPublicFile(
                                        fileName,
                                        'application/pdf',
                                        invoiceDetail[0],
                                        undefined,
                                        false,
                                        false,
                                    );
                                    break;
                                } catch (error) {
                                    uploadAttempts++;
                                    if (uploadAttempts >= maxAttempts) {
                                        this.logger.error(
                                            `[${provinceId}] Upload attempt ${uploadAttempts} failed for invoice ${publishedInvoice.id}: ${error.message}`,
                                        );
                                    }
                                    this.logger.warn(
                                        `[${provinceId}] Upload attempt ${uploadAttempts} failed for invoice ${publishedInvoice.id}: ${error.message}`,
                                    );
                                }
                            }

                            await this.updateInvoicePdfUrl(publishedInvoice.id, pdfUrl.Location, provinceId);
                        }
                    }

                    processedInvoices.push(publishedInvoice);
                } catch (error) {
                    this.logger.error(`[${provinceId}] Failed to process invoice ${orderInvoice.id}: ${error.message}`);
                    continue;
                }
            }

            this.logger.log(`Successfully created company invoice for order ${order.id}`);
            return processedInvoices;
        } catch (error) {
            this.logger.error(
                `Failed to create company invoice for order ${order.id}: ${error.message} ${error.stack}`,
            );
            await this.markInvoiceAsFailed(order.id, error.message, provinceId);
            return null;
        }
    }

    private async findExistingInvoices(orderId: number, provinceId: string): Promise<Invoice[]> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).find({
            where: { order_id: orderId },
        });
    }

    private async findExistingInvoice(orderId: number, provinceId: string): Promise<Invoice | null> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).findOne({
            where: { order_id: orderId },
        });
    }

    private generateRefId(orderId: number, provinceId: string): string {
        return `ORD-${provinceId}-${orderId}`;
    }

    private async createOrderInvoiceRecord(dto: CreateOrderInvoiceDto, provinceId: string): Promise<Invoice> {
        const orderInvoice = new Invoice({
            order_id: dto.order_id,
            ref_id: dto.ref_id,
            buyer_name: dto.buyer_name,
            buyer_tax_code: dto.buyer_tax_code,
            buyer_id_number: dto.buyer_id_number || '',
            buyer_email: dto.buyer_email,
            buyer_phone: dto.buyer_phone,
            buyer_address: dto.buyer_address,
            transaction_id: dto.transaction_id,
            status: EOrderInvoiceStatus.PENDING,
            invoice_series: EMisaInvoiceSeries['1C25MVS'],
            invoice_date: moment(dto.inv_date).toDate(),
            metadata: dto.metadata,
            name: dto.name,
            type: EInvoiceType.ORDER,
        });

        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).save(orderInvoice);
    }

    private async publishInvoiceToMisa(orderInvoice: Invoice, provinceId: string): Promise<Invoice> {
        try {
            const invoiceData: MisaAioInvoiceDataDto =
                orderInvoice.type === EInvoiceType.ADS_CAMPAIGN
                    ? this.mapAdsCampaignToMisaInvoice(orderInvoice, provinceId)
                    : this.mapOrderToMisaInvoice(orderInvoice);

            const requestData: MisaAioInvoiceRequestDto = {
                SignType: 2,
                InvoiceData: [invoiceData],
                PublishInvoiceData: null,
            };

            const response = await this.misaHttpService.publishInvoiceHsm(requestData);

            if (response.success) {
                const transactionId = this.extractTransactionIdFromMisaResponse(response.publishInvoiceResult);
                const invoiceNumber = this.extractInvoiceNumberFromMisaResponse(response.publishInvoiceResult);

                this.checkIsPublicInvoiceError(response.publishInvoiceResult, orderInvoice.ref_id);

                orderInvoice = await this.updateInvoiceStatusAndTransactionId(
                    orderInvoice,
                    EOrderInvoiceStatus.PUBLISHED,
                    provinceId,
                    null,
                    transactionId,
                    invoiceNumber,
                );

                this.logger.log(`Misa response: ${JSON.stringify(response)}`);

                this.logger.log(
                    `Successfully published invoice ${orderInvoice.ref_id} to MISA${
                        transactionId ? ` with transaction ID: ${transactionId}` : ''
                    }`,
                );
                return orderInvoice;
            } else {
                orderInvoice = await this.updateInvoiceStatus(
                    orderInvoice,
                    EOrderInvoiceStatus.FAILED,
                    provinceId,
                    response.errorCode,
                    this.buildInvoiceErrorMessage(response.errorCode, orderInvoice.buyer_tax_code),
                );
                this.logger.error(`Failed to publish invoice ${orderInvoice.ref_id} to MISA: ${response.errorCode}`);
                return orderInvoice;
            }
        } catch (error) {
            orderInvoice = await this.updateInvoiceStatus(
                orderInvoice,
                EOrderInvoiceStatus.FAILED,
                provinceId,
                error.message,
                this.buildInvoiceErrorMessage(error.message, orderInvoice.buyer_tax_code),
            );

            if (process.env.NODE_ENV?.toLowerCase() === 'production') {
                await this.mattermostService.sendMessage(
                    await this.buildMattermostErrorMessage(orderInvoice, provinceId),
                );
            }

            this.logger.error(
                `Error publishing invoice ${orderInvoice.ref_id} to MISA: ${error.message} ${error.stack}`,
            );
            return orderInvoice;
        }
    }
    private async buildMattermostErrorMessage(orderInvoice: Invoice, provinceId: string): Promise<string> {
        const province = await this.provinceService.getProvinceById(provinceId);
        const provinceName = province?.name;

        return `🚨 **Invoice Processing Error** 🚨
        ------------------------------------------------------------
**Province:** ${provinceName}
**Invoice ID:** ${orderInvoice.id}
**Order ID:** ${orderInvoice.order_id}
**Type:** ${orderInvoice.type}
**Ref ID:** ${orderInvoice.ref_id}
**Invoice Date:** ${moment(orderInvoice.invoice_date).format('YYYY-MM-DD')}
**Status:** ${orderInvoice.status}
**Error Code:** ${orderInvoice.error_code || 'N/A'}
**Error Message:** ${orderInvoice.error_message || 'N/A'}
**Buyer Tax Code:** ${orderInvoice.buyer_tax_code || 'N/A'}
**Total Amount:** ${orderInvoice.metadata.info.total_amount.toLocaleString()} VND
**Timestamp:** ${new Date().toISOString()}
`;
    }

    private buildInvoiceErrorMessage(errorCode: string, taxcode: string): string {
        if (errorCode === EInvoiceErrorCode.BUYER_TAX_CODE_INVALID) {
            return `<p>
            Mã số thuế bạn đã nhập cho đơn hàng này không hợp lệ: 
            <span style="color: red; font-weight: bold;">${taxcode}</span>. 
            Vui lòng kiểm tra lại mã số thuế và đảm bảo rằng thông tin chính xác 
            để có thể xuất hóa đơn ở đơn hàng tiếp theo.
            </p>
            <a href="https://tracuunnt.gdt.gov.vn/tcnnt/mstcn.jsp" target="_blank" style="color: #FF7E00;">
            Kiểm tra mã số thuế tại đây
            </a>`;
        }
        return '';
    }

    private checkIsPublicInvoiceError(publishInvoiceResult: string, refId: string): void {
        if (!publishInvoiceResult) {
            return;
        }

        const parsedResult = JSON.parse(publishInvoiceResult);

        if (Array.isArray(parsedResult) && parsedResult.length > 0) {
            const result = parsedResult.find((result) => result.RefID === refId);

            if (!result || !!result.ErrorCode) {
                throw new Error(`${result.ErrorCode}`);
            }
        }
    }

    private mapOrderToMisaInvoice(orderInvoice: Invoice): MisaAioInvoiceDataDto {
        const invoiceDetails: MisaOriginalInvoiceDetailDto[] = [];
        let sortOrder = 1;

        const items = orderInvoice.metadata.items;
        for (const item of items) {
            invoiceDetails.push({
                ItemType: 1,
                LineNumber: sortOrder,
                SortOrder: sortOrder,
                ItemCode: item.code,
                ItemName: item.name,
                UnitName: '',
                Quantity: 1,
                UnitPrice: item.amount_without_vat,
                DiscountRate: 0,
                DiscountAmountOC: 0,
                DiscountAmount: 0,
                AmountOC: item.amount_without_vat,
                Amount: item.amount_without_vat,
                AmountWithoutVATOC: item.amount_without_vat,
                AmountWithoutVAT: item.amount_without_vat,
                VATRateName: item.vat_rate + '%',
                VATAmountOC: item.vat_amount,
                VATAmount: item.vat_amount,
            });
            sortOrder++;
        }
        const taxRateInfo: MisaTaxRateInfoDto[] = [];

        // Group items by VAT rate and calculate totals
        const taxRateMap = new Map<number, { amountWithoutVAT: number; vatAmount: number }>();

        orderInvoice.metadata.items.forEach((item) => {
            const existing = taxRateMap.get(item.vat_rate) || {
                amountWithoutVAT: 0,
                vatAmount: 0,
            };
            existing.amountWithoutVAT += item.amount_without_vat;
            existing.vatAmount += item.vat_amount;
            taxRateMap.set(item.vat_rate, existing);
        });

        // Convert to tax rate info array
        taxRateMap.forEach((totals, vatRate) => {
            taxRateInfo.push({
                VATRateName: vatRate + '%',
                AmountWithoutVATOC: totals.amountWithoutVAT,
                VATAmountOC: totals.vatAmount,
            });
        });

        return {
            RefID: orderInvoice.ref_id,
            InvSeries: orderInvoice.invoice_series,
            InvoiceName: 'Hóa đơn giá trị gia tăng khởi tạo từ máy tính tiền',
            InvDate: moment(orderInvoice.invoice_date).format('YYYY-MM-DD'),
            CurrencyCode: 'VND',
            ExchangeRate: 1,
            PaymentMethodName: 'TM/CK',

            BuyerLegalName: orderInvoice.buyer_name,
            BuyerTaxCode: orderInvoice.buyer_tax_code,
            BuyerIDNumber: orderInvoice.buyer_id_number || '', // ID card number for non-company merchants
            BuyerEmail: orderInvoice.buyer_email,
            BuyerPhoneNumber: orderInvoice.buyer_phone,
            BuyerFullName: orderInvoice.buyer_name,
            BuyerAddress: orderInvoice.buyer_address,

            TotalSaleAmountOC: orderInvoice.metadata.info.total_amount_without_vat,
            TotalAmountWithoutVATOC: orderInvoice.metadata.info.total_amount_without_vat,
            TotalVATAmountOC: orderInvoice.metadata.info.total_amount_vat,
            TotalDiscountAmountOC: 0,
            TotalAmountOC: orderInvoice.metadata.info.total_amount,
            TotalSaleAmount: orderInvoice.metadata.info.total_amount_without_vat,
            TotalAmountWithoutVAT: orderInvoice.metadata.info.total_amount_without_vat,
            TotalVATAmount: orderInvoice.metadata.info.total_amount_vat,
            TotalDiscountAmount: 0,
            TotalAmount: orderInvoice.metadata.info.total_amount,
            TotalAmountInWords: this.convertNumberToWords(orderInvoice.metadata.info.total_amount),

            IsSendEmail: orderInvoice.buyer_email ? true : false,
            ReceiverEmail: orderInvoice.buyer_email,

            OriginalInvoiceDetail: invoiceDetails,

            TaxRateInfo: taxRateInfo,

            OptionUserDefined: {},
        };
    }

    private async updateInvoiceStatus(
        orderInvoice: Invoice,
        status: EOrderInvoiceStatus,
        provinceId: string,
        errorCode?: string,
        errorMessage?: string,
    ): Promise<Invoice> {
        orderInvoice.status = status;
        if (errorCode) {
            orderInvoice.error_code = errorCode;
        }
        if (errorMessage) {
            orderInvoice.error_message = errorMessage;
        }

        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).save(orderInvoice);
    }

    private async updateInvoiceStatusAndTransactionId(
        orderInvoice: Invoice,
        status: EOrderInvoiceStatus,
        provinceId: string,
        errorCode?: string,
        transactionId?: string,
        invoiceNumber?: string,
    ): Promise<Invoice> {
        orderInvoice.status = status;
        if (errorCode) {
            orderInvoice.error_code = errorCode;
        }
        if (transactionId) {
            orderInvoice.transaction_id = transactionId;
        }
        if (invoiceNumber) {
            orderInvoice.invoice_number = invoiceNumber;
        }
        const updatedInvoice = await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).save(orderInvoice);

        return updatedInvoice;
    }

    private async markInvoiceAsFailed(
        orderId: number,
        errorMessage: string,
        provinceId: string,
    ): Promise<Invoice | null> {
        try {
            let existingInvoice = await this.findExistingInvoice(orderId, provinceId);
            if (existingInvoice) {
                existingInvoice = await this.updateInvoiceStatus(
                    existingInvoice,
                    EOrderInvoiceStatus.FAILED,
                    provinceId,
                    errorMessage,
                );
                return existingInvoice;
            }
            return null;
        } catch (error) {
            this.logger.error(`Failed to mark invoice as failed for order ${orderId}: ${error.message}`);
        }
    }

    private convertNumberToWords(amount: number): string {
        return `${num2words(amount)} đồng`;
    }

    private extractTransactionIdFromMisaResponse(publishInvoiceResult: string): string | null {
        try {
            if (!publishInvoiceResult) {
                return null;
            }

            const parsedResult = JSON.parse(publishInvoiceResult);

            if (Array.isArray(parsedResult) && parsedResult.length > 0) {
                const firstResult = parsedResult[0];
                return firstResult.TransactionID || firstResult.transactionId || firstResult.transaction_id || null;
            }

            if (parsedResult.TransactionID) {
                return parsedResult.TransactionID;
            }

            if (parsedResult.transactionId) {
                return parsedResult.transactionId;
            }

            if (parsedResult.transaction_id) {
                return parsedResult.transaction_id;
            }

            return null;
        } catch (error) {
            this.logger.warn(`Failed to parse MISA publishInvoiceResult: ${error.message}`);
            return null;
        }
    }

    private extractInvoiceNumberFromMisaResponse(publishInvoiceResult: string): string | null {
        try {
            if (!publishInvoiceResult) {
                return null;
            }

            const parsedResult = JSON.parse(publishInvoiceResult);

            if (Array.isArray(parsedResult) && parsedResult.length > 0) {
                const firstResult = parsedResult[0];
                return firstResult.InvNo || firstResult.invNo || firstResult.inv_no || null;
            }

            if (parsedResult.InvNo) {
                return parsedResult.InvNo;
            }

            if (parsedResult.invNo) {
                return parsedResult.invNo;
            }

            if (parsedResult.inv_no) {
                return parsedResult.inv_no;
            }

            return null;
        } catch (error) {
            this.logger.warn(`Failed to parse MISA publishInvoiceResult: ${error.message}`);
            return null;
        }
    }

    async getInvoiceByOrderId(orderId: number, provinceId: string): Promise<Invoice | null> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).findOne({
            where: { order_id: orderId },
            relations: ['order'],
        });
    }

    async getInvoiceById(invoiceId: number, provinceId: string): Promise<Invoice | null> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).findOne({
            where: { id: invoiceId },
            relations: ['order'],
        });
    }

    async getInvoicesByStatus(status: EOrderInvoiceStatus, provinceId: string): Promise<Invoice[]> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).find({
            where: { status },
            relations: ['order'],
            order: { created_at: 'DESC' },
        });
    }

    async updateInvoicePdfUrl(invoiceId: number, pdfUrl: string, provinceId: string): Promise<void> {
        await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).update(invoiceId, {
            pdf_url: pdfUrl,
        });
    }

    async updateInvoiceTransactionId(invoiceId: number, transactionId: string, provinceId: string): Promise<void> {
        await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).update(invoiceId, {
            transaction_id: transactionId,
        });
    }

    async getInvoiceByTransactionId(transactionId: string, provinceId: string): Promise<Invoice | null> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).findOne({
            where: { transaction_id: transactionId },
            relations: ['order'],
        });
    }

    // ==================== ADS CAMPAIGN INVOICE METHODS ====================

    /**
     * Check if should use buyer_id_number instead of buyer_tax_code for ads campaign invoices
     * Based on environment variable only
     *
     * Environment Variable: ADS_INVOICE_USE_BUYER_ID_NUMBER
     * - Set to 'true' to replace buyer_tax_code with buyer_id_number in MISA invoice
     * - Set to 'false' or leave empty to use buyer_tax_code (default behavior)
     *
     * When enabled:
     * - BuyerTaxCode will use buyer_id_number value (replacing buyer_tax_code)
     * - BuyerIDNumber remains unchanged (keeps original buyer_id_number behavior)
     *
     * @returns boolean
     */
    private async shouldUseBuyerIdNumberForAdsInvoice(provinceId: string): Promise<boolean> {
        // const enableBuyerIdNumber = this.configService.get<string>('ADS_INVOICE_USE_BUYER_ID_NUMBER');
        // return enableBuyerIdNumber === 'true';
        const adsSettings = await DatabaseService.getRepositoryByProvinceId(AdsSettings, provinceId).find();
        const { is_use_buyer_id_number } = adsSettings[0];
        return is_use_buyer_id_number === 1;
    }

    /**
     * Xem trước thông tin hóa đơn trước khi tạo cho ads campaign
     */
    async previewAdsCampaignInvoice(adsCampaign: AdsCampaign, provinceId: string): Promise<any> {
        this.logger.log(`Previewing ads campaign invoice for campaign ${adsCampaign.id} in province ${provinceId}`);

        try {
            // Kiểm tra campaign có items không
            if (!adsCampaign.ads_items || adsCampaign.ads_items.length === 0) {
                return {
                    can_create_invoice: false,
                    reason: 'Chiến dịch quảng cáo không có items để tạo hóa đơn',
                };
            }

            // Kiểm tra hóa đơn đã tồn tại
            const existingInvoice = await this.findExistingAdsInvoice(adsCampaign.id, provinceId);
            if (existingInvoice && existingInvoice.status !== EOrderInvoiceStatus.FAILED) {
                return {
                    can_create_invoice: false,
                    reason: 'Hóa đơn đã tồn tại cho chiến dịch này',
                    existing_invoice: {
                        id: existingInvoice.id,
                        ref_id: existingInvoice.ref_id,
                        status: existingInvoice.status,
                        invoice_number: existingInvoice.invoice_number,
                        created_at: existingInvoice.created_at,
                    },
                };
            }

            const refId = this.generateAdsRefId(adsCampaign.id, provinceId);

            // Lấy thông tin buyer từ merchant (chủ nhà hàng)
            const buyerInfo = await this.getAdsBuyerInfo(adsCampaign, provinceId);

            // Ưu tiên sử dụng tax info từ campaign nếu có
            let totalTaxInfo = null;
            let adsTaxes = null;

            // Kiểm tra xem campaign có tax_info không (từ getDetails)
            if ((adsCampaign as any).tax_info) {
                totalTaxInfo = (adsCampaign as any).tax_info;
            } else {
                // Fallback: Lấy thông tin thuế từ bảng ads_tax theo từng item
                adsTaxes = await this.adsTaxService.getAdsTaxesByCampaignId(provinceId, adsCampaign.id);
                if (!adsTaxes || adsTaxes.length === 0) {
                    return {
                        can_create_invoice: false,
                        reason: 'Không tìm thấy thông tin thuế cho các item trong chiến dịch quảng cáo này',
                    };
                }

                // Tính tổng thuế từ tất cả items
                totalTaxInfo = this.calculateTotalTaxFromItems(adsTaxes);
            }

            // Tạo preview items
            const previewItems = this.createAdsCampaignInvoiceItems(adsCampaign, adsTaxes || []);

            return {
                can_create_invoice: true,
                preview_data: {
                    ref_id: refId,
                    buyer_info: buyerInfo,
                    campaign_info: {
                        id: adsCampaign.id,
                        name: `Chiến dịch quảng cáo #${adsCampaign.id}`,
                        created_at: adsCampaign.created_at,
                    },
                    tax_summary: {
                        total_amount: Number(totalTaxInfo.total_campaign),
                        total_amount_without_vat: Number(totalTaxInfo.total_order),
                        total_amount_vat: Number(totalTaxInfo.vat_amount),
                        vat_rate: Number(totalTaxInfo.vat_rate),
                    },
                    items: previewItems,
                    invoice_date: moment().format('YYYY-MM-DD'),
                },
            };
        } catch (error) {
            this.logger.error(`Error previewing ads campaign invoice: ${error.message}`, error.stack);
            return {
                can_create_invoice: false,
                reason: error.message,
            };
        }
    }

    async createAdsCampaignInvoice(
        adsCampaign: AdsCampaign,
        provinceId: string,
        buyerInfoOverride?: any,
    ): Promise<Invoice | null> {
        try {
            this.logger.log(`Creating ads campaign invoice for campaign ${adsCampaign.id} in province ${provinceId}`);
            // console.log(`[createAdsCampaignInvoice] Creating ads campaign invoice for campaign ${adsCampaign.id} in province ${provinceId}`);
            // console.log(`[createAdsCampaignInvoice] Campaign: `, adsCampaign);

            // Kiểm tra campaign đã thanh toán
            if (!this.isAdsCampaignPaid(adsCampaign)) {
                throw new Error('Chiến dịch quảng cáo chưa được thanh toán đầy đủ');
            }

            // Lấy thời gian thanh toán thành công gần nhất để làm invoice_date
            const latestPaymentDate = this.getAdsCampaignLatestPaymentDate(adsCampaign);
            this.logger.log(
                `[createAdsCampaignInvoice] Latest payment date for campaign ${adsCampaign.id}: ${
                    latestPaymentDate ? latestPaymentDate.toISOString() : 'not found'
                }`,
            );

            // Kiểm tra campaign có items không
            if (!adsCampaign.ads_items || adsCampaign.ads_items.length === 0) {
                throw new Error('Chiến dịch quảng cáo không có items để tạo hóa đơn');
            }

            // Kiểm tra hóa đơn đã tồn tại
            const existingInvoice = await this.findExistingAdsInvoice(adsCampaign.id, provinceId);
            if (existingInvoice && existingInvoice.status !== EOrderInvoiceStatus.FAILED) {
                this.logger.warn(`Invoice already exists for ads campaign ${adsCampaign.id}`);
                return existingInvoice;
            }

            const refId = this.generateAdsRefId(adsCampaign.id, provinceId);

            // Lấy thông tin buyer từ merchant (chủ nhà hàng)
            let buyerInfo = await this.getAdsBuyerInfo(adsCampaign, provinceId);

            // Ghi đè thông tin buyer nếu có trong request body
            if (buyerInfoOverride) {
                buyerInfo = {
                    ...buyerInfo,
                    // Chỉ ghi đè các field không null/undefined từ override
                    ...(buyerInfoOverride.buyer_name !== undefined && {
                        buyer_name: buyerInfoOverride.buyer_name,
                    }),
                    ...(buyerInfoOverride.buyer_tax_code !== undefined && {
                        buyer_tax_code: buyerInfoOverride.buyer_tax_code,
                    }),
                    ...(buyerInfoOverride.buyer_id_number !== undefined && {
                        buyer_id_number: buyerInfoOverride.buyer_id_number,
                    }),
                    ...(buyerInfoOverride.buyer_address !== undefined && {
                        buyer_address: buyerInfoOverride.buyer_address,
                    }),
                    ...(buyerInfoOverride.buyer_email !== undefined && {
                        buyer_email: buyerInfoOverride.buyer_email,
                    }),
                    ...(buyerInfoOverride.buyer_phone !== undefined && {
                        buyer_phone: buyerInfoOverride.buyer_phone,
                    }),
                    ...(buyerInfoOverride.buyer_code !== undefined && {
                        buyer_code: buyerInfoOverride.buyer_code,
                    }),
                };
                this.logger.log(
                    `Applied buyer info override for campaign ${adsCampaign.id}:`,
                    JSON.stringify(buyerInfoOverride),
                );
            }

            // Ưu tiên sử dụng tax info từ campaign nếu có
            let totalTaxInfo = null;
            let adsTaxes = null;

            // Kiểm tra xem campaign có tax_info không (từ getDetails)
            if ((adsCampaign as any).tax_info) {
                console.log(`[createAdsCampaignInvoice] Using tax_info from campaign:`, (adsCampaign as any).tax_info);
                totalTaxInfo = (adsCampaign as any).tax_info;
            } else {
                // Fallback: Lấy thông tin thuế từ bảng ads_tax theo từng item
                console.log(`[createAdsCampaignInvoice] Fallback to querying ads_tax table`);
                adsTaxes = await this.adsTaxService.getAdsTaxesByCampaignId(provinceId, adsCampaign.id);
                if (!adsTaxes || adsTaxes.length === 0) {
                    throw new Error('Không tìm thấy thông tin thuế cho các item trong chiến dịch quảng cáo này');
                }

                // Tính tổng thuế từ tất cả items
                totalTaxInfo = this.calculateTotalTaxFromItems(adsTaxes);
            }

            const createInvoiceDto = {
                ads_campaign_id: adsCampaign.id,
                ref_id: refId,
                ...buyerInfo,
                metadata: {
                    info: {
                        order_id: adsCampaign.id,
                        order_code: `ADS-${adsCampaign.id}`,
                        order_type: 'ads' as any,
                        order_date: moment(adsCampaign.created_at).format('YYYY-MM-DD'),
                        total_amount: Number(totalTaxInfo.total_campaign), // VND thực tế từ ads_tax
                        total_amount_without_vat: Number(totalTaxInfo.total_order), // VND thực tế từ ads_tax
                        total_amount_vat: Number(totalTaxInfo.vat_amount), // VND thực tế từ ads_tax
                    },
                    items: this.createAdsCampaignInvoiceItems(adsCampaign, adsTaxes || []),
                },
                name: 'hoa-don-quang-cao-' + adsCampaign.id,
            };

            let adsInvoice: Invoice;
            if (existingInvoice && existingInvoice.status === EOrderInvoiceStatus.FAILED) {
                adsInvoice = await this.updateInvoiceStatus(existingInvoice, EOrderInvoiceStatus.PENDING, provinceId);
                console.log(`[createAdsCampaignInvoice] Updating existing invoice: `, adsInvoice);
                // Update all buyer info and metadata
                adsInvoice.buyer_name = createInvoiceDto.buyer_name;
                adsInvoice.buyer_tax_code = createInvoiceDto.buyer_tax_code;
                adsInvoice.buyer_id_number = createInvoiceDto.buyer_id_number;
                adsInvoice.buyer_email = createInvoiceDto.buyer_email;
                adsInvoice.buyer_phone = createInvoiceDto.buyer_phone;
                adsInvoice.buyer_address = createInvoiceDto.buyer_address;
                adsInvoice.buyer_code = createInvoiceDto.buyer_code;
                adsInvoice.metadata = createInvoiceDto.metadata;

                // Update invoice_date with payment date if available
                if (latestPaymentDate) {
                    adsInvoice.invoice_date = latestPaymentDate;
                    this.logger.log(
                        `[createAdsCampaignInvoice] Updated existing invoice_date to payment date: ${latestPaymentDate.toISOString()} for campaign ${
                            adsCampaign.id
                        }`,
                    );
                }

                adsInvoice = await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).save(adsInvoice);
            } else {
                adsInvoice = await this.createAdsInvoiceRecord(createInvoiceDto, provinceId, latestPaymentDate);
            }

            adsInvoice = await this.publishInvoiceToMisa(adsInvoice, provinceId);

            // Upload PDF nếu thành công
            if (adsInvoice.status === EOrderInvoiceStatus.PUBLISHED) {
                const invoiceDetail = await this.misaHttpService.downloadInvoice([adsInvoice.transaction_id]);
                if (invoiceDetail.length > 0) {
                    const pdfUrl = await this.s3Service.uploadPublicFile(
                        `invoice/ads-campaign-${adsCampaign.id}.pdf`,
                        'application/pdf',
                        invoiceDetail[0],
                        undefined,
                        false,
                        false,
                    );
                    await this.updateInvoicePdfUrl(adsInvoice.id, pdfUrl.Location, provinceId);
                }
            }

            this.logger.log(`Successfully created ads campaign invoice for campaign ${adsCampaign.id}`);
            return adsInvoice;
        } catch (error) {
            this.logger.error(
                `Failed to create ads campaign invoice for campaign ${adsCampaign.id}: ${error.message} ${error.stack}`,
            );
            await this.markAdsInvoiceAsFailed(adsCampaign.id, error.message, provinceId);
            // Throw error để controller nhận được đúng error message
            throw error;
        }
    }

    private async findExistingAdsInvoice(adsCampaignId: number, provinceId: string): Promise<Invoice | null> {
        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId)
            .createQueryBuilder('invoice')
            .where('invoice.type = :type', { type: EInvoiceType.ADS_CAMPAIGN })
            .andWhere('invoice.ads_campaign_id = :campaignId', {
                campaignId: adsCampaignId,
            })
            .getOne();
    }

    private generateAdsRefId(adsCampaignId: number, provinceId: string): string {
        return `ADS-${provinceId}-${adsCampaignId}`;
    }

    private isAdsCampaignPaid(adsCampaign: AdsCampaign): boolean {
        console.log(`[isAdsCampaignPaid] Checking payment status for campaign ${adsCampaign.id}`);
        console.log(`[isAdsCampaignPaid] Campaign payments: `, adsCampaign.ads_payments);

        if (!adsCampaign.ads_payments || adsCampaign.ads_payments.length === 0) {
            return false;
        }

        const totalPaid = adsCampaign.ads_payments
            .filter((payment) => payment.status === 'paid')
            .reduce((sum, payment) => sum + payment.amount, 0);

        console.log(`[isAdsCampaignPaid] Total paid: ${totalPaid}, Campaign total: ${adsCampaign.total_price}`);
        return totalPaid >= adsCampaign.total_price;
    }

    private getAdsCampaignLatestPaymentDate(adsCampaign: AdsCampaign): Date | null {
        console.log(`[getAdsCampaignLatestPaymentDate] Getting latest payment date for campaign ${adsCampaign.id}`);

        if (!adsCampaign.ads_payments || adsCampaign.ads_payments.length === 0) {
            console.log(`[getAdsCampaignLatestPaymentDate] No payments found for campaign ${adsCampaign.id}`);
            return null;
        }

        // Filter successful payments and sort by updated_at descending
        const successfulPayments = adsCampaign.ads_payments
            .filter((payment) => payment.status === 'paid')
            .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());

        if (successfulPayments.length === 0) {
            console.log(
                `[getAdsCampaignLatestPaymentDate] No successful payments found for campaign ${adsCampaign.id}`,
            );
            return null;
        }

        const latestPayment = successfulPayments[0];
        const paymentDate = new Date(latestPayment.updated_at);

        console.log(
            `[getAdsCampaignLatestPaymentDate] Latest payment date for campaign ${
                adsCampaign.id
            }: ${paymentDate.toISOString()}`,
        );
        console.log(`[getAdsCampaignLatestPaymentDate] Payment details:`, {
            payment_id: latestPayment.id,
            amount: latestPayment.amount,
            payment_method: latestPayment.payment_method,
            status: latestPayment.status,
            updated_at: latestPayment.updated_at,
        });

        return paymentDate;
    }

    private async getAdsBuyerInfo(
        adsCampaign: AdsCampaign,
        provinceId: string,
    ): Promise<{
        buyer_name: string;
        buyer_tax_code?: string;
        buyer_id_number?: string;
        buyer_email?: string;
        buyer_phone?: string;
        buyer_address?: string;
        buyer_code?: string;
    }> {
        this.logger.log(`[getAdsBuyerInfo] ===== STARTING getAdsBuyerInfo for campaign ${adsCampaign?.id} =====`);
        // Lấy thông tin merchant (chủ nhà hàng) - merchant là người mua dịch vụ quảng cáo
        const restaurant = adsCampaign.restaurant;
        if (!restaurant) {
            throw new Error('Không tìm thấy thông tin restaurant cho chiến dịch quảng cáo');
        }

        try {
            // Sử dụng endpoint chuẩn để lấy merchant detail
            const merchantInfo = await this.getMerchantByRestaurant(restaurant?.id, provinceId);
            if (!merchantInfo) {
                this.logger.error(
                    `[getAdsBuyerInfo] No merchant found for restaurant ${restaurant?.id} - province ${provinceId}`,
                );
                throw new Error('Không tìm thấy thông tin merchant cho nhà hàng');
            }
            this.logger.log(
                `[getAdsBuyerInfo] Merchant info: ${JSON.stringify({
                    id: merchantInfo?.id,
                    business_type: merchantInfo.business_type,
                    real_name: merchantInfo.real_name,
                    personal_tax_code: merchantInfo.personal_tax_code,
                    companyProfile: merchantInfo.companyProfile,
                    merchantBusinessType: merchantInfo.merchantBusinessType,
                })}`,
            );

            if (merchantInfo) {
                // Kiểm tra business_type hợp lệ trước
                if (![1, 2, 3].includes(merchantInfo.business_type)) {
                    throw new Error(
                        `Business type không hợp lệ: ${merchantInfo.business_type}. Chỉ hỗ trợ business_type: 1 (Cá nhân), 2 (Hộ gia đình), 3 (Công ty/Doanh nghiệp).`,
                    );
                }

                // Lấy tax code theo business_type cụ thể
                let taxCode = '';
                let businessTypeName = '';

                if (merchantInfo.business_type === 3) {
                    // Công ty/Doanh nghiệp - Sử dụng company tax code
                    taxCode = merchantInfo.companyProfile?.tax_code || '';
                    businessTypeName = 'Công ty/Doanh nghiệp';
                    // Kiểm tra merchant phải có mã số thuế để xuất hóa đơn
                    if (!taxCode || taxCode.trim() === '') {
                        throw new Error(
                            `Merchant (${businessTypeName}) chưa cập nhật mã số thuế. Vui lòng cập nhật thông tin doanh nghiệp trước khi tạo hóa đơn.`,
                        );
                    }
                } else if (merchantInfo.business_type === 2) {
                    // Hộ gia đình - Sử dụng personal tax code
                    taxCode = merchantInfo.personal_tax_code || '';
                    businessTypeName = 'Hộ gia đình';
                } else if (merchantInfo.business_type === 1) {
                    // Cá nhân - Sử dụng personal tax code
                    taxCode = merchantInfo.personal_tax_code || '';
                    businessTypeName = 'Cá nhân';
                }

                // Lấy ID card cho merchant hộ gia đình (business_type = 2) hoặc cá nhân (business_type = 1)
                let buyerIdNumber = '';
                if (merchantInfo.business_type === 1 || merchantInfo.business_type === 2) {
                    // business_type = 1: Cá nhân
                    // business_type = 2: Hộ gia đình
                    // business_type = 3: Công ty/Doanh nghiệp (không cần ID card)
                    try {
                        const idCardInfo = await lastValueFrom(
                            await this.merchantIdCardCommandService.getMerchantIdCard(merchantInfo?.id),
                        );
                        if (idCardInfo?.id_card) {
                            buyerIdNumber = idCardInfo.id_card;
                            if (!buyerIdNumber || buyerIdNumber.trim() === '') {
                                this.logger.log(`[getAdsBuyerInfo] Merchant ${merchantInfo?.id} chưa cập nhật CCCD`);
                                throw new Error('Merchant chưa cập nhật CCCD');
                            }
                            const businessTypeName = merchantInfo.business_type === 1 ? 'Cá nhân' : 'Hộ gia đình';
                            this.logger.log(
                                `[getAdsBuyerInfo] Found ID card ${buyerIdNumber} for ${businessTypeName} merchant ${merchantInfo?.id}`,
                            );
                        }
                    } catch (error) {
                        this.logger.warn(
                            `[getAdsBuyerInfo] Could not get ID card for merchant ${merchantInfo?.id}: ${error.message}`,
                        );
                        // Không throw error, chỉ log warning vì ID card có thể không bắt buộc
                    }
                }

                // Phân biệt rõ ràng theo business_type để xác định buyerInfo
                let buyerInfo: {
                    buyer_name: string;
                    buyer_tax_code: string;
                    buyer_id_number: string;
                    buyer_email: string;
                    buyer_phone: string;
                    buyer_address: string;
                    buyer_code: string;
                };

                // Check if should use buyer_id_number instead of buyer_tax_code
                const useBuyerIdNumber = this.shouldUseBuyerIdNumberForAdsInvoice(provinceId);
                this.logger.log(
                    `[getAdsBuyerInfo] Using buyer_id_number instead of buyer_tax_code: ${useBuyerIdNumber} for ads campaign invoice ${adsCampaign?.id}`,
                );

                if (merchantInfo.business_type === 3) {
                    // Công ty/Doanh nghiệp - Sử dụng thông tin công ty
                    this.logger.log(`[getAdsBuyerInfo] Using COMPANY logic for business_type = 3`);
                    buyerInfo = {
                        buyer_name: merchantInfo.companyProfile?.name?.trim() || restaurant.name?.trim(),
                        // buyer_name: restaurant.name.trim(),
                        buyer_tax_code: taxCode,
                        buyer_id_number: buyerIdNumber,
                        buyer_email: merchantInfo.companyProfile?.email || merchantInfo.email || '',
                        buyer_phone: merchantInfo.companyProfile?.phone || restaurant.phone || restaurant.mobile || '',
                        buyer_address: merchantInfo.companyProfile?.address || restaurant.address || '',
                        buyer_code: restaurant.code || '',
                    };
                } else if (merchantInfo.business_type === 2) {
                    // Hộ gia đình - Sử dụng thông tin cá nhân + ID card
                    this.logger.log(`[getAdsBuyerInfo] Using HOUSEHOLD logic for business_type = 2`);
                    buyerInfo = {
                        // buyer_name: merchantInfo.real_name || restaurant.name || 'Household Business',
                        buyer_name: restaurant.name?.trim(),
                        buyer_tax_code: useBuyerIdNumber ? buyerIdNumber : taxCode,
                        buyer_id_number: buyerIdNumber,
                        buyer_email: merchantInfo.email || '',
                        buyer_phone: merchantInfo.phone || restaurant.phone || restaurant.mobile || '',
                        buyer_address: restaurant.address || '',
                        buyer_code: restaurant.code || '',
                    };
                } else if (merchantInfo.business_type === 1) {
                    // Cá nhân - Sử dụng thông tin cá nhân + ID card
                    this.logger.log(`[getAdsBuyerInfo] Using INDIVIDUAL logic for business_type = 1`);
                    buyerInfo = {
                        // buyer_name: merchantInfo.real_name || restaurant.name || 'Individual',
                        buyer_name: restaurant.name?.trim(),
                        buyer_tax_code: useBuyerIdNumber ? buyerIdNumber : taxCode,
                        buyer_id_number: buyerIdNumber,
                        buyer_email: merchantInfo.email || '',
                        buyer_phone: merchantInfo.phone || restaurant.phone || restaurant.mobile || '',
                        buyer_address: restaurant.address || '',
                        buyer_code: restaurant.code || '',
                    };
                }
                // Không có fallback case - đã kiểm tra business_type hợp lệ ở trên

                this.logger.log(
                    `[getAdsBuyerInfo] Buyer info for restaurant ${restaurant?.id}: ${JSON.stringify({
                        merchant_id: merchantInfo?.id,
                        business_type: merchantInfo.business_type,
                        business_type_name: businessTypeName,
                        has_company_profile: !!merchantInfo.companyProfile,
                        tax_code_source: merchantInfo.business_type === 3 ? 'company' : 'personal',
                        needs_id_card: merchantInfo.business_type === 1 || merchantInfo.business_type === 2,
                        has_id_card: !!buyerIdNumber,
                        buyer_name: buyerInfo.buyer_name,
                        buyer_tax_code: buyerInfo.buyer_tax_code,
                        buyer_id_number: buyerInfo.buyer_id_number,
                    })}`,
                );

                return buyerInfo;
            }
        } catch (error) {
            // Re-throw error nếu là lỗi validation tax_code
            if (error.message.includes('mã số thuế')) {
                throw error;
            }
            this.logger.error(`Error getting merchant info: ${error.message}`);
        }
    }

    /**
     * Tính tổng thuế từ tất cả items trong campaign
     */
    private calculateTotalTaxFromItems(adsTaxes: AdsTax[]): {
        total_campaign: number;
        total_order: number;
        vat_amount: number;
        vat_rate: number;
    } {
        const totalCampaign = adsTaxes.reduce((sum, tax) => sum + Number(tax.total_item), 0);
        const totalOrder = adsTaxes.reduce((sum, tax) => sum + Number(tax.total_order), 0);
        const vatAmount = adsTaxes.reduce((sum, tax) => sum + Number(tax.vat_amount), 0);

        // Tính VAT rate trung bình có trọng số (weighted average theo total_item)
        const totalWeight = adsTaxes.reduce((sum, tax) => sum + Number(tax.total_item), 0);
        const weightedVatRate =
            totalWeight > 0
                ? adsTaxes.reduce((sum, tax) => sum + Number(tax.vat_rate) * Number(tax.total_item), 0) / totalWeight
                : 0;

        return {
            total_campaign: totalCampaign,
            total_order: totalOrder,
            vat_amount: vatAmount,
            vat_rate: weightedVatRate, // VAT rate trung bình có trọng số
        };
    }

    private async createAdsInvoiceRecord(dto: any, provinceId: string, paymentDate?: Date): Promise<Invoice> {
        const campaignId = dto.ads_campaign_id;
        // Check if should use buyer_id_number instead of buyer_tax_code
        const useBuyerIdNumber = this.shouldUseBuyerIdNumberForAdsInvoice(provinceId);
        this.logger.log(
            `[createAdsInvoiceRecord] Using buyer_id_number instead of buyer_tax_code: ${useBuyerIdNumber} for ads campaign invoice ${campaignId}`,
        );

        // Use payment date if provided, otherwise use current date
        const invoiceDate = paymentDate || new Date();
        this.logger.log(
            `[createAdsInvoiceRecord] Using invoice_date: ${invoiceDate.toISOString()} for campaign ${campaignId} (payment date: ${
                paymentDate ? paymentDate.toISOString() : 'not provided'
            })`,
        );

        const adsInvoice = new Invoice({
            order_id: null,
            ads_campaign_id: campaignId,
            ref_id: dto.ref_id,
            buyer_name: dto.buyer_name,
            buyer_tax_code: dto.buyer_tax_code,
            buyer_id_number: dto.buyer_id_number || '',
            buyer_email: dto.buyer_email,
            buyer_phone: dto.buyer_phone,
            buyer_address: dto.buyer_address,
            buyer_code: dto.buyer_code,
            status: EOrderInvoiceStatus.PENDING,
            invoice_series: EMisaInvoiceSeries['1C25MVS'],
            invoice_date: invoiceDate,
            metadata: dto.metadata,
            name: dto.name,
            type: EInvoiceType.ADS_CAMPAIGN,
        });

        return await DatabaseService.getRepositoryByProvinceId(Invoice, provinceId).save(adsInvoice);
    }

    private async markAdsInvoiceAsFailed(
        adsCampaignId: number,
        errorMessage: string,
        provinceId: string,
    ): Promise<Invoice | null> {
        try {
            let existingInvoice = await this.findExistingAdsInvoice(adsCampaignId, provinceId);
            if (existingInvoice) {
                existingInvoice = await this.updateInvoiceStatus(
                    existingInvoice,
                    EOrderInvoiceStatus.FAILED,
                    provinceId,
                    errorMessage,
                );
                return existingInvoice;
            }
            return null;
        } catch (error) {
            this.logger.error(`Failed to mark ads invoice as failed for campaign ${adsCampaignId}: ${error.message}`);
        }
    }

    async getInvoiceByAdsCampaignId(adsCampaignId: number, provinceId: string): Promise<Invoice | null> {
        return await this.findExistingAdsInvoice(adsCampaignId, provinceId);
    }

    private mapAdsCampaignToMisaInvoice(adsInvoice: Invoice, provinceId: string): MisaAioInvoiceDataDto {
        const metadata = adsInvoice.metadata;

        // Check if should use buyer_id_number instead of buyer_tax_code
        const useBuyerIdNumber = this.shouldUseBuyerIdNumberForAdsInvoice(provinceId);
        this.logger.log(
            `[mapAdsCampaignToMisaInvoice] Using buyer_id_number instead of buyer_tax_code: ${useBuyerIdNumber} for ads campaign invoice ${adsInvoice.id}`,
        );

        // Lấy dữ liệu từ metadata mới
        const totalAmountWithoutVAT = metadata.info.total_amount_without_vat;
        const totalVATAmount = metadata.info.total_amount_vat;
        const totalAmount = metadata.info.total_amount;
        const vatRate = metadata.items[0]?.vat_rate;
        if (!vatRate) {
            throw new Error('Không tìm thấy VAT rate cho item đầu tiên trong metadata');
        }
        this.logger.log(
            `[mapAdsCampaignToMisaInvoice] Using VAT rate: ${vatRate}% for ads campaign invoice ${adsInvoice.id}`,
        );
        if (!totalAmountWithoutVAT || !totalVATAmount || !totalAmount) {
            throw new Error('Không tìm thấy thông tin tổng giá trị cho hóa đơn');
        }
        this.logger.log(
            `[mapAdsCampaignToMisaInvoice] Total amount without VAT: ${totalAmountWithoutVAT}, total VAT amount: ${totalVATAmount}, total amount: ${totalAmount} for ads campaign invoice ${adsInvoice.id}`,
        );

        // Tạo invoice details cho từng campaign item
        const invoiceDetails: MisaOriginalInvoiceDetailDto[] = this.createMisaInvoiceDetails(
            metadata,
            totalAmountWithoutVAT,
            totalVATAmount,
            vatRate,
        );

        // Tax rate info
        const taxRateInfo: MisaTaxRateInfoDto[] = [
            {
                VATRateName: `${vatRate}%`,
                AmountWithoutVATOC: totalAmountWithoutVAT,
                VATAmountOC: totalVATAmount,
            },
        ];

        return {
            RefID: adsInvoice.ref_id,
            InvSeries: adsInvoice.invoice_series,
            InvoiceName: 'Hóa đơn giá trị gia tăng khởi tạo từ máy tính tiền',
            InvDate: moment(adsInvoice.invoice_date).format('YYYY-MM-DD'),
            CurrencyCode: 'VND',
            ExchangeRate: 1,
            PaymentMethodName: 'TM/CK',

            BuyerLegalName: adsInvoice.buyer_name,
            BuyerTaxCode: adsInvoice.buyer_tax_code,
            BuyerIDNumber: adsInvoice.buyer_id_number || '', // ID card number for non-company merchants
            BuyerEmail: adsInvoice.buyer_email,
            BuyerPhoneNumber: adsInvoice.buyer_phone,
            // BuyerFullName: adsInvoice.buyer_name,
            BuyerAddress: adsInvoice.buyer_address,
            BuyerCode: adsInvoice.buyer_code,

            TotalSaleAmountOC: totalAmountWithoutVAT,
            TotalAmountWithoutVATOC: totalAmountWithoutVAT,
            TotalVATAmountOC: totalVATAmount,
            TotalDiscountAmountOC: 0,
            TotalAmountOC: totalAmount,
            TotalSaleAmount: totalAmountWithoutVAT,
            TotalAmountWithoutVAT: totalAmountWithoutVAT,
            TotalVATAmount: totalVATAmount,
            TotalDiscountAmount: 0,
            TotalAmount: totalAmount,
            TotalAmountInWords: this.convertNumberToWords(totalAmount),

            IsSendEmail: adsInvoice.buyer_email ? true : false,
            ReceiverEmail: adsInvoice.buyer_email,

            OriginalInvoiceDetail: invoiceDetails,
            TaxRateInfo: taxRateInfo,

            OptionUserDefined: {},
        };
    }

    private async getMerchantByRestaurant(restaurantId: number, provinceId: string): Promise<any | null> {
        try {
            // Bước 1: Lấy danh sách merchant IDs từ restaurant (giống endpoint /api/restaurants/:id/merchant-account)
            const allMerchants = (await lastValueFrom(
                await this.merchantCommandService.findMerchantByRestaurantIdAndProvinceId(
                    restaurantId,
                    parseInt(provinceId),
                ),
            )) as any[];

            if (!allMerchants || allMerchants.length === 0) {
                this.logger.warn(`No merchants found for restaurant ${restaurantId}`);
                return null;
            }

            // Bước 2: Tìm owner merchant ID
            let ownerMerchantId = null;

            // Ưu tiên merchant owner đang active
            const activeOwner = allMerchants.find((merchant) =>
                merchant.merchantHasRoles?.some(
                    (mhr: any) => mhr.role?.role === EMerchantRole.OWNER && mhr.is_active === 1,
                ),
            );

            if (activeOwner) {
                ownerMerchantId = activeOwner.id;
            } else {
                // Nếu không có owner active, lấy owner bất kỳ (có thể inactive)
                const anyOwner = allMerchants.find((merchant) =>
                    merchant.merchantHasRoles?.some((mhr: any) => mhr.role?.role === EMerchantRole.OWNER),
                );
                if (anyOwner) {
                    ownerMerchantId = anyOwner.id;
                }
            }

            if (!ownerMerchantId) {
                this.logger.warn(`No owner merchant found for restaurant ${restaurantId}`);
                return null;
            }

            // Bước 3: Sử dụng CHÍNH XÁC endpoint chuẩn để lấy merchant detail
            // Giống như endpoint /api/merchants/self/:merchantId
            const merchantDetail = await lastValueFrom(
                await this.merchantCommandService.getDetailById(ownerMerchantId),
            );

            this.logger.log(
                `[getMerchantByRestaurant] Found owner merchant ${ownerMerchantId} for restaurant ${restaurantId}`,
            );
            return merchantDetail;
        } catch (error) {
            this.logger.error(
                `[getMerchantByRestaurant] Error getting merchant for restaurant ${restaurantId}: ${error.message}`,
                error.stack,
            );
            return null;
        }
    }

    private createAdsCampaignInvoiceItems(adsCampaign: AdsCampaign, adsTaxes: AdsTax[]): any[] {
        const items = [];

        // Kiểm tra campaign có items không
        if (!adsCampaign.ads_items || adsCampaign.ads_items.length === 0) {
            throw new Error('Campaign không có items để tạo hóa đơn');
        }

        // Tạo map để lookup tax theo item ID
        const taxMap = new Map<number, AdsTax>();
        adsTaxes.forEach((tax) => {
            taxMap.set(tax.ads_campaign_item_id, tax);
        });

        // Tạo item cho mỗi campaign item với thuế riêng biệt
        adsCampaign.ads_items.forEach((campaignItem: any) => {
            // Ưu tiên sử dụng tax_info từ campaign item nếu có
            let itemTax = null;

            if (campaignItem.tax_info) {
                // Sử dụng tax info đã được load từ getDetails
                itemTax = campaignItem.tax_info;
                console.log(
                    `[createAdsCampaignInvoiceItems] Using tax_info from campaign item ${campaignItem.id}:`,
                    itemTax,
                );
            } else {
                // Fallback: lookup từ adsTaxes
                itemTax = taxMap.get(campaignItem.id);
                console.log(
                    `[createAdsCampaignInvoiceItems] Using tax from adsTaxes for item ${campaignItem.id}:`,
                    itemTax,
                );
            }

            if (!itemTax) {
                throw new Error(`Không tìm thấy thông tin thuế cho item ${campaignItem.id}`);
            }

            const invoiceItem = {
                name: this.getAdsCampaignServiceName(campaignItem, adsCampaign.id),
                code: this.getAdsCampaignItemCode(campaignItem),
                quantity: 1,
                amount_without_vat: Number(itemTax.total_order),
                amount: Number(itemTax.total_item),
                vat_rate: Number(itemTax.vat_rate),
                vat_amount: Number(itemTax.vat_amount),
            };

            console.log(
                `[createAdsCampaignInvoiceItems] Invoice item for campaign item ${campaignItem.id}:`,
                invoiceItem,
            );
            items.push(invoiceItem);
        });

        return items;
    }

    private getAdsCampaignServiceName(campaignItem: any, _campaignId: number): string {
        // Chỉ dùng category_code, báo lỗi nếu không có
        if (!campaignItem.adCategory || !campaignItem.adCategory.code) {
            throw new Error('Campaign item phải có category_code để tạo hóa đơn');
        }

        const baseServiceName = `Dịch vụ quảng cáo Vill Media -`;

        // Map tên dịch vụ dựa trên category_code (16 codes)
        switch (campaignItem.adCategory.code) {
            case EAdsCategoryCode.category_ad_page:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_TOP}`;
            case EAdsCategoryCode.search_ad_page:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_TOP}`;
            case EAdsCategoryCode.home_banner:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_BANNER}`;
            case EAdsCategoryCode.category_banner:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_BANNER}`;
            case EAdsCategoryCode.cate_ads_banner_follow_order:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_BANNER}`;
            case EAdsCategoryCode.category_icon:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_ICON}`;
            case EAdsCategoryCode.news_feed_ad_page:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_NEWFEED}`;
            case EAdsCategoryCode.newsfeed_banner:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_NEWFEED}`;
            case EAdsCategoryCode.food_notification:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_PUSH_NOTIFICATION}`;
            case EAdsCategoryCode.push_notification:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_PUSH_NOTIFICATION}`;
            case EAdsCategoryCode.app_notification:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_PUSH_NOTIFICATION}`;
            case EAdsCategoryCode.pin_google_map:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_GGMAP}`;
            case EAdsCategoryCode.facebook_PR:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_PR_FACEBOOK}`;
            case EAdsCategoryCode.collection_popup:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_POPUP}`;
            case EAdsCategoryCode.top_rating:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_CHUANVILL}`;
            case EAdsCategoryCode.video_ads:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_VIDEO}`;
            case EAdsCategoryCode.freeship_5k_fl_40k:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_MERFREESHIP}`;
            case EAdsCategoryCode.freeship_10k_fl_60k:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_MERFREESHIP}`;
            case EAdsCategoryCode.freeship_12k_fl_80k:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_MERFREESHIP}`;
            case EAdsCategoryCode.freeship_15k_fl_100k:
                return `${baseServiceName} ${EInvoiceItemCode.ADS_MERFREESHIP}`;
            default:
                throw new Error(`Không hỗ trợ category_code: ${campaignItem.adCategory.code}`);
        }
    }

    private getAdsCampaignItemCode(campaignItem: any): EInvoiceItemCode {
        // Chỉ dùng category_code, báo lỗi nếu không có
        if (!campaignItem.adCategory || !campaignItem.adCategory.code) {
            throw new Error('Campaign item phải có category_code để tạo hóa đơn');
        }

        // Map 1:1 với EAdCategoryCode (16 codes)
        switch (campaignItem.adCategory.code) {
            case EAdsCategoryCode.category_ad_page:
                return EInvoiceItemCode.ADS_TOP;
            case EAdsCategoryCode.search_ad_page:
                return EInvoiceItemCode.ADS_TOP;
            case EAdsCategoryCode.home_banner:
                return EInvoiceItemCode.ADS_BANNER;
            case EAdsCategoryCode.category_banner:
                return EInvoiceItemCode.ADS_BANNER;
            case EAdsCategoryCode.cate_ads_banner_follow_order:
                return EInvoiceItemCode.ADS_BANNER;
            case EAdsCategoryCode.category_icon:
                return EInvoiceItemCode.ADS_ICON;
            case EAdsCategoryCode.news_feed_ad_page:
                return EInvoiceItemCode.ADS_NEWFEED;
            case EAdsCategoryCode.newsfeed_banner:
                return EInvoiceItemCode.ADS_NEWFEED;
            case EAdsCategoryCode.food_notification:
                return EInvoiceItemCode.ADS_PUSH_NOTIFICATION;
            case EAdsCategoryCode.push_notification:
                return EInvoiceItemCode.ADS_PUSH_NOTIFICATION;
            case EAdsCategoryCode.app_notification:
                return EInvoiceItemCode.ADS_PUSH_NOTIFICATION;
            case EAdsCategoryCode.pin_google_map:
                return EInvoiceItemCode.ADS_GGMAP;
            case EAdsCategoryCode.facebook_PR:
                return EInvoiceItemCode.ADS_PR_FACEBOOK;
            case EAdsCategoryCode.collection_popup:
                return EInvoiceItemCode.ADS_POPUP;
            case EAdsCategoryCode.top_rating:
                return EInvoiceItemCode.ADS_CHUANVILL;
            case EAdsCategoryCode.video_ads:
                return EInvoiceItemCode.ADS_VIDEO;
            case EAdsCategoryCode.freeship_5k_fl_40k:
                return EInvoiceItemCode.ADS_MERFREESHIP;
            case EAdsCategoryCode.freeship_10k_fl_60k:
                return EInvoiceItemCode.ADS_MERFREESHIP;
            case EAdsCategoryCode.freeship_12k_fl_80k:
                return EInvoiceItemCode.ADS_MERFREESHIP;
            case EAdsCategoryCode.freeship_15k_fl_100k:
                return EInvoiceItemCode.ADS_MERFREESHIP;
            default:
                throw new Error(`Không hỗ trợ category_code: ${campaignItem.adCategory.code}`);
        }
    }

    private createMisaInvoiceDetails(
        metadata: any,
        totalAmountWithoutVAT: number,
        totalVATAmount: number,
        vatRate: number,
    ): any[] {
        const invoiceDetails = [];

        // Kiểm tra metadata có items không
        if (!metadata.items || metadata.items.length === 0) {
            throw new Error('Metadata không có items để tạo MISA invoice details');
        }

        metadata.items.forEach((item: any, index: number) => {
            // Sử dụng giá trị thực tế từ item
            const itemAmountWithoutVAT = item.amount_without_vat || 0;
            const itemVATAmount = item.vat_amount || 0;
            const itemVATRate = item.vat_rate || vatRate;

            console.log(
                `[createMisaInvoiceDetails] Item ${index + 1} (${
                    item.code
                }): amount_without_vat=${itemAmountWithoutVAT}, vat_amount=${itemVATAmount}, vat_rate=${itemVATRate}`,
            );
            this.logger.log(
                `[createMisaInvoiceDetails] Item ${index + 1} (${
                    item.code
                }): amount_without_vat=${itemAmountWithoutVAT}, vat_amount=${itemVATAmount}, vat_rate=${itemVATRate}`,
            );
            if (itemVATRate > 8) {
                this.logger.log(`VAT rate for item ${item.code} is greater than 8%`);
                throw new Error(`VAT rate for item ${item.code} is greater than 8%`);
            }

            invoiceDetails.push({
                ItemType: 1,
                LineNumber: index + 1,
                SortOrder: index + 1,
                ItemCode:
                    item.code ||
                    (() => {
                        throw new Error('Item phải có code để tạo MISA invoice detail');
                    })(),
                ItemName:
                    item.name ||
                    (() => {
                        throw new Error('Item phải có name để tạo MISA invoice detail');
                    })(),
                UnitName: 'Gói',
                Quantity: 1,
                UnitPrice: itemAmountWithoutVAT,
                DiscountRate: 0,
                DiscountAmountOC: 0,
                DiscountAmount: 0,
                AmountOC: itemAmountWithoutVAT,
                Amount: itemAmountWithoutVAT,
                AmountWithoutVATOC: itemAmountWithoutVAT,
                AmountWithoutVAT: itemAmountWithoutVAT,
                VATRateName: `${itemVATRate}%`,
                VATAmountOC: itemVATAmount,
                VATAmount: itemVATAmount,
            });
        });
        console.log(`[createMisaInvoiceDetails] Invoice details:`, invoiceDetails);

        return invoiceDetails;
    }

    async retryAdsCampaignInvoice(
        adsCampaignId: number,
        provinceId: string,
        buyerInfoOverride?: any,
    ): Promise<Invoice> {
        this.logger.log(`Retrying ads campaign invoice for campaign ${adsCampaignId} in province ${provinceId}`);

        try {
            // Lấy thông tin campaign
            const adsCampaign = await DatabaseService.getRepositoryByProvinceId(AdsCampaign, provinceId)
                .createQueryBuilder('campaign')
                .leftJoinAndSelect('campaign.ads_items', 'ads_items')
                .leftJoinAndSelect('ads_items.adCategory', 'adCategory')
                .leftJoinAndSelect('campaign.ads_payments', 'ads_payments')
                .leftJoinAndSelect('campaign.restaurant', 'restaurant')
                .where('campaign.id = :id', { id: adsCampaignId })
                .getOne();

            if (!adsCampaign) {
                throw new Error(`Ads campaign ${adsCampaignId} not found`);
            }

            // Tạo lại hóa đơn (sẽ update existing failed invoice)
            const invoice = await this.createAdsCampaignInvoice(adsCampaign, provinceId, buyerInfoOverride);

            this.logger.log(`Successfully retried ads campaign invoice for campaign ${adsCampaignId}`);
            return invoice;
        } catch (error) {
            this.logger.error(`Failed to retry ads campaign invoice for campaign ${adsCampaignId}: ${error.message}`);
            throw error;
        }
    }
}
