// import { Injectable } from '@nestjs/common';
// import { Request } from 'express';
// import { LoggerService } from 'src/common/logger/logger.service';
// import { ScoringHistory } from 'src/entities/driverScoringHistory.entity';
// import { Order } from 'src/entities/order.entity';
// import { AppSettingService } from 'src/models/appSetting/appSetting.service';
// import { DatabaseService } from 'src/providers/database/database.service';
// import {
//     IOrderScoringContext,
//     LeaderBoardService,
//     LeaderboardType,
//     ScoringHistoryService,
//     ScoringRuleEngine,
// } from '../../driverScoring';
// import { Driver } from 'src/entities/driver.entity';
// import { EPointType } from 'src/entities/types/EScoringRule.enum';

// @Injectable()
// export class OrderScoringService {
//     constructor(
//         private readonly loggerService: LoggerService,
//         private readonly leaderBoardService: LeaderBoardService,
//         private readonly scoringRuleEngine: ScoringRuleEngine,
//         private readonly scoringHistoryService: ScoringHistoryService,
//         private readonly appSettingService: AppSettingService,
//     ) {}

//     /**
//      * Process scoring for completed orders
//      * Only keeps one record per order - deletes old records and creates new ones
//      * @param createSingleRecord - If true, creates one consolidated record instead of one per rule
//      * @param forceProcess - If true, bypasses duplicate processing check
//      */
//     async processOrderScoring(order: Order, provinceId: string): Promise<void> {
//         const startTime = Date.now();

//         // ----- Duplicate-processing guard ----------------------------------
//         try {
//             const isDriverRankingEnabled = await this.appSettingService.getDriverRankingEnabled(provinceId);
//             if (!isDriverRankingEnabled) {
//                 this.loggerService.debug(`Driver ranking is disabled for province ${provinceId}, skipping scoring`);
//                 return;
//             }
//             const driver = await DatabaseService.getEntityManagerByProvinceId(provinceId).findOne(Driver, {
//                 where: { user_id: order.driver_id },
//                 select: {
//                     id: true,
//                     user_id: true,
//                     province_id: true,
//                     is_active: true,
//                 },
//             });

//             const connection = DatabaseService.getConnectionByProvinceId(provinceId);
//             const queryRunner = connection.createQueryRunner();
//             await queryRunner.connect();
//             await queryRunner.startTransaction();

//             try {
//                 // Step 1: Get existing scoring records for this order to calculate points to subtract
//                 const existingRecords = await queryRunner.manager
//                     .createQueryBuilder(ScoringHistory, 'sh')
//                     .where('sh.source_id = :orderId', { orderId: order.id })
//                     .getMany();

//                 // Step 2: Delete existing records for this order
//                 if (existingRecords.length > 0) {
//                     await queryRunner.manager
//                         .createQueryBuilder()
//                         .delete()
//                         .from(ScoringHistory)
//                         .where('source_id = :orderId', { orderId: order.id })
//                         .execute();

//                     this.loggerService.debug(
//                         `Deleted ${existingRecords.length} old scoring records for order ${order.id}`,
//                     );
//                 }

//                 // Step 3: Calculate new scoring
//                 const scoringContext: IOrderScoringContext = {
//                     order: order,
//                     driver_id: driver.id,
//                     province_id: parseInt(provinceId),
//                 };

//                 const scoringResult = await this.scoringRuleEngine.evaluateOrderScoring(scoringContext);
//                 const newTotalPoints = scoringResult.total_points;

//                 // Step 4: Insert new scoring records
//                 const newRecords: ScoringHistory[] = [];
//                 if (newTotalPoints > 0) {
//                     if (createSingleRecord) {
//                         // Create one consolidated record
//                         const applicableRules = scoringResult.rules_evaluated.filter(
//                             (rule) => rule.conditions_met && rule.points_awarded !== 0,
//                         );

//                         if (applicableRules.length > 0) {
//                             const consolidatedRuleName = applicableRules.map((r) => r.rule.name).join(', ');

//                             const consolidatedDetails = {
//                                 rule_type: 'CONSOLIDATED',
//                                 conditions_met: [],
//                                 calculation_method: 'CONSOLIDATED',
//                                 input_values: { total_rules_applied: applicableRules.length },
//                                 formula_used: `Total from ${applicableRules.length} rules`,
//                                 result: newTotalPoints,
//                                 timestamp: new Date().toISOString(),
//                                 rules_breakdown: applicableRules.map((r) => ({
//                                     rule_id: r.rule.id,
//                                     rule_name: r.rule.name,
//                                     points: r.points_awarded,
//                                 })),
//                             };

//                             this.loggerService.debug(
//                                 `Creating consolidated ScoringHistory record with ${newTotalPoints} total points from ${applicableRules.length} rules`,
//                             );

//                             const record = new ScoringHistory({
//                                 source_id: order.id,
//                                 driver_id: driver.id,
//                                 points_type: EPointType.ADD,
//                                 rule_id: applicableRules[0].rule.id, // Use first rule's ID
//                                 rule_name: consolidatedRuleName,
//                                 source_type: applicableRules[0].rule.source_type,
//                                 points_awarded: newTotalPoints,
//                                 calculation_details: JSON.stringify(consolidatedDetails),
//                             });

//                             newRecords.push(record);
//                         }
//                     } else {
//                         // Original logic: Create one record per rule
//                         for (const ruleEvaluation of scoringResult.rules_evaluated) {
//                             console.log(ruleEvaluation);
//                             if (ruleEvaluation.conditions_met && ruleEvaluation.points_awarded !== 0) {
//                                 const record = new ScoringHistory({
//                                     source_id: order.id,
//                                     driver_id: driver.id,
//                                     rule_id: ruleEvaluation.rule.id,
//                                     rule_name: ruleEvaluation.rule.name,
//                                     source_type: ruleEvaluation.rule.source_type,
//                                     points_type: ruleEvaluation.points_type,
//                                     points_awarded: ruleEvaluation.points_awarded,
//                                     calculation_details: ruleEvaluation.calculation_details,
//                                 });

//                                 newRecords.push(record);
//                             } else {
//                                 this.loggerService.debug(
//                                     `Skipping rule: ${ruleEvaluation.rule.name} (ID: ${ruleEvaluation.rule.id}), ConditionsMet=${ruleEvaluation.conditions_met}, Points=${ruleEvaluation.points_awarded}`,
//                                 );
//                             }
//                         }
//                     }

//                     if (newRecords.length > 0) {
//                         await queryRunner.manager.save(newRecords);
//                         this.loggerService.debug(
//                             `Saved ${newRecords.length} new scoring records for order ${order.id}`,
//                         );
//                     }
//                 }

//                 // Step 5: Update leaderboard with the difference and get tracking info

//                 let trackingResult = null;
//                 // Only update leaderboard if there's a points difference
//                 if (newTotalPoints !== 0) {
//                     const provinceIdNumber = parseInt(provinceId);

//                     // Update all leaderboard types with points difference (not total points)
//                     trackingResult = await this.updateLeaderboardsWithRetry(
//                         provinceIdNumber,
//                         driver.id,
//                         newTotalPoints,
//                         order.id,
//                     );

//                     this.loggerService.debug(
//                         `Updated leaderboards with ${newTotalPoints} points difference for driver ${driver.id}`,
//                     );
//                 } else {
//                     this.loggerService.debug(
//                         `No leaderboard update needed for order ${order.id} - points difference is 0`,
//                     );
//                 }

//                 // Step 6: Update scoring history records with points tracking info
//                 if (newRecords.length > 0 && trackingResult) {
//                     for (const record of newRecords) {
//                         record.points_before = trackingResult.points_before;
//                         record.points_after = trackingResult.points_after;
//                         record.driver_score_id = trackingResult.driverScore.id;
//                     }
//                     // Re-save records with updated tracking info
//                     await queryRunner.manager.save(newRecords);
//                     this.loggerService.debug(`Updated ${newRecords.length} scoring records with points tracking info`);
//                 }
//                 // }

//                 await queryRunner.commitTransaction();

//                 // Log detailed scoring information
//                 if (newTotalPoints > 0) {
//                     const rulesSummary = scoringResult.rules_evaluated
//                         .filter((rule) => rule.conditions_met && rule.points_awarded > 0)
//                         .map((rule) => `${rule.rule.name}: ${rule.points_awarded} points`)
//                         .join(', ');

//                     const executionTime = Date.now() - startTime;

//                     this.loggerService.log(
//                         `Successfully processed scoring for order ${
//                             order.id
//                         }: ${newTotalPoints} points awarded to driver ${
//                             driver.id
//                         } in ${executionTime}ms. Rules applied: ${rulesSummary || 'None'}. Database and cache updated.`,
//                     );
//                 } else {
//                     const executionTime = Date.now() - startTime;
//                     this.loggerService.debug(
//                         `No points awarded for order ${order.id} - no applicable scoring rules found in ${executionTime}ms.`,
//                     );
//                 }

//                 // Log leaderboard update metrics for monitoring
//                 if (newTotalPoints !== 0) {
//                     this.loggerService.log(
//                         `Leaderboard update completed for order ${order.id}: Driver ${driver.id} received ${newTotalPoints} points in province ${provinceId}. Database-based leaderboard system active.`,
//                     );
//                 }
//             } catch (transactionError) {
//                 await queryRunner.rollbackTransaction();
//                 throw transactionError;
//             } finally {
//                 await queryRunner.release();
//             }
//         } catch (error) {
//             const executionTime = Date.now() - startTime;

//             this.loggerService.error(
//                 `Error processing scoring for order ${order.id} after ${executionTime}ms: ${error.message}`,
//                 error.stack,
//             );

//             // Log metrics for monitoring
//             this.loggerService.warn(
//                 `Scoring failure metrics - Order: ${order.id},  Province: ${provinceId}, Error: ${error.constructor.name}`,
//             );

//             // Don't throw error to avoid affecting order completion flow
//         }
//     }

//     async recalculateOrderScoring(
//         ctx: Request,
//         orderId: number,
//         provinceId: string,
//         forceRecalculation: boolean = false,
//     ): Promise<void> {
//         try {
//             this.loggerService.debug(`Recalculating scoring for order ${orderId} (force: ${forceRecalculation})`);

//             // Get the order with necessary relations
//             const order = await DatabaseService.getRepository(Order, ctx).findOne({
//                 where: { id: orderId },
//                 relations: ['driver', 'payment', 'status'],
//             });

//             if (!order) {
//                 this.loggerService.warn(`Order ${orderId} not found for scoring recalculation`);
//                 return;
//             }

//             const driver = await DatabaseService.getEntityManagerByProvinceId(provinceId).findOne(Driver, {
//                 where: { user_id: order.driver_id },
//                 select: {
//                     id: true,
//                     user_id: true,
//                     province_id: true,
//                     is_active: true,
//                 },
//             });

//             if (!driver) {
//                 this.loggerService.warn(
//                     `Driver ${order.driver_id} not found for province ${provinceId}, skipping scoring recalculation`,
//                 );
//                 return;
//             }

//             // Process scoring (this will handle delete old + insert new automatically)
//             await this.processOrderScoring(order, provinceId, false);
//         } catch (error) {
//             this.loggerService.error(`Error recalculating scoring for order ${orderId}: ${error.message}`, error.stack);
//             // Don't throw to avoid affecting main order flow
//         }
//     }

//     /**
//      * Update leaderboards with retry logic for better reliability
//      * Returns tracking information for all leaderboard types in JSON format
//      */
//     private async updateLeaderboardsWithRetry(
//         provinceId: number,
//         driverId: number,
//         pointsDifference: number,
//         orderId: number,
//         maxRetries: number = 3,
//     ): Promise<any> {
//         const leaderboardTypes = [LeaderboardType.DAILY, LeaderboardType.WEEKLY, LeaderboardType.MONTHLY];
//         const trackingResults: any = {};

//         for (const type of leaderboardTypes) {
//             let retryCount = 0;
//             let success = false;

//             while (retryCount < maxRetries && !success) {
//                 try {
//                     // Use tracking method for all leaderboard types to get complete points info
//                     const result = await this.leaderBoardService.addPointsWithTracking(
//                         type,
//                         provinceId,
//                         driverId,
//                         pointsDifference,
//                     );
//                     trackingResults[type.toLowerCase()] = result;
//                     success = true;
//                     this.loggerService.debug(
//                         `Successfully updated ${type} leaderboard for driver ${driverId} with ${pointsDifference} points`,
//                     );
//                 } catch (error) {
//                     retryCount++;
//                     this.loggerService.warn(
//                         `Failed to update ${type} leaderboard for driver ${driverId} (attempt ${retryCount}/${maxRetries}): ${error.message}`,
//                     );

//                     if (retryCount >= maxRetries) {
//                         this.loggerService.error(
//                             `Failed to update ${type} leaderboard for driver ${driverId} after ${maxRetries} attempts. Order: ${orderId}`,
//                             error.stack,
//                         );
//                         // Don't throw error to avoid affecting order completion flow
//                         // But log it for monitoring and manual intervention if needed
//                     } else {
//                         // Wait before retry (exponential backoff)
//                         const delay = Math.pow(2, retryCount - 1) * 1000; // 1s, 2s, 4s...
//                         await new Promise((resolve) => setTimeout(resolve, delay));
//                     }
//                 }
//             }
//         }

//         // Prepare JSON format for points tracking
//         const pointsBeforeJson = {
//             daily: trackingResults.daily?.points_before || 0,
//             weekly: trackingResults.weekly?.points_before || 0,
//             monthly: trackingResults.monthly?.points_before || 0,
//         };

//         const pointsAfterJson = {
//             daily: trackingResults.daily?.points_after || 0,
//             weekly: trackingResults.weekly?.points_after || 0,
//             monthly: trackingResults.monthly?.points_after || 0,
//         };

//         return {
//             points_before: pointsBeforeJson,
//             points_after: pointsAfterJson,
//             driverScore: trackingResults.daily?.driverScore || null, // Use daily as primary reference
//         };
//     }

//     /**
//      * Get current scoring total for an order
//      */
//     async getOrderScoringTotal(ctx: Request, orderId: number): Promise<number> {
//         try {
//             return await this.scoringHistoryService.getSourceScoringTotal(ctx, orderId);
//         } catch (error) {
//             this.loggerService.error(`Error getting scoring total for order ${orderId}: ${error.message}`);
//             return 0;
//         }
//     }
// }
