import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { IInvoiceMetadata } from 'src/entities/invoice.entity';

export class CreateOrderInvoiceDto {
    @IsNotEmpty()
    @IsNumber()
    order_id: number;

    @IsNotEmpty()
    @IsString()
    ref_id: string;

    @IsNotEmpty()
    @IsString()
    inv_date: string;

    @IsOptional()
    @IsString()
    buyer_name?: string;

    @IsOptional()
    @IsString()
    buyer_tax_code?: string;

    @IsOptional()
    @IsString()
    buyer_id_number?: string;

    @IsOptional()
    @IsString()
    buyer_address?: string;

    @IsOptional()
    @IsString()
    buyer_email?: string;

    @IsOptional()
    @IsString()
    buyer_phone?: string;

    @IsOptional()
    @IsString()
    transaction_id?: string;

    @IsOptional()
    metadata?: IInvoiceMetadata;

    @IsOptional()
    @IsString()
    name?: string;
}
