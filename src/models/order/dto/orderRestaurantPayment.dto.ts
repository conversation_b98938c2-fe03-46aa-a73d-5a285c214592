import { Transform, TransformFnParams } from "class-transformer";
import { IsDateString, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min, ValidateIf } from "class-validator";
import { SortedByEnum } from "src/common/pipes/global.dto";
import * as _ from 'lodash';
import { isNil } from "lodash";
import { EOrderRestaurantPaymentStatus, EOrderRestaurantPaymentType } from "src/entities/orderRestaurantPayments.entity";

export class GetOrderRestaurantPaymentListDto {
    @IsOptional()
    @IsString()
    orderBy = 'order_id';

    @Transform(({ value }) => (!_.isEmpty(value) ? _.upperCase(value) : 'DESC'), { toClassOnly: true })
    sortedBy: SortedByEnum = SortedByEnum.DESC;

    @IsOptional()
    @Transform(({ value }: TransformFnParams) => _.toNumber(value))
    @IsNumber()
    @Min(1)
    limit = 20;

    @IsOptional()
    @Transform(({ value }) => _.toNumber(value))
    @IsNumber()
    @Min(0)
    page = 0;

    @IsOptional()
    @IsDateString()
    fromOrderDate: string;

    @IsOptional()
    @IsDateString()
    toOrderDate: string;

    @IsOptional()
    @IsEnum(EOrderRestaurantPaymentStatus)
    status: EOrderRestaurantPaymentStatus;

    @IsOptional()
    @IsEnum(EOrderRestaurantPaymentStatus)
    reversalStatus: EOrderRestaurantPaymentStatus;

    @ValidateIf((object, value) => !isNil(value))
    @IsNumber()
    @Transform(({ value }) => _.toNumber(value))
    restaurantId: number;

    @IsOptional()
    @IsEnum(EOrderRestaurantPaymentType)
    paymentMethod: EOrderRestaurantPaymentType;
}

export class GetOrderRestaurantPaymentSummaryDto {
    @IsNotEmpty()
    @IsString()
    fromDate: string;

    @IsNotEmpty()
    @IsString()
    toDate: string;
}
