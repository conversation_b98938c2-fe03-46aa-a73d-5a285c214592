import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { IInvoiceMetadata } from 'src/entities/invoice.entity';

export class BuyerInfoOverrideDto {
    @IsOptional()
    @IsString()
    buyer_name?: string;

    @IsOptional()
    @IsString()
    buyer_tax_code?: string;

    @IsOptional()
    @IsString()
    buyer_id_number?: string;

    @IsOptional()
    @IsString()
    buyer_address?: string;

    @IsOptional()
    @IsString()
    buyer_email?: string;

    @IsOptional()
    @IsString()
    buyer_phone?: string;

    @IsOptional()
    @IsString()
    buyer_code?: string;
}

export class CreateAdsCampaignInvoiceRequestDto {
    @IsOptional()
    @ValidateNested()
    @Type(() => BuyerInfoOverrideDto)
    buyer_info?: BuyerInfoOverrideDto;
}

export class CreateAdsInvoiceDto {
    @IsNotEmpty()
    @IsNumber()
    ads_campaign_id: number;

    @IsNotEmpty()
    @IsString()
    ref_id: string;

    @IsOptional()
    @IsString()
    buyer_name?: string;

    @IsOptional()
    @IsString()
    buyer_tax_code?: string;

    @IsOptional()
    @IsString()
    buyer_id_number?: string;

    @IsOptional()
    @IsString()
    buyer_address?: string;

    @IsOptional()
    @IsString()
    buyer_email?: string;

    @IsOptional()
    @IsString()
    buyer_phone?: string;

    @IsOptional()
    @IsString()
    buyer_code?: string;

    @IsOptional()
    @IsString()
    transaction_id?: string;

    @IsOptional()
    metadata?: IInvoiceMetadata;

    @IsOptional()
    @IsString()
    name?: string;
}

export class AdsCampaignInvoiceResponseDto {
    success: boolean;
    message: string;
    invoice?: {
        id: number;
        ref_id: string;
        status: string;
        transaction_id?: string;
        invoice_number?: string;
        pdf_url?: string;
        buyer_name: string;
        buyer_tax_code?: string;
        total_amount: number;
        created_at: Date;
    };
}

export class AdsCampaignInvoicePreviewResponseDto {
    success: boolean;
    message: string;
    data: {
        can_create_invoice: boolean;
        reason?: string;
        existing_invoice?: {
            id: number;
            ref_id: string;
            status: string;
            invoice_number?: string;
            created_at: Date;
        };
        preview_data?: {
            ref_id: string;
            buyer_info: {
                buyer_name: string;
                buyer_tax_code?: string;
                buyer_id_number?: string;
                buyer_email?: string;
                buyer_phone?: string;
                buyer_address?: string;
                buyer_code?: string;
            };
            campaign_info: {
                id: number;
                name: string;
                created_at: Date;
            };
            tax_summary: {
                total_amount: number;
                total_amount_without_vat: number;
                total_amount_vat: number;
                vat_rate: number;
            };
            items: Array<{
                name: string;
                code: string;
                quantity: number;
                amount_without_vat: number;
                amount: number;
                vat_rate: number;
                vat_amount: number;
            }>;
            invoice_date: string;
        };
    };
}
