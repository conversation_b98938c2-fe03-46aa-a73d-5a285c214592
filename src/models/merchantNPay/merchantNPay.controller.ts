import { Controller, UseInterceptors, HttpCode, Post, Body, Res } from '@nestjs/common';
import { Response } from 'express';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { NPayTransactionIPNDto } from './dto/nPayTransactionIPN.dto';
import { MerchantNPayTransactionIPNHandlerService } from './services/nPayTransactionIPNHandler.service';

@UseInterceptors(LoggingInterceptor)
@Controller('merchant-wallets')
export class MerchantWalletController {
    // constructor(
    //     private readonly nPayTransactionIPNHandlerService: MerchantNPayTransactionIPNHandlerService,
    // ) {}

    // @HttpCode(200)
    // @Post('9pay/transaction/ipn')
    // async transactionIPN(
    //     @Body(new HttpValidationPipe())
    //     nPayTransactionIPNDto: NPayTransactionIPNDto,
    //     @Res() res: Response
    // ): Promise<void> {
    //     await this.nPayTransactionIPNHandlerService.transactionIPN(
    //         nPayTransactionIPNDto
    //     );
    //     res.status(200).end();
    //     return;
    // }
}
