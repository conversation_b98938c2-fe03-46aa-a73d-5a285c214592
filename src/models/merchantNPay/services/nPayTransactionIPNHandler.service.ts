import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { NPayTransactionIPNDto } from '../dto/nPayTransactionIPN.dto';
import { NPayService } from 'src/providers/nPay/services/npay.service';
import { NPayTransactionTransferDataService } from './nPayTransactionTransferData.service';
import { ETransactionType } from '../enums';
import { OrderRestaurantPaymentService } from 'src/models/order/services/orderRestaurantPayment.service';

@Injectable()
export class MerchantNPayTransactionIPNHandlerService {
    private readonly logger = new Logger(MerchantNPayTransactionIPNHandlerService.name);
    constructor(
        private readonly nPayService: NPayService,
        private readonly nPayTransactionTransferDataService: NPayTransactionTransferDataService,
        // private orderRestaurantPaymentService: OrderRestaurantPaymentService
    ) {}


    /**
     * Handles NPay Transaction IPN with request code.
     * @param nPayTransactionIPNDto - The DTO containing transaction details.
     * @returns {Promise<void>}
     */
    async handleTransactionIPNWithRequestCode(nPayTransactionIPNDto: NPayTransactionIPNDto): Promise<void> {
        try {
            const { request_code } = nPayTransactionIPNDto;
            if (!request_code) {
                this.logger.warn(
                    `[transactionIPNWithRequestCode] Missing request_code in transaction IPN: ${JSON.stringify(nPayTransactionIPNDto)}`
                );
                return;
            }

            const parsedAppIdResult =
                this.nPayTransactionTransferDataService.parseAppTransId(
                    request_code
                );

            this.logger.log(
                `[transactionIPNWithRequestCode] parsedAppIdResult: ${JSON.stringify(
                    parsedAppIdResult,
                    null,
                    2
                )}`
            );
            const { sourceId, transactionType, provinceId } = parsedAppIdResult;

            this.logger.log(
                `[transactionIPNWithRequestCode] Processing transaction IPN for sourceId: ${sourceId}, transactionType: ${transactionType}, provinceId: ${provinceId}`
            );
            // await this.orderRestaurantPaymentService.updateStatusOrReversalStatusByIPN(nPayTransactionIPNDto, parsedAppIdResult)

        } catch (error) {
            this.logger.error(
                `Error processing NPay Transaction IPN with request code: ${error.message} | stack: ${error.stack} | transaction: ${JSON.stringify(nPayTransactionIPNDto)}`
            );
            throw new InternalServerErrorException('Lỗi xử lý giao dịch NPay');
        }
    }

    /**
     * Handles NPay Transaction IPN (Instant Payment Notification) messages.
     * @param nPayTransactionIPNDto - The DTO containing transaction details.
     * @returns {Promise<void>}
     */
    async transactionIPN(nPayTransactionIPNDto: NPayTransactionIPNDto): Promise<void> {
        try {
            console.log('Received NPay Transaction IPN:', nPayTransactionIPNDto);
            const verifySuccess = this.nPayService.verifyTransactionIPNSignature(nPayTransactionIPNDto);
            if (!verifySuccess) {
                throw new InternalServerErrorException('Signature không chính xác');
            }

            this.logger.log(
                `Processing NPay Transaction IPN for transaction ID: ${nPayTransactionIPNDto.request_code}`,
            );

            await this.handleTransactionIPNWithRequestCode(nPayTransactionIPNDto);
        } catch (error) {
            this.logger.error(
                `Error processing NPay Transaction IPN: ${error.message} | stack: ${
                    error.stack
                } | transaction: ${JSON.stringify(nPayTransactionIPNDto)}`,
            );
            return;
        }
    }
}
