import { Injectable, BadRequestException } from '@nestjs/common';
import { EAppTransIdPrefix, ETransactionType } from '../enums';
import { NPayService } from 'src/providers/nPay/services/npay.service';
import { NPayWalletToBusinessDto, WalletTransferToBusinessRequestDto } from 'src/providers/nPay/dtos';
import { TRANSACTION_TYPE_PREFIX_APP_TRANS_ID_MAPPER, PREFIX_APP_TRANS_ID_MAPPER } from '../constants';
import { IParsedAppTransIdResult } from '../types/parsedAppTransIdResult.interface';
import * as crypto from 'crypto';
import { BusinessToWalletTransferDto } from '../dto/businessToWalletTransfer.dto';
import { WalletToBusinessTransferDto } from '../dto/walletToBusinessTransfer.dto';
import {
    INPayBusinessToWalletTransaction,
    INPayResponse,
    INPayWalletToBusinessTransaction,
} from 'src/providers/nPay/interfaces';
import { NPayTransactionTransferDataService } from './nPayTransactionTransferData.service';

@Injectable()
export class MerchantNPayTransactionService {
    constructor(
        private readonly nPayService: NPayService,
        private readonly nPayTransactionTransferDataService: NPayTransactionTransferDataService,
    ) {
        // Initialize any dependencies or services here
    }

    async makePayment(data: any): Promise<any> {
        return this.nPayService.businessWalletTransfer(data);
    }

    async makeWalletToBusinessTransaction(
        dto: WalletToBusinessTransferDto,
    ): Promise<INPayResponse<INPayWalletToBusinessTransaction>> {
        const { amount, note, transactionType, appTransId, sourceId, accountLinking, provinceId } = dto;
        if (!accountLinking) {
            throw new BadRequestException('Thông tin liên kết tài khoản không hợp lệ');
        }
        const isValidAppTransId = this.nPayTransactionTransferDataService.isAppTransIdValid(appTransId, transactionType, sourceId, provinceId);
        if (!isValidAppTransId) {
            throw new BadRequestException('Mã giao dịch không hợp lệ');
        }
        if (amount <= 0) {
            throw new BadRequestException('Số tiền chuyển khoản phải lớn hơn 0');
        }
        if (!note) {
            throw new BadRequestException('Nội dung chuyển khoản không được để trống');
        }
        if (!transactionType) {
            throw new BadRequestException('Loại giao dịch không được để trống');
        }
        if (!appTransId) {
            throw new BadRequestException('Mã giao dịch không được để trống');
        }
        const walletTransferToBusinessRequestDto = new NPayWalletToBusinessDto({
            amount: amount,
            requestAction: transactionType,
            phone: accountLinking.phone,
            content: note,
            requestCode: appTransId,
        });
        return this.nPayService.NPayWalletToBusinessWallet(walletTransferToBusinessRequestDto);
    }

    async makeBusinessToWalletTransfer(
        dto: BusinessToWalletTransferDto,
    ): Promise<INPayResponse<INPayBusinessToWalletTransaction>> {
        const { amount, note, transactionType, appTransId, sourceId, accountLinking, provinceId } = dto;
        if (!accountLinking) {
            throw new BadRequestException('Thông tin liên kết tài khoản không hợp lệ');
        }
        const isValidAppTransId = this.nPayTransactionTransferDataService.isAppTransIdValid(appTransId, transactionType, sourceId, provinceId);
        if (!isValidAppTransId) {
            throw new BadRequestException('Mã giao dịch không hợp lệ');
        }
        if (amount <= 0) {
            throw new BadRequestException('Số tiền chuyển khoản phải lớn hơn 0');
        }
        if (!note) {
            throw new BadRequestException('Nội dung chuyển khoản không được để trống');
        }
        if (!transactionType) {
            throw new BadRequestException('Loại giao dịch không được để trống');
        }
        if (!appTransId) {
            throw new BadRequestException('Mã giao dịch không được để trống');
        }
        const walletTransferToBusinessRequestDto = new WalletTransferToBusinessRequestDto({
            amount: amount,
            requestAction: transactionType,
            phone: accountLinking.phone,
            content: note,
            requestCode: appTransId,
        });
        return this.nPayService.businessWalletTransfer(walletTransferToBusinessRequestDto);
    }
}
