import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { PREFIX_APP_TRANS_ID_MAPPER, TRANSACTION_TYPE_PREFIX_APP_TRANS_ID_MAPPER } from '../constants';
import { EAppTransIdPrefix, ETransactionType } from '../enums';
import { IParsedAppTransIdResult } from '../types/parsedAppTransIdResult.interface';


@Injectable()
export class NPayTransactionTransferDataService {

    generateAppTransId(tenantId: string, transactionType: ETransactionType, sourceId: string): string {
        const prefix = TRANSACTION_TYPE_PREFIX_APP_TRANS_ID_MAPPER[transactionType];
        if (!prefix) {
            throw new Error('Prefix not found');
        }
        return `${prefix}-${tenantId}-${sourceId}-${crypto.randomBytes(12).toString('hex').toUpperCase()}`;
    }

    parseAppTransId(appTransId: string): IParsedAppTransIdResult {
        const [prefix, provinceId, sourceId, id] = appTransId.split('-');
        const transactionType = PREFIX_APP_TRANS_ID_MAPPER[prefix];
        return {
            transactionType,
            sourceId,
            id,
            prefix,
            provinceId,
            inputId: appTransId,
        };
    }

    isSourceTypeValid(inputSourceType: string): boolean {
        return Object.values<string>(ETransactionType).includes(inputSourceType);
    }

    isPrefixValid(inputPrefix: string): boolean {
        return Object.values<string>(EAppTransIdPrefix).includes(inputPrefix);
    }

    isAppTransIdValid(
        inputAppTransId: string,
        inputSourceType: string,
        inputSourceId: string,
        inputTenantId: string,
    ): boolean {
        if (!inputAppTransId || !inputSourceType || !inputSourceId || !inputTenantId) {
            return false;
        }

        const isSourceTypeValid = this.isSourceTypeValid(inputSourceType);
        if (!isSourceTypeValid) {
            return false;
        }

        const parsedResult = this.parseAppTransId(inputAppTransId);
        if (!parsedResult) {
            return false;
        }

        const { transactionType, sourceId, prefix, provinceId } = parsedResult;
        const isPrefixValid = this.isPrefixValid(prefix);
        if (!isPrefixValid) {
            return false;
        }

        if (transactionType !== inputSourceType) {
            return false;
        }

        if (sourceId !== inputSourceId) {
            return false;
        }

        if (provinceId !== inputTenantId) {
            return false;
        }

        return true;
    }

}
