import { Module } from '@nestjs/common';
import { NPayModule } from 'src/providers/nPay/nPay.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MerchantNPayTransactionService } from './services/merchantNPayTransaction.service';
import { MerchantWalletController } from './merchantNPay.controller';
import { MerchantNPayTransactionIPNHandlerService } from './services/nPayTransactionIPNHandler.service';
import { NPayTransactionTransferDataService } from './services/nPayTransactionTransferData.service';
import { OrderRestaurantPaymentService } from '../order/services/orderRestaurantPayment.service';
import { OrderModule } from '../order/order.module';
//merchantNPayConfig
@Module({
    imports: [
        NPayModule.forRoot({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                const { privateKey, publicKey, domain, merchantKey, secretKey } =
                    configService.get('MERCHANT_NPAY_CONFIG');
                return {
                    privateKey,
                    publicKey,
                    domain,
                    merchantKey,
                    secretKey,
                };
            },
        }),
        
    ],
    controllers: [MerchantWalletController],
    providers: [
        MerchantNPayTransactionService,
        MerchantNPayTransactionIPNHandlerService,
        NPayTransactionTransferDataService,
    ],
    exports: [
        MerchantNPayTransactionService,
        MerchantNPayTransactionIPNHandlerService,
        NPayTransactionTransferDataService,
    ],
})
export class MerchantNPayModule {}
