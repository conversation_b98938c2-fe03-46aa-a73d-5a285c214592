import { ApiProperty } from '@nestjs/swagger';
import {
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    ValidateIf,
} from 'class-validator';
import { isNil } from 'lodash';
import { ENPayTransactionIPNStatus, ENPayTransactionType, INPayTransactionIPN } from 'src/providers/nPay/interfaces/nPayTransactionIPN.interface';

export class NPayTransactionIPNDto implements INPayTransactionIPN {
    @ApiProperty({
        description: 'Transaction code',
        example: '8MZ1BV6ROMPO',
    })
    @IsNotEmpty({ message: 'Transaction code is required' })
    @IsString()
    code: string;

    @ApiProperty({
        description: 'Request code',
        example: 'ORD-1-1234-**********',
    })
    @ValidateIf((obj, value) => !isNil(value))
    @IsNotEmpty({ message: 'Request code is required' })
    @IsString()
    request_code: string;

    @ApiProperty({
        description: 'Transaction title',
        example: '<PERSON>h toán tại đối tác <PERSON>D',
    })
    @IsNotEmpty({ message: 'Transaction title is required' })
    @IsString()
    title: string;

    @ApiProperty({
        description: 'Amount before fees',
        example: 200000,
    })
    @IsNotEmpty({ message: 'Actual amount is required' })
    @IsNumber()
    actual_amount: number;

    @ApiProperty({
        description:
            'Final amount to be paid (after deducting fees or promotions)',
        example: 200000,
    })
    @IsNotEmpty({ message: 'Amount is required' })
    @IsNumber()
    amount: number;

    @ApiProperty({
        description: 'Transaction fee',
        example: 0,
    })
    @IsNotEmpty({ message: 'Fee is required' })
    @IsNumber()
    fee: number;

    @ApiProperty({
        description: 'Transaction status',
        enum: ENPayTransactionIPNStatus,
        example: ENPayTransactionIPNStatus.SUCCESS,
    })
    @IsNotEmpty({ message: 'Status is required' })
    @IsEnum(ENPayTransactionIPNStatus, {
        message: 'Invalid transaction status',
    })
    status: ENPayTransactionIPNStatus;

    @ApiProperty({
        description: 'Transaction type',
        enum: ENPayTransactionType,
        example: ENPayTransactionType.WALLET_PAYMENT,
    })
    @IsNotEmpty({ message: 'Type is required' })
    @IsEnum(ENPayTransactionType, { message: 'Invalid transaction type' })
    type: ENPayTransactionType;

    @ApiProperty({
        description: 'Creation time',
        example: '2024-08-28 15:36:26',
    })
    @IsNotEmpty({ message: 'Creation time is required' })
    @IsString()
    created_at: string;

    @ApiProperty({
        description: 'Completion time (optional)',
        example: '2024-08-28 15:36:32',
        required: false,
    })
    @IsOptional()
    @IsString()
    completed_at?: string;

    @ApiProperty({
        description: "User's phone number who performed the transaction",
        example: '012345678',
    })
    @IsNotEmpty({ message: 'User phone is required' })
    @IsString()
    user_phone: string;

    @ApiProperty({
        description: 'Merchant key linked to the user',
        example: 'abcd',
    })
    @IsNotEmpty({ message: 'Merchant key is required' })
    @IsString()
    merchant_key: string;

    @ApiProperty({
        description: 'Security signature',
        example:
            'G/zJWBLHEr+bJcEUmbqMhqc0oYrEvuiy0ln8yqlFxag2o5TTIgrD0ZUJKKnXZQ7Iv952EWbQqZDvn2IuXGxdlU/7sgKAQox6WRazdogMoLw5GSLoLLrRINJTXThqbgrSUNpHSpxOPH97Ex/Jpvv5I8+8gI/KOEnrBax1MTwY/SouFY/iGjuM4om8vzgaHKIPM0fi71XlzqZgDbQvhqz4GyZI/b1e4nZEolRt1Jau07/zX1BXgaZ/xMvwsPwTFOXHLuk70PMJ2f/X80tGdrRnYG7TcN3VjSsMpluQ5nzkSGSUzuiLkR8cdMctloSQ4SPl30hteW/ciHr+yHozr5psBA==',
    })
    @IsNotEmpty({ message: 'Signature is required' })
    @IsString()
    signature: string;
}
