import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { INPayAccountLinking } from 'src/providers/microservices/deliveryServiceProxy/interfaces/nPayAccountLinking.interface';

export class BusinessToWalletTransferDto {
    /**
     * The ID of the province where the business is located.
     */
    @IsNotEmpty()
    @IsString()
    provinceId: string;

    /**
     * The ID of the business making the transfer.
     */
    @IsNotEmpty()
    @IsNumber()
    orderId: number;

    /**
     * The amount to be transferred from the business wallet to the merchant wallet.
     */
    @IsNotEmpty()
    @IsNumber()
    amount: number;

    @IsNotEmpty()
    @IsString()
    transactionType: string;

    @IsNotEmpty()
    accountLinking: INPayAccountLinking;

    /**
     * A note or description for the transaction.
     */
    @IsNotEmpty()
    @IsString()
    note: string;

    /**
     * The application transaction ID.
     */
    @IsNotEmpty()
    @IsString()
    appTransId: string;

    /**
     * The ID of the source initiating the transaction.
     */
    @IsNotEmpty()
    @IsString()
    sourceId: string;

}