import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import OpenAI from 'openai';

@Injectable()
export class ChatGptService {
    private readonly logger = new Logger(ChatGptService.name);
    private readonly openai: OpenAI = null;

    constructor() {
        this.openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });
    }

    async generateFoodDescription(title: string): Promise<string> {
        const result: [string, string] = [null, null];
        try {
            const response: OpenAI.Chat.Completions.ChatCompletion = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'user',
                        content:
                            'tôi muốn bạn viết miêu tả món ăn từ 8 đến 12 từ về món ăn này, nhớ là không cần nhắc lại tên món và lịch sự dùng để quảng cáo, khiến người khác muốn thử món ăn.',
                    },
                    {
                        role: 'assistant',
                        content: 'bạn muốn mô tả món ăn nào?',
                    },
                    {
                        role: 'user',
                        content: title,
                    },
                ],
                stream: false,
            });

            result[0] = response.choices[0].message.content;
        } catch (error) {
            const message = error.error.message || error.status || error.type || 'Unknown error';
            result[1] = message;
            result[0] = 'Xin lỗi, đã có lỗi xảy khi tạo mô tả quán. Vui lòng thử lại sau.';
        }

        if (result[1]) {
            this.logger.error(result[1]);
            throw new BadRequestException(result[0]);
        }

        return result[0];
    }

    async generateKeyWord(title: string, foodString: string, address?: string): Promise<string[]> {
        const result: [string[], string] = [null, null];
        try {
            const response: OpenAI.Chat.Completions.ChatCompletion = await this.openai.chat.completions.create({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'user',
                        content:
                            'tôi muốn bạn viết cho tôi mảng gồm 10 cụm từ để gợi ý tìm kiếm cửa hàng: ' + title +'; có địa chỉ tại: '+address+'. Các cụm từ liên quan đến tên quán và tên món ăn. Nhớ là cụm từ cần ngắn gọn, lịch sự, khiến người khác dễ tìm kiếm. Kết quả thể hiện ở dạng mảng',
                    },
                    {
                        role: 'assistant',
                        content: 'bạn muốn các cụm từ tìm kiếm cho những món ăn nào?',
                    },
                    {
                        role: 'user',
                        content: `Các món ăn: ${foodString}`,
                    },
                    {
                        role: 'assistant',
                        content: 'bạn muốn các cụm từ tìm kiếm ngắn ngọn trong bao nhiêu từ?',
                    },
                    {
                        role: 'user',
                        content: `Các cụm từ ngắn gọn, các cụm không trùng lặp với nhau, có từ 2 đến 4 từ trong 1 cụm, lược bỏ từ không cần thiết;`,
                    },
                ],
                stream: false,
            });
            // console.log(response);
            // console.log(response.choices[0].message.content);

            result[0] = JSON.parse(response.choices[0].message.content);
        } catch (error) {
            console.log(error);
            const message = error?.error?.message || error?.status || error?.type || 'Unknown error';
            result[1] = message;
            result[0] = ['Xin lỗi, đã có lỗi xảy khi tạo cụm từ hỗ trợ tìm kiếm. Vui lòng thử lại sau.'];
        }

        if (result[1]) {
            this.logger.error(result[1]);
            throw new BadRequestException(result[0]);
        }

        return result[0];
    }
}
