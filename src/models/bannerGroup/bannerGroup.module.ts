import { BannerGroupService } from './services/bannerGroup.service';
import { BannerGroupController } from './bannerGroup.controller';
import { Module } from '@nestjs/common';
import { ProvinceService } from '../province/province.service';
import { AdBannerService } from './services/adBanner.service';
@Module({
    controllers: [BannerGroupController],
    providers: [BannerGroupService, ProvinceService, AdBannerService],
})
export class BannerGroupModule {}
