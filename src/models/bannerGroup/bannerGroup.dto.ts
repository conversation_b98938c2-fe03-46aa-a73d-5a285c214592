import { Transform, Type } from 'class-transformer';
import {
    <PERSON><PERSON><PERSON>y,
    IsBoolean,
    IsEnum,
    IsDateString,
    IsNotEmpty,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    IsUrl,
    ValidateIf,
    ValidateNested,
} from 'class-validator';
import { toNumber } from 'lodash';
import { TinyInt } from 'src/common/constants';
import { EPageDisplay } from 'src/entities/types/EPayDisplay.enum';

export enum EBannerEvent {
    REDIRECT_RESTAURANT = 'redirect_restaurant',
    REDIRECT_WEBSITE = 'redirect_website',
    HTML_RENDERING = 'html_rendering',
    REDIRECT_COLLECTION = 'redirect_collection',
}

export enum EBannerTypes {
    CLIENT = 'CLIENT',
    RESTAURANT = 'RESTAURANT',
}

export enum EBannerPaymentType {
    AUTOMATIC = 'automatic', // Thanh toán tự động (chuyển khoản, ví)
    MANUAL = 'manual', // <PERSON>h toán thủ công (tiền mặt, các phương thức khác)
}

export enum EPosition {
    TOP = 'top',
    MIDDLE = 'mid',
    BOTTOM = 'bottom',
    WAITING_SHIPPER_SCREEN = 'waiting_shipper_screen',
}

export enum EDisplayType {
    HORIZONTAL = 'horizontal',
    VERTICAL = 'vertical',
}

export enum EEventType {
    REDIRECT_COLLECTION = 'redirect_collection',
    REDIRECT_WEBSITE = 'redirect_website',
    HTML_RENDERING = 'html_rendering',
    REDIRECT_RESTAURANT = 'redirect_restaurant',
}

export enum EObjType {
    BLOG = 'blog',
    PROMO = 'promo',
    VIDEO = 'video',
    NEWSFEED = 'newsfeed',
}
export class CreateBannerGroupDto {
    @IsNotEmpty()
    @IsString()
    title: string;

    @IsOptional()
    @IsString()
    subtitle: string;

    @IsNotEmpty()
    @IsBoolean()
    is_activated: boolean;

    @IsOptional()
    @IsArray()
    time_range: [Date, Date];

    @IsNotEmpty()
    @IsEnum(EDisplayType)
    display_type: EDisplayType;

    @IsNotEmpty()
    @IsEnum(EPosition)
    position: EPosition;

    @IsNotEmpty()
    @IsEnum(TinyInt)
    random_items: number;

    @IsOptional()
    @IsNumber()
    collection_id: number;

    @IsOptional()
    @IsEnum(EPageDisplay)
    page_display: EPageDisplay;

    @IsOptional()
    @IsNumber()
    ads_campaign_item_id: number;
}

export class UpdateBannerGroupDto extends CreateBannerGroupDto {}

export class MetadataDto {
    @IsOptional()
    @IsNumber()
    restaurant_id: number;

    @IsOptional()
    @IsString()
    url: string;

    @IsOptional()
    @IsString()
    open_app_deeplink_android: string;

    @IsOptional()
    @IsString()
    open_app_deeplink_ios: string;

    @IsOptional()
    @IsString()
    collection_code: string;

    @IsOptional()
    @IsString()
    html_content: string;
}

export class ExternalDataDto {
    @IsOptional()
    @IsString()
    main_btn_text: string;

    @IsOptional()
    @IsString()
    main_btn_event_type: EEventType; // redirect_collection, redirect_website, html_rendering, redirect_restaurant,

    @IsOptional()
    @IsString()
    second_btn_text: string;

    @IsOptional()
    @IsString()
    second_btn_event_type: EEventType; // redirect_collection, redirect_website, html_rendering, redirect_restaurant,

    @IsOptional()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata_main: MetadataDto;

    @IsOptional()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata_second: MetadataDto;
}

export class CreateAdBannerDto {
    @IsNotEmpty()
    @IsString()
    title: string;

    @IsOptional()
    @IsString()
    subtitle: string;

    @IsOptional()
    @IsString()
    headline: string;

    @IsOptional()
    @IsUrl()
    url: string;

    @IsNotEmpty()
    @IsEnum(EBannerTypes)
    type: EBannerTypes;

    @IsOptional()
    @IsNumber()
    restaurant_id: number;

    @ValidateIf((obj, value) => !!value)
    @IsDateString()
    start_time: Date;

    @ValidateIf((obj, value) => !!value)
    @IsDateString()
    end_time: Date;

    @IsOptional()
    @IsString()
    main_bg_color: string;

    @IsOptional()
    @IsString()
    description: string;

    @IsOptional()
    @IsString()
    image: string;

    @IsOptional()
    @IsString()
    video_thumbnail_url: string;

    @IsOptional()
    @IsString()
    video_url: string;

    @IsNotEmpty()
    @IsBoolean()
    is_activated: boolean;

    @IsOptional()
    @IsEnum(EBannerEvent)
    event_type: EBannerEvent;

    @IsOptional()
    @IsEnum(EObjType)
    obj_type: EObjType;

    @ValidateIf((obj, value) => !!value)
    @IsDateString()
    promo_end_time: Date;

    @IsOptional()
    @IsNumber()
    read_time: number;

    @IsNotEmpty()
    @IsObject()
    @ValidateNested()
    @Type(() => MetadataDto)
    metadata: MetadataDto;

    @IsNotEmpty()
    @IsObject()
    @ValidateNested()
    @Type(() => ExternalDataDto)
    external_data: ExternalDataDto;

    @IsNotEmpty()
    @IsNumber()
    group_id: number;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    provinceIds: number[];

    @IsOptional()
    @IsNumber()
    ads_campaign_item_id: number;
}

export class UpdateAdBannerDto extends CreateAdBannerDto {}

export class GetBannerGroupDto {
    @IsOptional()
    @Transform((value) => toNumber(value))
    @IsNumber()
    ads_campaign_item_id: number;
}

export class GetAllBannersDto {
    @IsNumber()
    @Type(() => Number)
    page: number;

    @IsNumber()
    @Type(() => Number)
    limit: number;

    @IsOptional()
    @IsString()
    keyword: string;
}
