import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
} from '@nestjs/common';
import { Request } from 'express';
import { toNumber } from 'lodash';
import { BannerGroup } from 'src/entities/bannerGroup.entity';
import { EPageDisplay } from 'src/entities/types/EPayDisplay.enum';
import { DatabaseService } from 'src/providers/database/database.service';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { CreateBannerGroupDto, EObjType, EPosition, GetAllBannersDto } from '../bannerGroup.dto';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { AdBanner } from 'src/entities/adBanner.entity';

@Injectable()
export class BannerGroupService {
    async getBannerGroup(req: Request) {
        return await DatabaseService.getRepository(BannerGroup, req).find({ relations: ['collection'] });
    }

    async getBannerGroupV2(req: Request) {
        return await DatabaseService.getRepository(BannerGroup, req).findAndCount({ relations: ['collection'] });
    }

    async getBannerGroupV3(req: Request) {
        const { adsCampaignItemId } = req.query;

        if (adsCampaignItemId && adsCampaignItemId !== 'undefined' && adsCampaignItemId !== 'null') {
            return await DatabaseService.getRepository(BannerGroup, req).findAndCount({
                where: {
                    ads_campaign_item_id: toNumber(adsCampaignItemId),
                },
                relations: ['collection'],
            });
        }
        return await DatabaseService.getRepository(BannerGroup, req).findAndCount({ relations: ['collection'] });
    }

    async createBannerGroup(
        req: Request,
        {
            title,
            is_activated,
            display_type,
            position,
            subtitle,
            random_items,
            collection_id,
            page_display,
        }: CreateBannerGroupDto,
    ) {
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            if ([EPosition.TOP, EPosition.MIDDLE].includes(position)) {
                const bannerGroupWithPositionExisting = await entityManager.getRepository(BannerGroup).findOne({
                    where: {
                        position,
                        collection_id,
                    },
                });

                if (bannerGroupWithPositionExisting) {
                    throw new BadRequestException('Banner group vị trí này đã tồn tại');
                }
            }
            const bannerGroup = new BannerGroup({
                title,
                subtitle,
                is_activated,
                display_type,
                position,
                random_items,
                collection_id,
                page_display,
            });
            return await entityManager.getRepository(BannerGroup).save(bannerGroup);
        });
    }

    async createBannerGroupV3(
        req: Request,
        {
            title,
            is_activated,
            display_type,
            position,
            subtitle,
            random_items,
            collection_id,
            page_display,
            ads_campaign_item_id,
        }: CreateBannerGroupDto,
    ) {
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            if ([EPosition.TOP, EPosition.MIDDLE].includes(position)) {
                const bannerGroupWithPositionExisting = await entityManager.getRepository(BannerGroup).findOne({
                    where: {
                        position,
                        collection_id,
                    },
                });

                if (bannerGroupWithPositionExisting) {
                    throw new BadRequestException('Banner group vị trí này đã tồn tại');
                }
            }
            const bannerGroup = new BannerGroup({
                title,
                subtitle,
                is_activated,
                display_type,
                position,
                random_items,
                collection_id,
                page_display,
                ads_campaign_item_id,
            });
            return await entityManager.getRepository(BannerGroup).save(bannerGroup);
        });
    }

    async deleteBannerGroup(id: number, req: Request) {
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            const bannerGroupExisting = await entityManager.getRepository(BannerGroup).findOne({
                where: {
                    id,
                },
            });
            if (!bannerGroupExisting) {
                throw new BadRequestException('Không tìm thấy banner group');
            }
            await entityManager.getRepository(BannerGroup).delete({ id });
            return bannerGroupExisting;
        });
    }

    async fetchBannerGroupDetails(id: number, req: Request) {
        return await DatabaseService.getRepository(BannerGroup, req).findOne({
            where: {
                id,
            },
        });
    }

    async updateBannerGroup(
        id: number,
        {
            title,
            subtitle,
            is_activated,
            display_type,
            random_items,
            collection_id,
            page_display,
            position,
        }: CreateBannerGroupDto,
        req: Request,
    ) {
        try {
            return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
                if ([EPosition.TOP, EPosition.MIDDLE].includes(position)) {
                    const bannerGroupWithPositionExisting = await entityManager.getRepository(BannerGroup).findOne({
                        where: {
                            position,
                            collection_id,
                        },
                    });

                    if (bannerGroupWithPositionExisting && bannerGroupWithPositionExisting.id !== id) {
                        throw new BadRequestException('Banner group vị trí này đã tồn tại');
                    }
                }
                const bannerGroupExisting = await entityManager
                    .getRepository(BannerGroup)
                    .createQueryBuilder('bannerGroup')
                    .where('bannerGroup.id = :id', { id })
                    .setLock('pessimistic_write')
                    .getOne();
                if (!bannerGroupExisting) {
                    throw new BadRequestException('Không tìm thấy banner group');
                }
                if (page_display != EPageDisplay.COLLECTION && collection_id) {
                    throw new BadRequestException('Không thể chọn collection khi page_display khác collection');
                }

                const updateData: QueryDeepPartialEntity<BannerGroup> = {
                    title,
                    subtitle,
                    is_activated,
                    display_type,
                    random_items,
                    collection_id,
                    page_display,
                    position,
                };
                const updateResult = await entityManager
                    .getRepository(BannerGroup)
                    .createQueryBuilder('bannerGroup')
                    .update()
                    .set(updateData)
                    .where('id = :id', { id })
                    .execute();

                if (updateResult.affected) {
                    return await entityManager.getRepository(BannerGroup).findOne({
                        where: {
                            id,
                        },
                    });
                } else {
                    throw new InternalServerErrorException('Cập nhật banner group thất bại');
                }
            });
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getAllBanners(getAllBannersDto: GetAllBannersDto, provinceId: string) {
        const { page, limit, keyword } = getAllBannersDto;
        const queryBuilder = DatabaseService.getRepositoryByProvinceId(AdBanner, provinceId)
            .createQueryBuilder('adBanner')
            .where('adBanner.obj_type = :obj_type', { obj_type: EObjType.NEWSFEED })
            .skip((page - 1) * limit)
            .take(limit);

        if (keyword) {
            queryBuilder.andWhere('adBanner.title LIKE :keyword', { keyword: `%${keyword}%` });
        }

        const [items, total] = await queryBuilder.getManyAndCount();

        if (items.length === 0) {
            return {
                items: [],
                total: 0,
            };
        }

        const bannerIds = items.map((item) => item.id);
        const banners = await DatabaseService.getRepositoryByProvinceId(AdBanner, provinceId)
            .createQueryBuilder('adBanner')
            .leftJoinAndSelect('adBanner.bannerGroup', 'bannerGroup')
            .leftJoinAndSelect('bannerGroup.collection', 'collection')
            .where('adBanner.id IN (:...bannerIds)', { bannerIds })
            .getMany();

        return {
            items: banners,
            total,
        };
    }
}
