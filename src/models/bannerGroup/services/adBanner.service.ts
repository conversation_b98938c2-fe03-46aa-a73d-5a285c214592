import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { PROVINCE_HEADER, TinyInt } from 'src/common/constants';
import { AdBanner } from 'src/entities/adBanner.entity';
import { ProvinceService } from 'src/models/province/province.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { Not, IsNull, Brackets } from 'typeorm';
import { CreateAdBannerDto, EPosition, UpdateAdBannerDto } from '../bannerGroup.dto';
import { BannerGroupService } from './bannerGroup.service';

@Injectable()
export class AdBannerService {
    constructor(private provinceService: ProvinceService, private bannerGroupService: BannerGroupService) {}
    async createBanner(params: CreateAdBannerDto, req: Request) {
        const {
            title,
            headline,
            subtitle,
            image,
            url,
            is_activated,
            type,
            start_time,
            end_time,
            restaurant_id,
            main_bg_color,
            description,
            video_thumbnail_url,
            video_url,
            event_type,
            obj_type,
            promo_end_time,
            read_time,
            metadata,
            external_data,
            group_id,
            provinceIds,
        } = params;
        await this.checkWaitingShipperScreenBannerExistingOrThrowError(req, group_id, provinceIds);
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            let newAdBanner = new AdBanner({
                title,
                headline,
                subtitle,
                image,
                url,
                is_activated,
                type,
                start_time,
                end_time,
                restaurant_id,
                main_bg_color,
                description,
                video_thumbnail_url,
                video_url,
                event_type,
                obj_type,
                promo_end_time,
                read_time,
                metadata,
                external_data,
                group_id,
                is_global: provinceIds.length === 0 ? TinyInt.YES : TinyInt.NO,
            });
            newAdBanner = await entityManager.getRepository(AdBanner).save(newAdBanner);
            if (provinceIds.length > 0) {
                const provinceExisting = await this.provinceService.findProvincesByIdsWithTransactionWrapper(
                    entityManager,
                    provinceIds,
                );
                newAdBanner.provinces = provinceExisting;
                return await entityManager.getRepository(AdBanner).save(newAdBanner);
            } else {
                return newAdBanner;
            }
        });
    }

    private async checkWaitingShipperScreenBannerExistingOrThrowError(
        req: Request,
        group_id: number,
        provinceIds: number[],
        ignoreBannerId?: number,
    ) {
        const groupBannerDetails = await this.bannerGroupService.fetchBannerGroupDetails(group_id, req);
        if (groupBannerDetails.position === EPosition.WAITING_SHIPPER_SCREEN) {
            if (!provinceIds || provinceIds.length == 0) {
                const bannerExisting = await DatabaseService.getRepository(AdBanner, req).findOne({
                    where: { group_id: groupBannerDetails.id, id: ignoreBannerId ? Not(ignoreBannerId) : undefined },
                });
                if (bannerExisting) {
                    throw new HttpException('Banner ở thị trường này đã tồn tại', HttpStatus.BAD_REQUEST);
                }
            } else {
                const bannerExistingQb = DatabaseService.getRepository(AdBanner, req)
                    .createQueryBuilder('adBanner')
                    .leftJoinAndSelect('adBanner.provinces', 'provinces')
                    .where('group_id = :group_id', { group_id })
                    .andWhere(
                        new Brackets((qb) => {
                            qb.where('is_global = :is_global', { is_global: TinyInt.YES });
                            if (provinceIds && provinceIds.length > 0) {
                                qb.orWhere('provinces.id IN (:...provinceIds)', { provinceIds });
                            }
                        }),
                    );
                if (ignoreBannerId) {
                    bannerExistingQb.andWhere('adBanner.id != :ignoreBannerId', { ignoreBannerId });
                }
                const bannerExisting = await bannerExistingQb.getOne();
                if (bannerExisting) {
                    throw new HttpException('Banner ở thị trường này đã tồn tại', HttpStatus.BAD_REQUEST);
                }
            }
        }
    }

    async updateBanner(
        req: Request,
        id: number,
        {
            title,
            headline,
            subtitle,
            image,
            url,
            is_activated,
            type,
            start_time,
            end_time,
            restaurant_id,
            main_bg_color,
            description,
            video_thumbnail_url,
            video_url,
            event_type,
            obj_type,
            promo_end_time,
            read_time,
            metadata,
            external_data,
            group_id,
            provinceIds,
        }: UpdateAdBannerDto,
    ) {
        await this.checkWaitingShipperScreenBannerExistingOrThrowError(req, group_id, provinceIds, id);
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            const adBannerExisting = await entityManager
                .getRepository(AdBanner)
                .createQueryBuilder('adBanner')
                .leftJoinAndSelect('adBanner.provinces', 'provinces')
                .where('adBanner.id = :id', { id })
                .setLock('pessimistic_write')
                .getOne();
            if (!adBannerExisting) {
                throw new HttpException('Không tìm thấy banner', HttpStatus.NOT_FOUND);
            }
            const updateResult = await entityManager
                .getRepository(AdBanner)
                .createQueryBuilder('adBanner')
                .update()
                .set({
                    title,
                    headline,
                    subtitle,
                    image,
                    url,
                    is_activated,
                    type,
                    start_time,
                    end_time,
                    restaurant_id,
                    main_bg_color,
                    description,
                    video_thumbnail_url,
                    video_url,
                    event_type,
                    obj_type,
                    promo_end_time,
                    read_time,
                    metadata,
                    external_data,
                    group_id,
                    is_global: provinceIds.length === 0 ? TinyInt.YES : TinyInt.NO,
                })
                .where('id = :id', { id })
                .execute();
            if (!updateResult || updateResult.affected === 0) {
                throw new HttpException('Cập nhật banner thất bại', HttpStatus.INTERNAL_SERVER_ERROR);
            } else {
                const adBannerUpdated = await entityManager
                    .getRepository(AdBanner)
                    .createQueryBuilder('adBanner')
                    .leftJoinAndSelect('adBanner.provinces', 'provinces')
                    .where('adBanner.id = :id', { id })
                    .getOne();
                if (!adBannerUpdated.is_global) {
                    const provinceExisting = await this.provinceService.findProvincesByIdsWithTransactionWrapper(
                        entityManager,
                        provinceIds,
                    );
                    adBannerUpdated.provinces = provinceExisting;
                    return await entityManager.getRepository(AdBanner).save(adBannerUpdated);
                } else {
                    adBannerUpdated.provinces = [];
                    return await entityManager.getRepository(AdBanner).save(adBannerUpdated);
                }
            }
        });
    }

    async fetchBannerDetails(req: Request, id: number) {
        return await DatabaseService.getRepository(AdBanner, req).findOne({
            relations: ['provinces', 'bannerGroup'],
            where: {
                id,
                group_id: Not(IsNull()),
            },
        });
    }

    async removeBanner(id: number, req: Request) {
        return await DatabaseService.getEntityManager(req).transaction(async (entityManager) => {
            const adBannerExisting = await entityManager
                .getRepository(AdBanner)
                .createQueryBuilder('adBanner')
                .leftJoinAndSelect('adBanner.provinces', 'provinces')
                .where('adBanner.id = :id', { id })
                .getOne();
            if (!adBannerExisting) {
                throw new HttpException('Không tìm thấy banner', HttpStatus.NOT_FOUND);
            } else {
                if (adBannerExisting.provinces.length > 0) {
                    adBannerExisting.provinces = [];
                    await entityManager.getRepository(AdBanner).save(adBannerExisting);
                }
                await entityManager.getRepository(AdBanner).delete({ id });
                return adBannerExisting;
            }
        });
    }

    async fetchBannerListByGroupId(groupId: number, req: Request) {
        return DatabaseService.getRepositoryByProvinceId(AdBanner, req.get(PROVINCE_HEADER)).find({
            relations: ['provinces'],
            where: {
                group_id: groupId,
            },
        });
    }
}
