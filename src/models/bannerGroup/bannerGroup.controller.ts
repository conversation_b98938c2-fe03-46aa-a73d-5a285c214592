import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import {
    CreateAdBannerDto,
    CreateBannerGroupDto,
    GetAllBannersDto,
    UpdateAdBannerDto,
    UpdateBannerGroupDto,
} from './bannerGroup.dto';
import { AdBannerService } from './services/adBanner.service';
import { BannerGroupService } from './services/bannerGroup.service';

import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { HeaderProvince } from 'src/common/decorators/province.decorator';

@UseInterceptors(LoggingInterceptor)
@Controller('banner-group')
@UseGuards(AuthGuard)
export class BannerGroupController {
    constructor(private readonly bannerGroupService: BannerGroupService, private adBannerService: AdBannerService) {}
    @Get()
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_LIST)
    async getList(@Req() req: Request) {
        return this.bannerGroupService.getBannerGroup(req);
    }

    @Get('v2')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_LIST)
    async getListV2(@Req() req: Request) {
        return this.bannerGroupService.getBannerGroupV2(req);
    }

    @Get('v3')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_LIST)
    async getListV3(@Req() req: Request) {
        return this.bannerGroupService.getBannerGroupV3(req);
    }

    @Get('banner/details/:id')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_ONE)
    async fetchBannerDetails(@Req() req: Request, @Param('id', new ParseIntPipe()) id: number) {
        return this.adBannerService.fetchBannerDetails(req, id);
    }

    @Get('banner/:id')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_LIST)
    async fetchBannerList(@Req() req: Request, @Param('id', new ParseIntPipe()) groupId: number) {
        return await this.adBannerService.fetchBannerListByGroupId(groupId, req);
    }

    @Delete('banner/:id')
    @RequirePermissions(PermissionsAccessAction.BANNER_REMOVE)
    async removeBanner(@Req() req: Request, @Param('id', new ParseIntPipe()) id: number) {
        return await this.adBannerService.removeBanner(id, req);
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_REMOVE)
    async deleteGroup(@Req() req: Request, @Param('id', new ParseIntPipe()) id: number) {
        return this.bannerGroupService.deleteBannerGroup(id, req);
    }

    @Post()
    @RequirePermissions(PermissionsAccessAction.BANNER_CREATE)
    async create(@Req() req: Request, @Body(new HttpValidationPipe()) params: CreateBannerGroupDto) {
        return await this.bannerGroupService.createBannerGroup(req, params);
    }

    @Post('v3')
    @RequirePermissions(PermissionsAccessAction.BANNER_CREATE)
    async createV3(@Req() req: Request, @Body(new HttpValidationPipe()) params: CreateBannerGroupDto) {
        return await this.bannerGroupService.createBannerGroupV3(req, params);
    }

    @Get('banners')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_ONE)
    async getAllBanners(
        @Query(new HttpValidationPipe()) getAllBannersDto: GetAllBannersDto,
        @HeaderProvince() provinceId: string,
    ) {
        return await this.bannerGroupService.getAllBanners(getAllBannersDto, provinceId);
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_ONE)
    async fetchGroupDetails(@Req() req: Request, @Param('id', new ParseIntPipe()) id: number) {
        return await this.bannerGroupService.fetchBannerGroupDetails(id, req);
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_UPDATE)
    async update(
        @Param('id', new ParseIntPipe()) id: number,
        @Body(new HttpValidationPipe()) params: UpdateBannerGroupDto,
        @Req() req: Request,
    ) {
        return await this.bannerGroupService.updateBannerGroup(id, params, req);
    }

    @Post('banner')
    @RequirePermissions(PermissionsAccessAction.BANNER_CREATE)
    async createBanner(@Req() req: Request, @Body(new HttpValidationPipe()) params: CreateAdBannerDto) {
        return await this.adBannerService.createBanner(params, req);
    }

    @Put('banner/:id')
    @RequirePermissions(PermissionsAccessAction.BANNER_UPDATE)
    async updateBanner(
        @Req() req: Request,
        @Body(new HttpValidationPipe()) params: UpdateAdBannerDto,
        @Param('id', new ParseIntPipe()) id: number,
    ) {
        return await this.adBannerService.updateBanner(req, id, params);
    }
}
