import {
    Body,
    Controller,
    Delete,
    Get,
    NotFoundException,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    UseGuards,
} from '@nestjs/common';

import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { HttpValidationPipe } from 'src/common/pipes/httpValidation.pipe';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { CreateAdBannerDto, UpdateAdBannerDto } from './adBanner.dto';
import { AdBannerService } from './adbanner.service';

import { UseInterceptors } from '@nestjs/common';

import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';

@UseInterceptors(LoggingInterceptor)
@Controller('ad-banners')
@UseGuards(AuthGuard)
export class AdBannerController {
    constructor(private readonly adBannerService: AdBannerService) {}
    @Post()
    async create(@Body(new HttpValidationPipe()) data: CreateAdBannerDto) {
        return await this.adBannerService.create(data);
    }

    @Get()
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_LIST)
    async list(@Query(new HttpValidationPipe()) query: BaseQueryFilterDto) {
        const result = await this.adBannerService.list(query);
        return {
            items: result[0],
            total: result[1],
        };
    }

    @Get(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_FIND_ONE)
    async getById(@Param('id', new ParseIntPipe()) id: number) {
        const result = await this.adBannerService.getById(id);
        if (!result) throw new NotFoundException('category not found');
        return result;
    }

    @Delete(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_REMOVE)
    async deleteById(@Param('id', new ParseIntPipe()) id: number) {
        return await this.adBannerService.delete(id);
    }

    @Put(':id')
    @RequirePermissions(PermissionsAccessAction.BANNER_UPDATE)
    async update(@Body(new HttpValidationPipe()) data: UpdateAdBannerDto, @Param('id', new ParseIntPipe()) id: number) {
        return await this.adBannerService.update(id, data);
    }
}
