import { Transform } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    ValidateIf,
} from 'class-validator';
import { EBannerEvent, EBannerTypes } from '../bannerGroup/bannerGroup.dto';
// import { EBannerEvent, EBannerTypes } from 'src/entities/adBanner.entity';

export class CreateAdBannerDto {
    @IsNotEmpty()
    @IsString()
    title: string;

    @IsNotEmpty()
    @IsString()
    subtitle: string;

    @IsOptional()
    @IsString()
    headline: string;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    url: string;

    @IsNotEmpty()
    @IsEnum(EBannerTypes)
    type: EBannerTypes;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsNumber()
    restaurant_id: number;

    @IsOptional()
    // @ValidateIf((obj, value) => value != undefined && value != null)
    @Transform(({ value }) => new Date(value))
    @IsDate()
    start_time: Date;

    @IsOptional()
    // @ValidateIf((obj, value) => value != undefined && value != null)
    @Transform(({ value }) => new Date(value))
    @IsDate()
    end_time: Date;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    main_bg_color: string;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    position: string;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    description: string;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    image: string;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsString()
    video_url: string;

    @IsNotEmpty()
    @IsBoolean()
    is_activated: boolean;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsEnum(EBannerEvent)
    event_type: EBannerEvent;

    @IsOptional()
    @ValidateIf((obj, value) => value != undefined && value != null)
    @IsArray()
    @IsNumber({ allowInfinity: false, allowNaN: false }, { each: true })
    provinceIds: number[];
}

export class UpdateAdBannerDto extends CreateAdBannerDto { }
