import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    NotFoundException,
    Scope,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import * as _ from 'lodash';
import { BaseQueryFilterDto } from 'src/common/pipes/global.dto';
import { AdBanner } from 'src/entities/adBanner.entity';
import { Restaurant } from 'src/entities/restaurant.entity';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { DatabaseService } from 'src/providers/database/database.service';
import { IsNull, Repository } from 'typeorm';
import { CreateAdBannerDto, UpdateAdBannerDto } from './adBanner.dto';
import { CreateBannerGroupDto } from '../bannerGroup/bannerGroup.dto';
import { BannerGroup } from 'src/entities/bannerGroup.entity';

@Injectable({ scope: Scope.REQUEST })
export class AdBannerService {
    private adBannerRepo: Repository<AdBanner>;
    constructor(@Inject(REQUEST) private request: Request, private readonly s3Service: AwsS3Service) {
        this.adBannerRepo = DatabaseService.getRepository(AdBanner, this.request);
    }

    async list({ sortedBy, orderBy, limit, page }: BaseQueryFilterDto): Promise<[AdBanner[], number]> {
        return await this.adBannerRepo.findAndCount({
            relations: ['restaurant'],
            where: { group_id: IsNull() },
            order: {
                [orderBy]: sortedBy,
            },
            skip: page * limit,
            take: limit,
        });
    }

    getById(id: number) {
        return this.adBannerRepo.findOne({ where: { id }, relations: ['restaurant'] });
    }

    async create({
        subtitle,
        headline,
        title,
        url,
        type,
        position,
        start_time,
        end_time,
        main_bg_color,
        description,
        restaurant_id,
        image,
        event_type,
        is_activated,
        video_url,
    }: CreateAdBannerDto) {
        const newEntity = new AdBanner({
            title,
            headline,
            subtitle,
            url,
            type,
            position,
            start_time,
            end_time,
            main_bg_color,
            description,
            restaurant_id,
            image,
            event_type,
            is_activated,
            video_url,
        });
        return await this.adBannerRepo.save(newEntity);
    }

    async delete(id: number): Promise<any> {
        const item = await this.getById(id);
        if (!item) throw new NotFoundException();

        return await this.adBannerRepo.delete({ id: item.id });
    }

    async update(
        id: number,
        {
            subtitle,
            title,
            headline,
            url,
            type,
            position,
            start_time,
            end_time,
            main_bg_color,
            description,
            restaurant_id,
            image,
            event_type,
            is_activated,
            video_url,
        }: UpdateAdBannerDto,
    ) {
        try {
            const item = await this.getById(id);
            if (!item) throw new NotFoundException();
            console.log('item', item);
            Object.assign(item, {
                title,
                headline,
                subtitle,
                type,
                image,
                event_type,
                is_activated,
                video_url,
            });
            console.log('item', item);
            console.log('restaurant_id', headline);
            if (!_.isNil(url)) item.url = url;
            if (!_.isNil(position)) item.position = position;
            if (!_.isNil(start_time)) item.start_time = start_time;
            if (!_.isNil(end_time)) item.end_time = end_time;
            if (!_.isNil(main_bg_color)) item.main_bg_color = main_bg_color;
            if (!_.isNil(description)) item.description = description;
            if (!_.isNil(restaurant_id)) {
                item.restaurant = await DatabaseService.getRepository(Restaurant, this.request).findOne({
                    where: { id: restaurant_id },
                });
            }

            return this.adBannerRepo.save(item);
        } catch (error) {
            throw new BadRequestException(`Không thể cập nhật banner: ${error.message}`);
        }
    }

    async getBannerGroup(req: Request) {
        return await DatabaseService.getRepository(BannerGroup, req).find();
    }

    async createBannerGroup(req: Request, { title, is_activated, time_range }: CreateBannerGroupDto) {
        let start_time: Date;
        let end_time: Date;
        if (time_range) {
            start_time = time_range[0];
            end_time = time_range[1];
            console.log(start_time, end_time);
        }
        const repo = DatabaseService.getRepository(BannerGroup, req);
        try {
            repo.save({ title, is_activated, start_time, end_time });
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return await repo.find();
    }

    async deleteBannerGroup(id: number, req: Request) {
        const repo = DatabaseService.getRepository(BannerGroup, req);
        await DatabaseService.getRepository(AdBanner, req).delete({ group_id: id });
        await repo.delete({ id });
        return await repo.find();
    }

    async fetchBannetGroupDetails(id: number, req: Request) {
        return await DatabaseService.getRepository(BannerGroup, req).findOne({ where: { id } });
    }

    async updateBannerGroup(id: number, params: CreateBannerGroupDto, req: Request) {
        const repo = DatabaseService.getRepository(BannerGroup, req);
        try {
            repo.createQueryBuilder().update().set(params).where({ id }).execute();
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return repo.findOne({ where: { id } });
    }
}
