import { Injectable, Logger } from '@nestjs/common';

import { DRIVER_WALLET_PROVIDER_TEXT } from 'src/common/constants/walletProvider.constant';
import { EDriverWalletProvider } from 'src/entities/driver.entity';
import { DriverDisciplinaryActionService } from '../driverDiscipline/services/driverDisciplinaryAction.service';
import { DriverRewardService } from '../driverReward/services/driverReward.service';
import { OrderDriverExpenseService } from '../order/services/orderDriverExpense.service';
import { GetSummaryDto } from './dto';
import {
    ECompanyReportAmountType,
    ICompanyReportSummaryItem,
    ICompanyReportSummaryResult,
} from './types/companyReportSummaryResult.interface';

@Injectable()
export class CompanyReportService {
    private readonly logger = new Logger(CompanyReportService.name);
    constructor(
        private readonly orderExpenseService: OrderDriverExpenseService,
        private readonly driverRewardService: DriverRewardService,
        private readonly driverDisciplinaryActionService: DriverDisciplinaryActionService,
    ) {}

    private async aggOrderSourceSummaries(
        getSummaryDto: GetSummaryDto,
        provinceId: string,
    ): Promise<ICompanyReportSummaryItem[]> {
        const aggResult = await this.orderExpenseService.aggregateDebtExpenseAndRevenueByDateRange(
            getSummaryDto.fromDate,
            getSummaryDto.toDate,
            provinceId,
        );
        // todo
        return aggResult;
    }

    private async aggRewardSourceSummaries(
        getSummaryDto: GetSummaryDto,
        provinceId: string,
    ): Promise<ICompanyReportSummaryItem[]> {
        const aggResults = await this.driverRewardService.aggregateRewardByDateRange(
            getSummaryDto.fromDate,
            getSummaryDto.toDate,
            provinceId,
        );
        // todo
        return aggResults;
    }

    private async aggDisciplineSourceSummaries(
        getSummaryDto: GetSummaryDto,
        provinceId: string,
    ): Promise<ICompanyReportSummaryItem[]> {
        const aggResult = await this.driverDisciplinaryActionService.aggregateDisciplinaryActionByDateRange(
            getSummaryDto.fromDate,
            getSummaryDto.toDate,
            provinceId,
        );
        // todo
        return aggResult;
    }

    private getAmountTypeText(amountType: ECompanyReportAmountType): string {
        if (amountType === ECompanyReportAmountType.revenue) {
            return 'thu';
        }
        if (amountType === ECompanyReportAmountType.expense) {
            return 'chi';
        }
        return 'cân đối';
    }

    summaryByAmountTypeByWalletProvider(
        sourceSummaries: ICompanyReportSummaryItem[],
        amountType: ECompanyReportAmountType,
        walletProvider: EDriverWalletProvider,
    ): ICompanyReportSummaryItem {
        const title = `Tổng ${this.getAmountTypeText(amountType)} qua ${DRIVER_WALLET_PROVIDER_TEXT[walletProvider]}`;
        const amount = sourceSummaries
            .filter((item) => item.amount_type === amountType && item.wallet_provider === walletProvider)
            .reduce((acc, item) => acc + item.amount, 0);
        return {
            title,
            amount,
            unit: 'VND',
            source: null,
            amount_type: amountType,
            wallet_provider: walletProvider,
        };
    }

    calculateNetProfitByWalletProvider(
        sourceSummaries: ICompanyReportSummaryItem[],
        walletProvider: EDriverWalletProvider,
    ): ICompanyReportSummaryItem {
        const title = `Cân đối qua ${DRIVER_WALLET_PROVIDER_TEXT[walletProvider]}`;
        const expenseAmount = sourceSummaries
            .filter(
                (item) =>
                    item.amount_type === ECompanyReportAmountType.expense && item.wallet_provider === walletProvider,
            )
            .reduce((acc, item) => acc + item.amount, 0);
        const revenueAmount = sourceSummaries
            .filter(
                (item) =>
                    item.amount_type === ECompanyReportAmountType.revenue && item.wallet_provider === walletProvider,
            )
            .reduce((acc, item) => acc + item.amount, 0);
        const amount = Math.abs(revenueAmount) - Math.abs(expenseAmount);
        return {
            title,
            amount,
            unit: 'VND',
            source: null,
            amount_type: ECompanyReportAmountType.net_profit,
            wallet_provider: null,
        };
    }

    summarizeBySourceSummaries(sourceSummaries: ICompanyReportSummaryItem[]): ICompanyReportSummaryItem[] {
        const revenueSummary: ICompanyReportSummaryItem = {
            title: 'Tổng thu',
            amount: sourceSummaries
                .filter((item) => item.amount_type === ECompanyReportAmountType.revenue)
                .reduce((acc, item) => acc + item.amount, 0),
            unit: 'VND',
            source: null,
            amount_type: ECompanyReportAmountType.revenue,
            wallet_provider: null,
        };
        const expenseSummary: ICompanyReportSummaryItem = {
            title: 'Tổng chi',
            amount: sourceSummaries
                .filter((item) => item.amount_type === 'expense')
                .reduce((acc, item) => acc + item.amount, 0),
            unit: 'VND',
            source: null,
            amount_type: ECompanyReportAmountType.expense,
            wallet_provider: null,
        };
        const netProfitSummary: ICompanyReportSummaryItem = {
            title: 'Cân đối',
            amount: Math.abs(revenueSummary.amount) - Math.abs(expenseSummary.amount),
            unit: 'VND',
            source: null,
            amount_type: ECompanyReportAmountType.net_profit,
            wallet_provider: null,
        };
        const walletProviders = Object.values(EDriverWalletProvider);
        const walletProviderSummaries: ICompanyReportSummaryItem[] = [];
        walletProviders.forEach((walletProvider) => {
            const walletProviderRevenueSummary = this.summaryByAmountTypeByWalletProvider(
                sourceSummaries,
                ECompanyReportAmountType.revenue,
                walletProvider,
            );
            const walletProviderExpenseSummary = this.summaryByAmountTypeByWalletProvider(
                sourceSummaries,
                ECompanyReportAmountType.expense,
                walletProvider,
            );
            const walletProviderNetProfitSummary: ICompanyReportSummaryItem = this.calculateNetProfitByWalletProvider(
                sourceSummaries,
                walletProvider,
            );
            walletProviderSummaries.push(
                walletProviderRevenueSummary,
                walletProviderExpenseSummary,
                walletProviderNetProfitSummary,
            );
        });

        return [revenueSummary, expenseSummary, netProfitSummary, ...walletProviderSummaries];
    }

    async getSummary(getSummaryDto: GetSummaryDto, provinceId: string): Promise<ICompanyReportSummaryResult> {
        const orderSourceSummaries = await this.aggOrderSourceSummaries(getSummaryDto, provinceId);
        const rewardSourceSummaries = await this.aggRewardSourceSummaries(getSummaryDto, provinceId);
        const disciplineSourceSummaries = await this.aggDisciplineSourceSummaries(getSummaryDto, provinceId);
        const aggregatedSourceSummaries: ICompanyReportSummaryItem[] = [
            ...orderSourceSummaries,
            ...rewardSourceSummaries,
            ...disciplineSourceSummaries,
        ];

        const summarizedSourceSummaries = this.summarizeBySourceSummaries(aggregatedSourceSummaries);
        return {
            summaries: summarizedSourceSummaries,
        };
    }
}
