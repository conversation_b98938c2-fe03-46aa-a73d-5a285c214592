import { Module } from '@nestjs/common';

import { CompanyReportController } from './companyReport.controller';
import { CompanyReportService } from './companyReport.service';
import { OrderModule } from '../order/order.module';
import { DriverRewardModule } from '../driverReward/driverReward.module';
import { DriverDisciplineModule } from '../driverDiscipline/driverDiscipline.module';

@Module({
    imports: [
        OrderModule,
        DriverRewardModule,
        DriverDisciplineModule,
    ],
    providers: [CompanyReportService],
    controllers: [CompanyReportController],
    
})
export class CompanyReportModule {}
