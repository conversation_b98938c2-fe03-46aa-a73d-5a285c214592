import { EDriverWalletProvider } from 'src/entities/driver.entity';

export enum ECompanyReportSource {
    order = 'order',
    reward = 'reward',
    discipline = 'discipline',
}

export enum ECompanyReportAmountType {
    expense = 'expense',
    revenue = 'revenue',
    net_profit = 'net_profit',
}

export interface ICompanyReportSummaryItem {
    title: string;
    amount: number;
    unit: string;
    amount_type: ECompanyReportAmountType;
    source: ECompanyReportSource | null; // null for summary
    wallet_provider: EDriverWalletProvider | null; // null for summary
}

export interface ICompanyReportSummaryResult {
    summaries: ICompanyReportSummaryItem[];
}
