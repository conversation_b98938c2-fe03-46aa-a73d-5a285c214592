import { Body, Controller, Get, Post, Query, UseGuards, UseInterceptors } from '@nestjs/common';

import AuthGuard from 'src/common/middlewares/auth.gaurd';
import { LoggingInterceptor } from 'src/common/interceptors/logging.interceptor';
import { HeaderProvince } from 'src/common/decorators/province.decorator';
import { CompanyReportService } from './companyReport.service';
import { GetSummaryDto } from './dto';
import { RequirePermissions } from 'src/common/middlewares/permissions.decorator';
import { PermissionsAccessAction } from 'src/entities/permission.entity';
import { ICompanyReportSummaryResult } from './types/companyReportSummaryResult.interface';

@UseInterceptors(LoggingInterceptor)
@UseGuards(AuthGuard)
@Controller('company-reports')
export class CompanyReportController {
    constructor(private readonly companyReportService: CompanyReportService) {}

    @Post('summary/get')
    @RequirePermissions(PermissionsAccessAction.COMPANY_REPORT_SUMMARY_GET)
    async getSummary(
        @Body() getSummaryDto: GetSummaryDto,
        @HeaderProvince() provinceId: string,
    ): Promise<ICompanyReportSummaryResult> {
        return await this.companyReportService.getSummary(getSummaryDto, provinceId);
    }
}
